/**
 * Repomix Configuration for VMS React Component Library
 * 
 * This configuration defines how the VMS React component library
 * should be packaged and documented for distribution and analysis.
 */

module.exports = {
  // Output configuration
  output: {
    filePath: 'vms-react-components.txt',
    style: 'xml', // Use XML style for better structure
    headerText: [
      "# VMS React Component Library",
      "## Enterprise-Grade UI Component Collection",
      "",
      "A comprehensive React component library featuring 40+ production-ready components",
      "for building modern web applications with consistent design and functionality.",
      "",
      "### Component Categories:",
      "- **Form Controls**: InputText, InputCheckbox, InputRadioButton, InputDateField, MobileInputText",
      "- **Navigation**: Tabs, AnchoredHorizontalTabs, Breadcrumb, Pagination, BottomBar",
      "- **Data Display**: Table, Card, Skeleton, Text, LazyImage, VideoPlayer",
      "- **Feedback**: ToastMessage, ProgressTracker, ProgressStepper, RatingBar",
      "- **Layout**: ModalOverlay, SideBarOverlay, Accordion, AccordionGroup, TitleBandHeader",
      "- **Interactive**: Button, IconButton, ToggleButton, Stepper, RangeSelector, IndexSlider",
      "- **Date/Time**: Calendar_V1, Calendar_V2, TimePicker, DateRangeCalendar, DropDownCalendar",
      "- **Maps & Location**: ReactGoogleMap, GoogleMapReact",
      "- **Media & Content**: VideoPlayer, Carousel, LazyImage, Chip",
      "- **Specialized**: PaxSelection, TravellerClass, FairBreakup, AutoCompleteDropDown",
      "",
      "Built with React, Styled-Components, and modern development practices."
    ].join('\n'),
    removeComments: false,
    removeEmptyLines: false,
    topFilesLength: 50,
    showLineNumbers: true,
  },

  // Include patterns - focus on the main component library
  include: [
    // Main component files
    'vms/react/src/components/**/*.js',
    'vms/react/src/components/**/*.jsx',

    // Styled components
    'vms/react/src/components/**/*.styled.js',

    // Component stories for documentation
    'vms/react/src/components/**/*.stories.js',

    // Index files for exports
    'vms/react/src/components/**/index.js',
    'vms/react/src/components/index/index.js',

    // Hooks and utilities
    'vms/react/src/hooks/**/*.js',
    'vms/react/src/utils/**/*.js',

    // Theme and context
    'vms/react/src/components/Theme/**/*.js',

    // Configuration files
    'package.json',
    'rollup.config.js',
    '.babelrc',

    // Documentation
    'README.md',
    'CHANGELOG.md',

    // Component-specific CSS when needed
    'vms/react/src/components/**/*.css',
  ],

  // Exclude patterns
  ignore: [
    // Dependencies
    'node_modules/**',
    'dist/**',
    'build/**',

    // Test files
    '**/*.test.js',
    '**/*.test.jsx',
    '**/*.spec.js',
    '**/*.spec.jsx',
    '__tests__/**',

    // Development files
    '.git/**',
    '.vscode/**',
    '.idea/**',

    // Temporary files
    '**/*.tmp',
    '**/*.temp',
    '**/.DS_Store',

    // Log files
    '**/*.log',

    // Coverage reports
    'coverage/**',

    // Storybook build
    'storybook-static/**',

    // Large asset files
    '**/*.png',
    '**/*.jpg',
    '**/*.jpeg',
    '**/*.gif',
    '**/*.ico',
    '**/*.woff',
    '**/*.woff2',
    '**/*.ttf',
    '**/*.eot',

    // Documentation that's not essential
    'docs/**',

    // Example/demo files
    'examples/**',
    'demo/**',
  ],

  // Security settings
  security: {
    enableSecurityCheck: true,
  },

  // Processing options
  processing: {
    // Group related files together
    groupByDirectory: true,

    // Sort files for better organization
    sortFiles: true,

    // Include file metadata
    includeMetadata: true,
  },
};
