REACT COMPONENT OPTIMIZATION INSTRUCTIONS
=========================================

Based on the optimized Accordion.js component, apply these optimization patterns to all components:

1. PERFORMANCE OPTIMIZATIONS
----------------------------
- Use React.memo() for components that don't need frequent re-renders
- Implement useMemo() for expensive calculations and computed values
- Use useCallback() for event handlers to prevent unnecessary re-renders
- Memoize complex conditional rendering logic
- Extract static components outside main component when possible

Example:
```javascript
const isControlled = useMemo(() => isNonNull(expanded), [expanded]);
const expansionHandler = useCallback((event) => {
  // handler logic
}, [dependencies]);
```

2. PROP DESTRUCTURING & VALIDATION
----------------------------------
- Destructure all props at the top of component
- Use defaultProps for default values
- Implement comprehensive PropTypes validation
- Group related props together in destructuring
- Use meaningful default values

Example:
```javascript
const {
  id,
  children,
  title,
  // ... group related props
  defaultExpanded = false,
  expanded,
  onExpansionToggled,
} = props;
```

3. STATE MANAGEMENT
-------------------
- Use controlled/uncontrolled pattern consistently
- Add proper error handling for missing required props
- Implement validation for prop combinations
- Use functional state updates when depending on previous state

Example:
```javascript
const isControlled = useMemo(() => isNonNull(expanded), [expanded]);
setExpandedState(prev => !prev);
```

4. ACCESSIBILITY IMPROVEMENTS
------------------------------
- Add proper ARIA attributes
- Include meaningful alt text for images
- Ensure keyboard navigation support
- Add proper focus management
- Use semantic HTML elements
- Include screen reader support

Example:
```javascript
<img src={titleIcon} alt="Title icon" />
<AccordionHeaderDiv
  role="button"
  tabIndex={0}
  aria-expanded={isAccordionExpanded}
  onKeyDown={handleKeyDown}
>
```

5. CODE CONSISTENCY PATTERNS
-----------------------------
- Use consistent naming conventions (camelCase for variables, PascalCase for components)
- Implement consistent error handling patterns
- Use utility functions for common operations (like isNonNull)
- Follow consistent prop ordering
- Use consistent className patterns

Example:
```javascript
const isNonNull = (prop) => prop !== null && prop !== undefined;
const className = useClassName(props, additionalClassName);
```

6. COMPONENT STRUCTURE
-----------------------
- Organize imports logically (React, PropTypes, styled-components, hooks, utils)
- Place utility functions before main component
- Use consistent component export patterns
- Add displayName for better debugging
- Group related functionality together

**IMPORTANT: Always preserve styled-components - optimize them, don't remove them**

7. EVENT HANDLING
------------------
- Use event.stopPropagation() when necessary
- Implement proper event delegation
- Add null checks for event handlers
- Use descriptive handler names
- Combine related handlers when possible

Example:
```javascript
const expansionHandler = useCallback((event) => {
  event?.stopPropagation();
  // handler logic
}, [dependencies]);
```

8. CONDITIONAL RENDERING
-------------------------
- Use early returns for simple conditions
- Memoize complex conditional logic
- Use ternary operators for simple conditions
- Extract complex conditions to variables
- Use logical AND for conditional rendering

Example:
```javascript
const expansionElement = useMemo(() => {
  if (isNonNull(expansionIcon) && isNonNull(expansionIconAfter)) {
    return <ExpansionIcon />;
  }
  return <DefaultIcon />;
}, [dependencies]);
```

9. STYLING INTEGRATION
-----------------------
- **PRESERVE ALL STYLED-COMPONENTS** - Never remove existing styled-components
- Use consistent styled-component naming (PascalCase)
- Pass theme props correctly to styled-components
- Implement responsive design patterns within styled-components
- Use CSS-in-JS best practices
- Maintain consistent spacing and layout
- Optimize styled-components performance with shouldForwardProp when needed
- Keep styled-components co-located with their usage

Example:
```javascript
const StyledButton = styled.button.withConfig({
  shouldForwardProp: (prop) => !['customProp'].includes(prop),
})`
  background: ${props => props.theme.colors.primary};
  // ...existing styles...
`;
```

9.1 STYLED-COMPONENTS OPTIMIZATION (DO NOT REMOVE)
---------------------------------------------------
- Keep all existing styled-components intact
- Optimize performance with shouldForwardProp when passing many props
- Use theme props consistently across all styled-components
- Implement proper prop filtering to avoid DOM warnings
- Maintain existing styled-component structure and naming
- Add responsive breakpoints within styled-components when needed

Example of optimizing without removing:
```javascript
// Keep existing styled-component, just optimize it
const ExistingStyledDiv = styled.div.withConfig({
  shouldForwardProp: (prop) => !['isExpanded', 'customProp'].includes(prop),
})`
  display: ${props => props.isExpanded ? 'block' : 'none'};
  transition: all 0.3s ease;
  ${props => props.theme.breakpoints.mobile} {
    padding: 8px;
  }
`;
```

10. ERROR HANDLING
-------------------
- Add comprehensive error boundaries
- Implement prop validation errors
- Use console.error for development warnings
- Add fallback UI for error states
- Validate prop combinations

Example:
```javascript
const ERROR = "Component error message with clear instructions";
useEffect(() => {
  if (isControlled && !isNonNull(onExpansionToggled)) {
    console.error(ERROR);
  }
}, [isControlled, onExpansionToggled]);
```

11. TESTING CONSIDERATIONS
---------------------------
- Add data-testid attributes for testing
- Ensure components are easily testable
- Use descriptive className patterns
- Make state changes predictable
- Export utility functions for testing

12. MOBILE RESPONSIVENESS
-------------------------
- Implement consistent mobile view patterns
- Use responsive breakpoints
- Handle touch events properly
- Optimize for mobile performance
- Test on various screen sizes

IMPLEMENTATION CHECKLIST:
□ All props properly destructured and validated
□ Performance optimizations applied (memo, useMemo, useCallback)
□ **ALL STYLED-COMPONENTS PRESERVED AND OPTIMIZED**
□ Accessibility attributes added
□ Error handling implemented
□ Consistent naming conventions used
□ Mobile responsiveness considered
□ Code properly organized and commented
□ PropTypes comprehensive and accurate
□ Event handlers optimized
□ Conditional rendering memoized
□ Styled-components performance optimized (shouldForwardProp if needed)