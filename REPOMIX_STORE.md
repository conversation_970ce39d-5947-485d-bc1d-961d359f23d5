# VMS React Component Library - Repomix Store

## 📦 Package Information

**Package Name**: VMS React Component Library
**Version**: 1.0.0
**Type**: Enterprise UI Component Library
**Framework**: React 16.8+
**Styling**: Styled-Components
**Bundle Size**: Modular and tree-shakeable

## 🎯 Purpose

This repomix package contains a comprehensive React component library with 40+ production-ready components designed for enterprise web applications. The library provides consistent design patterns, reusable functionality, and modern development practices.

## 🚀 Key Features

### Component Architecture
- ✅ **Modular design** with individual component exports
- ✅ **Styled-components** integration for theming
- ✅ **PropTypes validation** for type safety
- ✅ **Consistent API** patterns across components
- ✅ **Tree-shaking support** for optimal bundle sizes

### Design System
- ✅ **Theme provider** for consistent styling
- ✅ **Responsive breakpoints** built into components
- ✅ **Color palette** with design tokens
- ✅ **Typography system** with standardized scales
- ✅ **Spacing system** using consistent units

### Development Experience
- ✅ **Storybook documentation** for all components
- ✅ **PropTypes definitions** for development guidance
- ✅ **Default props** for easy implementation
- ✅ **Error handling** and validation
- ✅ **Mobile responsiveness** built-in

## 📋 Component Inventory

### Form Controls (8 Components)
- **InputText** - Text input with validation and styling
- **InputCheckbox** - Checkbox with custom styling
- **InputRadioButton** - Radio button with group support
- **InputDateField** - Date input with calendar picker
- **MobileInputText** - Mobile-optimized text input with country codes
- **AutoCompleteDropDown** - Searchable dropdown with filtering
- **VariantOfAutocomplete** - Advanced autocomplete with custom rendering
- **OptionSelector** - Multi-option selection component

### Navigation (6 Components)
- **Tabs** - Horizontal tab navigation
- **AnchoredHorizontalTabs** - Tabs with anchor navigation
- **Breadcrumb** - Hierarchical navigation breadcrumbs
- **Pagination** - Page navigation with customizable controls
- **BottomBar** - Mobile-friendly bottom navigation
- **SideBarOverlay** - Slide-out sidebar navigation

### Data Display (9 Components)
- **Table** - Feature-rich data table with sorting and filtering
- **Card** - Content container with consistent styling
- **Skeleton** - Loading placeholder components
- **Text** - Typography component with variants
- **LazyImage** - Performance-optimized image loading
- **VideoPlayer** - Video playback with controls
- **FairBreakup** - Expandable price breakdown display
- **Chip** - Tag-like display component
- **FilterTag** - Removable filter indicators

### Layout & Containers (7 Components)
- **ModalOverlay** - Modal dialog with overlay
- **ModalPopup** - Popup modal with customizable content
- **Accordion** - Collapsible content sections
- **AccordionGroup** - Grouped accordion management
- **TitleBandHeader** - Page header with title and actions
- **FilterBar** - Filter controls container
- **ToastMessage** - Notification message display

### Interactive Controls (8 Components)
- **Button** - Primary action button with variants
- **IconButton** - Icon-based button component
- **ToggleButton** - Toggle state button
- **ToggleView** - View switcher component
- **Stepper** - Numeric input with increment/decrement
- **RangeSelector** - Dual-handle range selection
- **IndexSlider** - Single-value slider component
- **RatingBar** - Star rating input and display

### Date & Time (6 Components)
- **Calendar_V1** - Basic calendar picker
- **Calendar_V2** - Enhanced calendar with advanced features
- **TimePicker** - Time selection component
- **DateRangeCalendar** - Date range selection
- **DateRangeCalendarMobile** - Mobile-optimized date range picker
- **DropDownCalendar** - Dropdown-style date picker

### Maps & Location (2 Components)
- **ReactGoogleMap** - Google Maps integration
- **GoogleMapReact** - Advanced Google Maps wrapper

### Specialized Components (4 Components)
- **PaxSelection** - Passenger and room selection for travel
- **TravellerClass** - Travel class selection component
- **ProgressTracker** - Multi-step process visualization
- **ProgressStepper** - Step-by-step progress indicator

## 🛠 Technical Specifications

### Dependencies
```json
{
  "react": ">=16.8.0",
  "react-dom": ">=16.8.0",
  "styled-components": ">=5.0.0",
  "prop-types": ">=15.7.0"
}
```

### Build Tools
- **Rollup** for optimized bundling
- **Babel** for JavaScript transpilation
- **PostCSS** for CSS processing
- **Storybook** for component documentation

### Browser Support
- Chrome 70+
- Firefox 65+
- Safari 12+
- Edge 79+
- Mobile browsers (iOS Safari, Chrome Mobile)

## 📁 Package Structure

```
vms-react-components/
├── components/           # Main component library
│   ├── Accordion/       # Individual component folders
│   ├── Button/
│   ├── Table/
│   └── ...
├── hooks/               # Custom React hooks
├── utils/               # Utility functions
├── assets/              # Static assets
├── stories/             # Storybook stories
└── index.js            # Main export file
```

## 🎨 Styling Architecture

### Styled-Components Integration
- **Theme provider** for consistent design tokens
- **Responsive breakpoints** built into components
- **CSS-in-JS** for component-scoped styling
- **Dynamic theming** support

### Design System
- **Color palette** with light/dark mode support
- **Typography scale** with consistent font sizes
- **Spacing system** using standardized units
- **Component variants** for different use cases

## 📖 Usage Examples

### Basic Implementation
```jsx
import { Button, Table, Calendar } from 'vms-react-components';

function App() {
  return (
    <div>
      <Button onClick={handleClick}>Click me</Button>
      <Table data={tableData} headers={headers} />
      <Calendar onDateSelected={handleDateSelect} />
    </div>
  );
}
```

### Advanced Configuration
```jsx
import { ProgressTracker, PaxSelection } from 'vms-react-components';

function BookingFlow() {
  return (
    <ProgressTracker
      steps={bookingSteps}
      currentStep={2}
      onStepClick={handleStepChange}
      isMobile={isMobileView}
    />
  );
}
```

## 🧪 Testing & Quality

### Code Quality
- **ESLint** configuration for consistent code style
- **Prettier** for code formatting
- **PropTypes** validation for runtime type checking
- **Error boundaries** for graceful error handling

### Performance Metrics
- **Bundle size** optimized with tree-shaking
- **Render performance** optimized with React.memo
- **Memory usage** minimized with proper cleanup
- **Accessibility score** 95+ on Lighthouse

## 📚 Documentation

### Available Resources
- **Storybook** interactive component playground
- **PropTypes** documentation for each component
- **Usage examples** in story files
- **Accessibility guidelines** for each component

### Getting Started
1. Extract the repomix package
2. Install dependencies: `npm install`
3. Run Storybook: `npm run storybook`
4. Build library: `npm run build`

## 🔄 Version History

### v1.0.0 (Current)
- ✅ Complete component library with 40+ components
- ✅ Styled-components integration with theme support
- ✅ Storybook documentation for all components
- ✅ PropTypes validation and error handling
- ✅ Mobile-responsive design patterns
- ✅ Comprehensive component API design

## 🤝 Contributing

This package represents a snapshot of the VMS React Component Library. For contributions and updates, please refer to the main repository.

## 📄 License

This component library is proprietary software. Please refer to the license agreement for usage terms and conditions.

---

**Generated by Repomix** - Comprehensive code packaging for AI analysis and documentation
