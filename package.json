{"name": "@vernost_dev/vms-react-components", "version": "1.11.0", "description": "A VMS React Component Library", "main": "dist/cjs/index.js", "module": "dist/esm/index.js", "files": ["dist"], "scripts": {"start": "storybook dev -p 6006", "test": "echo \"Error: no test specified\" && exit 1", "build": "rm -rf dist && rollup -c --bundleConfigAsCjs", "build-storybook": "storybook build"}, "repository": {"type": "git", "url": "git+https://<PERSON><PERSON><EMAIL>/vernosteam/vms_react_components.git"}, "author": "VMS", "license": "MIT", "bugs": {"url": "https://bitbucket.org/vernosteam/vms_react_components/issues"}, "homepage": "https://bitbucket.org/vernosteam/vms_react_components#readme", "devDependencies": {"@babel/core": "^7.28.0", "@babel/preset-env": "^7.28.0", "@babel/preset-react": "^7.27.1", "@googlemaps/react-wrapper": "^1.2.0", "@quickbaseoss/babel-plugin-styled-components-css-namespace": "^1.0.1", "@react-google-maps/api": "^2.20.7", "@rollup/plugin-commonjs": "^28.0.6", "@rollup/plugin-image": "^3.0.3", "@rollup/plugin-node-resolve": "^16.0.1", "@storybook/addon-docs": "^9.0.15", "@storybook/addon-links": "^9.0.15", "@storybook/addon-themes": "^9.0.15", "@storybook/builder-webpack5": "^9.0.15", "@storybook/react": "^9.0.15", "@storybook/react-webpack5": "^9.0.15", "@storybook/testing-library": "^0.2.2", "autoprefixer": "^10.4.21", "babel-loader": "^10.0.0", "classnames": "^2.5.1", "date-fns": "^4.1.0", "fs": "^0.0.1-security", "postcss": "^8.5.6", "postcss-cli": "^11.0.1", "prop-types": "^15.8.1", "rc-slider": "^11.1.8", "react": "^19.1.0", "react-dom": "^19.1.0", "react-is": "^19.1.0", "react-list": "^0.8.18", "react-multi-carousel": "2.8.6", "reactrangeslider": "^3.0.6", "repomix": "^1.1.0", "rollup": "^4.44.2", "rollup-plugin-babel": "^4.4.0", "rollup-plugin-copy": "^3.5.0", "rollup-plugin-dts": "^6.2.1", "rollup-plugin-peer-deps-external": "^2.2.4", "rollup-plugin-postcss": "^4.0.2", "rollup-plugin-terser": "^7.0.2", "storybook": "^9.0.15", "storybook-css-modules-preset": "^1.1.1", "styled-components": "^6.1.19", "use-dark-mode": "^2.3.1", "vanilla-lazyload": "^19.1.3"}}