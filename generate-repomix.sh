#!/bin/bash

# VMS React Component Library - Repomix Generation Script
# This script generates a comprehensive repomix package for the component library

echo "🚀 VMS React Component Library - Repomix Generator"
echo "=================================================="

# Check if repomix is installed
if ! command -v repomix &> /dev/null; then
    echo "❌ Repomix is not installed. Installing..."

    # Try different package managers
    if command -v npm &> /dev/null; then
        npm install -g repomix
    elif command -v yarn &> /dev/null; then
        yarn global add repomix
    elif command -v pnpm &> /dev/null; then
        pnpm add -g repomix
    else
        echo "❌ No package manager found. Please install repomix manually:"
        echo "   npm install -g repomix"
        echo "   or"
        echo "   yarn global add repomix"
        exit 1
    fi

    if [ $? -ne 0 ]; then
        echo "❌ Failed to install repomix. Please install manually:"
        echo "   npm install -g repomix"
        exit 1
    fi
    echo "✅ Repomix installed successfully"
fi

# Verify configuration file exists
if [ ! -f "repomix.config.json" ]; then
    echo "❌ repomix.config.json not found in current directory"
    echo "   Please ensure you're in the correct directory"
    exit 1
fi

echo "📋 Configuration found: repomix.config.json"

# Create output directory if it doesn't exist
mkdir -p repomix-output

# Generate the repomix package
echo "🔄 Generating repomix package..."
repomix --config repomix.config.json

if [ $? -eq 0 ]; then
    echo "✅ Repomix package generated successfully!"
    
    # Move generated file to output directory
    if [ -f "vms-react-components.txt" ]; then
        mv vms-react-components.txt repomix-output/
        echo "📁 Moved vms-react-components.txt to repomix-output/"
    fi
    
    # Copy documentation files to output directory
    echo "📚 Copying documentation files..."
    cp REPOMIX_STORE.md repomix-output/ 2>/dev/null || echo "⚠️  REPOMIX_STORE.md not found"
    cp REPOMIX_USAGE_GUIDE.md repomix-output/ 2>/dev/null || echo "⚠️  REPOMIX_USAGE_GUIDE.md not found"
    cp repomix-package.json repomix-output/ 2>/dev/null || echo "⚠️  repomix-package.json not found"
    cp repomix.config.json repomix-output/
    
    # Generate package summary
    echo "📊 Generating package summary..."
    cat > repomix-output/PACKAGE_SUMMARY.md << EOF
# VMS React Component Library - Repomix Package Summary

## 📦 Package Contents

Generated on: $(date)
Package Size: $(du -h repomix-output/vms-react-components.txt 2>/dev/null | cut -f1 || echo "Unknown")

### Files Included:
- \`vms-react-components.txt\` - Main packaged code
- \`REPOMIX_STORE.md\` - Package documentation  
- \`REPOMIX_USAGE_GUIDE.md\` - Usage instructions
- \`repomix-package.json\` - Package metadata
- \`repomix.config.json\` - Configuration file
- \`PACKAGE_SUMMARY.md\` - This summary

### Component Count: 48 Total
- 8 Recently Optimized Components
- 40+ Production-Ready Components

### Key Features:
✅ Performance optimized with React.memo, useCallback, useMemo
✅ Accessibility-first design with ARIA support  
✅ Mobile-responsive components
✅ Styled-components integration
✅ Comprehensive PropTypes and error handling
✅ Storybook documentation included

### Usage:
1. Extract the package contents
2. Install dependencies: \`npm install\`
3. Start development: \`npm run storybook\`
4. Build library: \`npm run build\`

### Import Example:
\`\`\`jsx
import { Button, Table, Calendar } from './components';
\`\`\`

For detailed usage instructions, see REPOMIX_USAGE_GUIDE.md
EOF

    echo "✅ Package summary created"
    
    # Create a simple README for the output directory
    cat > repomix-output/README.md << EOF
# VMS React Component Library - Repomix Package

This directory contains the complete repomix package for the VMS React Component Library.

## Quick Start

1. **Main Package**: \`vms-react-components.txt\` contains all the source code
2. **Documentation**: Read \`REPOMIX_STORE.md\` for package overview
3. **Usage Guide**: See \`REPOMIX_USAGE_GUIDE.md\` for implementation details
4. **Package Info**: Check \`repomix-package.json\` for metadata

## What's Included

- 48 React components (8 recently optimized)
- Styled-components integration
- Storybook documentation
- Custom hooks and utilities
- Theme system
- Complete source code

## Features

✅ Performance optimized  
✅ Accessibility ready  
✅ Mobile responsive  
✅ TypeScript compatible  
✅ Production tested  

Generated on: $(date)
EOF

    echo "📄 README created for output directory"
    
    # Display final summary
    echo ""
    echo "🎉 REPOMIX PACKAGE GENERATION COMPLETE!"
    echo "======================================"
    echo "📁 Output directory: repomix-output/"
    echo "📦 Main package: vms-react-components.txt"
    echo "📚 Documentation: REPOMIX_STORE.md"
    echo "🔧 Usage guide: REPOMIX_USAGE_GUIDE.md"
    echo ""
    echo "📊 Package Statistics:"
    echo "   - Components: 48 total (8 recently optimized)"
    echo "   - File size: $(du -h repomix-output/vms-react-components.txt 2>/dev/null | cut -f1 || echo "Unknown")"
    echo "   - Generated: $(date)"
    echo ""
    echo "🚀 Next Steps:"
    echo "   1. Review the generated package in repomix-output/"
    echo "   2. Share the package for AI analysis or documentation"
    echo "   3. Use REPOMIX_USAGE_GUIDE.md for implementation"
    echo ""
    echo "✨ Happy coding with VMS React Components!"
    
else
    echo "❌ Failed to generate repomix package"
    echo "   Please check the configuration and try again"
    exit 1
fi
EOF
