# VMS React Component Library - Repomix Usage Guide

## 🚀 Quick Start

### 1. Generate the Repomix Package
```bash
# Install repomix if not already installed
npm install -g repomix

# Generate the package using the configuration
repomix --config repomix.config.js

# This will create: vms-react-components.txt
```

### 2. Package Contents
After generation, you'll have:
- `vms-react-components.txt` - Main packaged code
- `REPOMIX_STORE.md` - Package documentation
- `repomix.config.js` - Configuration file
- `repomix-package.json` - Package metadata

## 📦 What's Included

### Core Components (48 Total)
The repomix package includes all source code for:

#### Recently Optimized Components (8)
- **IconButton** - Performance optimized icon buttons
- **ProgressTracker** - Multi-step progress visualization  
- **InputDateField** - Advanced date input with validation
- **RangeSelector** - Dual-handle range selection
- **Table** - Feature-rich data table
- **IndexSlider** - Customizable slider component
- **Calendar_V2** - Modern calendar picker
- **ReactGoogleMap** - Google Maps integration

#### Production-Ready Components (40+)
- Form controls, navigation, data display
- Layout containers, interactive elements
- Date/time pickers, specialized components

### Supporting Files
- **Styled Components** - All styling definitions
- **Hooks** - Custom React hooks
- **Utilities** - Helper functions
- **Stories** - Storybook documentation
- **Theme** - Design system configuration

## 🛠 Development Workflow

### Setting Up Development Environment
```bash
# Extract the repomix package
repomix extract vms-react-components.txt

# Install dependencies
npm install

# Start Storybook for component development
npm run storybook

# Build the library
npm run build
```

### Component Usage Examples

#### Basic Button Implementation
```jsx
import { Button } from './components/Button/Button';

function App() {
  return (
    <Button 
      buttonType="primary"
      onClick={handleClick}
      disabled={false}
    >
      Click Me
    </Button>
  );
}
```

#### Advanced Table with Optimization
```jsx
import { Table } from './components/Table/Table';

function DataView() {
  const tableData = [
    { id: 1, name: 'John', email: '<EMAIL>' },
    { id: 2, name: 'Jane', email: '<EMAIL>' }
  ];

  const headers = ['ID', 'Name', 'Email'];

  return (
    <Table
      data={tableData}
      headers={headers}
      showSearch={true}
      sortableColumns={[0, 1, 2]}
      onSort={handleSort}
      isMobile={isMobileView}
    />
  );
}
```

#### Calendar Integration
```jsx
import { Calendar_V2 } from './components/Calendar_V2/CalendarV2';

function DatePicker() {
  return (
    <Calendar_V2
      selectedDate={selectedDate}
      onDateSelected={handleDateSelect}
      minDate={new Date()}
      maxDate={addYears(new Date(), 1)}
      isMobile={isMobileDevice}
    />
  );
}
```

## 🎨 Styling Integration

### Theme Configuration
```jsx
import { ThemeWrapper } from './components/Theme/ThemeContext';

function App() {
  return (
    <ThemeWrapper isMobile={isMobileView}>
      <YourComponents />
    </ThemeWrapper>
  );
}
```

### Custom Styling
```jsx
import { Button } from './components/Button/Button';

function CustomButton() {
  return (
    <Button
      additionalClassName={['custom-button']}
      additionalStyle={{ margin: '10px' }}
      buttonType="secondary"
    >
      Custom Styled Button
    </Button>
  );
}
```

## 📱 Mobile Responsiveness

### Mobile-First Approach
```jsx
import { useEffect, useState } from 'react';

function ResponsiveComponent() {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  return (
    <ComponentName 
      isMobile={isMobile}
      isMobileView={isMobile}
    />
  );
}
```

## ♿ Accessibility Implementation

### ARIA Support
```jsx
import { IconButton } from './components/IconButton/IconButton';

function AccessibleButton() {
  return (
    <IconButton
      icon={menuIcon}
      onClick={toggleMenu}
      ariaLabel="Open navigation menu"
      ariaExpanded={isMenuOpen}
      role="button"
      tabIndex={0}
    />
  );
}
```

### Keyboard Navigation
```jsx
import { Table } from './components/Table/Table';

function AccessibleTable() {
  return (
    <Table
      data={data}
      headers={headers}
      onKeyDown={handleKeyNavigation}
      role="grid"
      ariaLabel="Data table with sortable columns"
    />
  );
}
```

## 🔧 Performance Optimization

### Leveraging Built-in Optimizations
```jsx
// Components are already optimized with React.memo
import { ProgressTracker } from './components/ProgressTracker/ProgressTracker';

// Use memoized callbacks for better performance
const handleStepClick = useCallback((stepIndex) => {
  setCurrentStep(stepIndex);
}, []);

function OptimizedProgress() {
  return (
    <ProgressTracker
      steps={steps}
      currentStep={currentStep}
      onStepClick={handleStepClick} // Memoized callback
    />
  );
}
```

### Tree Shaking
```jsx
// Import only what you need for optimal bundle size
import { Button } from './components/Button/Button';
import { Table } from './components/Table/Table';
// Don't import the entire library
```

## 🧪 Testing Integration

### Component Testing
```jsx
import { render, screen } from '@testing-library/react';
import { Button } from './components/Button/Button';

test('renders button with correct text', () => {
  render(<Button>Test Button</Button>);
  expect(screen.getByText('Test Button')).toBeInTheDocument();
});
```

### Accessibility Testing
```jsx
import { axe, toHaveNoViolations } from 'jest-axe';

expect.extend(toHaveNoViolations);

test('button should be accessible', async () => {
  const { container } = render(<Button>Accessible Button</Button>);
  const results = await axe(container);
  expect(results).toHaveNoViolations();
});
```

## 📚 Documentation Access

### Storybook Stories
Each component includes comprehensive Storybook stories:
```bash
# View component documentation
npm run storybook

# Navigate to specific components:
# - Button stories
# - Table stories  
# - Calendar stories
# - etc.
```

### PropTypes Reference
All components include detailed PropTypes:
```jsx
// Example from Button component
Button.propTypes = {
  children: PropTypes.node.isRequired,
  onClick: PropTypes.func,
  buttonType: PropTypes.oneOf(['primary', 'secondary', 'tertiary']),
  disabled: PropTypes.bool,
  // ... more props
};
```

## 🔄 Customization Guide

### Extending Components
```jsx
import { Button } from './components/Button/Button';

const CustomButton = ({ variant, ...props }) => {
  const buttonType = variant === 'danger' ? 'secondary' : 'primary';
  
  return (
    <Button
      {...props}
      buttonType={buttonType}
      additionalClassName={[`custom-${variant}`]}
    />
  );
};
```

### Theme Customization
```jsx
// Extend the theme context
const customTheme = {
  palette: {
    primary: {
      main: '#your-color',
      // ... other theme properties
    }
  }
};
```

## 📊 Performance Monitoring

### Bundle Analysis
```bash
# Analyze bundle size after building
npm run build
npm run analyze # If available

# Check for unused components
# Use tree-shaking to optimize bundle
```

### Runtime Performance
```jsx
// Components include performance optimizations
// Monitor with React DevTools Profiler
// Look for unnecessary re-renders
```

## 🤝 Best Practices

### Component Usage
1. **Always use TypeScript/PropTypes** for type safety
2. **Implement proper error boundaries** around components
3. **Use memoized callbacks** for event handlers
4. **Leverage built-in accessibility** features
5. **Test on mobile devices** regularly

### Performance Tips
1. **Import components individually** for tree-shaking
2. **Use React.memo** for wrapper components
3. **Implement proper key props** for lists
4. **Avoid inline objects** in props
5. **Use the built-in optimizations**

---

This repomix package provides everything needed to implement, customize, and extend the VMS React Component Library in your projects!
