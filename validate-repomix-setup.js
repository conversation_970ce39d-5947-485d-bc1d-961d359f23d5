#!/usr/bin/env node

/**
 * VMS React Component Library - Repomix Setup Validator
 * 
 * This script validates that all necessary files are in place
 * for generating the repomix package.
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 VMS React Component Library - Repomix Setup Validator');
console.log('======================================================');

// Required files for repomix generation
const requiredFiles = [
  'repomix.config.js',
  'REPOMIX_STORE.md',
  'REPOMIX_USAGE_GUIDE.md',
  'repomix-package.json',
  'generate-repomix.sh'
];

// Component directories to check
const componentDirs = [
  'vms/react/src/components',
  'vms/react/src/hooks',
  'vms/react/src/utils'
];

// Recently optimized components to verify
const optimizedComponents = [
  'vms/react/src/components/IconButton/IconButton.js',
  'vms/react/src/components/ProgressTracker/ProgressTracker.js',
  'vms/react/src/components/InputDateField/InputDateField.js',
  'vms/react/src/components/RangeSelector/component/RangeSelector.js',
  'vms/react/src/components/Table/Table.js',
  'vms/react/src/components/IndexSlider/IndexSlider.js',
  'vms/react/src/components/Calendar_V2/CalendarV2.js',
  'vms/react/src/components/ReactGoogleMap/ReactGoogleMap.js'
];

let allValid = true;

// Check required files
console.log('\n📋 Checking required files...');
requiredFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`✅ ${file}`);
  } else {
    console.log(`❌ ${file} - MISSING`);
    allValid = false;
  }
});

// Check component directories
console.log('\n📁 Checking component directories...');
componentDirs.forEach(dir => {
  if (fs.existsSync(dir)) {
    const stats = fs.statSync(dir);
    if (stats.isDirectory()) {
      const files = fs.readdirSync(dir);
      console.log(`✅ ${dir} (${files.length} items)`);
    } else {
      console.log(`❌ ${dir} - NOT A DIRECTORY`);
      allValid = false;
    }
  } else {
    console.log(`❌ ${dir} - MISSING`);
    allValid = false;
  }
});

// Check optimized components
console.log('\n🚀 Checking recently optimized components...');
optimizedComponents.forEach(component => {
  if (fs.existsSync(component)) {
    // Check if file contains optimization patterns
    const content = fs.readFileSync(component, 'utf8');
    const hasReactMemo = content.includes('React.memo') || content.includes('memo(');
    const hasUseCallback = content.includes('useCallback');
    const hasUseMemo = content.includes('useMemo');
    
    if (hasReactMemo && hasUseCallback && hasUseMemo) {
      console.log(`✅ ${path.basename(component)} - Fully optimized`);
    } else {
      console.log(`⚠️  ${path.basename(component)} - Partially optimized`);
      console.log(`    memo: ${hasReactMemo}, useCallback: ${hasUseCallback}, useMemo: ${hasUseMemo}`);
    }
  } else {
    console.log(`❌ ${path.basename(component)} - MISSING`);
    allValid = false;
  }
});

// Check package.json for dependencies
console.log('\n📦 Checking package.json...');
if (fs.existsSync('package.json')) {
  try {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    const requiredDeps = ['react', 'styled-components', 'prop-types'];
    
    console.log('✅ package.json found');
    
    requiredDeps.forEach(dep => {
      if (packageJson.dependencies?.[dep] || packageJson.devDependencies?.[dep]) {
        console.log(`  ✅ ${dep}`);
      } else {
        console.log(`  ⚠️  ${dep} - Not found in dependencies`);
      }
    });
  } catch (error) {
    console.log('❌ package.json - Invalid JSON');
    allValid = false;
  }
} else {
  console.log('⚠️  package.json - Not found (optional)');
}

// Check repomix configuration
console.log('\n⚙️  Validating repomix configuration...');
if (fs.existsSync('repomix.config.js')) {
  try {
    const config = require('./repomix.config.js');
    
    if (config.output?.filePath) {
      console.log(`✅ Output file: ${config.output.filePath}`);
    } else {
      console.log('❌ Output file path not configured');
      allValid = false;
    }
    
    if (config.include?.length > 0) {
      console.log(`✅ Include patterns: ${config.include.length} patterns`);
    } else {
      console.log('❌ No include patterns configured');
      allValid = false;
    }
    
    if (config.ignore?.length > 0) {
      console.log(`✅ Ignore patterns: ${config.ignore.length} patterns`);
    } else {
      console.log('⚠️  No ignore patterns configured');
    }
    
  } catch (error) {
    console.log(`❌ repomix.config.js - Error: ${error.message}`);
    allValid = false;
  }
} else {
  console.log('❌ repomix.config.js - MISSING');
  allValid = false;
}

// Generate summary
console.log('\n📊 Validation Summary');
console.log('====================');

if (allValid) {
  console.log('🎉 ALL CHECKS PASSED!');
  console.log('✅ Ready to generate repomix package');
  console.log('');
  console.log('🚀 Next steps:');
  console.log('   1. Run: npm install -g repomix (if not installed)');
  console.log('   2. Run: ./generate-repomix.sh');
  console.log('   3. Check output in repomix-output/ directory');
} else {
  console.log('❌ VALIDATION FAILED');
  console.log('⚠️  Please fix the issues above before generating the package');
  console.log('');
  console.log('🔧 Common fixes:');
  console.log('   - Ensure you\'re in the correct directory');
  console.log('   - Check that all component files exist');
  console.log('   - Verify repomix configuration is valid');
}

console.log('');
console.log('📚 For help, see REPOMIX_USAGE_GUIDE.md');

// Exit with appropriate code
process.exit(allValid ? 0 : 1);
