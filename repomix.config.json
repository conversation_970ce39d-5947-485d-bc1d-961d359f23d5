{"output": {"filePath": "vms-react-components.txt", "style": "xml", "headerText": "# VMS React Component Library\n## Enterprise-Grade UI Component Collection\n\nA comprehensive React component library featuring 40+ production-ready components\nfor building modern web applications with consistent design and functionality.\n\n### Component Categories:\n- Form Controls: InputText, InputCheckbox, InputRadioButton, InputDateField, MobileInputText\n- Navigation: Tabs, AnchoredHorizontalTabs, Breadcrumb, Pagination, BottomBar\n- Data Display: Table, Card, Skeleton, Text, LazyImage, VideoPlayer\n- Feedback: ToastMessage, ProgressTracker, ProgressStepper, RatingBar\n- Layout: ModalOverlay, SideBarOverlay, Accordion, AccordionGroup, TitleBandHeader\n- Interactive: Button, IconButton, ToggleButton, Stepper, RangeSelector, IndexSlider\n- Date/Time: Calendar_V1, Calendar_V2, TimePicker, DateRangeCalendar, DropDownCalendar\n- Maps & Location: ReactGoogleMap, GoogleMapReact\n- Media & Content: VideoPlayer, Carousel, LazyImage, Chip\n- Specialized: PaxSelection, TravellerClass, FairBreakup, AutoCompleteDropDown\n\nBuilt with React, Styled-Components, and modern development practices."}, "include": ["vms/react/src/components/**/*.js", "vms/react/src/components/**/*.jsx", "vms/react/src/components/**/*.styled.js", "vms/react/src/components/**/*.stories.js", "vms/react/src/components/**/index.js", "vms/react/src/components/index/index.js", "vms/react/src/hooks/**/*.js", "vms/react/src/utils/**/*.js", "vms/react/src/components/Theme/**/*.js", "package.json", "rollup.config.js", ".babelrc", "README.md", "CHANGELOG.md", "vms/react/src/components/**/*.css"], "ignore": ["node_modules/**", "dist/**", "build/**", "**/*.test.js", "**/*.test.jsx", "**/*.spec.js", "**/*.spec.jsx", "__tests__/**", ".git/**", ".vscode/**", ".idea/**", "**/*.tmp", "**/*.temp", "**/.DS_Store", "**/*.log", "coverage/**", "storybook-static/**", "**/*.png", "**/*.jpg", "**/*.jpeg", "**/*.gif", "**/*.ico", "**/*.woff", "**/*.woff2", "**/*.ttf", "**/*.eot", "docs/**", "examples/**", "demo/**"]}