{"$schema": "https://repomix.com/schemas/latest/schema.json", "input": {"maxFileSize": 52428800}, "output": {"filePath": "repomix-output.xml", "style": "xml", "parsableStyle": false, "fileSummary": true, "directoryStructure": true, "files": true, "removeComments": false, "removeEmptyLines": false, "compress": false, "topFilesLength": 5, "showLineNumbers": false, "copyToClipboard": false, "git": {"sortByChanges": true, "sortByChangesMaxCommits": 100, "includeDiffs": false}}, "include": ["src/components/**/*.js", "src/components/**/*.jsx", "src/components/**/*.ts", "src/components/**/*.tsx"], "exclude": ["**/*.stories.js", "**/*.styled.js", "**/*.test.js", "**/*.spec.js", "**/*.min.js", "**/*.svg"], "ignore": {"useGitignore": true, "useDefaultPatterns": true, "customPatterns": []}, "security": {"enableSecurityCheck": true}, "tokenCount": {"encoding": "o200k_base"}}