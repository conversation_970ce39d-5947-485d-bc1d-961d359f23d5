import babel from "@rollup/plugin-babel"
import resolve from "@rollup/plugin-node-resolve";

import commonjs from "@rollup/plugin-commonjs";
import { terser } from "@rollup/plugin-terser";
import peerDepsExternal from 'rollup-plugin-peer-deps-external';
import postcss from "rollup-plugin-postcss";
import image from '@rollup/plugin-image';
import { getFolders } from "./vms/react/src/utils/buildUtils.js";
import copy from 'rollup-plugin-copy';
import packageJson from './package.json' with { type: 'json' };
import Config from './vms/react/src/config/config.json' with { type: 'json' };
// const packageJson = require("./package.json");


const plugins = [
  postcss({
    plugins: [],
    minimize: true,
  }),
  babel({
    exclude: 'node_modules/**',
    presets: ['@babel/preset-react', "@babel/preset-env"],
    plugins: [
      "@quickbaseoss/babel-plugin-styled-components-css-namespace",
      "babel-plugin-styled-components"
    ],
    babelHelpers: 'bundled'
  }),
  peerDepsExternal(),
  resolve(),
  commonjs(),
  // terser({
  //   compress: {
  //     drop_console: Config.isRenderConsoleLog
  //   }
  // }),
  image(),
  copy({
    targets: [
      { src: 'vms/react/src/assets/*', dest: 'dist/assets' }, // Define asset copy rule
    ],
  }),
];



const folderBuilds = getFolders('./vms/react/src/components').map((folder) => {
  return {
    input: `vms/react/src/components/${folder}/index.js`,
    output: {
      file: `dist/${folder}/index.js`,
      sourcemap: true,
      exports: 'named',
      format: 'cjs'
    },
    plugins,
    external: ['react', 'react-dom', '../../../../../vms-theme/theme_mobile.js', '../../../../../vms-theme/theme_desktop.js'],
  };
});


export default [
  {
    input: ['vms/react/src/components/index/index.js'],
    output: [
      {
        file: packageJson.module,
        format: 'cjs',
        sourcemap: true,
        exports: 'named'
      },
    ],
    plugins,
    external: ['react', 'react-dom', '../../../../../vms-theme/theme_mobile.js', '../../../../../vms-theme/theme_desktop.js'],
  },
  ...folderBuilds,
  {
    input: ['vms/react/src/components/index/index.js'],
    output: [
      {
        file: packageJson.main,
        format: 'cjs',
        sourcemap: true,
        exports: 'named'
      },
    ],
    plugins,
    external: ['react', 'react-dom', '../../../../../vms-theme/theme_mobile.js', '../../../../../vms-theme/theme_desktop.js'],
  },
];