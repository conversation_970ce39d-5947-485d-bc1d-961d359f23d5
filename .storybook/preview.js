import React from "react";
import { Theme } from "../vms/react/src/components/Theme/ThemeContext";

export const decorators = [
  (Story) => {
    return (
      <Theme sbDark={false}>
        <Story />
      </Theme>
    );
  },
];
export const parameters = {
  actions: { argTypesRegex: "^on[A-Z].*" },
  controls: {
    matchers: {
      color: /(background|color)$/i,
      date: /Date$/,
    },
  },
  viewport: {
    viewports: {
      responsive: {
        name: 'Responsive',
        styles: {
          width: '100%',
          height: '100%',
        },
      },
      mobile: {
        name: 'Mobile',
        styles: {
          width: '375px',
          height: '667px',
        },
      },
      tablet: {
        name: 'Tablet',
        styles: {
          width: '768px',
          height: '1024px',
        },
      },
      desktop: {
        name: 'Desktop',
        styles: {
          width: '1024px',
          height: '768px',
        },
      },
    },
  },
  options: {
    storySort: {
      method: '',
      order: ["Overview", "Changelog", "*"],
      locales: '',
    },
  },
};