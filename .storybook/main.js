module.exports = {
  "stories": [
    "../vms/react/src/**/*.stories.@(js|jsx|ts|tsx)"
  ],
  "addons": ["@storybook/addon-links", "@storybook/addon-docs", "@storybook/addon-themes", "@storybook/addon-essentials"],
  "framework": {
    "name": "@storybook/react-webpack5",
    "options": {}
  },
  staticDirs: ['./public'],
  babel: async (options) => ({
    ...options,
    presets: [
      ['@babel/preset-env', {
        targets: {
          browsers: ["> 1%", "last 2 versions"]
        }
      }],
      ['@babel/preset-react', {
        runtime: 'automatic'
      }]
    ]
  }),
  webpackFinal: async (config, { configType }) => {
    // Make sure JSX files are processed by babel-loader
    config.module.rules.push({
      test: /\.(js|jsx)$/,
      exclude: /node_modules/,
      use: {
        loader: 'babel-loader',
        options: {
          presets: [
            ['@babel/preset-env', {
              targets: {
                browsers: ["> 1%", "last 2 versions"]
              }
            }],
            ['@babel/preset-react', {
              runtime: 'automatic'
            }]
          ]
        }
      }
    });
    
    return config;
  }
}