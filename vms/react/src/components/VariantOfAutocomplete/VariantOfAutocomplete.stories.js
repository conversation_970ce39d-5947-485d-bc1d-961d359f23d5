import React, { Fragment, useState } from "react";
import { VariantOfAutocomplete } from "./VariantOfAutocomplete";
import styles from "./Variantautocomplete.stories.module.css";
import inputsvgIcon from "../../assets/images/variantofautocomplete/onwordflaghome.svg";
import childAirportIcon from "../../assets/images/variantofautocomplete/childAirportIcon.svg"
import { ModalPopup } from "../ModalPopup/ModalPopup";

export default {
  title: "VMS_REACT/Variant Of Auto complete",
  component: VariantOfAutocomplete,
  argTypes: {},
};
const flyOutItems = [
  { id: "1", label: "Ahmedabad", state: "Gujarat" },
  { id: "2", label: "Mumbai", state: "Maharashtra" },
  { id: "3", label: "Pune", state: "Maharashtra" },
  { id: "4", label: "Jaipur", state: "Rajasthan" },
  { id: "5", label: "Panaji", state: "Goa" },
  { id: "6", label: "Panaji1", state: "Goa" },
  { id: "7", label: "Panaji2", state: "Goa" },
  { id: "8", label: "Panaji3", state: "Goa" },
  { id: "9", label: "Panaji4", state: "Goa" },
  { id: "10", label: "Panaji5", state: "Goa" },
  { id: "11", label: "Panaji6", state: "Goa" },
];
const airports = [
  {
    id: "DEL",
    label: "New Delhi,India,Indira Gandhi International,(DEL)",
    type: "popular",
    ele: {
      AIRLINE_NAME: "Indira Gandhi International",
      AIRLINE_CODE: "DEL",
      CITY_NAME: "New Delhi",
      CITY_CODE: "DEL",
      COUNTRY_CODE: "IN",
      COUNTRY_NAME: "India",
    },
    CITY_NAME: "New Delhi",
    COUNTRY_NAME: "India",
    AIRPORT_NAME: "All Airports",
    COUNTRY_CODE: "IN",
    heading: "POPULAR CITIES",
    SUB_AIRPORTS: [
          {
            id: "DEL",
            label: "New Delhi,India,Indira Gandhi International,(DEL)",
            type: "popular",
            ele: {
              AIRLINE_NAME: "Indira Gandhi International",
              AIRLINE_CODE: "DEL",
              CITY_NAME: "New Delhi",
              CITY_CODE: "DEL",
              COUNTRY_CODE: "IN",
              COUNTRY_NAME: "India",
            },
            CITY_NAME: "New Delhi",
            COUNTRY_NAME: "India",
            AIRPORT_NAME: "Indira Gandhi International",
            COUNTRY_CODE: "IN",
            heading: "POPULAR CITIES",
          },
          {
            id: "BOM",
            label:
            "Mumbai,India,Chhatrapati Shivaji International (Sahar International),(BOM)",
            type: "popular",
            ele: {
              AIRLINE_NAME: "Chhatrapati Shivaji International (Sahar International)",
              AIRLINE_CODE: "BOM",
              CITY_NAME: "Mumbai",
              CITY_CODE: "BOM",
              COUNTRY_CODE: "IN",
              COUNTRY_NAME: "India",
              STATE: "MAHARASHTRA",
            },
            CITY_NAME: "Mumbai",
            COUNTRY_NAME: "India",
            AIRPORT_NAME: "Chhatrapati Shivaji International (Sahar International)",
            COUNTRY_CODE: "IN",
          },
          
        ]
  },
  {
    id: "BOM",
    label:
      "Mumbai,India,Chhatrapati Shivaji International (Sahar International),(BOM)",
    type: "popular",
    ele: {
      AIRLINE_NAME: "Chhatrapati Shivaji International (Sahar International)",
      AIRLINE_CODE: "BOM",
      CITY_NAME: "Mumbai",
      CITY_CODE: "BOM",
      COUNTRY_CODE: "IN",
      COUNTRY_NAME: "India",
      STATE: "MAHARASHTRA",
    },
    CITY_NAME: "Mumbai",
    COUNTRY_NAME: "India",
    AIRPORT_NAME: "Chhatrapati Shivaji International (Sahar International)",
    COUNTRY_CODE: "IN",
  },
  {
    id: "BLR",
    label: "Bangalore,India,Kempegowda Bangalore International Airport,(BLR)",
    type: "popular",
    ele: {
      AIRLINE_NAME: "Kempegowda Bangalore International Airport",
      AIRLINE_CODE: "BLR",
      CITY_NAME: "Bangalore",
      CITY_CODE: "BLR",
      COUNTRY_CODE: "IN",
      COUNTRY_NAME: "India",
    },
    CITY_NAME: "Bangalore",
    COUNTRY_NAME: "India",
    AIRPORT_NAME: "Kempegowda Bangalore International Airport",
    COUNTRY_CODE: "IN",
  },
  {
    id: "GOI",
    label: "Goa,India,Goa International,(GOI)",
    type: "popular",
    ele: {
      AIRLINE_NAME: "Goa International",
      AIRLINE_CODE: "GOI",
      CITY_NAME: "Goa",
      CITY_CODE: "GOI",
      COUNTRY_CODE: "IN",
      COUNTRY_NAME: "India",
      STATE: "GOA",
    },
    CITY_NAME: "Goa",
    COUNTRY_NAME: "India",
    AIRPORT_NAME: "Goa International",
    COUNTRY_CODE: "IN",
  },
  {
    id: "AMD",
    label: "Ahmedabad,India,Ahmedabad,(AMD)",
    type: "popular",
    ele: {
      AIRLINE_NAME: "Ahmedabad",
      AIRLINE_CODE: "AMD",
      CITY_NAME: "Ahmedabad",
      CITY_CODE: "AMD",
      COUNTRY_CODE: "IN",
      COUNTRY_NAME: "India",
      STATE: "GUJARAT",
    },
    CITY_NAME: "Ahmedabad",
    COUNTRY_NAME: "India",
    AIRPORT_NAME: "Ahmedabad",
    COUNTRY_CODE: "IN",
  },
  {
    id: "CCU",
    label: "Kolkata,India,Netaji Subhas Chandra,(CCU)",
    type: "popular",
    ele: {
      AIRLINE_NAME: "Netaji Subhas Chandra",
      AIRLINE_CODE: "CCU",
      CITY_NAME: "Kolkata",
      CITY_CODE: "CCU",
      COUNTRY_CODE: "IN",
      COUNTRY_NAME: "India",
      STATE: "WEST BENGAL",
    },
    CITY_NAME: "Kolkata",
    COUNTRY_NAME: "India",
    AIRPORT_NAME: "Netaji Subhas Chandra",
    COUNTRY_CODE: "IN",
  },
  {
    id: "HYD",
    label: "Hyderabad,India,Hyderabad Airport,(HYD)",
    type: "popular",
    ele: {
      AIRLINE_NAME: "Hyderabad Airport",
      AIRLINE_CODE: "HYD",
      CITY_NAME: "Hyderabad",
      CITY_CODE: "HYD",
      COUNTRY_CODE: "IN",
      COUNTRY_NAME: "India",
      STATE: "TELANGANA",
    },
    CITY_NAME: "Hyderabad",
    COUNTRY_NAME: "India",
    AIRPORT_NAME: "Hyderabad Airport",
    COUNTRY_CODE: "IN",
  },
  {
    id: "PNQ",
    label: "Pune,India,Lohegaon,(PNQ)",
    type: "popular",
    ele: {
      AIRLINE_NAME: "Lohegaon",
      AIRLINE_CODE: "PNQ",
      CITY_NAME: "Pune",
      CITY_CODE: "PNQ",
      COUNTRY_CODE: "IN",
      COUNTRY_NAME: "India",
      STATE: "MAHARASHTRA",
    },
    CITY_NAME: "Pune",
    COUNTRY_NAME: "India",
    AIRPORT_NAME: "Lohegaon",
    COUNTRY_CODE: "IN",
  },
  {
    id: "MAA",
    label: "Chennai,India,Chennai Airport (Meenambakkam),(MAA)",
    type: "popular",
    ele: {
      AIRLINE_NAME: "Chennai Airport (Meenambakkam)",
      AIRLINE_CODE: "MAA",
      CITY_NAME: "Chennai",
      CITY_CODE: "MAA",
      COUNTRY_CODE: "IN",
      COUNTRY_NAME: "India",
      STATE: "TAMIL NADU",
    },
    CITY_NAME: "Chennai",
    COUNTRY_NAME: "India",
    AIRPORT_NAME: "Chennai Airport (Meenambakkam)",
    COUNTRY_CODE: "IN",
  },
  {
    id: "IDR",
    label: "Indore,India,Devi Ahilyabai Holkar,(IDR)",
    type: "popular",
    ele: {
      AIRLINE_NAME: "Devi Ahilyabai Holkar",
      AIRLINE_CODE: "IDR",
      CITY_NAME: "Indore",
      CITY_CODE: "IDR",
      COUNTRY_CODE: "IN",
      COUNTRY_NAME: "India",
      STATE: "MADHYA PRADESH",
    },
    CITY_NAME: "Indore",
    COUNTRY_NAME: "India",
    AIRPORT_NAME: "Devi Ahilyabai Holkar",
    COUNTRY_CODE: "IN",
  },
  {
    id: "JAI",
    label: "Jaipur,India,Sanganeer,(JAI)",
    type: "popular",
    ele: {
      AIRLINE_NAME: "Sanganeer",
      AIRLINE_CODE: "JAI",
      CITY_NAME: "Jaipur",
      CITY_CODE: "JAI",
      COUNTRY_CODE: "IN",
      COUNTRY_NAME: "India",
      STATE: "RAJASTHAN",
    },
    CITY_NAME: "Jaipur",
    COUNTRY_NAME: "India",
    AIRPORT_NAME: "Sanganeer",
    COUNTRY_CODE: "IN",
  },
  {
    id: "IXC",
    label: "Chandigarh,India,Chandigarh,(IXC)",
    type: "popular",
    ele: {
      AIRLINE_NAME: "Chandigarh",
      AIRLINE_CODE: "IXC",
      CITY_NAME: "Chandigarh",
      CITY_CODE: "IXC",
      COUNTRY_CODE: "IN",
      COUNTRY_NAME: "India",
      STATE: "CHANDIGARH",
    },
    CITY_NAME: "Chandigarh",
    COUNTRY_NAME: "India",
    AIRPORT_NAME: "Chandigarh",
    COUNTRY_CODE: "IN",
  },
];
// inputIcon:`https://uat.intermiles.com/static/img/onwordflaghome.svg`,
const Template = (args) => {
  const [value, setValue] = useState("");
  const [menuVisible, setMenuVisibility] = useState(false);
  const _onChangeHandler = (e) => {
    const updatedValue = e.target.value;
    console.log("updated value",updatedValue);
    setValue(updatedValue);
  };
  const getItemValue = (item) => {
    console.log("getItemValue at user-end", item);
    return item.label;
  };
  const shouldItemRender = (item, value) => {
    if (value !== null && value !== undefined && value.trim() !== "") {
      return item.label.toLowerCase().startsWith(value.toLowerCase());
    } else {
      return true;
    }
  };
  const onItemSelect = (value, item) => {
    setValue(item.label);
  };

  return (
    <>
      <VariantOfAutocomplete
        {...args}
        id="ac-id"        
        value={value}
        onChange={_onChangeHandler}
        getItemValue={getItemValue}
        renderInput={null}
        shouldItemRender={shouldItemRender}
        onSelect={onItemSelect}
        onMenuVisibilityChange={(isOpen) => {
          setMenuVisibility(isOpen);
        }}
        altText={"autocomplete icon"}
      />
    </>
  );
};
export const AutocompleteWithoutIcon = Template.bind({});
AutocompleteWithoutIcon.args = {
  placeholder: "City of residence",
  items: flyOutItems,
  searchLabel: "CITY OF RESIDENCE",
  renderInput: null,
};
// const renderItemStyle = {
//   heading: {
//     color: "#939598",
//   },
//   container: {
//     display: "flex",
//     alignItems: "flex-start",
//     width: "-webkit-fill-available",
//   },
//   cityAirportDiv: {
//     display: "flex",
//     alignItems: "flex-start",
//     flexDirection: "column",
//     width: "inherit",
//   },
//   cityCountryName: {
//     fontWeight: "bold",
//     fontSize: "14px",
//   },
//   airportName: {
//     fontWeight: "lighter",
//     fontSize: "11px",
//     padding: "0px",
//     margin: "0px",
//     color: "#939598",
//   },
//   airportDiv: {
//     display: "flex",
//     alignItems: "flex-end",
//   },
//   airportCode: {
//     padding: "2px",
//     backgroundColor: "#ef4044",
//     color: "#fff",
//     fontSize: "12px",
//     fontWeight: "bold",
//     minWidth: "35px",
//     maxWidth: "35px",
//     textAlign: "center",
//   },
//   backDropStyle: {
//     display: "block",
//     position: "fixed",
//     top: 0,
//     left: 0,
//     zIndex: 100,
//     width: "100%",
//     height: "100vh",
//     backgroundColor: "rgba(0,0,0,0.8)",
//   },
// };

const renderItemToDisplay = (item, highlighted) => {
  return (
    <div>
      {!!item.heading && item.heading.length > 0 ? (
        <small className={styles.heading}>{item.heading}</small>
      ) : null}
      <div className={styles.container}>
        <div className={styles.cityAirportDiv}>
          <span
            className={styles.cityCountryName}
          >{`${item.CITY_NAME} ${item.COUNTRY_NAME}`}</span>
          <p className={styles.airportName}>{item.AIRPORT_NAME}</p>
        </div>
        <div className={styles.airportDiv}>
          <span className={styles.airportCode}>{item.id}</span>
        </div>
      </div>
    </div>
  );
};
const BackDropComponent = ({ onBackDropClick }) => {
  return (
    <div
      onClick={(e) => {
        e.stopPropagation();
        onBackDropClick();
      }}
      className={styles.backDropStyle}
    ></div>
  );
};
const renderMenuComponent = (
  children,
  menuVisible,
  onBackDropClick,
  isMobile
) => {
  return (
    <Fragment>
      {isMobile === false && menuVisible && (
        <BackDropComponent onBackDropClick={onBackDropClick} />
      )}
      <div
        style={{
          maxHeight: isMobile !== true && "250px",
          width: isMobile == false ? "30%" : "100%",
          display: "flex",
          zIndex: 101,
          backgroundColor: "#ffffff",
          borderRadius: isMobile !== true && "0px 0px 3px 3px",
          border: isMobile !== true && "1px solid #03868b",
          borderTopWidth: "0px",
        }}
      >
        <ul className={styles.listView}>{children}</ul>
      </div>
    </Fragment>
  );
};
export const CustomVariantOfAutoComplete = (args) => {
  const [value, setValue] = useState("");
  const [menuVisible, setMenuVisibility] = useState(false);
  const [itemsToDisplay, setItemsToDisplay] = useState(airports);
  const _onChangeHandler = (e) => {
    const updatedValue = e.target.value;
    setValue(updatedValue);
  };

  const getItemValue = (item) => {
    console.log("getItemValue at user-end", item);
    return item.label;
  };
  const shouldItemRender = (item, value) => {
    if (value !== null && value !== undefined && value.trim() !== "") {
      return item.label.toLowerCase().startsWith(value.toLowerCase());
    } else {
      return true;
    }
  };
  const onItemSelect = (value, item) => {
    setValue(item.CITY_NAME);
  };
  const onBackDropClick = () => {
    setMenuVisibility(false);
  };
  const isMobile = false;
  return (
    <VariantOfAutocomplete
    id="from-id"
      {...args}
      items={itemsToDisplay}
      value={value}
      renderInput={null}
      additionalClassName={[styles.myView]}
      onChange={_onChangeHandler}
      renderItem={renderItemToDisplay}
      getItemValue={getItemValue}
      secondPlaceholder={"Flying From"}
      onMenuVisibilityChange={(isOpen) => {
        setMenuVisibility(isOpen);
      }}
      onBlur={()=>{console.log("autocomplete is blur")}}
      onFocus={()=>{console.log("autocomplete is onFocus")}}
      shouldItemRender={shouldItemRender}
      onSelect={onItemSelect}
      inputIcon={inputsvgIcon}
      inputProps={{
        placeholder: "Where from?",
        style: { fontWeight: value != "" ? "700" : null },
      }}
      open={menuVisible}
      renderMenu={(children) => {
        return renderMenuComponent(
          children,
          menuVisible,
          onBackDropClick,
          isMobile
        );
      }}
      isMobileView={isMobile}
      altText={"autocomplete icon"}
    />
  );
};

export const CustomVariantOfAutoCompleteMobile = (args) => {
  const [value, setValue] = useState("");
  const [showModal, setShowModal] = useState(false);
  const [menuVisible, setMenuVisibility] = useState(false);
  const [itemsToDisplay, setItemsToDisplay] = useState(airports);
  
  // Get altText from args or use default
  const { altText = "Search location icon" } = args;
  
  const _onChangeHandler = (e) => {
    const updatedValue = e.target.value;
    setValue(updatedValue);
  };

  const getItemValue = (item) => {
    console.log("getItemValue at user-end", item);
    return item.label;
  };
  const shouldItemRender = (item, value) => {
    if (value !== null && value !== undefined && value.trim() !== "") {
      return item.label.toLowerCase().startsWith(value.toLowerCase());
    } else {
      return true;
    }
  };
  const onItemSelect = (value, item) => {
    setValue(item.CITY_NAME);
    setShowModal(false);
    setMenuVisibility(false);
  };
  const onBackDropClick = () => {
    setMenuVisibility(false);
  };
  const isMobile = true;
  return (
    <Fragment>
      <div className={styles.wrapperdiv}>
        <div className={styles.searchDiv}>
          <button id="btn-icon-id" className={styles.searchIcon}>
            <img src={inputsvgIcon} alt={altText}/>
          </button>
          <button
          id="btn-id"
            value={value}
            className={styles.searchInput}
            onClick={() => {
              setShowModal(true);
              setMenuVisibility(true);
            }}
          >
            {value == "" ? `Where from?` : value}
          </button>
        </div>
      </div>
      <ModalPopup
        isMobileView={true}
        isOpen={showModal}
        onRequestClose={() => {
          setShowModal(false);
          setMenuVisibility(false);
        }}
      >
        <VariantOfAutocomplete
        id={"from-id-mob"}
          {...args}
          isMobileView={true}
          renderInput={null}
          airportIconimgclass={"stringairimg"}
          items={itemsToDisplay}
          value={value}
          onChange={_onChangeHandler}
          secondPlaceholder={"Flying From"}
          renderItem={renderItemToDisplay}
          getItemValue={getItemValue}
          onMenuVisibilityChange={(isOpen) => {
            setMenuVisibility(isOpen);
            setShowModal(isOpen);
          }}
          searchLabel={`Departing From`}
          shouldItemRender={shouldItemRender}
          onSelect={onItemSelect}
          inputIcon={inputsvgIcon}
          defaultInputIcon={true}
          inputProps={{
            placeholder: "Select Option",
            style: { fontWeight: value != "" ? "700" : null },
          }}
          childAirportIcon={childAirportIcon}
          airportIcon={inputsvgIcon}
          open={menuVisible}
          renderMenu={(children) => {
            return renderMenuComponent(
              children,
              menuVisible,
              onBackDropClick,
              isMobile
            );
          }}
          altText={"autocomplete icon"}
        />
      </ModalPopup>
    </Fragment>
  );
};
CustomVariantOfAutoCompleteMobile.args = {
  altText: "Departure location search icon"
};
CustomVariantOfAutoCompleteMobile.parameters = {
  viewport: {
    defaultViewport: "iphone5",
  },
};
