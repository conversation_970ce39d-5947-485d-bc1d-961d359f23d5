import React, { Fragment, useEffect, useRef, useState, memo, useCallback, useMemo } from "react";
import PropTypes from "prop-types";
import {
  Div,
  SearchInput,
  SearchButton,
  Wrapper,
  Svg,
  Label,
  ListItems,
  ListItem,
  RenderItemDiv,
  InputDiv,
  SecondPlaceholderLabel,
  AirportIconImg,
  ChildAirportIconImg,
  ChildList,
} from "./VariantOfAutocomplete.styled";
import { useClassName } from "../../hooks/useClassName";
import { useClickAway, useOnClickOutside } from "../../hooks/outsideClickHandler";
import { ThemeWrapper } from "../Theme/ThemeContext";

// Constants
const CLEAR_ICON = "CLEAR_ICON";
const DROPDOWN_ICON = "DROPDOWN_ICON";

// Utility function
const isNonNull = (prop) => prop !== null && prop !== undefined;

// Memoized MenuComponent
const MenuComponent = memo(({
  items = [],
  renderMenu,
  shouldItemRender,
  renderItem,
  onMouseEnter,
  onClick,
  higligtedRenderItem,
  value,
  airportIcon,
  childAirportIcon,
  airportIconimgclass,
}) => {
  const filteredItems = useMemo(() => 
    items.filter((item) =>
      isNonNull(shouldItemRender) ? shouldItemRender(item, value) : true
    ), [items, shouldItemRender, value]
  );

  const childrenOfMenu = useMemo(() => 
    filteredItems.map((item, index) => (
      <Fragment key={`${item?.id || item?.label || index}`}>
        <ListItem
          onClick={(e) => {
            e.preventDefault();
            onClick(item);
          }}
          onMouseEnter={(e) => {
            onMouseEnter(item);
          }}
          role="option"
          aria-selected={item?.label === higligtedRenderItem?.label}
        >
          {isNonNull(airportIcon) && (
            <AirportIconImg 
              src={airportIcon} 
              alt="airport icon" 
              className={airportIconimgclass}
              loading="lazy"
            />
          )}
          {isNonNull(renderItem)
            ? renderItem(item, item?.label === higligtedRenderItem?.label)
            : item?.label}
        </ListItem>
        {item?.SUB_AIRPORTS?.length > 0 && item.SUB_AIRPORTS.map((airport, subIndex) => (
          <ChildList
            key={`${airport?.id || airport?.label || subIndex}`}
            onClick={(e) => {
              e.preventDefault();
              onClick(airport);
            }}
            onMouseEnter={(e) => {
              onMouseEnter(airport);
            }}
            role="option"
            aria-selected={airport?.label === higligtedRenderItem?.label}
          >
            {isNonNull(childAirportIcon || airportIcon) && (
              <ChildAirportIconImg 
                src={item?.AIRPORT_NAME === "All Airports" ? childAirportIcon : airportIcon}
                alt="airport icon"
                loading="lazy"
              />
            )}
            {isNonNull(renderItem)
              ? renderItem(airport, airport?.label === higligtedRenderItem?.label)
              : airport?.label}
          </ChildList>
        ))}
      </Fragment>
    )), [filteredItems, onClick, onMouseEnter, higligtedRenderItem, airportIcon, childAirportIcon, airportIconimgclass, renderItem]
  );

  return <Fragment>{renderMenu(childrenOfMenu)}</Fragment>;
});

MenuComponent.displayName = 'MenuComponent';

// Memoized SearchIconSvg component
const SearchIconSvg = memo(({ iconType, onClick }) => (
  <Svg
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="16"
    viewBox="0 0 16 16"
    onClick={onClick}
    role="button"
    tabIndex={0}
    aria-label={iconType === DROPDOWN_ICON ? "Open dropdown" : "Clear input"}
    onKeyDown={(e) => {
      if (e.key === 'Enter' || e.key === ' ') {
        e.preventDefault();
        onClick(e);
      }
    }}
  >
    {iconType === DROPDOWN_ICON ? (
      <g fill="none" fillRule="evenodd">
        <path
          fill="currentColor"
          d="M11.625 16.64L17.25 8 6 8z"
          transform="translate(-6 -8)"
        />
        <path d="M0 0H24V24H0z" transform="translate(-6 -8)" />
      </g>
    ) : (
      <g fill="none" fillRule="evenodd">
        <path
          fill="currentColor"
          fillRule="nonzero"
          d="M14 1.41L12.59 0 7 5.59 1.41 0 0 1.41 5.59 7 0 12.59 1.41 14 7 8.41 12.59 14 14 12.59 8.41 7z"
        />
        <path d="M-5-5h24v24H-5z" />
      </g>
    )}
  </Svg>
));

SearchIconSvg.displayName = 'SearchIconSvg';

export const VariantOfAutocomplete = memo((props) => {
  // 1. PROP DESTRUCTURING & VALIDATION
  const {
    value = "",
    onChange,
    placeholder = "",
    items = [],
    getItemValue,
    renderItem,
    shouldItemRender,
    inputProps = {},
    onMenuVisibilityChange,
    inputIcon,
    defaultInputIcon = true,
    isMobileView = false,
    searchLabel,
    renderInput,
    renderMenu,
    onSelect,
    onInputClick,
    additionalClassName,
    additionalStyle,
    inputClassName,
    inputStyle,
    open = false,
    onBlur,
    onFocus,
    airportIcon,
    childAirportIcon,
    secondPlaceholder,
    airportIconimgclass,
    altText = "icon",
    id,
    ...otherProps
  } = props;

  // 2. STATE MANAGEMENT
  const [showItems, setShowItems] = useState(false);
  const [rightIconType, setRightIconType] = useState(DROPDOWN_ICON);
  const [onItemHover, setOnItemHover] = useState(null);

  // 3. REFS
  const searchInputRef = useRef();
  const autoCompleteDropdownRef = useRef();

  // 4. PERFORMANCE OPTIMIZATIONS
  const computedClassName = useMemo(() => 
    useClassName(props, additionalClassName), 
    [props, additionalClassName]
  );

  const inputClassNameToApply = useMemo(() => 
    useClassName(props, inputClassName), 
    [props, inputClassName]
  );

  const filteredItems = useMemo(() => 
    items.filter((item) =>
      isNonNull(shouldItemRender) ? shouldItemRender(item, value) : true
    ), [items, shouldItemRender, value]
  );

  // 5. EVENT HANDLERS with useCallback
  const inputOnChangeHandler = useCallback((e) => {
    const inputValue = e.target.value;
    setRightIconType(inputValue === "" ? DROPDOWN_ICON : CLEAR_ICON);
    
    if (onChange) {
      try {
        onChange(e);
      } catch (error) {
        console.error("VariantOfAutocomplete: Error in onChange callback", error);
      }
    }
  }, [onChange]);

  const rightIconClickHandler = useCallback((e) => {
    e.stopPropagation();
    
    if (value === "") {
      setShowItems(true);
      if (onMenuVisibilityChange) {
        try {
          onMenuVisibilityChange(true);
        } catch (error) {
          console.error("VariantOfAutocomplete: Error in onMenuVisibilityChange callback", error);
        }
      }
      searchInputRef.current?.focus();
    } else {
      // Clear the input
      const syntheticEvent = { target: { value: "" } };
      if (onChange) {
        try {
          onChange(syntheticEvent);
        } catch (error) {
          console.error("VariantOfAutocomplete: Error in onChange callback during clear", error);
        }
      }
      setShowItems(true);
      if (onMenuVisibilityChange) {
        try {
          onMenuVisibilityChange(true);
        } catch (error) {
          console.error("VariantOfAutocomplete: Error in onMenuVisibilityChange callback", error);
        }
      }
    }
  }, [value, onChange, onMenuVisibilityChange]);

  const searchInputClickHandler = useCallback((e) => {
    e.stopPropagation();
    
    if (onInputClick) {
      try {
        onInputClick(e);
      } catch (error) {
        console.error("VariantOfAutocomplete: Error in onInputClick callback", error);
      }
    }
    
    if (!isMobileView) {
      const isOpen = isNonNull(open) ? open : !showItems;
      if (onMenuVisibilityChange) {
        try {
          onMenuVisibilityChange(isOpen);
        } catch (error) {
          console.error("VariantOfAutocomplete: Error in onMenuVisibilityChange callback", error);
        }
      }
      setShowItems(isOpen);
    }
  }, [isMobileView, open, showItems, onMenuVisibilityChange, onInputClick]);

  const onClickListItem = useCallback((item) => {
    if (getItemValue) {
      try {
        getItemValue(item);
      } catch (error) {
        console.error("VariantOfAutocomplete: Error in getItemValue callback", error);
      }
    }
    
    if (onSelect) {
      try {
        onSelect(item?.label, item);
      } catch (error) {
        console.error("VariantOfAutocomplete: Error in onSelect callback", error);
      }
    }
    
    setShowItems(false);
    if (onMenuVisibilityChange) {
      try {
        onMenuVisibilityChange(false);
      } catch (error) {
        console.error("VariantOfAutocomplete: Error in onMenuVisibilityChange callback", error);
      }
    }
  }, [getItemValue, onSelect, onMenuVisibilityChange]);

  const handleMouseEnter = useCallback((item) => {
    setOnItemHover(item);
  }, []);

  const handleFocus = useCallback((e) => {
    if (onFocus) {
      try {
        onFocus(e);
      } catch (error) {
        console.error("VariantOfAutocomplete: Error in onFocus callback", error);
      }
    }
  }, [onFocus]);

  const handleBlur = useCallback((e) => {
    if (onBlur) {
      try {
        onBlur(e);
      } catch (error) {
        console.error("VariantOfAutocomplete: Error in onBlur callback", error);
      }
    }
  }, [onBlur]);

  const handleKeyDown = useCallback((event) => {
    if (event.key === 'Escape' && showItems) {
      event.preventDefault();
      setShowItems(false);
      if (onMenuVisibilityChange) {
        onMenuVisibilityChange(false);
      }
    }
  }, [showItems, onMenuVisibilityChange]);

  // 6. EFFECTS
  useEffect(() => {
    if (open !== undefined && onMenuVisibilityChange) {
      onMenuVisibilityChange(open);
    }
    setShowItems(open);
  }, [open, onMenuVisibilityChange]);

  useEffect(() => {
    setRightIconType(value === "" ? DROPDOWN_ICON : CLEAR_ICON);
  }, [value]);

  // 7. useClickAway hook
  useClickAway(autoCompleteDropdownRef, () => {
    if (!isMobileView) {
      setShowItems(false);
      if (open && onMenuVisibilityChange) {
        onMenuVisibilityChange(false);
      }
    }
  });

  // 8. ERROR HANDLING
  useEffect(() => {
    if (!getItemValue) {
      console.warn('VariantOfAutocomplete: getItemValue prop is required');
    }
    if (!Array.isArray(items)) {
      console.warn('VariantOfAutocomplete: items should be an array');
    }
  }, [getItemValue, items]);

  // 9. MEMOIZED COMPONENTS
  const inputElement = useMemo(() => {
    const commonInputProps = {
      id: id || undefined,
      className: `${inputClassNameToApply} vms_variantOfautocomplete_searchinput`,
      style: inputStyle,
      ref: searchInputRef,
      value,
      placeholder,
      autoComplete: "off",
      onClick: searchInputClickHandler,
      onChange: inputOnChangeHandler,
      onFocus: handleFocus,
      onBlur: handleBlur,
      onKeyDown: handleKeyDown,
      role: "combobox",
      "aria-expanded": showItems,
      "aria-haspopup": "listbox",
      "aria-autocomplete": "list",
      ...inputProps
    };

    return (
      <Div 
        isMobile={isMobileView} 
        isOpen={showItems} 
        className="vms_variantOfautocomplete_search_input"
      >
        {isNonNull(inputIcon) && defaultInputIcon && (
          <SearchButton 
            id={id ? `${id}-searchbtn` : undefined} 
            className="vms_variantOfautocomplete_searchbutton" 
            onClick={(e) => { 
              e.stopPropagation(); 
              e.preventDefault(); 
            }}
          >
            <img src={inputIcon} alt={altText} loading="lazy" />
          </SearchButton>
        )}
        <InputDiv>
          {isNonNull(secondPlaceholder) && (
            <SecondPlaceholderLabel>{secondPlaceholder}</SecondPlaceholderLabel>
          )}
          <SearchInput {...commonInputProps} isMobile={isMobileView} />
        </InputDiv>
        {!isNonNull(inputIcon) && (
          <SearchButton 
            id={id ? `${id}-searchbtn` : undefined} 
            className="vms_variantOfautocomplete_searchbtnicon"
          >
            <SearchIconSvg 
              iconType={rightIconType} 
              onClick={rightIconClickHandler} 
            />
          </SearchButton>
        )}
      </Div>
    );
  }, [
    id, inputClassNameToApply, inputStyle, value, placeholder, searchInputClickHandler,
    inputOnChangeHandler, handleFocus, handleBlur, handleKeyDown, showItems, inputProps,
    isMobileView, inputIcon, defaultInputIcon, altText, secondPlaceholder, rightIconType,
    rightIconClickHandler
  ]);

  const menuElement = useMemo(() => {
    if (!showItems) return null;

    if (isNonNull(renderMenu)) {
      return (
        <MenuComponent
          airportIconimgclass={airportIconimgclass}
          id="menu-component"
          items={items}
          value={value}
          renderMenu={renderMenu}
          renderItem={renderItem}
          shouldItemRender={shouldItemRender}
          onClick={onClickListItem}
          onMouseEnter={handleMouseEnter}
          higligtedRenderItem={onItemHover}
          isMobile={isMobileView}
          className="vms_variantOfautocomplete_menucomponent"
          airportIcon={airportIcon}
          childAirportIcon={childAirportIcon}
        />
      );
    }

    return (
      <RenderItemDiv isMobile={isMobileView} className="vms_variantOfautocomplete_renderitem">
        <ListItems 
          isMobile={isMobileView} 
          className="vms_variantOfautocomplete_list_items"
          role="listbox"
          aria-label="Autocomplete options"
        >
          {filteredItems.map((item, index) => (
            <Fragment key={`${item?.id || item?.label || index}`}>
              <ListItem
                className="vms_variantOfautocomplete_listitem"
                onClick={(e) => {
                  e.preventDefault();
                  onClickListItem(item);
                }}
                onMouseEnter={(e) => {
                  handleMouseEnter(item);
                }}
                role="option"
                aria-selected={item?.label === onItemHover?.label}
              >
                {isNonNull(renderItem)
                  ? renderItem(item, item?.label === onItemHover?.label)
                  : item?.label}
              </ListItem>
            </Fragment>
          ))}
        </ListItems>
      </RenderItemDiv>
    );
  }, [
    showItems, renderMenu, items, value, renderItem, shouldItemRender, onClickListItem,
    handleMouseEnter, onItemHover, isMobileView, airportIconimgclass, airportIcon,
    childAirportIcon, filteredItems
  ]);

  return (
    <ThemeWrapper isMobile={isMobileView || false}>
      <Fragment>
        <Wrapper 
          id="vms_variantOfautocomplete_container" 
          className={`${computedClassName} vms_variantOfautocomplete_container`} 
          style={additionalStyle} 
          isOpen={showItems} 
          ref={autoCompleteDropdownRef}
          onKeyDown={handleKeyDown}
          {...otherProps}
        >
          {isNonNull(searchLabel) && (
            <Label 
              id={id ? `${id}-label` : undefined} 
              className="vms_variantOfautocomplete_label"
            >
              {searchLabel}
            </Label>
          )}
          {renderInput || inputElement}
          {menuElement}
        </Wrapper>
      </Fragment>
    </ThemeWrapper>
  );
});

VariantOfAutocomplete.displayName = 'VariantOfAutocomplete';

VariantOfAutocomplete.defaultProps = {
  defaultInputIcon: true,
  isMobileView: false,
  inputProps: null,
  renderMenu: null,
  renderItem: null,
  shouldItemRender: null,
  inputIcon: null,
  additionalClassName: null,
  additionalStyle: null,
  inputClassName: null,
  inputStyle: null,
  open: false,
  renderInput: null,
};

VariantOfAutocomplete.propTypes = {
  /**
   * Placeholder for input text.
   */
  placeholder: PropTypes.string,
  /**
   * The value to display in the input field
   */
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),

  /**
   * Invoked every time the user changes the input's value.
   */
  onChange: PropTypes.func,

  /**
   * List of options, The items to display in the dropdown menu.
   */
  items: PropTypes.array.isRequired,
  /**
   * Invoked when the user selects an item from the dropdown menu
   * (value: string, item: any) => void
   */
  onSelect: PropTypes.func,
  /**
   * function for values in input box, Used to read the display value from each entry in items
   * (item: any) => string
   */
  getItemValue: PropTypes.func.isRequired,

  /**
   * Invoked for each entry in items to generate the render tree for each item in the dropdown menu.
   * styles is an optional set of styles that can be applied to improve the look/feel of the items in the dropdown menu.
   * (item: any, isHighlighted: boolean, styles?: CSSProperties) => ReactNode
   */
  renderItem: PropTypes.func,
  /**
   * Invoked for each entry in items and its return value is used to determine whether or not it should be displayed in the dropdown menu.
   * By default all items are always rendered.
   * (item: any, value: string) => boolean
   */
  shouldItemRender: PropTypes.func,

  /**
   * Invoked to generate the custom input element
   */
  renderInput: PropTypes.node,

  /**
   * Invoked to generate the render tree for the dropdown menu
   * (items: any[], value: string, styles: object) => ReactNode
   */
  renderMenu: PropTypes.func,
  /**
   * Props passed to input element rendered by Autocomplete or renderInput props.
   * It Can be used for placeholder, event handlers (onFocus, onBlur, etc.), autoFocus, etc..
   * HTMLProps<HTMLInputElement>
   */
  inputProps: PropTypes.object,
  /**
   * Invoked every time the dropdown menu's visibility changes (i.e. every time it is displayed/hidden)
   * (isOpen: Boolean) => void
   */
  onMenuVisibilityChange: PropTypes.func,
  /**
   * Name of icon which to be display as input value.
   */
  inputIcon: PropTypes.string,
  /**
   * To display default icon at the start of select input, default: true
   */
  defaultInputIcon: PropTypes.bool,
  /**
   * Display mode of mobile or desktop.
   */
  isMobileView: PropTypes.bool,
  /**
   * label for input
   */
  searchLabel: PropTypes.string,
  /**
   * label class for input.
   */
  labelClassName: PropTypes.arrayOf(PropTypes.string),
  /**
   * Classes to add additional styling in the parent container
   */
  additionalClassName: PropTypes.arrayOf(PropTypes.string),
  /**
   * Property to set the `style` object on parent container
   */
  additionalStyle: PropTypes.object,
  /**
   * Provide classname to input control
   */
  inputClassName: PropTypes.arrayOf(PropTypes.string),
  /**
   * Property to set the `style` object on input element
   */
  inputStyle: PropTypes.object,
  /**
   * Used to override the internal logic which displays/hides the dropdown menu.
   * This is useful if you want to force a certain state based on your UX/business logic.
   * Use it together with onMenuVisibilityChange for fine-grained control over the dropdown menu dynamics
   */
  open: PropTypes.bool,
  /** 
   * onFocus event occurs when an input element gets focus.
   */
  onFocus: PropTypes.func,
  /**
   * onBlur event occurs when an input loses focus.
   */
  onBlur: PropTypes.func,
  airportIcon: PropTypes.string,
  childAirportIcon: PropTypes.string,
  secondPlaceholder: PropTypes.string,
  altText: PropTypes.string,
};
