.myView > div {
  width: 30%;
}
.listView {
  overflow: auto;
  margin: 0;
  list-style: none;
  padding-inline-start: 0px;
  margin-block-start: 0px;
  margin-block-end: 0px;
  width: 100%;
}
.listView::-webkit-scrollbar {
  width: 3.5px !important;
  background-color: transparent !important;
}
.listView::-webkit-scrollbar-thumb {
  background-color: #848884 !important;
  border-radius: 10px !important;
}
.heading {
  color: #939598;
}
.container {
  display: flex;
  align-items: flex-start;
  width: -webkit-fill-available;
}
.cityAirportDiv {
  display: flex;
  align-items: flex-start;
  flex-direction: column;
  width: inherit;
}
.cityCountryName {
  font-weight: bold;
  font-size: 14px;
}

.airportName {
  font-weight: lighter;
  font-size: 11px;
  padding: 0px;
  margin: 0px;
  color: #939598;
}
.airportDiv {
  display: flex;
  align-items: flex-end;
}
.airportCode {
  padding: 2px;
  background-color: #ef4044;
  color: #fff;
  font-size: 12px;
  font-weight: bold;
  min-width: 35px;
  max-width: 35px;
  text-align: center;
}
.backDropStyle {
  display: block;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 100;
  width: 100% !important;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.5);
}

.wrapperdiv {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: stretch;
  width: 100%;
  position: relative;
  z-index: 101;
}
.searchDiv {
  color: #555;
  display: flex;

  box-shadow: 0 2px 4px 0 rgb(0 0 0 / 9%);
  border: solid 1px #f4f4f4;
  border-radius: 3px;
  width: 100%;
  background-color: #ffffff;
  border-bottom-width: thin;
  z-index: 101;
}
.searchInput {
  width: 100%;
  border: none;
  margin: 0;
  padding: 12px 16px;
  font-size: 14px;
  outline: none;
  font-weight: 400;
  font-family: "'Montserrat',sans-serif";
  font-stretch: normal;
  font-style: normal;
  line-height: 1.43;
  letter-spacing: normal;
  background-color: transparent;
  text-align: left;
  color: #000000;
}
.searchIcon {
  overflow: hidden;
  width: 34px;
  height: 34px;
  margin: 0;
  border: none;
  border-radius: inherit;
  cursor: pointer;
  background-color: transparent;
  padding: 5px;
  display: flex;
  align-items: center;
  height: auto;
}
img.stringairimg{
  margin: 0px 10px 0px 0px;
}
@media only screen and (max-width: 768px) {
  body {
    /* background-color: #eaf5ea; */
    width: 100%;
    height: auto;
    background-size: cover !important;
    background-position: center !important;
  }
  .cityCountryName {
    font-size: 11px;
  }
  .airportName {
    font-size: 10px;
  }
  .airportCode {
    padding: 2px;
    font-size: 11px;
    min-width: 30px;
    max-width: 30px;
  }
  
}
