import styled from "styled-components";

export const Wrapper = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: stretch;
  width: 100%;
  position: relative;
  z-index: ${({isOpen})=>isOpen===true&&"101"};
`;

export const Div = styled.div`
  color: #555;
  display: flex;
  border: ${({theme, isMobile }) => isMobile !== true && `1px solid ${theme.palette.primary[theme.mode]}`};
  border-radius: ${({ isMobile }) => isMobile !== true && "3px"};
  width: 100%;
  background-color: #ffffff;
  border-bottom-width: thin;
  z-index: ${({isOpen})=>isOpen===true &&"101"};
  ${({ isMobile }) => isMobile === true && `border-bottom: 1px solid #000`};
`;

export const InputDiv = styled.div`
  height: auto;
`;
export const SecondPlaceholderLabel = styled.p`
font-size:11px;
margin: 10px 0px 0px 16px;
`

export const SearchInput = styled.input`
  width: 100%;
  border: none;
  margin: 0;
  padding: 12px 16px;
  outline: none;
  font-weight: ${({ theme }) => theme?.typography?.text?.fontWeight};
  font-family: ${({ theme }) => theme?.typography?.fontFamily};
  font-size: ${({ theme }) => theme?.typography?.text?.fontSize};
  font-stretch: normal;
  font-style: normal;
  line-height: ${({ theme }) => theme?.typography?.lineHeight};
  letter-spacing: normal;
`;
export const Svg = styled.svg`
  color: ${({ theme }) => theme.palette.primary[theme.mode]};
`;
export const Label = styled.label`
  width: 196px;
  height: 18px;
  font-weight: ${({ theme }) => theme?.typography?.text?.fontWeight};
  font-family: ${({ theme }) => theme?.typography?.fontFamily};
  font-size: ${({ theme }) => theme?.typography?.text?.fontSize};
  font-stretch: normal;
  font-style: normal;
  line-height: ${({ theme }) => theme?.typography?.lineHeight};
  letter-spacing: normal;
  color: #435b73;
  margin-bottom: 10px;
`;
export const SearchButton = styled.button`
  overflow: hidden;
  width: 34px;
  height: 34px;
  margin: 0;
  border: none;
  border-radius: inherit;
  cursor: pointer;
  background-color: transparent;
  padding: 5px;
  display: flex;
  align-items: center;
  height: auto;
`;
export const RenderItemDiv = styled.div`
  width: 100%;
  max-height: ${({ isMobile }) => isMobile !== true && "250px"};
  display: flex;
  position: absolute;
  top: 69.5px;
  z-index: 101;
  background-color: #ffffff;
  border-radius: 0px 0px 3px 3px;
  border: 1px solid #03868b;
  border-top-width: 0px;
`;
export const ListItems = styled.ul`
  overflow: auto;
  margin: 0;
  list-style: none;
  padding-inline-start: 0px;
  margin-block-start: 0px;
  margin-block-end: 0px;
  width: 100%;
  ${({ isMobile }) =>
    isMobile === true &&
    `
  ::-webkit-scrollbar {
  width: 3.5px !important;
  background-color: transparent !important;
}
::-webkit-scrollbar-thumb {
  background-color: #848884 !important;
  border-radius: 10px !important;
}
  `};
`;

export const ListItem = styled.li`
  padding: 10px;
  border-bottom: 1px solid #ebebec;
  display:flex;
  font-family: ${({ theme }) => theme?.typography?.fontFamily};
  font-size: ${({ theme }) => theme?.typography?.text?.fontSize};
  font-weight: ${({ theme }) => theme?.typography?.text?.fontWeight};
  font-style: normal;
  font-stretch: normal;
  line-height: ${({ theme }) => theme?.typography?.lineHeight};
  letter-spacing: ${({ theme }) => theme?.typography?.letterSpacing};
`;
// &:hover {
//   background-color: ${({ theme }) => theme.palette.primary[theme.mode]};
//   color: ${({ theme }) => theme.palette.primary.contrastText};
// }
export const ChildList = styled.div`
  width: 100%;
  display: flex;
  align-items: center;
  padding: 10px 0px 10px 40px;
`;

export const AirportIconImg = styled.img`

 margin: 0px 10px 0px 0px
`;

export const ChildAirportIconImg = styled.img`

 margin: 0px 10px 0px 0px
`;
