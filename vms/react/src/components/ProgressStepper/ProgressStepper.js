import React, { memo, useCallback, useMemo } from "react";
import {
  Container,
  ProgressSteps,
  StepperContainer,
} from "./ProgressStepper.styled";
import PropTypes from "prop-types";
import { TitleComponents } from "./Title.compoents";
import { useClassName } from "../../hooks/useClassName";
import { ThemeWrapper } from "../Theme/ThemeContext";

// Utility function (placed before main component)
const isNonNull = (prop) => prop !== null && prop !== undefined;

const ProgressStepper = memo((props) => {
  // 1. PROP DESTRUCTURING & VALIDATION
  const {
    noofSteps = 5,
    disablePrevSteps = false,
    activeStep,
    headerTitle,
    type = "primary",
    headerTitleStyle,
    additionalStyle,
    headerProps,
    customiseBackIcon,
    headerTitleClassName,
    additionalClassName,
    isMobile,
    isMobileView,
    ...otherProps
  } = props;

  // 2. PERFORMANCE OPTIMIZATIONS
  const headerClassName = useMemo(() => 
    useClassName(props, headerTitleClassName), 
    [props, headerTitleClassName]
  );

  const computedAdditionalClassName = useMemo(() => 
    useClassName(props, additionalClassName), 
    [props, additionalClassName]
  );

  const isMobileDevice = useMemo(() => 
    isMobile || isMobileView || false, 
    [isMobile, isMobileView]
  );

  // 3. EVENT HANDLING with useCallback
  const handleLeftItemClick = useCallback(() => {
    if (isNonNull(headerProps?.onLeftItemClick)) {
      headerProps.onLeftItemClick();
    }
  }, [headerProps?.onLeftItemClick]);

  // 4. MEMOIZED COMPONENTS
  const titleComponent = useMemo(() => (
    <TitleComponents
      headerTitle={headerTitle}
      className={`${headerClassName} vms_progress_stepper_title`}
      style={headerTitleStyle}
      onLeftItemClick={handleLeftItemClick}
      customiseBackIcon={customiseBackIcon}
    />
  ), [headerTitle, headerClassName, headerTitleStyle, handleLeftItemClick, customiseBackIcon]);

  const progressSteps = useMemo(() => {
    if (!noofSteps) return null;

    return [...Array(noofSteps)].map((e, i) => (
      <ProgressSteps
        disabled={disablePrevSteps}
        active={activeStep === i + 1}
        index={i}
        noofSteps={noofSteps}
        type={type}
        key={`progress-step-${i}`}
        id={`progress-step-${i}`}
        role="progressbar"
        aria-valuenow={activeStep === i + 1 ? 1 : 0}
        aria-valuemin={0}
        aria-valuemax={1}
        aria-label={`Step ${i + 1} of ${noofSteps}${activeStep === i + 1 ? ' (current)' : ''}`}
      />
    ));
  }, [noofSteps, disablePrevSteps, activeStep, type]);

  // 5. ERROR HANDLING
  React.useEffect(() => {
    if (!isNonNull(headerTitle)) {
      console.warn('ProgressStepper: headerTitle prop is required');
    }
    if (!isNonNull(activeStep)) {
      console.warn('ProgressStepper: activeStep prop is required');
    }
    if (activeStep && (activeStep < 1 || activeStep > noofSteps)) {
      console.warn(`ProgressStepper: activeStep (${activeStep}) should be between 1 and ${noofSteps}`);
    }
    if (noofSteps && noofSteps < 1) {
      console.warn('ProgressStepper: noofSteps should be greater than 0');
    }
  }, [headerTitle, activeStep, noofSteps]);

  return (
    <ThemeWrapper isMobile={isMobileDevice}>
      <Container 
        className={`${computedAdditionalClassName} vms_progress_stepper`} 
        style={additionalStyle}
        role="region"
        aria-label={`Progress stepper: ${headerTitle}`}
        {...otherProps}
      >
        {titleComponent}
        <StepperContainer
          className="vms_progress_stepper_step"
          role="progressbar"
          aria-valuenow={activeStep}
          aria-valuemin={1}
          aria-valuemax={noofSteps}
          aria-label={`Progress: step ${activeStep} of ${noofSteps}`}
        >
          {progressSteps}
        </StepperContainer>
      </Container>
    </ThemeWrapper>
  );
});

ProgressStepper.displayName = 'ProgressStepper';

ProgressStepper.defaultProps = {
  additionalStyle: null,
  additionalClassName: null,
  headerTitleClassName: null,
  headerTitleStyle: null,
  noofSteps: 5,
  disablePrevSteps: false,
  type: "primary",
  customiseBackIcon: null,
};

ProgressStepper.propTypes = {
  /**
   * title of selected tab as header
   */
  headerTitle: PropTypes.string.isRequired,
  /**
   * Property to append additional css class
   */
  additionalClassName: PropTypes.arrayOf(PropTypes.string),
  /**
   * Property to set the `style` object on button
   */
  additionalStyle: PropTypes.object,

  /**
   * external class for header title from application side
   */
  headerTitleClassName: PropTypes.arrayOf(PropTypes.string),

  /**
   *external style for header title from application side
   */
  headerTitleStyle: PropTypes.object,

  /**
   *No of steps to display
   */
  noofSteps: PropTypes.number,

  /**
   *Active step index. Note: index start from 1
   */
  activeStep: PropTypes.number.isRequired,

  /**
   *By default it will apply active class to all tabs before activeStep. if you want to apply disable class then pass true.
   */
  disablePrevSteps: PropTypes.bool,

  /**
   * Variations of ProgressStepper Type
   */
  type: PropTypes.oneOf(["primary", "secondary"]),

  /**
   *TitleBandHeader Props Note: Please don't pass title, titleItemClassName, titleStyle in these object, use @property headerTitle, @property headerTitleClassName, @property headerTitleStyle.
   */
  headerProps: PropTypes.any,
  /**
   * customise back icon
   */
  customiseBackIcon: PropTypes.string,
};
export { ProgressStepper };
