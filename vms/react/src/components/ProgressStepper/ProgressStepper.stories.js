import React, { useState } from "react";
import { Button } from "../Button/Button";

import { ProgressStepper } from "./ProgressStepper";
import {} from "./ProgressStepper.css";

export default {
  title: "VMS_REACT/Progress Stepper",
  component: ProgressStepper,
  parameters: {
    controls: {
      exclude: /.*/g,
    },
  },
};

const Template = (args) => {
  const [currentActiveStep, setActiveStep] = useState(1);

  return (
    <>
      <div style={{ margin: "0px" }}>
        <h4>Active Style for Previous Steps</h4>
        <ProgressStepper
          headerTitle={`Step ${currentActiveStep}`}
          activeStep={currentActiveStep}
          headerProps={{
            onLeftItemClick: () => {
              alert("back clicked");
            },
          }}
          {...args}
        />
      </div>
      <div style={{ margin: "50px 0px 0px 0px" }}>
        <h4>Disable Style for Previous Steps</h4>
        <ProgressStepper
          headerTitle={`Step ${currentActiveStep}`}
          activeStep={currentActiveStep}
          headerProps={{
            onLeftItemClick: () => {
              alert("back clicked");
            },
          }}
          disablePrevSteps
          {...args}
        />
      </div>

      <div style={{ textAlign: "center" }}>
        <h3>Buttons In Application</h3>
        <Button
        {...args}
          buttonType="tertiary"
          disabled={currentActiveStep === args.noofSteps}
          onClick={() => setActiveStep(currentActiveStep + 1)}
          additionalClassName={["StepperButton"]}
        >
          Next
        </Button>
        <Button
        {...args}
          buttonType="tertiary"
          disabled={currentActiveStep < 2}
          onClick={() => setActiveStep(currentActiveStep - 1)}
          additionalClassName={["StepperButton"]}
        >
          Back
        </Button>
      </div>
    </>
  );
};

export const Default = Template.bind({});
Default.args = {
  noofSteps: 5,
  id:"btn-id"
};
export const FourSteps = Template.bind({});
FourSteps.args = {
  noofSteps: 4,
  id:"btn-4-id"
};

export const ThreeSteps = Template.bind({});
ThreeSteps.args = {
  noofSteps: 3,
  id:"btn-3-id"
};
export const TwoSteps = Template.bind({});
TwoSteps.args = {
  noofSteps: 2,
    id:"btn-2-id"
};
