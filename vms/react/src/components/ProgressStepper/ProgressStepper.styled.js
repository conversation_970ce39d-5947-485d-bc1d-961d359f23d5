import styled from "styled-components";

export const Container = styled.div`
  display: block;
`;

export const StepperContainer = styled.div`
  display: flex;
`;

export const ProgressStepsConatiner = styled.div``;

function getBorderColor(props) {
  const { active, disabled, theme, type } = props;
  if (active) {
    return theme?.palette?.[type]?.main;
  } else if (disabled) {
    return "#d1d3d4";
  } else {
    return "#b3dbdc";
  }
}

function getMarginRight(index, noofSteps) {
  if (noofSteps === 2) {
    return 40;
  } else {
    const rightValue = 48 / (noofSteps - 1);
    return rightValue;
  }
}

export const ProgressSteps = styled.div`
  margin-right: ${({ index, noofSteps }) =>
    `${
      index + 1 === noofSteps ? "0px" : `${getMarginRight(index, noofSteps)}px`
    }`};
  width: ${({ noofSteps }) =>
    `calc((100% -  ${noofSteps === 2 ? "40px" : "48px"}) / ${noofSteps})`};
  border-bottom: ${(props) => `1px solid ${getBorderColor(props)} `};
`;

export const TitleBandMainContainer = styled.div`
  background: #fff;
  color: #000;
  margin-top: 17px;
  margin-bottom: 19px;
`;

export const TitleBandContainer = styled.div`
  display: flex;
  align-items: center;
`;

export const LeftIcon = styled.li`
  margin-left: 16px;
  list-style: none;
`;

export const TitleContainer = styled.li`
  margin-left: 24px;
  max-width: 90%;
  color: #4d4d4f;
  font-weight: ${({ theme }) => theme?.typography?.text?.fontWeight || ""};
  font-family: ${({ theme }) => theme?.typography?.fontFamily || ""};
  list-style: none;
`;

export const TitleText = styled.div`
  font-size: 14px;
  line-height: 20px;
  letter-spacing: 0.25px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
`;

export const BackImage = styled.img`
  background-repeat: no-repeat;
  width: 24px;
  height: 24px;
  background-size: 24px 24px !important;
`;
