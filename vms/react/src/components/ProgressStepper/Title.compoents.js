import React from "react";
import {
  BackImage,
  Container,
  LeftIcon,
  TitleBandContainer,
  TitleBandMainContainer,
  TitleContainer,
  TitleText,
} from "./ProgressStepper.styled";
import backArrowSvg from "../../assets/images/ProgessStepper/backArrow.svg";

const isNonNull = (prop) => {
  return prop !== null && prop !== undefined;
};

const TitleComponents = (props) => {
  const { headerTitle, className, style,onLeftItemClick,customiseBackIcon } = props;
  return (
    <Container>
      <TitleBandMainContainer className={className} style={style}>
        <TitleBandContainer className={`vms_titleband_container`}>
          <LeftIcon onClick={()=>onLeftItemClick()} className={`vms_titleband_left_icon`}>
            {isNonNull(customiseBackIcon)?<img src={customiseBackIcon} width="24" height="24"/>: <BackImage src={backArrowSvg} alt="BackArrow" />}
           
          </LeftIcon>
          <TitleContainer className={`vms_titleband_titlecontainer`}>
            <TitleText>{headerTitle}</TitleText>
          </TitleContainer>
        </TitleBandContainer>
      </TitleBandMainContainer>
    </Container>
  );
};
export { TitleComponents };
