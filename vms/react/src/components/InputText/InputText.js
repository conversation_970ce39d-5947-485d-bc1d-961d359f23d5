import React, { useState, useEffect, useRef, memo, useCallback, useMemo } from "react";
import PropTypes from "prop-types";
import { StyledInputText, StyledLabelText, StyledMessage, StyledRightView, StyledLeftView, Styled<PERSON>oader, MainContainer, InputContainer, StyledSecondPlaceholderContainer } from "./Input.styled";
import { useClassName } from "../../hooks/useClassName";
import Loader from "../../assets/images/InputText/loader.svg"
import { ThemeWrapper } from "../Theme/ThemeContext";

// Utility function (placed before main component)
const isNonNull = (prop) => prop !== null && prop !== undefined;

const InputText = memo(React.forwardRef((props, ref) => {
  // 1. PROP DESTRUCTURING & VALIDATION
  const {
    value,
    placeholder,
    label,
    message,
    messageType = "info",
    disabled = false,
    readOnly = false,
    required = false,
    autoFocus = false,
    isLoading = false,
    maxLength,
    minLength,
    type = "text",
    onChange,
    onFocus,
    onBlur,
    onKeyPress,
    leftView,
    rightView,
    additionalClassName,
    additionalStyle,
    additionalMessageClassName,
    additionalLeftViewClassName,
    additionalRightViewClassName,
    isMobile,
    isMobileView,
    secondPlaceholder,
    inputAttributes,
    inputMethods,
    ...otherProps
  } = props;

  // 2. STATE MANAGEMENT
  const [inputVal, setInputVal] = useState(value);

  // 3. PERFORMANCE OPTIMIZATIONS
  const computedClassName = useMemo(() => 
    useClassName(props, additionalClassName), 
    [props, additionalClassName]
  );

  const messageClassName = useMemo(() => 
    useClassName(props, additionalMessageClassName), 
    [props, additionalMessageClassName]
  );

  const leftViewClassName = useMemo(() => 
    useClassName(props, additionalLeftViewClassName), 
    [props, additionalLeftViewClassName]
  );

  const rightViewClassName = useMemo(() => 
    useClassName(props, additionalRightViewClassName), 
    [props, additionalRightViewClassName]
  );

  const hasLeftView = useMemo(() => isNonNull(leftView), [leftView]);
  const hasRightView = useMemo(() => isNonNull(rightView), [rightView]);
  const hasMessage = useMemo(() => isNonNull(message), [message]);
  const hasLabel = useMemo(() => isNonNull(label), [label]);

  // 4. EVENT HANDLING with useCallback
  const handleChange = useCallback((event) => {
    const newValue = event.target.value;
    setInputVal(newValue);
    
    if (isNonNull(onChange)) {
      onChange(event);
    }
  }, [onChange]);

  const handleFocus = useCallback((event) => {
    if (isNonNull(onFocus)) {
      onFocus(event);
    }
  }, [onFocus]);

  const handleBlur = useCallback((event) => {
    if (isNonNull(onBlur)) {
      onBlur(event);
    }
  }, [onBlur]);

  const handleKeyPress = useCallback((event) => {
    if (isNonNull(onKeyPress)) {
      onKeyPress(event);
    }
  }, [onKeyPress]);

  // 5. EFFECTS
  useEffect(() => {
    setInputVal(value);
  }, [value]);

  // 6. CONDITIONAL RENDERING (memoized)
  const labelElement = useMemo(() => {
    if (!hasLabel) return null;
    
    return (
      <StyledLabelText 
        className={`${computedClassName} vms_inputtext_label`}
        required={required}
      >
        {label}
        {required && <span aria-label="required"> *</span>}
      </StyledLabelText>
    );
  }, [hasLabel, label, required, computedClassName]);

  const leftViewElement = useMemo(() => {
    if (!hasLeftView) return null;
    
    return (
      <StyledLeftView className={`${leftViewClassName} vms_inputtext_leftview`}>
        {leftView}
      </StyledLeftView>
    );
  }, [hasLeftView, leftView, leftViewClassName]);

  const rightViewElement = useMemo(() => {
    if (!hasRightView && !isLoading) return null;
    
    return (
      <StyledRightView className={`${rightViewClassName} vms_inputtext_rightview`}>
        {isLoading ? (
          <StyledLoader src={Loader} alt="Loading" />
        ) : (
          rightView
        )}
      </StyledRightView>
    );
  }, [hasRightView, isLoading, rightView, rightViewClassName]);

  const messageElement = useMemo(() => {
    if (!hasMessage) return null;
    
    return (
      <StyledMessage 
        className={`${messageClassName} vms_inputtext_message`}
        messageType={messageType}
        role={messageType === "error" ? "alert" : "status"}
      >
        {message}
      </StyledMessage>
    );
  }, [hasMessage, message, messageType, messageClassName]);

  const secondPlaceholderElement = useMemo(() => {
    if (!secondPlaceholder || inputVal) return null;
    
    return (
      <StyledSecondPlaceholderContainer className="vms_inputtext_secondplaceholder">
        {secondPlaceholder}
      </StyledSecondPlaceholderContainer>
    );
  }, [secondPlaceholder, inputVal]);

  // 7. ERROR HANDLING
  React.useEffect(() => {
    if (required && !value && value !== 0) {
      // Could add validation logic here if needed
    }
  }, [required, value]);

  return (
    <ThemeWrapper isMobile={isMobile || isMobileView || false}>
      <MainContainer 
        className={`${computedClassName} vms_inputtext_container`}
        style={additionalStyle}
      >
        {labelElement}
        
        <InputContainer className="vms_inputtext_inputcontainer">
          {leftViewElement}
          
          <StyledInputText
            ref={ref}
            className={`${computedClassName} vms_inputtext_input`}
            value={inputVal || ''}
            placeholder={placeholder}
            disabled={disabled}
            readOnly={readOnly}
            required={required}
            autoFocus={autoFocus}
            maxLength={maxLength}
            minLength={minLength}
            type={type}
            onChange={handleChange}
            onFocus={handleFocus}
            onBlur={handleBlur}
            onKeyPress={handleKeyPress}
            aria-label={label || placeholder}
            aria-invalid={messageType === "error"}
            aria-describedby={hasMessage ? `${props.id}-message` : undefined}
            {...inputAttributes}
            {...inputMethods}
            {...otherProps}
          />
          
          {secondPlaceholderElement}
          {rightViewElement}
        </InputContainer>
        
        {messageElement}
      </MainContainer>
    </ThemeWrapper>
  );
}));

// Component display name for debugging
InputText.displayName = 'InputText';

InputText.propTypes = {
  /**
   * Returns / Sets the element's accept attribute, 
   * containing comma-separated list of file types that can be selected.
   */
  accept: PropTypes.string,
  /**
   * Returns / Sets the element's alt attribute, containing alternative text to use.
   */
  alt: PropTypes.string,
  /**
   * Sets the element's autocomplete attribute, 
   * indicating whether the value of the control can be automatically completed by the browser.
   */
  autocomplete: PropTypes.string,
  /**
   * Sets the element's autofocus attribute, which specifies that a form control 
   * should have input focus when the page loads, unless the user overrides it, 
   * for example by typing in a different control. Only one form element in a document can have the autofocus attribute
   */
  autoFocus: PropTypes.bool,//done
  capture: PropTypes.oneOfType([PropTypes.string, PropTypes.bool]),
  /**
   * Returns / Sets the current state of the element.
  */
  checked: PropTypes.bool,//done
  crossOrigin: PropTypes.string,//done
  /**
   * Use this prop to enable/disable the component
   */
  disabled: PropTypes.bool,//done
  form: PropTypes.string,//done
  formAction: PropTypes.string,//done
  formMethod: PropTypes.string,//done
  formEncType: PropTypes.string,//done
  formNoValidate: PropTypes.bool,//done
  formTarget: PropTypes.string,//done
  /**
   * Returns the element pointed to by the list attribute. 
   *   The property may be null if no HTML element is found in the same tree.
   */
  list: PropTypes.string,
  /**
   *  Sets the element's height attribute, which defines the height of the image displayed for the button.
   */
  height: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  /**
   *  Sets the element's max attribute, containing the maximum (numeric or date-time) value for this item, 
   * which must not be less than its minimum (min attribute) value.
   */
  max: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  /**
   * Sets the element's maxlength attribute, 
   * containing the maximum number of characters that the value can have.
   */
  maxLength: PropTypes.number,
  /**
   * Returns / Sets the element's min attribute, containing the minimum (numeric or date-time) value for this item, 
   *  which must not be greater than its maximum (max attribute) value.
   */
  min: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  /**
   *  Sets the element's minlength attribute, containing the minimum number of characters
   *  (in Unicode code points) that the value can have.
   */
  minLength: PropTypes.number,//done
  multiple: PropTypes.bool,
  /**
   * Name of the input element
   */
  name: PropTypes.string,//done
  /**
   * use this props to display a placeholder string in text input.
   */
  placeholder: PropTypes.string,//done
  /**
   * Indicates that the element is not editable, but is otherwise operable.
   */
  readOnly: PropTypes.bool,//done
  pattern: PropTypes.string,//done
  required: PropTypes.bool,//done
  size: PropTypes.number,//done
  src: PropTypes.string,//done
  step: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  /**
   * Callback function when input onchange event is triggered
   */
  onChange: PropTypes.func,//done
  defaultChecked: PropTypes.bool,//done
  defaultValue: PropTypes.oneOfType([PropTypes.string, PropTypes.number, PropTypes.arrayOf(PropTypes.string)]),//done
  accesskey: PropTypes.string,
  /**
   * Custom class name that can be added to the component
   */
  className: PropTypes.string,//done
  contentEditable: PropTypes.oneOfType([PropTypes.bool, PropTypes.oneOf(["true", "false"])]),//done
  hidden: PropTypes.bool,
  /**
   * Unique ID for the field. Required for web accessibility
   */
  id: PropTypes.string,//done
  lang: PropTypes.string,//done
  slot: PropTypes.string,//done
  spellcheck: PropTypes.bool,//done
  dir: PropTypes.string,//done
  style: PropTypes.object,//done
  tabIndex: PropTypes.number,//done
  title: PropTypes.string,//done
  translate: PropTypes.oneOf(["yes", "no"]),//done
  radioGroup: PropTypes.string,//done
  role: PropTypes.string,//done
  about: PropTypes.string,
  datatype: PropTypes.string,
  prefix: PropTypes.string,
  property: PropTypes.string,
  resource: PropTypes.string,
  typeof: PropTypes.string,
  vocab: PropTypes.string,
  /**
   * Classes to add additional styling in the parent container
   */
  additionalClassName: PropTypes.arrayOf(PropTypes.string),
  /**
   * Inline style to add additional styling in the parent container
   */
  additionalStyle: PropTypes.object,
  /**
   * Classes to add additional styling in the message container
   */
  additionalMessageClassName: PropTypes.arrayOf(PropTypes.string),
  /**
   * Inline style to add additional styling in the message container
   */
  additionalMessageStyle: PropTypes.object,
  /**
   * Classes to add additional styling in the left view container
   */
  additionalLeftViewClassName: PropTypes.arrayOf(PropTypes.string),
  /**
   * Inline style to add additional styling in the left view container
   */
  additionalLeftViewStyle: PropTypes.object,
  /**
   * Classes to add additional styling in the right view container
   */
  additionalRightViewClassName: PropTypes.arrayOf(PropTypes.string),
  /**
   * Inline style to add additional styling in the right view container
   */
  additionalRightViewStyle: PropTypes.object,
  /**
   * Current field value
   */

  value: PropTypes.oneOfType([PropTypes.string, PropTypes.number, PropTypes.arrayOf(PropTypes.string)]),
  /**
   * Type of the input field. Allowed types: text, number, tel, email, password
   */
  type: PropTypes.oneOf(["text", "number", "tel", "email", "password"]),
  onClick: PropTypes.func,
  isLoading: PropTypes.bool,
  /**
   * Message mode
   * "error" | "information" | "success" | "warning"
   */
  messageMode: PropTypes.oneOf(["error", "info", "success", "alert"]),
  /**
   * Message text content in input text.
   */
  msgText: PropTypes.oneOfType([PropTypes.node, PropTypes.string]),
  /**
   *Label for the field
   */
  label: PropTypes.string,
  /**
   * Secondary Label for the field
   */
  secondaryLabel: PropTypes.string,//done
  /**
   * Method to handle focus state
   * (event: FocusEvent<HTMLInputElement>) => void
   */
  onFocus: PropTypes.func,
  /**
   * Method to handle blur state 
   * (event: FocusEvent<HTMLInputElement>) => void
   */
  onBlur: PropTypes.func,
  /**
   * (event: FormEvent<HTMLInputElement>) => void
   */
  onSubmit: PropTypes.func,
  /**
   * (event: KeyboardEvent<HTMLInputElement>) => void
   */
  onKeyPress: PropTypes.func,//done
  // onSubmit: PropTypes.func,//done
  /**
   * (event: MouseEvent<HTMLInputElement, MouseEvent>) => void
   */
  onMouseEnter: PropTypes.func,
  /**
   * left content for input text. Note: Please give padding to input as same of left view
   */
  leftView: PropTypes.node,
  /**
   * right content for input text. Note: Please give padding to input as same of right view
   */
  rightView: PropTypes.node,
  inputAttributes: PropTypes.object,
  inputMethods: PropTypes.object,
  /**
   * Ref<unknown>
   */
  ref: PropTypes.oneOfType([
    // Either a function
    PropTypes.func,
    // Or the instance of a DOM native element (see the note about SSR)
    PropTypes.shape({ current: PropTypes.instanceOf(InputText) }),
  ]),
  /**
    * Variations of Input Type
    */
  inputType: PropTypes.oneOf(["primary", "secondary"]),
};

export { InputText };