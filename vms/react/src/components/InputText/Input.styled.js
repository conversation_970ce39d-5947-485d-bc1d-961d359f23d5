import styled from "styled-components";

function _fontWeight(theme) {
  return theme?.typography?.text?.fontWeight || "";
}
function _fontFamily(theme) {
  return theme?.typography?.fontFamily || "";
}
function _fontSize(theme) {
  return theme?.typography?.text?.fontSize?.desktop || "";
}
function _lineHeight(theme) {
  return theme?.typography?.lineHeight || "";
}
function _letterSpacing(theme) {
  return theme?.typography?.letterSpacing || "";
}

function _borderRadius(theme) {
  return theme?.shape?.borderRadius;
}

function messageColor(theme, messageMode) {
  switch (messageMode) {
    case "error":
      return theme.palette.error.main;
    case "alert":
      return theme.palette.warning.main;
    case "success":
      return theme.palette.success.main;
    case "info":
      return theme.palette.info.main;
    default:
      return "#000";
  }
}

export const StyledInputText = styled.input.attrs((props) => ({
  maxLength: props.maxLength,
  autoComplete:props.autoComplete 
}))`
  display: block;
  box-sizing: border-box;
  font-weight: ${({ theme }) => theme?.typography?.heading?.fontWeight};
  font-family: ${({ theme }) => _fontFamily(theme)};
  font-size: ${({ theme }) => _fontSize(theme)};
  font-stretch: normal;
  font-style: normal;
  line-height: ${({ theme }) => _lineHeight(theme)};
  width: 100%;
  letter-spacing: ${({ theme }) => _letterSpacing(theme)};
  border-radius: ${({ theme }) => _borderRadius(theme)};
  color: #4d4d4f;
  padding: ${({ isLoading }) =>
    isLoading ? "14px 40px 14px 16px" : "14px 16px"};
  margin-top: 0px;
  text-overflow: ellipsis;
  height: 48px;
  background-color: #fff;
  border: none;
  ${({ isMobile }) =>
    isMobile
      ? { "border-bottom": "1px solid #4d4d4f", "border-radius": "0px" }
      : { "border": "1px solid #4d4d4f" }};

  &:focus {
    outline: none;
    ${({ isMobile,theme,inputType }) =>
      isMobile
        ? { "border-bottom": `1px solid ${theme?.palette?.[inputType]?.[theme.mode]}` }
        : { "border": `1px solid ${theme?.palette?.[inputType]?.[theme.mode]}` }};
  }
  &::placeholder {
    font-weight: 400;
    font-family: Montserrat;
    font-size: ${({ placeholderTextSize }) => (placeholderTextSize == 'small' ? '14px' :'16px')};
    font-stretch: normal;
    font-style: normal;
    line-height: 1.43;
    border-radius: 3px;
    letter-spacing: 0.3px;
    color: #4d4d4f;
  }
  &:disabled{
    opacity:0.5
  }
`;

export const StyledLabelText = styled.p`
  font-weight: ${({ theme }) => _fontWeight(theme)};
  font-family: ${({ theme }) => _fontFamily(theme)};
  font-size: ${({ theme }) => _fontSize(theme)};
  line-height: ${({ theme }) => _lineHeight(theme)};
  letter-spacing: ${({ theme }) => _letterSpacing(theme)};
  color: #000;
  font-stretch: normal;
  font-style: normal;
`;

export const StyledMessage = styled.p`
  font-weight: ${({ theme }) => _fontWeight(theme)};
  font-family: ${({ theme }) => _fontFamily(theme)};
  font-size: ${({ theme }) => _fontSize(theme)};
  line-height: ${({ theme }) => _lineHeight(theme)};
  letter-spacing: ${({ theme }) => _letterSpacing(theme)};
  color: ${({ theme, messageMode }) => messageColor(theme, messageMode)};
`;

export const StyledRightView = styled.div`
  position: absolute;
  top: 0;
  right: 0;
`;

export const StyledLeftView = styled.div`
  position: absolute;
  top: 0;
  left: 0;
`;

export const StyledLoader = styled.img`
  position: absolute;
  top: 7px;
  right: 4px;
`;

export const StyledSecondPlaceholderContainer = styled.div`
  position: absolute;
  top: 0;
  left: 18px;
`;

export const MainContainer = styled.div`
  width: 220px;
`;

export const InputContainer = styled.div`
  position: relative;
`;
