import React from "react";
import { InputText } from "./InputText";



export default {
  title: "VMS_REACT/Input TextBox",
  component: InputText,
  argTypes: {
    id: { control: { type: "" } },
    label: {controls : {type : ""}},
    type: { control: { type: "" } },
    // value: { control: { type: "" } },
    additionalClassName: { control: { type: "" } },
    onChange: { action: "clicked" },
  },
};

const Template = (args) => <InputText {...args}>InputTextBox</InputText>;

export const Default = Template.bind({});
Default.args = {
  value: "",
  label: "TextBox",
  disabled: false,
  required: false,
  placeholder:"enter name",
  id : "ip-text-1"
  // isLoading:true
};

export const Defaultwo = Template.bind({});
Defaultwo.args = {
  value: "",
  label: "TextBox",
  disabled: false,
  required: false,
  placeholder:"enter name",
  secondPlaceHolder:"Second Placeholder",
  // isLoading:true
};
