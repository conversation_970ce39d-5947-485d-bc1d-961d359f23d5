import styled from "styled-components";

export const StyledIconButton = styled.img.withConfig({
  shouldForwardProp: (prop) => !['disabled'].includes(prop),
})`
  color: ${({ theme }) => theme.palette.primary.main};
  background-color: transparent;
  fill: ${({ theme }) => theme.palette.primary.main};
  stroke: ${({ theme }) => theme.palette.primary.main};
  object-fit: fill;
  width: 20px;
  height: 20px;
  cursor: ${({ disabled }) => disabled ? 'not-allowed' : 'pointer'};
  opacity: ${({ disabled }) => disabled ? 0.6 : 1};
  transition: opacity 0.2s ease;

  &:hover {
    opacity: ${({ disabled }) => disabled ? 0.6 : 0.8};
  }

  &:focus {
    outline: 2px solid ${({ theme }) => theme.palette.primary.main};
    outline-offset: 2px;
  }

  &:focus:not(:focus-visible) {
    outline: none;
  }
`