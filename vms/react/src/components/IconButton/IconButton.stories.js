import React from "react";
import { IconButton } from "./IconButton";

export default {
    title: "VMS_REACT/IconButton",
    component: IconButton,
    argTypes: {
        additionalStyle: { control: { type: "" } },
        additionalClassName: { control: { type: "" } },
        imageSrc: { control: { type: "" } },
    },
};


const Template = (args) => <IconButton {...args}></IconButton>;
export const Default = Template.bind({});

Default.args = {
    mode: "backArrow"
};

export const crossIcon = Template.bind({});

crossIcon.args = {
    mode: "crossIcon"
};


export const IconFromUrl = Template.bind({});
IconFromUrl.args = {
    mode: "other",
    imageSrc:"https://ibhuat.intermiles.com/static/images/1583169742132Fitness%20facilities%20(surcharge).svg"
};

export const CustomCss = Template.bind({});
CustomCss.args = {
    mode: "other",
    imageSrc:"https://ibhuat.intermiles.com/static/images/1583169742132Fitness%20facilities%20(surcharge).svg",
    additionalStyle:{
        width:"50px",
        height:"50px"
      }
};