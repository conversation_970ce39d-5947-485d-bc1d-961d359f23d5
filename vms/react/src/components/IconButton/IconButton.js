import React, { memo, useCallback, useMemo } from "react";
import PropTypes from "prop-types";
import { StyledIconButton } from "./IconButton.styled";
import backArrowSvg from "../../assets/images/IconButton/backArrow.svg";
import crossIconSvg from "../../assets/images/IconButton/crossIcon.svg";
import { useClassName } from "../../hooks/useClassName";
import { ThemeWrapper } from "../Theme/ThemeContext";

// Utility function (placed before main component)
const isNonNull = (prop) => prop !== null && prop !== undefined;

/**
 * Icon Button UI component for user interaction
 */
const IconButton = memo((props) => {
    // 1. PROP DESTRUCTURING & VALIDATION
    const {
        id,
        onClick,
        mode = "other",
        imageSrc,
        imageAlt = "Icon button",
        disabled = false,
        additionalClassName,
        additionalStyle,
        className,
        isMobile,
        isMobileView,
        ...otherProps
    } = props;

    // 2. PERFORMANCE OPTIMIZATIONS
    const computedClassName = useMemo(
        () => useClassName(props, additionalClassName),
        [props, additionalClassName]
    );

    const iconSrc = useMemo(() => {
        if (isNonNull(imageSrc)) {
            return imageSrc;
        }

        switch (mode) {
            case "crossIcon":
                return crossIconSvg;
            case "backArrow":
                return backArrowSvg;
            default:
                return "";
        }
    }, [imageSrc, mode]);

    const isMobileDevice = useMemo(
        () => isMobile || isMobileView || false,
        [isMobile, isMobileView]
    );

    // 3. EVENT HANDLING with useCallback
    const handleClick = useCallback(
        (event) => {
            if (disabled) {
                event.preventDefault();
                return;
            }

            event?.stopPropagation();

            if (isNonNull(onClick)) {
                onClick(event);
            }
        },
        [disabled, onClick]
    );

    const handleKeyDown = useCallback(
        (event) => {
            if (event.key === "Enter" || event.key === " ") {
                event.preventDefault();
                handleClick(event);
            }
        },
        [handleClick]
    );

    // 4. ERROR HANDLING
    React.useEffect(() => {
        if (!iconSrc && !imageSrc) {
            console.warn(
                "IconButton: Either imageSrc prop or a valid mode should be provided"
            );
        }
        if (!imageAlt) {
            console.warn(
                "IconButton: imageAlt prop is recommended for accessibility"
            );
        }
    }, [iconSrc, imageSrc, imageAlt]);

    return (
        <ThemeWrapper isMobile={isMobileDevice}>
            <StyledIconButton
                id={id}
                src={iconSrc}
                onClick={handleClick}
                onKeyDown={handleKeyDown}
                className={`${computedClassName} vms_icon_button`}
                style={additionalStyle}
                alt={imageAlt}
                disabled={disabled}
                role="button"
                tabIndex={disabled ? -1 : 0}
                aria-disabled={disabled}
                aria-label={imageAlt}
                {...otherProps}
            />
        </ThemeWrapper>
    );
});

// Component display name for debugging
IconButton.displayName = "IconButton";

IconButton.defaultProps = {
    mode: "other",
    imageAlt: "Icon button",
    disabled: false,
};

IconButton.propTypes = {
    /**
     * Unique identifier for the button
     */
    id: PropTypes.string,

    /**
     * Optional click handler
     */
    onClick: PropTypes.func,

    /**
     * Variation of Icon Button mode
     */
    mode: PropTypes.oneOf(["other", "backArrow", "crossIcon"]),

    /**
     * Custom Image of Icon Button
     */
    imageSrc: PropTypes.string,

    /**
     * Alternate Text for image
     */
    imageAlt: PropTypes.string,

    /**
     * Whether the button is disabled
     */
    disabled: PropTypes.bool,

    /**
     * Property to append additional css class
     */
    additionalClassName: PropTypes.arrayOf(PropTypes.string),

    /**
     * Property to set the `inline style` object on button
     */
    additionalStyle: PropTypes.object,

    /**
     * Additional CSS class name
     */
    className: PropTypes.string,

    /**
     * Whether component is in mobile mode
     */
    isMobile: PropTypes.bool,

    /**
     * Whether component is in mobile view
     */
    isMobileView: PropTypes.bool,
};

export { IconButton };