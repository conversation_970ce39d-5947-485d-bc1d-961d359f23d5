import React from "react";
import PropTypes from "prop-types";
import { StyledIconButton } from "./IconButton.styled";
import backArrowSvg from "../../assets/images/IconButton/backArrow.svg";
import crossIconSvg from "../../assets/images/IconButton/crossIcon.svg";
import { ThemeWrapper } from "../Theme/ThemeContext";

/**
 * Icon Button UI component for user interaction
 */
const IconButton = (props) => {
    const getClassName = () => {
        let cssClasses = [];
        if (
            props.additionalClassName !== null &&
            props.additionalClassName !== undefined &&
            props.additionalClassName?.length > 0
        ) {
            cssClasses = cssClasses.concat(props.additionalClassName);
        }

        if (cssClasses.length > 0) {
            return `${cssClasses.join(" ")}`;
        }
        return props.className === undefined || null ? "" : props.className;
    };
    const className = getClassName();
    return (
        <>
            <ThemeWrapper isMobile={props?.isMobile || props?.isMobileView || false}>
                <StyledIconButton
                    src={props.imageSrc ? props.imageSrc : props.mode == "crossIcon" ? crossIconSvg : props.mode == "backArrow" ? backArrowSvg : ""}
                    onClick={props.onClick}
                    className={`${className} vms_icon_button`}
                    style={props.additionalStyle}
                    alt={props.imageAlt}
                />
            </ThemeWrapper>
        </>
    )
}

IconButton.propTypes = {

    /**
     * Optional click handler
     */
    onClick: PropTypes.func,

    /**
     * Variation of Icon Button mode
     */
    mode: PropTypes.oneOf(["other", "backArrow", "crossIcon"]),

    /**
     * Custom Image of Icon Button 
     */
    imageSrc: PropTypes.string,

    /**
     * Alternate Text for image
     */
    imageAlt: PropTypes.string,

    /**
     * Property to append additional css class
     */
    additionalClassName: PropTypes.arrayOf(PropTypes.string),

    /**
     * Property to set the `inline style` object on button
     */
    additionalStyle: PropTypes.object,
};


export { IconButton };