import styled, { keyframes, css } from "styled-components";
const keyFrameFadeIn = keyframes`
0% {
    opacity: 0;
}
100% {
    opacity: 1;
}
`;

export const ModalOverlay = styled.div`
  ${({ isOverlay }) =>
    isOverlay
      ? `
      display: block;
      position: fixed;
      top: 0;
      left: 0;
      z-index: 100;
      right:0;
      bottom:0;
      background-color: rgba(0,0,0,0.8);
`
      : `display: none;`};
`;

function getAnimation(animationType) {
  if (animationType === "fadeIn") {
     return keyFrameFadeIn;
  } else {
    return ``;
  }
}

export const ModalWrapper = styled.div`
  top: ${({ isMobile,showOverlayInMobile }) => (isMobile ? showOverlayInMobile?`3%`:`0px` : "50px")};
  left: ${({ isMobile,showOverlayInMobile }) => (isMobile ? showOverlayInMobile?`3%`:`0px` : "140px")};
  right: ${({ isMobile,showOverlayInMobile }) => (isMobile ? showOverlayInMobile?`3%`:`0px` : "140px")};
  bottom: ${({ isMobile, showOverlayInMobile }) => (isMobile ? showOverlayInMobile?`3%`:`0px` : "50px")};
  background-color: #fff;
  border: 0px;
  z-index: 101;
  border-radius: ${({ theme }) => theme?.shape?.borderRadius};
  position: fixed;
  display: ${({ isOpen }) => (isOpen ? "flex" : "none")};
  flex-direction: column;
  animation: ${({ animationType }) => animationType != null ? css`1s ${getAnimation(animationType)} ease`:""};
`;
export const ModalHeader = styled.div`
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
`;
export const ModalContent = styled.div`
  margin: 2%;
  overflow: auto;
  height: 100%;
`;

export const ModalClose = styled.div`
  display: flex;
  padding: 2% 2%;
  float: ${({ closeButtonAlignment }) => closeButtonAlignment};
  ${({ closeButtonAlignment }) =>
    closeButtonAlignment === "left" ? "margin-right:auto" : "margin-left:auto"};
`;
export const Svg = styled.svg`
  color: ${({ theme }) => theme?.palette?.primary[theme.mode]};
  width: 16px;
  height: 16px;
  cursor: pointer;
`;
export const ModalFooter = styled.div`
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
`;
