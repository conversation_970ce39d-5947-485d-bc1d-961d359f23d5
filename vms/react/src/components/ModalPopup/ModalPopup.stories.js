import React, { Fragment, useState } from "react";

import { ModalPopup } from "./ModalPopup";
import { Button } from "../Button/Button";

export default {
  title: "VMS_REACT/ModalPopup",
  component: ModalPopup,
};

const Template = (args) => {
  const [showModal, setShowModal] = useState(false);
  return (
    <Fragment>
      <Button
      id="default-btn-id"
        onClick={(e) => {
          e.stopPropagation();
          setShowModal(!showModal);
        }}
      >
        Open Modal
      </Button>
      <ModalPopup
        {...args}
        isOpen={showModal || args.isOpen}
        isMobileView={false}
        onRequestClose={(e) => {
          setShowModal(false);
        }}
        onAfterClose={(e) => {
          setShowModal(false);
          args.isOpen = false;
        }}
        closeButtonAlignment={"right"}
      >
        <div style={{ textAlign: "center", margin: "10%" }}>
          <div>
            <h2>
              Oops! <br />
              Looks like your payment has failed
            </h2>
          </div>
          <div>
            <Button id="try-btn-id" rippleEffect>Try Again</Button>
          </div>
        </div>
      </ModalPopup>
    </Fragment>
  );
};

export const Default = Template.bind({});
Default.args = {
  isOpen: false,
  closeButtonAlignment: "right",
};

export const CustomModalHeaderFooter = (args) => {
  const [showModal, setShowModal] = useState(false);
  return (
    <Fragment>
      <Button
      id={"cust-btn-id"}
        onClick={(e) => {
          e.stopPropagation();
          setShowModal(!showModal);
        }}
      >
        Open Modal
      </Button>
      <ModalPopup
        modalHeaderCustomView={
          <div
            style={{
              backgroundColor: "#C0C0C0",
              width: "100%",
              padding: "20px",
              display: "flex",
              justifyContent: "space-between",
              // padding: "10px 10px",
            }}
          >
            <strong>Header</strong>
            <button
              onClick={(e) => {
                e.stopPropagation();
                setShowModal(false);
              }}
            >
              Close
            </button>
          </div>
        }
        modalFooterCustomView={
          <div
            style={{
              backgroundColor: "#C0C0C0",
              width: "100%",
              padding: "20px",
            }}
          >
            Footer
          </div>
        }
        isOpen={showModal || args.isOpen}
        isMobileView={false}
        onRequestClose={(e) => {
          setShowModal(false);
        }}
        onAfterClose={(e) => {
          setShowModal(false);
          args.isOpen = false;
        }}
        closeButtonAlignment={"right"}
      >
        <div style={{ textAlign: "center", margin: "10%" }}>
          <div>
            <h2>
              Oops! <br />
              Looks like your payment has failed
            </h2>
          </div>
          <div>
            <Button id="cust-try-btn-id" rippleEffect>Try Again</Button>
          </div>
        </div>
        <div style={{ textAlign: "center", margin: "10%" }}>
          <div>
            <h2>
              Oops! <br />
              Looks like your payment has failed
            </h2>
          </div>
          <div>
            <Button rippleEffect>Try Again</Button>
          </div>
        </div>
        <div style={{ textAlign: "center", margin: "10%" }}>
          <div>
            <h2>
              Oops! <br />
              Looks like your payment has failed
            </h2>
          </div>
          <div>
            <Button rippleEffect>Try Again</Button>
          </div>
        </div>
      </ModalPopup>
    </Fragment>
  );
};

export const ModalAnimation = Template.bind({});
ModalAnimation.args = {
  isOpen: false,
  closeButtonAlignment: "right",
  animationType: "fadeIn",
};

export const DefaultMobile = (args) => {
  const [showModal, setShowModal] = useState(false);
  return (
    <Fragment>
      <Button
      id="mob-btn-id"
        onClick={(e) => {
          e.stopPropagation();
          setShowModal(!showModal);
        }}
        isMobile={true}
      >
        Open Modal
      </Button>
      <ModalPopup
        {...args}
        isOpen={showModal || args.isOpen}
        isMobileView={true}
        onRequestClose={(e) => {
          setShowModal(false);
        }}
        onAfterClose={(e) => {
          setShowModal(false);
          args.isOpen = false;
        }}
      >
        <div style={{ textAlign: "center" }}>
          <div>
            <h2>
              Oops! <br />
              Looks like your payment has failed
            </h2>
          </div>
          <div>
            <Button id="try-mob-btn-id" rippleEffect>Try Again</Button>
          </div>
        </div>
      </ModalPopup>
    </Fragment>
  );
};
DefaultMobile.parameters = {
  viewport: {
    defaultViewport: "iphone5",
  },
};

export const MobileWithOverlay = (args) => {
  const [showModal, setShowModal] = useState(false);
  return (
    <Fragment>
      <Button
      id="overlay-btn"
      
        onClick={(e) => {
          e.stopPropagation();
          setShowModal(!showModal);
        }}
        isMobile={true}
      >
        Open Modal
      </Button>
      {Array.from({length:30}).map((v,index)=>(
        <div key={index} style={{display:"flex",textAlign:"center", border:"1px solid red"}}>
          <p>{`Element-${index+1}`}</p>
        </div>
      ))}
      {showModal && (

      <ModalPopup
        {...args}
        isOpen={showModal || args.isOpen}
        isMobileView={true}
        onRequestClose={(e) => {
          setShowModal(false);
        }}
        onAfterClose={(e) => {
          setShowModal(false);
          args.isOpen = false;
        }}
        showOverlayInMobile={true}
      >
        <div style={{ textAlign: "center" }}>
          <div>
            <h2>
              Oops! <br />
              Looks like your payment has failed
            </h2>
          </div>
          <div>
            <Button id="overlay-try-btn" rippleEffect>Try Again</Button>
          </div>
          {Array.from({length:30}).map((v,index)=>(
        <div key={index} style={{display:"flex",textAlign:"center", border:"1px solid red"}}>
          <p>{`Modal-Body-${index+1}`}</p>
        </div>
      ))}
        </div>
      </ModalPopup>
      )}
    </Fragment>
  );
};
MobileWithOverlay.parameters = {
  viewport: {
    defaultViewport: "iphone5",
  },
};
