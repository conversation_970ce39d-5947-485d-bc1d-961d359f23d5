import React, { Fragment, useEffect, useState, useRef, memo, useCallback, useMemo } from "react";
import PropTypes from "prop-types";
import {
  ModalWrapper,
  ModalHeader,
  ModalClose,
  ModalContent,
  Svg,
  ModalOverlay,
  ModalFooter,
} from "./ModalPopup.styled";
import { useClassName } from "../../hooks/useClassName";
import { ThemeWrapper } from "../Theme/ThemeContext";

// Utility function (placed before main component)
const isNonNull = (prop) => prop !== null && prop !== undefined;

export const ModalPopup = memo((props) => {
  // 1. PROP DESTRUCTURING & VALIDATION
  const {
    closeButtonAlignment = "left",
    isMobile,
    isMobileView,
    children,
    isOpen,
    onRequestClose,
    shouldCloseOnEsc = true,
    shouldCloseOnOverlayClick = true,
    onAfterClose,
    closeButton = true,
    overlayClassName,
    additionalClassName,
    additionalStyle,
    animationType,
    customiseCloseIcon,
    modalHeaderCustomView,
    modalFooterCustomView,
    showOverlayInMobile = false,
    ...otherProps
  } = props;

  // 2. PERFORMANCE OPTIMIZATIONS
  const computedClassName = useMemo(() => 
    useClassName(props, additionalClassName), 
    [props, additionalClassName]
  );

  const hasCustomHeader = useMemo(() => isNonNull(modalHeaderCustomView), [modalHeaderCustomView]);
  const hasCustomFooter = useMemo(() => isNonNull(modalFooterCustomView), [modalFooterCustomView]);
  const hasCustomCloseIcon = useMemo(() => isNonNull(customiseCloseIcon), [customiseCloseIcon]);

  // 3. EVENT HANDLERS
  const handleEscapeKey = useCallback((event) => {
    if (event.keyCode === 27 && shouldCloseOnEsc && isNonNull(onRequestClose)) {
      onRequestClose(event);
      if (isNonNull(onAfterClose)) {
        onAfterClose(event);
      }
    }
  }, [shouldCloseOnEsc, onRequestClose, onAfterClose]);

  const handleOverlayClick = useCallback((event) => {
    if (shouldCloseOnOverlayClick && isNonNull(onRequestClose)) {
      document.querySelector("body").style.removeProperty("overflow");
      onRequestClose(event);
      if (isNonNull(onAfterClose)) {
        onAfterClose(event);
      }
    }
  }, [shouldCloseOnOverlayClick, onRequestClose, onAfterClose]);

  const handleCloseButtonClick = useCallback((event) => {
    if (isNonNull(onRequestClose)) {
      onRequestClose(event);
    }
    if (isNonNull(onAfterClose)) {
      onAfterClose(event);
    }
  }, [onRequestClose, onAfterClose]);

  // 4. EFFECTS
  useEffect(() => {
    if (shouldCloseOnEsc) {
      window.addEventListener("keydown", handleEscapeKey);
      return () => {
        window.removeEventListener("keydown", handleEscapeKey);
      };
    }
  }, [shouldCloseOnEsc, handleEscapeKey]);

  useEffect(() => {
    if (isOpen) {
      document.querySelector("body").style.overflow = "hidden";
    }
    return () => {
      document.querySelector("body").style.removeProperty("overflow");
    };
  }, [isOpen]);

  // 5. CONDITIONAL RENDERING (memoized)
  const overlayElement = useMemo(() => (
    <ModalOverlay
      onClick={handleOverlayClick}
      isOverlay={isMobileView ? 
        isNonNull(showOverlayInMobile) && showOverlayInMobile === true && isOpen : 
        isOpen
      }
      className={`${overlayClassName || ''} vms_modalpop_modaloverlay`}
      role="presentation"
    />
  ), [handleOverlayClick, isMobileView, showOverlayInMobile, isOpen, overlayClassName]);

  const closeButtonElement = useMemo(() => {
    if (!closeButton) return null;
    
    return (
      <ModalClose
        className="vms_modalpop_modalclose"
        closeButtonAlignment={closeButtonAlignment}
        onClick={handleCloseButtonClick}
        role="button"
        aria-label="Close modal"
        tabIndex={0}
        onKeyDown={(event) => {
          if (event.key === 'Enter' || event.key === ' ') {
            event.preventDefault();
            handleCloseButtonClick(event);
          }
        }}
      >
        {hasCustomCloseIcon ? (
          <img 
            src={customiseCloseIcon} 
            width="24" 
            height="24" 
            alt="Close"
          />
        ) : (
          <CrossIcon />
        )}
      </ModalClose>
    );
  }, [
    closeButton,
    closeButtonAlignment,
    handleCloseButtonClick,
    hasCustomCloseIcon,
    customiseCloseIcon
  ]);

  const headerElement = useMemo(() => (
    <ModalHeader className="vms_modalpop_modalheader">
      {hasCustomHeader ? modalHeaderCustomView : closeButtonElement}
    </ModalHeader>
  ), [hasCustomHeader, modalHeaderCustomView, closeButtonElement]);

  const footerElement = useMemo(() => {
    if (!hasCustomFooter) return null;
    
    return (
      <ModalFooter className="vms_modalpopup_footer">
        {modalFooterCustomView}
      </ModalFooter>
    );
  }, [hasCustomFooter, modalFooterCustomView]);

  const modalWrapperElement = useMemo(() => (
    <ModalWrapper
      className={`${computedClassName} vms_modalpop_modalcontainer`}
      style={additionalStyle}
      isOpen={isOpen}
      isMobile={isMobileView}
      showOverlayInMobile={showOverlayInMobile}
      animationType={animationType}
      role="dialog"
      aria-modal="true"
      aria-labelledby="modal-header"
      tabIndex={-1}
      {...otherProps}
    >
      {headerElement}
      <ModalContent className="vms_modalpop_content" id="modal-content">
        {children}
      </ModalContent>
      {footerElement}
    </ModalWrapper>
  ), [
    computedClassName,
    additionalStyle,
    isOpen,
    isMobileView,
    showOverlayInMobile,
    animationType,
    headerElement,
    children,
    footerElement,
    otherProps
  ]);

  // 6. ERROR HANDLING
  React.useEffect(() => {
    if (!isNonNull(onRequestClose)) {
      console.warn('ModalPopup: onRequestClose callback is required for proper functionality');
    }
    if (!children) {
      console.warn('ModalPopup: children prop is required for modal content');
    }
  }, [onRequestClose, children]);

  return (
    <ThemeWrapper isMobile={isMobile || isMobileView || false}>
      <Fragment>
        {overlayElement}
        {modalWrapperElement}
      </Fragment>
    </ThemeWrapper>
  );
});

// Memoized Cross Icon component
const CrossIcon = memo(() => (
  <Svg
    x="0px"
    y="0px"
    width="24px"
    height="24px"
    viewBox="364 364 24 24"
    enableBackground="new 364 364 24 24"
    version="1.1"
    id="Layer_1"
    xmlSpace="preserve"
    xmlns="http://www.w3.org/2000/svg"
    xmlnsXlink="http://www.w3.org/1999/xlink"
    role="img"
    aria-hidden="true"
  >
    <path
      fill="currentColor"
      d="M386.631,366.739l-1.361-1.362l-9.265,9.265l-9.265-9.265l-1.361,1.362l9.265,9.264l-9.265,9.265l1.361,1.361l9.265-9.264
  l9.265,9.264l1.361-1.361l-9.265-9.265L386.631,366.739z"
    />
  </Svg>
));

// Component display names for debugging
ModalPopup.displayName = 'ModalPopup';
CrossIcon.displayName = 'CrossIcon';

ModalPopup.defaultProps = {
  isOpen: false,
  closeButtonAlignment: "left",
  shouldCloseOnOverlayClick: true,
  closeButton: true,
  shouldCloseOnEsc: true,
  isMobileView: false,
  onAfterClose: null,
  onRequestClose: null,
  overlayClassName: null,
  additionalStyle: null,
  additionalClassName: null,
  animationType: null,
  customiseCloseIcon: null,
  showOverlayInMobile: false,
};

ModalPopup.propTypes = {
  /**
   * Boolean describing if the modal should be shown or not.
   * Defaults to false.
   */
  isOpen: PropTypes.bool.isRequired,
  /**
   * Property to append additional css class
   */
  overlayClassName: PropTypes.string,
  /**
   * Function that will be run after the modal has opened.
   * OnAfterOpenCallback
   */
  onAfterOpen: PropTypes.func,
  /**
   * Function that will be run after the modal has closed.
   * () => void
   */
  onAfterClose: PropTypes.func,
  /**
   * Function that will be run when the modal is requested to be closed, prior to actually closing.
   * (event: MouseEvent<Element, MouseEvent> | KeyboardEvent<Element>) => void
   */
  onRequestClose: PropTypes.func,
  /**
   * Boolean indicating if the overlay should close the modal. Defaults to true
   */
  shouldCloseOnOverlayClick: PropTypes.bool,
  /**
   * Boolean indicating if pressing the esc key should close the modal
   */
  shouldCloseOnEsc: PropTypes.bool,
  /**
   * Content of modal
   * `ReactNode`
   */
  children: PropTypes.node,
  /**
   * Display mode of mobile or desktop
   */
  isMobileView: PropTypes.bool,
  /**
   * Close button for model, Default:false
   */
  closeButton: PropTypes.bool,
  /**
   * Position of Close button for model, Default:left
   */
  closeButtonAlignment: PropTypes.oneOf(["left", "right"]),
  /**
   * Classes to add additional styling in the parent container
   * string[]
   */
  additionalClassName: PropTypes.arrayOf(PropTypes.string),
  /**
   * Inline styles to add additional styling in the parent containers
   */
  additionalStyle: PropTypes.object,
  /**
   * Applies animations while opening modal.Not applicable for mobile
   */
  animationType: PropTypes.oneOf(["fadeIn"]),
  /**
   * Customise close icon
   */
  customiseCloseIcon: PropTypes.string,
  /**
   * Add Custom React Component View in Modal Header
   */
  modalHeaderCustomView: PropTypes.node,
  /**
   * Add Custom React Component View in Modal Footer
   */
  modalFooterCustomView: PropTypes.node,
  /**
   * View Modal Overlay (applicable in mobile-view only)
   * default: false
   */
  showOverlayInMobile: PropTypes.bool
};
