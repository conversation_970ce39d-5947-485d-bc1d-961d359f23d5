.rdrMonths {
    display: flex;
}

.rdrMonth {
    width: 300px;
    margin: 10px 17px 13px;
    padding: 0;
    position: relative;
}

.rdrDays {
    display: flex;
    flex-wrap: wrap;
}

.rdrDayStartPreview {
    border-top-width: 1px;
    border-left-width: 1px;
    border-bottom-width: 1px;
    border-top-left-radius: 1.333em;
    border-bottom-left-radius: 1.333em;
    left: 0px;
}

.rdrDayInPreview {
    border-top-width: 1px;
    border-bottom-width: 1px;
}

.rdrDayEndPreview {
    border-top-width: 1px;
    border-right-width: 1px;
    border-bottom-width: 1px;
    border-top-right-radius: 1.333em;
    border-bottom-right-radius: 1.333em;
    right: 2px;
    right: 0px;
}


.rdrSelected,
.rdrInRange,
.rdrStartEdge,
.rdrEndEdge {
    background: #DDEEDE;
    position: absolute;
    top: 5px;
    left: 0;
    right: 0;
    bottom: 5px;
}

.rdrSelected {
    left: 2px;
    right: 2px;
}

.rdrInRange {}

.rdrRangeStartDate {
    border-radius: 0;
    background: transparent url('../../assets/images/DateRangeCalendar/startDate.svg');
    background-size: 100% 100%;
    background-position: center;
    position: absolute;
    top: 0;
    bottom: 0;
    right: 0px !important;
    left: 0px !important;
    z-index: 4 !important;
    font-weight: bold !important;
}

.rdrRangeEndDate {
    border-top-right-radius: 1.042em;
    border-bottom-right-radius: 1.042em;
    right: 2px;

    border-radius: 0;
    background: transparent url('../../assets/images/DateRangeCalendar/endDate.svg');
    background-size: 100% 100%;
    background-position: center;
    position: absolute;
    top: 0;
    bottom: 0;
    right: 0px !important;
    left: 0px !important;
    z-index: 4 !important;
    font-weight: bold !important;
}

.rdrSelected {
    border-radius: 1.042em;
}