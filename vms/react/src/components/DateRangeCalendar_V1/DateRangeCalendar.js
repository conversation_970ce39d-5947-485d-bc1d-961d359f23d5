import React, { useState, useEffect, useRef } from "react";
import PropTypes from "prop-types";
import { DateInput } from "./Components/DateInput";
import {
  CalendarContainer,
  CalendarFlyOut,
  CalendarWrapper,
  OverlayContainer,
  OverlayDiv,
} from "./Components/CalendarContainer";
import { MonthHeader } from "./Components/MonthHeader";
import {
  addDay,
  addMonth,
  formatWithLocale,
  getNextYear,
  subMonth,
  whetherSameDay,
  checkRangeIsValid,
  checkRangeErrorCode,
  whetherAfter,
  findNextRangeIndex,
} from "../../hooks/calendarHooks";
import { useOnClickOutside } from "../../hooks/outsideClickHandler";
import "./style.css";
import {
  addMonths,
  differenceInCalendarDays,
  differenceInDays,
  isBefore,
  isSameDay,
  min,
} from "date-fns";
import Month from "./Components/Month";
import { useClassName } from "../../hooks/useClassName";
import { ThemeWrapper } from "../Theme/ThemeContext";
const DateRangeCalendar_V1 = (props) => {
  const className = useClassName(props, props.additionalClassName);
  const [editting, setEditting] = useState(props.openCalendar);
  const [fromDate, setFromDate] = useState(props.startDate);
  const [toDate, setToDate] = useState(props.endDate);
  const [fromMonth, setFromMonth] = useState(new Date());
  const [toMonth, setToMonth] = useState(
    addMonth(new Date(), props.noofMonthView - 1)
  );
  const [selectedDate, setSelectedDate] = useState();
  const [selectDateChange, setSelectedDateChange] = useState(false);
  const [whetherFirstInput, setWhetherFirstInput] = useState(true);
  const [focusedRange, setfocusedRange] = useState([0, 0]);
  const [newSelectedDate, setNewSelectedDate] = useState(null);
  const [ranges, setRange] = useState([
    {
      startDate: props.startDate,
      endDate: props.endDate,
    },
  ]);
  const [preview, setPreview] = useState(null);

  const [drag, setDrag] = useState({
    status: false,
    range: { startDate: null, endDate: null },
  });
  let calendarDropdownref = useRef();
  useOnClickOutside(calendarDropdownref, () => setEditting(false));
  const {
    calendarType,
    noofMonthView,
    maxBookingDays,
    isOverlay,
    maxDate,
    dateDisplayFormat,
    indicatorColor,
    arrowImages,
    rangeColors,
    minDate,
    mode,
    flyoutPosition,
    currentSelection,
    isMobile,
    isMobileView,id
  } = props;

  useEffect(() => {
    const { onCalendarVisibilityChange } = props;
    if (editting) {
      onCalendarVisibilityChange(editting);
    }
  }, [editting]);

  useEffect(() => {
    setEditting(props.openCalendar);
    setRange([
      {
        startDate: props.startDate,
        endDate: props.endDate,
      },
    ]);
    if (props.currentSelection === "endDate") {
      setfocusedRange([0, 1])
      setFromMonth(new Date())
      setToMonth(addMonths(props.startDate, noofMonthView - 1))
    }
    setSelectedDate(
      props.startDate ? props.startDate : new Date()
    );
    setNewSelectedDate(
      props.currentSelection === "startDate"
        ? props.startDate
        : props.currentSelection === "endDate"
          ? props.endDate
          : new Date()
    );
  }, [
    props.openCalendar,
    props.currentSelection,
    props.startDate,
    // props.endDate,
  ]);

  const nextMonth = () => {
    setFromMonth(addMonth(fromMonth, 1));
    setToMonth(addMonth(toMonth, 1));
    const newDate = addMonth(selectedDate, 1);
    setSelectedDate(newDate);
  };

  const prevMonth = () => {
    setFromMonth(subMonth(fromMonth, 1));
    setToMonth(subMonth(toMonth, 1));
    setSelectedDate(subMonth(selectedDate, 1));
  };

  const onMonthClick = (index, lastIndex) => {
    const { noofMonthView, maxDate } = props;
    let newFromMonth = addMonth(
      new Date(),
      lastIndex
        ? index - (noofMonthView > 2 ? noofMonthView - 2 : noofMonthView - 1)
        : index
    );
    let newToMonth = addMonth(newFromMonth, noofMonthView - 1);
    setFromMonth(newFromMonth);
    setToMonth(newToMonth);
    setSelectedDate(newFromMonth);
    if (
      !whetherSameDay(newToMonth, maxDate) &&
      whetherAfter(newToMonth, maxDate)
    ) {
      let newFromMonth = subMonth(newToMonth, noofMonthView);
      setSelectedDate(newFromMonth);
      setFromMonth(newFromMonth);
      setToMonth(addMonth(newFromMonth, noofMonthView - 1));
    }
  };

  const onItemClick = (date, range) => {
    const {
      onSingleDateRangeSelectionChange,
      maxBookingDays,
      minBookingDays,
      currentSelection,
      onDateSelectionChange,
    } = props;

    console.log("vale", date, range);
    if (props.mode == "singleDateRange") {
      let isValid = checkRangeIsValid(
        range[0].startDate,
        range[0].endDate,
        minBookingDays,
        maxBookingDays
      );
      let result = {
        calendarState: {
          // startDate:currentSelection == "startDate" ? date : fromDate,
          // endDate:currentSelection == "endDate" ? date :  toDate,
          valid: isValid,
        },
        date: date,
        dateType: currentSelection,
        format1: formatWithLocale(date, "yyyy-MM-dd'T'HH:mm:ss'Z'"),
        format2: formatWithLocale(date, "dd-MM-yyyy"),
      };
      if (currentSelection === "startDate") {
        result.calendarState.startDate = range[0].startDate;
        result.calendarState.endDate = range[0].endDate;
      }
      if (currentSelection === "endDate") {
        result.calendarState.startDate = range[0].startDate;
        result.calendarState.endDate = range[0].endDate;
      }

      if (!isValid) {
        let errorCode = checkRangeErrorCode(
          range[0].startDate,
          range[0].endDate,
          minBookingDays,
          maxBookingDays
        );
        if (errorCode !== 0) {
          result.calendarState.errorCode = errorCode;
        }
      }
      console.log("result", result);
      onSingleDateRangeSelectionChange(result);
      setNewSelectedDate(date);
      setSelectedDateChange(true);
      setEditting(!editting);
    } else {
      if (whetherFirstInput) {
        setWhetherFirstInput(false);
      } else {
        let isRangeValid = checkRangeIsValid(
          range[0].startDate,
          range[0].endDate,
          minBookingDays,
          maxBookingDays
        );
        let rangeResult = {
          startDate: range[0].startDate,
          endDate: range[0].endDate,
          valid: isRangeValid,
          datesInFormat: {
            format1: {
              startDate: formatWithLocale(
                range[0].startDate,
                "yyyy-MM-dd'T'HH:mm:ss'Z'"
              ),
              endDate: formatWithLocale(
                range[0].endDate,
                "yyyy-MM-dd'T'HH:mm:ss'Z'"
              ),
            },
            format2: {
              startDate: formatWithLocale(range[0].endDate, "dd-MM-yyyy"),
              endDate: formatWithLocale(range[0].endDate, "dd-MM-yyyy"),
            },
          },
        };
        if (!isRangeValid) {
          let errorRangeCode = checkRangeErrorCode(
            range[0].startDate,
            range[0].endDate,
            minBookingDays,
            maxBookingDays
          );
          if (errorRangeCode !== 0) {
            rangeResult.errorCode = errorRangeCode;
          }
        }
        setWhetherFirstInput(true);
        // setFromDate(fromDate);
        // setToDate(date);
        onDateSelectionChange(rangeResult);
        setEditting(!editting);
      }
    }
  };

  const calcNewSelection = (value, isSingleValue = true) => {
    const { moveRangeOnFirstSelection } = props;

    console.log("ranges", ranges);
    const focusedRangeIndex = focusedRange[0];
    const selectedRange = ranges[focusedRangeIndex];
    if (!selectedRange) return {};
    let { startDate, endDate } = selectedRange;
    const now = new Date();
    let nextFocusRange;
    if (!isSingleValue) {
      startDate = value.startDate;
      endDate = value.endDate;
    } else if (focusedRange[1] === 0) {
      // startDate selection
      const dayOffset = differenceInCalendarDays(endDate || now, startDate);
      const calculateEndDate = () => {
        return value || now;
      };
      startDate = value;
      endDate = calculateEndDate();
      if (maxDate) endDate = min([endDate, maxDate]);
      nextFocusRange = [focusedRange[0], 1];
    } else {
      endDate = value;
    }

    // reverse dates if startDate before endDate
    let isStartDateSelected = focusedRange[1] === 0;
    if (isBefore(endDate, startDate)) {
      isStartDateSelected = !isStartDateSelected;
      [startDate, endDate] = [endDate, startDate];
    }

    if (!nextFocusRange) {
      const nextFocusRangeIndex = findNextRangeIndex(ranges, focusedRange[0]);
      nextFocusRange = [nextFocusRangeIndex, 0];
    }
    return {
      range: { startDate, endDate },
      nextFocusRange: nextFocusRange,
    };
  };
  const onPreviewChange = (date) => {
    // calcNewSelection(date ? date : null)

    if (!date) {
      setPreview(null);
      return;
    }
    // const { rangeColors, ranges } = this.props;
    // const color =
    //   ranges[focusedRange[0]]?.color || rangeColors[focusedRange[0]] || color;
    const newSelection = calcNewSelection(date);
    setPreview({ ...newSelection.range });
  };

  const setSelection = (value, isSingleValue) => {
    const focusedRangeIndex = focusedRange[0];
    const selectedRange = ranges[focusedRangeIndex];
    if (!selectedRange) return;
    const newSelection = calcNewSelection(value, isSingleValue);
    setRange([newSelection.range]);
    onItemClick(value, [newSelection.range]);
    // if(isDayClicked){
    //   onItemClick(value)
    // }
    setfocusedRange(newSelection.nextFocusRange);
    // onRangeFocusChange && onRangeFocusChange(newSelection.nextFocusRange);
  };

  const onDragSelectionStart = (date) => {
    setDrag({
      status: true,
      range: { startDate: date, endDate: date },
      disablePreview: true,
    });
    setSelection(date);
  };

  const onDragSelectionEnd = (date) => {
    const newRange = {
      startDate: drag.range.startDate,
      endDate: date,
    };
    if (isSameDay(newRange.startDate, date)) {
      setDrag({
        status: false,
        range: {},
        disablePreview: true,
      });
    } else {
      setDrag({
        status: false,
        range: {},
        disablePreview: true,
      });
      setSelection(date, false);
      // updateRange(newRange);
    }
  };

  const onDragSelectionMove = (date) => {
    if (!drag.status) return;
    setDrag({
      status: drag.status,
      range: { startDate: drag.range.startDate, endDate: date },
      disablePreview: true,
    });
  };


  return (
    <ThemeWrapper isMobile={isMobile || isMobileView || false}>
      <OverlayContainer
        className={`vms_DateRangeCalendar_OverlayContainer`}
        isOverlay={isOverlay}
        ref={calendarDropdownref}
        editting={editting}
      >
        <CalendarContainer
          className={`${className} vms_DateRangeCalendar_CalendarContainer`}
          style={props.additionalStyle}
        >
          <DateInput
            editting={editting}
            fromDate={fromDate}
            toDate={toDate}
            selectedDate={newSelectedDate}
            mode={props.mode}
            currentSelection={props.currentSelection}
            inputBoxLabel={props.inputBoxLabel}
            formatDateInput={dateDisplayFormat}
            selectDateChange={selectDateChange}
            inputClick={() => setEditting(!editting)}
            customInputView={props.customInputView}
            calendarType={calendarType}
            id={props.id ? props.id : null}
            range={ranges}
          />
          <CalendarFlyOut flyoutPosition={flyoutPosition}>
            {editting && selectedDate && ranges && (
              <>
                <CalendarWrapper className="rdrCalendarWrapper">
                  <MonthHeader
                    from={new Date()}
                    to={
                      maxBookingDays !== 30
                        ? addDay(new Date(), maxBookingDays)
                        : getNextYear(1)
                    }
                    fromMonth={fromMonth}
                    toMonth={toMonth}
                    onClick={(index, lastIndex) =>
                      onMonthClick(index, lastIndex)
                    }
                    indicatorColor={indicatorColor}
                  />
                  <div className="rdrMonths">
                    {new Array(noofMonthView).fill(null).map((_, i) => {
                      let monthStep = addMonths(selectedDate, i);
                      return (
                        <Month
                        id={id ? id : null}
                          key={i}
                          month={monthStep}
                          showWeekDays
                          showMonthName
                          isFromMonth={i === 0}
                          isToMonth={i === noofMonthView - 1}
                          selectedDate={selectedDate}
                          fixedHeight={false}
                          maxBookingDays={maxBookingDays}
                          minDate={minDate}
                          maxDate={maxDate}
                          arrowImages={arrowImages}
                          onNextMonth={() => nextMonth()}
                          onPrevMonth={() => prevMonth()}
                          ranges={ranges}
                          drag={drag}
                          onDragSelectionStart={onDragSelectionStart}
                          onDragSelectionEnd={onDragSelectionEnd}
                          onDragSelectionMove={onDragSelectionMove}
                          onMouseLeave={onPreviewChange}
                          onPreviewChange={onPreviewChange}
                          focusedRange={focusedRange}
                          noofMonthView={noofMonthView}
                          newSelectedDate={null}
                          fromMonth={fromMonth}
                          toMonth={toMonth}
                          preview={preview}
                          mode={mode}
                          currentSelection={currentSelection}
                          // onMouseLeave={() => onPreviewChange && onPreviewChange()}
                          onDayClick={(value) => {
                            onItemClick(value);
                          }}
                        // isDateChange={isDateChange}
                        // newSelectedDate={null}
                        />
                      );
                    })}
                  </div>
                </CalendarWrapper>
              </>
            )}
          </CalendarFlyOut>
        </CalendarContainer>
      </OverlayContainer>
      {isOverlay && editting && (
        <OverlayDiv
          className="vms_DateRangeCalendar_OverlayDiv"
          editting={editting}
        />
      )}
    </ThemeWrapper>
  );
};

DateRangeCalendar_V1.defaultProps = {
  minDate: new Date(),
  maxDate: getNextYear(1),
  noofMonthView: 2,
  onRangeSelected: () => { },
  onSingleDateRangeSelectionChange: () => { },
  onDateSelectionChange: () => { },
  view: "range",
  onCalendarVisibilityChange: () => { },
  dateDisplayFormat: "dd MMM yyyy",
  additionalClassName: null,
  additionalStyle: null,
  inputBoxLabel: "",
  inputPlaceholder: "Select Date",
  isOverlay: false,
  calendarType: "primary",
  minBookingDays: 1,
  maxBookingDays: 30,
  currentSelection: "startDate",
  mode: "multiDateRange",
  openCalendar: false,
  indicatorColor: "#f26b6a",
  focusedRange: [0, 0],
  rangeColors: ["#3d91ff", "#3ecf8e", "#fed14c"],
  flyoutPosition: 'left'
};

DateRangeCalendar_V1.propTypes = {
  /**
    
  * ID for the input
   */
  id: PropTypes.string,

  /**
   * Variations of Calendar Type
   */
  calendarType: PropTypes.oneOf(["primary", "secondary"]),

  /**
   * Range start Date.
Date
   */

  startDate: PropTypes.instanceOf(Date).isRequired,

  /**
   * Range End Date.
Date
   */
  endDate: PropTypes.instanceOf(Date).isRequired,

  /**
   * Defines how many months need to display side by side. it will take 2 as default value.
   */
  noofMonthView: PropTypes.number,

  /**
   * If I want to open calendar pass true.
   */
  openCalendar: PropTypes.bool,
  /**
   * To display label above input box. Note: It will be ignored if @property customInputView is provided
string
string
   */

  inputBoxLabel: PropTypes.string,

  /**
   * 	
Defines minimum date. Disabled earlier dates. it will take current Date as default value.

Date
   */
  minDate: PropTypes.instanceOf(Date),

  /**
   * Defines maximum date. Disabled later dates it will take current Date + 1 year as default value.
    Date
   */
  maxDate: PropTypes.oneOfType([PropTypes.instanceOf(Date)]),

  /**
   * It will replace back and next arrow with given values. you can pass direct web URL or local image path
{ prevArrowImage: string; nextArrowImage: string; prevArrowImageAlt?: string; nextArrowImageAlt?: string; }
   */
  arrowImages: PropTypes.shape({
    prevArrowImage: PropTypes.string,
    nextArrowImage: PropTypes.string,
    prevArrowImageAlt: PropTypes.string,
    nextArrowImageAlt: PropTypes.string,
  }),

  /**
   * If @property isMobile is true then this function will be called when user click on done button
If @property isMobile is false then this function will be called when user click on any date
it will return {
   date: Date | undefined,
   format1 (ISO format): string,
   format2 (DD-MM-YYYY): string,
   };
}

(result: CalendarOutput) => void
   */
  onRangeSelected: PropTypes.func,

  /**
   * 
    callback function for range changes when @property mode is multiDateRange.
it will return {
   startDate: Date,
   endDate: Date,
   valid: boolean,
   errorCode?: number (optional),
   datesInFormat: {
     format1 (ISO format): {
       startDate: string;
       endDate: string;
     };
     format2 (DD-MM-YYYY): {
       startDate: string;
       endDate: string;
     };
   };
}
if valid = false then it will return errorCode.
errorCode values
1 = When start and end Date same
2 = When selected range is less than minBookingDays
3 = When selected range is more than maxBookingDays

(result: DateRangeCalendarOutput) => void
   */
  onDateSelectionChange: PropTypes.func,

  /**
   * Displayed Date format . Default value dd MMM yyyy
Please refer https://date-fns.org/v2.12.0/docs/format for various allowed format

string
   */
  dateDisplayFormat: PropTypes.string,

  /**
   * 	
It will give you callback when calendar open/close. It will be useful when you want to do some conditional coding based on calendarVisibility

(isOpen: boolean) => void
   */
  onCalendarVisibilityChange: PropTypes.func,
  /**
   *Inline styles to add additional styling in the parent container
CSSProperties
   */
  additionalStyle: PropTypes.object,
  /**
   * Classes to add additional styling in the parent container
string[]
   */

  additionalClassName: PropTypes.arrayOf(PropTypes.string),
  /**
   * Place holder text for input box. Default value Select Date
string
   */
  inputPlaceholder: PropTypes.string,
  /**
   *
To wrap the component with overlay. Default value false
boolean
   */
  isOverlay: PropTypes.bool,

  /**
   * Custom input, It will replace calendar's default input box
However component will bind onClick event to this custom input so calendar will open on click of it

ReactNode
   */
  customInputView: PropTypes.oneOfType([PropTypes.node, PropTypes.string]),

  /**
   * Calendar Mode
singleDateRange - It will allow to select only one date (based on @property currentSelection value) in single calendar
multiDateRange - It will allow to select two dates in single calendar

"multiDateRange" | "singleDateRange"
   * 
   */
  mode: PropTypes.oneOf(["singleDateRange", "multiDateRange"]),

  /**
   *  Minimum days differece between start and end date. it will take 1 as default value.
number
   */
  minBookingDays: PropTypes.number,
  /**
   *  Maximum days differece between start and end date. it will take 30 as default value.
number
 */
  maxBookingDays: PropTypes.number,

  /**
   *   Only used in singleDateRange mode, based on this value calendar will select the range type
  "startDate" | "endDate"
 */
  currentSelection: PropTypes.oneOf(["startDate", "endDate"]),
  /**
   *  Active MonthName color
string
   */
  indicatorColor: PropTypes.string,

  /**
   * 	
It will give you callback when calendar open/close. It will be useful when you want to do some conditional coding based on calendarVisibility

(result) => void
   */
  onSingleDateRangeSelectionChange: PropTypes.func,
};

export { DateRangeCalendar_V1 };
