import styled from "styled-components";

const CalendarContainer = styled.div`
  position: relative;
  min-width: 320px;
  --widthA: calc(100% - 28px);
  --widthB: calc(var(--widthA) / 7);
`;
const OverlayDiv = styled.div`
  top: 0%;
  left: 0%;
  width: 100%;
  height: 100%;
  position: fixed;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 99;
  display: ${(props) => (props.editting ? "block" : "none")};
`;

const OverlayContainer = styled.div`
  position: relative;
  z-index: ${({ isOverlay }) => (isOverlay ? 100 : "")};
`;

const CalendarFlyOut = styled.div`
  display: flex;
  justify-content: ${({flyoutPosition}) => (flyoutPosition === 'left' ? "flex-start" : "center")};
`;

const CalendarWrapper = styled.div`
  box-sizing: border-box;
  background: #ffffff;
  display: inline-flex;
  flex-direction: column;
  user-select: none;
  color: #000000;
  font-size: 12px;
  border-radius: 3px;
  box-shadow: 0 2px 4px 0 rgb(0 0 0 / 12%);
  border: solid 1px #03868b;
`;

export {
  CalendarContainer,
  OverlayDiv,
  OverlayContainer,
  CalendarFlyOut,
  CalendarWrapper,
};
