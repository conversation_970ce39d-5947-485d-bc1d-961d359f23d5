import React, { PureComponent } from "react";
import DayCell from "./DayCell";
import {
  format,
  startOfWeek,
  endOfWeek,
  isWithinInterval,
  eachDayOfInterval,
} from "date-fns";
import {
  addDay,
  getMonthDisplayRange,
  whetherDisabled,
  whetherSameDay,
} from "../../../hooks/calendarHooks";
import "../style.css";
import styled from "styled-components";
import { Header } from "./Header";
import {isWeekend} from 'date-fns'

const WeekDayContainer = styled.div`
  width: 100%;
  box-sizing: border-box;
  padding: 0px;
  display: flex;
`;

const WeekDayLabel = styled.span`
  font-size: 12px;
  line-height: 1.4;
  letter-spacing: normal;
  text-align: center;
  color: #4d4d4f;
  font-weight: 400;
  font-family: Montserrat;
  flex-basis: calc(100% / 7);
  box-sizing: inherit;
  text-align: center;
  font-weight: 400;
  line-height: 2.667em;
  padding: 0.7em 0.3em;
`;

const MonthNameLabel = styled.div`
  text-align: center;
  font-weight: 700;
  font-family: ${({ theme }) => theme?.typography?.fontFamily || ""};
  color: #4d4d4f;
  font-size: 14px;
  padding: 0.6em 0;
  line-height: 1.43;
  letter-spacing: 0.3px;
`;

function renderWeekdays(dateOptions, weekdayDisplayFormat) {
  const now = new Date();

  return (
    <WeekDayContainer className="vms_Calendar_weekdaysContainer">
      {eachDayOfInterval({
        start: startOfWeek(now, dateOptions),
        end: endOfWeek(now, dateOptions),
      }).map((day, i) => (
        <WeekDayLabel className="vms_Calendar_weekdaysText" key={i}>
          {format(day, weekdayDisplayFormat)}
        </WeekDayLabel>
      ))}
    </WeekDayContainer>
  );
}

class Month extends PureComponent {
  render() {
    const now = new Date();
    const {
      selectedDate,
      newSelectedDate,
      isDateChange,
      showWeekDays,
      Monthstyle,
      isMobile,
      maxBookingDays,
      maxDate,
      arrowImages,
      isFromMonth,
      isToMonth,
      noofMonthView,
      preview,
      fromMonth,
      toMonth,
      mode,
      currentSelection,
      onDayClick = () => {},
      onNextMonth = () => {},
      onPrevMonth = () => {},
      minDate,id
    } = this.props;
    this.dateOptions = { locale: "en-US" };
    const monthDisplay = getMonthDisplayRange(
      this.props.month,
      this.dateOptions,
      this.props.fixedHeight
    );


    console.log("monthDisplay",monthDisplay,this.props.ranges)
    let ranges = this.props.ranges;

    // if (drag.status) {
    //   let { startDate, endDate } = drag.range;
    //   ranges = ranges.map((range, i) => {
    //     if (i !== focusedRange[0]) return range;
    //     return {
    //       ...range,
    //       startDate,
    //       endDate,
    //     };
    //   });
    // }
    return (
      <div
        className={`${"rdrMonth vms_Calendar_monthContainer"}`}
        style={Monthstyle}
      >
        <Header
          selectedDate={ isFromMonth ? fromMonth : (isToMonth ? toMonth :selectedDate ) }
          view="range"
          formatMonthYear="MMMM yyyy"
          next={onNextMonth}
          noofMonthView={noofMonthView}
          prev={onPrevMonth}
          isFromMonth={isFromMonth}
          newSelectedDate={newSelectedDate}
          isToMonth={isToMonth}
          minDate={minDate}
          startDate={new Date()}
          endDate={
            maxBookingDays !== 30 ? addDay(new Date(), maxBookingDays) : maxDate
          }
          maxDate={maxDate}
          arrowImages={arrowImages}
        />

        {this.props.showMonthName ? (
          <MonthNameLabel className={"vms_Calendar_monthNameText"}>
            {format(this.props.month, "MMM yyyy", this.props.dateOptions)}
          </MonthNameLabel>
        ) : null}
        {showWeekDays && renderWeekdays(this.dateOptions, "eeeee")}
        <div
          className={`${
            "rdrDays  vms_Calendar_rdrDaysContainer"
          }`}
        >
          {eachDayOfInterval({
            start: monthDisplay.start,
            end: monthDisplay.end,
          }).map((day,i) => {
            const isStartOfMonth = whetherSameDay(
              day,
              monthDisplay.startDateOfMonth
            );
            const isEndOfMonth = whetherSameDay(
              day,
              monthDisplay.endDateOfMonth
            );
            const isDisabledDay = whetherDisabled(
              day,
              monthDisplay.startDateOfMonth,
              this.props.minDate,
              this.props.maxDate,
              mode,
              currentSelection,
              ranges[0].startDate,
            );
            // console.log("isDisabledDay",isDisabledDay,day)
            return (
              <div key={i}>
                <DayCell
                id={id ? id : null}
                day={day}
                ranges={ranges}
                isWeekend={isWeekend(day, this.dateOptions)}
                isToday={whetherSameDay(day, now)}
                isStartOfWeek={whetherSameDay(
                  day,
                  startOfWeek(day, this.props.dateOptions)
                )}
                isEndOfWeek={whetherSameDay(
                  day,
                  endOfWeek(day, this.props.dateOptions)
                )}
                disabled={isDisabledDay}
                selectedDate={selectedDate}
                isStartOfMonth={isStartOfMonth}
                isEndOfMonth={isEndOfMonth}
                isPassive={
                  !isWithinInterval(day, {
                    start: monthDisplay.startDateOfMonth,
                    end: monthDisplay.endDateOfMonth,
                  })
                }
                isMobile={isMobile}
                newSelectedDate={newSelectedDate}
                isDateChange={isDateChange}
                onDayClick={(val) => {
                  onDayClick(val);
                }}
                preview={preview}
                onMouseDown={this.props.onDragSelectionStart}
                onMouseUp={this.props.onDragSelectionEnd}
                onMouseEnter={this.props.onDragSelectionMove}
                onPreviewChange={this.props.onPreviewChange}
              />
              </div>
            );
          })}
        </div>
      </div>
    );
  }
}

export default Month;
