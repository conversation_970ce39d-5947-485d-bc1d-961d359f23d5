import React from "react";
import styled from "styled-components";
import { formatWithLocale } from "../../../hooks/calendarHooks";
import ArrowUpSvg from "../../../assets/images/DateRangeCalendar/rangeArrowup.svg";
import ArrowDownSvg from "../../../assets/images/DateRangeCalendar/rangeArrowdown.svg";
import PropTypes from "prop-types";

function border(isMobile, editting, theme, type) {
  if (isMobile) {
    return "none";
  } else {
    return editting
      ? `solid 1px ${theme?.palette?.[type]?.main}`
      : "solid 1px #4d4d4f";
  }
}


const DateInputContainer = styled.div`
  max-width: 320px;
`;

const Label = styled.label`
  font-weight: 400;
  font-family: ${({ theme }) => theme?.typography?.fontFamily || ""};
  font-size: ${({ theme }) => theme?.typography?.text?.fontSize || ""};
  font-stretch: normal;
  font-style: normal;
  line-height: 20px;
  letter-spacing: 0.3px;
  color: #4d4d4f;
  display: block;
  position: relative;
  text-transform: uppercase;
`;



const InputContainer = styled.input`
  display: block;
  box-sizing: border-box;
  font-weight: 700;
  font-family: ${({ theme }) => theme?.typography?.fontFamily || ""};
  font-size: ${({ theme }) => `${theme?.typography?.text?.fontSize}` || ""};
  font-stretch: normal;
  font-style: normal;
  line-height: 1.43;
  width: 100%;
  border-radius: 3px;
  letter-spacing: 0.3px;
  color: #4d4d4f;
  padding: 14px 16px;
  margin-top: 12px;
  text-overflow: ellipsis;
  height: 48px;
  position: relative;
  border: ${({ theme, isMobile, editting, calendarType }) =>
    border(isMobile, editting ? true : false, theme, calendarType)};
  background-color: #fff;

  &::placeholder {
    font-weight: 700;
    font-family: ${({ theme }) => theme?.typography?.fontFamily || ""};
    font-size: ${({ theme }) => `${theme?.typography?.text?.fontSize}` || ""};
    font-stretch: normal;
    font-style: normal;
    line-height: 1.43;
    border-radius: 3px;
    letter-spacing: 0.3px;
    color: #4d4d4f;
  }

  &:focus {
    outline: none;
    height: 48px;
    border-radius: 3px;
    border: ${({ theme, isMobile, editting, calendarType }) =>
      border(isMobile, editting ? true : false, theme, calendarType)};
    background-color: #fff;
  }
`;

const IconContainer = styled.div`
  display: inline-block;
  background-size: 20px 18px !important;
  background-repeat: no-repeat !important;
  background-position: center !important;
  width: 24px;
  height: 24px;
  text-decoration: none;
  outline: none;
  position: absolute;
  bottom: 12px;
  right: 15px;
`;

const DateInput = (props) => {
  const {
    inputClick,
    editting,
    formatDateInput,
    inputBoxLabel,
    mode,
    selectedDate,
    currentSelection,
    selectDateChange,
    customInputView,
    calendarType,
    range,id
  } = props;
  const FormattedFromDate = range &&  range[0]
    ? formatWithLocale(range[0].startDate, formatDateInput)
    : "";
  const FormattedToDate = range &&  range[0]
    ? formatWithLocale(range[0].endDate, formatDateInput)
    : "";
  const FormattedSelectedDate = selectedDate
    ? formatWithLocale(selectedDate, formatDateInput)
    : "";
    console.log("ranges",range,FormattedFromDate,FormattedToDate)
  return (
    <>
      {customInputView ? (
        <>{customInputView}</>
      ) : (
        <DateInputContainer
          className={`vms_DateRangeCalendar_InputContainer`}
          {...props}
        >
          {inputBoxLabel ? (
            <Label id={id ? id+"-label" : null} className={`vms_DateRangeCalendar_Input_lable`}>
              {inputBoxLabel}
            </Label>
          ) : (
            ""
          )}
          <div style={{ position: "relative" }}>
            <InputContainer
            id={id? id : null}
              type="date-text"
              editting={editting}
              value={
                mode === "singleDateRange"
                  ? currentSelection == "startDate"
                    ? FormattedSelectedDate
                    : ""
                  : FormattedFromDate + "-" + FormattedToDate
              }
              onClick={(e) => inputClick(e)}
              readOnly
              isMobile={props.isMobile}
              placeholder={
                selectDateChange
                  ? FormattedSelectedDate
                  : currentSelection == "endDate" &&
                    mode === "singleDateRange" &&
                    "Select Placeholder"
              }
              className={`vms_DateRangeCalendar_Input`}
              calendarType={calendarType}
            />
            <IconContainer className={`vms_DateRangeCalendar_IconCotainer`}>
              <img src={editting ? ArrowUpSvg : ArrowDownSvg} alt="plus" />
            </IconContainer>
          </div>
        </DateInputContainer>
      )}
    </>
  );
};

DateInput.defaultProps = {
  inputClick: () => {},
};

DateInput.propTypes = {
  inputClick: PropTypes.func,
  placeholder: PropTypes.string,
  id:PropTypes.string
};

export { DateInput };
