import React from "react";
import styled from "styled-components";
import PropTypes from "prop-types";
import {
  addMonth,
  formatWithLocale,
  whetherAfter,
  whetherBefore,
  whetherBetween,
} from "../../../hooks/calendarHooks";
import BackArrowSvg from "../../../assets/images/DateRangeCalendar/backArrow.svg";
import ForwardArrowSvg from "../../../assets/images/DateRangeCalendar/forwardArrow.svg";
import { differenceInDays, differenceInMonths } from "date-fns";

const HeaderContainer = styled.div`
  position: absolute;
  top: 9px;
  left: ${({ isFromMonth }) => (isFromMonth ? "11px" : "auto")};
  right: ${({ isFromMonth }) => (isFromMonth ? "" : "7px")};
  z-index: 1;
`;

const IconContainer = styled.div`
  background-repeat: no-repeat;
  border: 0;
  width: 20px;
  height: 25px;
  background-size: unset;
  outline: none;
  display: ${(props) => (props.isDisplay === false ? "none" : "")};
`;

const TitleButton = styled.div`
  text-align: center;
  font-weight: 700;
  font-family: ${({ theme }) => theme?.typography?.fontFamily || ""};
  font-size: ${({ theme }) => theme?.typography?.text?.fontSize || ""};
  color: #4d4d4f;
  padding: 0.6em 0;
  line-height: 1.43;
  letter-spacing: 0.3px;
`;

const Header = (props) => {
  const {
    selectedDate,
    next,
    prev,
    arrowImages,
    isFromMonth,
    isToMonth,
    startDate,
    endDate,
    minDate,
    maxDate,
    newSelectedDate,
    noofMonthView,
  } = props;
  const formattedStartDate = formatWithLocale(startDate, "MMMM yyyy");
  const formattedEndDate = formatWithLocale(endDate, "MMMM yyyy");
  const formattedSelectedDate = formatWithLocale(selectedDate, "MMMM yyyy");
  const monthOffset = differenceInMonths(maxDate, selectedDate);
  console.log("formattedStartDate", selectedDate,startDate,endDate);
  return (
    <>
      <HeaderContainer
        className={"vms_DateRangeCalendar_HeaderContainer"}
        isFromMonth={isFromMonth}
      >
        {isFromMonth && formattedSelectedDate !== formattedStartDate  && (
          <IconContainer
            className={"vms_DateRangeCalendar_PrevIconContainer"}
            isDisplay={true}
            onClick={prev}
          >
            <img
              src={arrowImages?.prevArrowImage || BackArrowSvg}
              alt={
                arrowImages?.prevArrowImageAlt
                  ? arrowImages?.prevArrowImageAlt
                  : "prev"
              }
            />
          </IconContainer>
        )}
      </HeaderContainer>
      <HeaderContainer
        className={"vms_DateRangeCalendar_HeaderContainer"}
        isFromMonth={false}
      >
        {isToMonth &&
        formattedEndDate !== formattedSelectedDate && (
            <IconContainer
              className={"vms_DateRangeCalendar_NextIconContainer"}
              isDisplay={true}
              onClick={next}
            >
              <img
                src={arrowImages?.nextArrowImage || ForwardArrowSvg}
                alt={
                  arrowImages?.nextArrowImageAlt
                    ? arrowImages?.nextArrowImageAlt
                    : "next"
                }
              />
            </IconContainer>
          )}
      </HeaderContainer>
    </>
  );
};

Header.defaultProps = {
  selectedDate: new Date(),
  prev: () => {},
  next: () => {},
  prevYear: () => {},
  nextYear: () => {},
  changeView: () => {},
  showSubView: () => {},
  fromMonth: new Date(),
  formatMonthYear: "MMMM yyyy",
  toMonth: addMonth(new Date(), 1),
};

Header.propTypes = {
  selectedDate: PropTypes.instanceOf(Date),
  view: PropTypes.string.isRequired,
  prev: PropTypes.func,
  next: PropTypes.func,
  formatMonthYear: PropTypes.string,
  changeView: PropTypes.func,
  fromMonth: PropTypes.instanceOf(Date),
  toMonth: PropTypes.instanceOf(Date),
};

export { Header };
