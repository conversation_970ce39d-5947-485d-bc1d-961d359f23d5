import React from "react";
import styled from "styled-components";
import { formatWithLocale, getAllMonths } from "../../../hooks/calendarHooks";
import classNames from "classnames";

const HeaderContainer = styled.div`
  height: 35px;
  margin-bottom: 10px;
  border-bottom: 1px solid #ddd;
  box-shadow: 0 1px 0 0 #ddd;
  font-size: 12px;
  letter-spacing: normal;
  text-align: center;
  color: #939598;
`;

const NavButton = styled.div`
  display: flex;
  align-items: center;
`;

const NavMonth = styled.div`
  flex: 1 1;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  height: 35px;
  border-bottom: 4px solid transparent;
  outline: none;
  &.active {
    border-bottom: ${(props) => (props.color ? `4.8px solid ${props.color}` : "4.8px solid #f26b6a")};
    color: #4d4d4f;
    outline: none;
  }
  &:hover {
    background-color: #f1f6de;
    outline: none;
  }
`;
const MonthHeader = (props) => {
  var d1 = props.from;
  var d2 = props.to;
  let selectedfromMonth = formatWithLocale(props.fromMonth, "MMM");
  let selectedToMonth = formatWithLocale(props.toMonth, "MMM");
  let selectedfromYear = formatWithLocale(props.fromMonth, "yyyy");
  let selectedToYear = formatWithLocale(props.toMonth, "yyyy");

  const getAllMonthsName = getAllMonths(d1, d2);
  const ActiveMonthName = getAllMonths(props.fromMonth, props.toMonth);
  // getAllMonthsName.map((item, i) => {
    getAllMonthsName.forEach((item, i) => {
    const checkActiveMonth = ActiveMonthName.some(
      (e) => e.monthName === item.monthName && e.year === item.year
    );
    if (checkActiveMonth) {
      item.active = true;
    } else {
      item.active = false;
    }
  });
  return (
    <HeaderContainer className={`vms_DateRangeCalendar_HeaderContainer`}>
      <NavButton className={`vms_DateRangeCalendar_HeaderNavButton`}>
        {getAllMonthsName &&
          getAllMonthsName.length > 0 &&
          getAllMonthsName.map((item, i) => {
            return (
              <NavMonth
                tabindex="-1"
                role="button"
                className={`${classNames({
                  active: item.active,
                })} vms_DateRangeCalendar_HeaderNavMonth`}
                key={i}
                onClick={() =>
                  item.active
                    ? ""
                    : props.onClick(i, i === getAllMonthsName.length - 1)
                }
                color={props.indicatorColor}
              >
                {item.monthName}
              </NavMonth>
            );
          })}
      </NavButton>
    </HeaderContainer>
  );
};

export { MonthHeader };
