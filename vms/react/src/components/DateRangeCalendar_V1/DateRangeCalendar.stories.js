import React, { useState } from "react";
import { addDay, getNextYear } from "../../hooks/calendarHooks";

import { DateRangeCalendar_V1 } from "./DateRangeCalendar";

export default {
  title: "VMS_REACT/DateRangeCalendar_V1",
  component: DateRangeCalendar_V1,
  argTypes: {
    id: { control: { type: "" } },
    additionalStyle: { control: { type: "" } },
    additionalClassName: { control: { type: "" } },
    dateDisplayFormat: { control: { type: "" } },
    startDate: { control: { type: "" } },
    endDate: { control: { type: "" } },
    customInputView: { control: { type: "" } },
    arrowImages: { control: { type: "" } },
    minDate: { control: { type: "" } },
    maxDate: { control: { type: "" } },
  },
};


const today = new Date();
let tomorrow = new Date();
tomorrow.setDate(new Date().getDate() + 1);

const DisplayCurrentState = (fromDate, toDate, result, mode) => {
  const isValid = result?.calendarState
    ? result?.calendarState?.valid.toString()
    : result?.valid.toString();
  return (
    <div style={{ marginTop: "10px" }}>
      <h5>Callback Value</h5>
      <ul style={{ paddingLeft: "15px", paddingTop: "1px" }}>
        <li>Start Date : {fromDate.toString()}</li>
        <li>End Date :{toDate.toString()}</li>
        <li>Valid :{isValid}</li>
        {isValid === "false" && (
          <>
            <li>
              ErrorCode :
              {result?.calendarState
                ? result?.calendarState?.errorCode
                : result?.errorCode}
            </li>
          </>
        )}
      </ul>
    </div>
  );
};

const Template = (args) => {
  const [startDate, setStartDate] = useState( new Date());
  const [endDate, setEndDate] = useState(addDay(new Date(), 1));
  const [result, setResult] = useState();
  return (
    <>
      <div>
        <DateRangeCalendar_V1
          startDate={startDate}
          endDate={endDate}
          onDateSelectionChange={(resul) => {
            console.log("resul",resul)
            setStartDate(resul?.startDate);
            setEndDate(resul?.endDate);
            setResult(resul);
          }}
          onSingleDateRangeSelectionChange={(resul) => {
            console.log("resul from onSingleDateRangeSelectionChange",resul)
            setStartDate(resul.calendarState.startDate);
            setEndDate(resul.calendarState.endDate);
            setResult(resul);
          }}
          {...args}
        />
      </div>
      <>{result ? DisplayCurrentState(startDate, endDate, result) : ""} </>
    </>
  );
};

const SelectedRangeApplication = (args) => {
  const [startDate, setStartDate] = useState(addDay(new Date(), 50));
  const [endDate, setEndDate] = useState(addDay(new Date(), 60));
  const [result, setResult] = useState();
  return (
    <>
      <div>
        <DateRangeCalendar_V1
          startDate={startDate}
          endDate={endDate}
          onDateSelectionChange={(resul) => {
            console.log("resul",resul)
            setStartDate(resul?.startDate);
            setEndDate(resul?.endDate);
            setResult(resul);
          }}
          onSingleDateRangeSelectionChange={(resul) => {
            console.log("resul from onSingleDateRangeSelectionChange",resul)
            setStartDate(resul.calendarState.startDate);
            setEndDate(resul.calendarState.endDate);
            setResult(resul);
          }}
          {...args}
        />
      </div>
      <>{result ? DisplayCurrentState(startDate, endDate, result) : ""} </>
    </>
  );
};

const customInputView = (args) => {
  const [startDate, setStartDate] = useState(today);
  const [endDate, setEndDate] = useState(tomorrow);
  const [result, setResult] = useState();
  return (
    <>
      <div style={{ display: "flex", flex: 1 }}>
        <DateRangeCalendar_V1
        id={"custom-start-id"}
          inputBoxLabel="Start Date"
          mode="singleDateRange"
          currentSelection="startDate"
          startDate={startDate}
          endDate={endDate}
          onSingleDateRangeSelectionChange={(resul) => {
            console.log("resul",resul)
            setStartDate(resul.calendarState.startDate);
            setEndDate(resul.calendarState.endDate);
            setResult(resul);
          }}
          // {...args}
        />

        <div style={{ marginLeft: "100px" }}>
          <DateRangeCalendar_V1
          id={"custom-end-id"}
            inputBoxLabel="End Date"
            mode="singleDateRange"
            currentSelection="endDate"
            startDate={startDate}
            endDate={endDate}
            onSingleDateRangeSelectionChange={(resul) => {
              console.log("resul",resul);
              setStartDate(resul.calendarState.startDate);
              setEndDate(resul.calendarState.endDate);
              setResult(resul);
            }}
            // {...args}
          />
        </div>
      </div>
      <>{result ? DisplayCurrentState(startDate, endDate, result) : ""} </>
    </>
  );
};

// const CustomInput = () => {
//   return (
//     <div>
//       <div>Start Date</div>
//       <input placeholder={"Select Date"} id={id} />
//     </div>
//   );
// };

export const Default = Template.bind({});
Default.args = {
  isOverlay: false,
  // startDate: new Date(),
  // endDate: addDay(new Date(), 1),
  noofMonthView: 2,
  openCalendar: false,
  inputBoxLabel: "",
  inputPlaceholder: "",
  id:"dr-id"
};

export const oneMonthView = Template.bind({});
oneMonthView.storyName = "1 Month view";

oneMonthView.args = {
  isOverlay: false,
  noofMonthView: 1,
  openCalendar: false,
  inputBoxLabel: "",
  inputPlaceholder: "",
  id:"1-id"
};

export const threeMonthView = Template.bind({});
threeMonthView.storyName = "3 Month view";

threeMonthView.args = {
  isOverlay: false,
  // startDate: new Date(),
  // endDate: addDay(new Date(), 1),
  noofMonthView: 3,
  openCalendar: false,
  inputBoxLabel: "",
  inputPlaceholder: "",
  id:"3-id"
};

export const WithOverlay = Template.bind({});
WithOverlay.storyName = "Wrap with Overlay";
WithOverlay.args = {
  isOverlay: true,
  startDate: new Date(),
  endDate: addDay(new Date(), 1),
  openCalendar: false,
  inputBoxLabel: "",
  inputPlaceholder: "",
  id:"overlay-id"
};

export const MutipleCalendar = customInputView.bind({});
MutipleCalendar.storyName = "Multiple Calendar";
MutipleCalendar.args = {
  isOverlay: false,
  openCalendar: false,
};

export const CustomDateFormat = Template.bind({});
CustomDateFormat.storyName = "Custom Date format";
CustomDateFormat.args = {
  startDate: new Date(),
  endDate: addDay(new Date(), 1),
  dateDisplayFormat: "MMM dd yyyy",
  id:"cust-id"
};

export const customArrowImages = Template.bind({});
customArrowImages.args = {
  startDate: new Date(),
  endDate: addDay(new Date(), 1),
  arrowImages: {
    prevArrowImage: "https://img.icons8.com/pastel-glyph/24/000000/minus.png",
    nextArrowImage: "https://img.icons8.com/pastel-glyph/24/000000/plus.png",
    prevArrowImageAlt: "prev",
    nextArrowImageAlt: "next",
  },
  id:"cust-arrow-id"
};

export const openCalendarByApplication = Template.bind({});
openCalendarByApplication.args = {
  openCalendar: true,
};

export const selectedRangePassByApplication = SelectedRangeApplication.bind({});
selectedRangePassByApplication.args = {
  openCalendar: false,
  id:"select-id"
};

export const minAndMaxBookingDays = Template.bind({});
minAndMaxBookingDays.args = {
  minBookingDays: 3,
  maxBookingDays: 90,
  openCalendar: false,
  id:"minmax-id"
};
