import React, { useState } from "react";
import { BottomBar } from "./BottomBar";
const IMAGE_1 = "https://img.icons8.com/material/24/939598/share.png";
const IMAGE_2 = "https://img.icons8.com/material/24/939598/download.png";
const IMAGE_3 = "https://img.icons8.com/material/24/939598/edit.png";
const IMAGE_4 = "https://img.icons8.com/material/24/939598/star.png";
const IMAGE_5 =
  "https://img.icons8.com/windows/24/939598/filled-plus-2-math.png";
const IMAGE_1_SELECTED = "https://img.icons8.com/material/24/f26b6a/share.png";
const IMAGE_2_SELECTED =
  "https://img.icons8.com/material/24/f26b6a/download.png";
const IMAGE_3_SELECTED = "https://img.icons8.com/material/24/f26b6a/edit.png";
const IMAGE_4_SELECTED = "https://img.icons8.com/material/24/f26b6a/star.png";
const IMAGE_5_SELECTED =
  "https://img.icons8.com/windows/24/f26b6a/filled-plus-2-math.png";
const IMAGE_1_ROW = "https://img.icons8.com/material/24/ebebec/share.png";
const IMAGE_2_ROW = "https://img.icons8.com/material/24/ebebec/download.png";
const IMAGE_3_ROW = "https://img.icons8.com/material/24/ebebec/edit.png";

export default {
  title: "VMS_REACT/BottomBar",
  component: BottomBar,
};

const DEFAULT_DATA_FIVE = [
  { title: "Share", image: IMAGE_1, selectedImage: IMAGE_1_SELECTED },
  { title: "Download", image: IMAGE_2, selectedImage: IMAGE_2_SELECTED },
  { title: "Modify", image: IMAGE_3, selectedImage: IMAGE_3_SELECTED },
  { title: "Claim Reward", image: IMAGE_4, selectedImage: IMAGE_4_SELECTED },
  { title: "Add", image: IMAGE_5, selectedImage: IMAGE_5_SELECTED },
];

const DEFAULT_DATA_FOUR = [
  { title: "Share", image: IMAGE_1, selectedImage: IMAGE_1_SELECTED },
  { title: "Download", image: IMAGE_2, selectedImage: IMAGE_2_SELECTED },
  { title: "Modify", image: IMAGE_3, selectedImage: IMAGE_3_SELECTED },
  { title: "Claim Reward", image: IMAGE_4, selectedImage: IMAGE_4_SELECTED },
];

const DEFAULT_DATA_THREE = [
  { title: "Share", image: IMAGE_1, selectedImage: IMAGE_1_SELECTED },
  { title: "Download", image: IMAGE_2, selectedImage: IMAGE_2_SELECTED },
  { title: "Modify", image: IMAGE_3, selectedImage: IMAGE_3_SELECTED },
];

const DEFAULT_DATA_THREE_ROW = [
  { title: "Share", image: IMAGE_1_ROW, selectedImage: IMAGE_1_SELECTED },
  { title: "Download", image: IMAGE_2_ROW, selectedImage: IMAGE_2_SELECTED },
  { title: "Modify", image: IMAGE_3_ROW, selectedImage: IMAGE_3_SELECTED },
];

const DEFAULT_DATA_TWO = [
  { title: "Share", image: IMAGE_1_ROW, selectedImage: IMAGE_1_SELECTED },
  { title: "Download", image: IMAGE_2_ROW, selectedImage: IMAGE_2_SELECTED },
];

const DEFAULT_DATA_ONE = [
  { title: "Share", image: IMAGE_1_ROW, selectedImage: IMAGE_1_SELECTED },
];

export const Default = () => {
  const [selectedTab, setSelectedTab] = useState(0);

  return (
    <div className={`parent`} style={{ margin: 0, padding: 0 }}>
      <h5 style={{ margin: '0px' }}>Default</h5>
      <BottomBar
        data={DEFAULT_DATA_FIVE}
        index={selectedTab}
        onItemClick={(index) => {
          setSelectedTab(index);
        }}
      />
    </div>
  );
};

Default.story = {
  name: "Default",
  parameters: { viewport: { defaultViewport: "iphonex" } },
};

export const DefaultFour = () => {
  const [selectedTab, setSelectedTab] = useState(0);

  return (
    <div className={`parent`} style={{ margin: '0px', padding: '0px'}}>
      <h5 style={{ margin: '0px' }}>Default Four</h5>
      <BottomBar
        data={DEFAULT_DATA_FOUR}
        index={selectedTab}
        onItemClick={(index) => {
          setSelectedTab(index);
        }}
      />
    </div>
  );
};

DefaultFour.story = {
  name: "Default Four",
  parameters: { viewport: { defaultViewport: "iphonex" } },
};

export const DefaultThree = () => {
  const [selectedTab, setSelectedTab] = useState(0);

  return (
    <div className={`parent`} style={{ margin: '0px', padding: '0px'}}>
      <h5 style={{ margin: '0px' }}>Default Three</h5>
      <BottomBar
        data={DEFAULT_DATA_THREE}
        index={selectedTab}
        onItemClick={(index) => {
          setSelectedTab(index);
        }}
      />
    </div>
  );
};

DefaultThree.story = {
  name: "Default Three",
  parameters: { viewport: { defaultViewport: "iphonex" } },
};

export const DefaultTwo = () => {
  const [selectedTab, setSelectedTab] = useState(0);

  return (
    <div className={`parent`} style={{ margin: '0px', padding: '0px'}}>
      <h5 style={{ margin: '0px' }}>Default Two</h5>
      <BottomBar
        data={DEFAULT_DATA_TWO}
        index={selectedTab}
        onItemClick={(index) => {
          setSelectedTab(index);
        }}
      />
    </div>
  );
};

DefaultTwo.story = {
  name: "Default Two",
  parameters: { viewport: { defaultViewport: "iphonex" } },
};

export const DefaultOne = () => {
  const [selectedTab, setSelectedTab] = useState(-1);

  return (
    <div className={`parent`} style={{ margin: '0px', padding: '0px'}}>
      <h5 style={{ margin: '0px' }}>Default One</h5>
      <BottomBar
        data={DEFAULT_DATA_ONE}
        index={selectedTab}
        onItemClick={(index) => {
          if (selectedTab === -1) {
            setSelectedTab(index);
          } else {
            setSelectedTab(-1);
          }
        }}
      />
    </div>
  );
};

DefaultOne.story = {
  name: "Default One",
  parameters: { viewport: { defaultViewport: "iphonex" } },
};

export const CustomAlignment = () => {
  const [selectedTab, setSelectedTab] = useState(0);

  return (
    <div className={`parent`} style={{ margin: '0px', padding: '0px'}}>
      <h5 style={{ margin: '0px' }}>Custom Alignment</h5>
      <BottomBar
        alignment="horizontal"
        data={DEFAULT_DATA_THREE_ROW}
        index={selectedTab}
        onItemClick={(index) => {
          setSelectedTab(index);
        }}
      />
    </div>
  );
};

CustomAlignment.story = {
  name: "Custom Alignment",
  parameters: { viewport: { defaultViewport: "iphonex" } },
};

export const ClickEvent = () => {
  return (
    <div className={`parent`} style={{ margin: '0px', padding: '0px'}}>
      <h5 style={{ margin: '0px' }}>Click event</h5>
      <BottomBar
        alignment="horizontal"
        data={DEFAULT_DATA_THREE_ROW}
        onItemClick={(index) => {
          alert("Selected Index : " + index);
        }}
      />
    </div>
  );
};

ClickEvent.story = {
  name: "Click event",
  parameters: { viewport: { defaultViewport: "iphonex" } },
};

export const CustomStyle = () => {
  return (
    <div className={`parent`} style={{ margin: '0px', padding: '0px'}}>
      <h5 style={{ margin: '0px' }}>Custom Style</h5>
      <BottomBar
        additionalClassName={["custom-bottom-bar"]}
        data={DEFAULT_DATA_FOUR}
      />
    </div>
  );
};

CustomStyle.story = {
  name: "Custom Style",
  parameters: { viewport: { defaultViewport: "iphonex" } },
};
