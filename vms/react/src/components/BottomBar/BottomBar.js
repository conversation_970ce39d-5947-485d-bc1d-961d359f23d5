import React, { memo, useCallback, useMemo } from "react";
import PropTypes from "prop-types";
import { useClassName } from "../../hooks/useClassName";
import {
  BottomBarWrapper,
  BottomBarContainer,
  BottomBarItem,
  BottomBarItemContainer,
  BottomBarIcon,
  BottomItemText,
} from "./BottomBar.styled";
import { ThemeWrapper } from "../Theme/ThemeContext";

// Utility function (placed before main component)
const isNonNull = (prop) => prop !== null && prop !== undefined;

const BottomBar = memo((props) => {
  // 1. PROP DESTRUCTURING & VALIDATION
  const {
    additionalClassName,
    additionalStyle,
    data = [],
    index,
    onItemClick,
    alignment = "vertical",
    ...otherProps
  } = props;

  // 2. PERFORMANCE OPTIMIZATIONS
  const computedClassName = useMemo(() => 
    useClassName(props, additionalClassName), 
    [props, additionalClassName]
  );

  // 3. EVENT HANDLING with useCallback
  const handleItemClick = useCallback((itemIndex) => {
    if (isNonNull(onItemClick)) {
      onItemClick(itemIndex);
    }
  }, [onItemClick]);

  // 4. CONDITIONAL RENDERING (memoized)
  const bottomBarItems = useMemo(() => {
    if (!data || data.length === 0) {
      return null;
    }

    return data.map((ele, i) => (
      <BottomBarItem
        key={i}
        onClick={() => handleItemClick(i)}
        className="vms_bottombar_item"
        role="button"
        tabIndex={0}
        aria-pressed={index === i}
        onKeyDown={(event) => {
          if (event.key === 'Enter' || event.key === ' ') {
            event.preventDefault();
            handleItemClick(i);
          }
        }}
      >
        <BottomBarItemContainer
          alignment={alignment}
          className="vms_bottombar_item_container"
        >
          <BottomBarIcon
            style={{
              background: `url(${index === i ? ele.selectedImage : ele.image}) center center no-repeat`,
            }}
            className="vms_bottombar_icon"
            role="img"
            aria-label={`${ele.title} icon`}
          />
          <BottomItemText
            isSelected={index === i}
            className="vms_bottombar_text"
          >
            {ele.title}
          </BottomItemText>
        </BottomBarItemContainer>
      </BottomBarItem>
    ));
  }, [data, index, alignment, handleItemClick]);

  // 5. ERROR HANDLING
  React.useEffect(() => {
    if (!data || data.length === 0) {
      console.warn('BottomBar: data prop is required and should contain at least one item');
    }
    
    data?.forEach((item, idx) => {
      if (!item.title) {
        console.warn(`BottomBar: Item at index ${idx} is missing required title property`);
      }
      if (!item.image) {
        console.warn(`BottomBar: Item at index ${idx} is missing required image property`);
      }
    });

    if (!isNonNull(onItemClick)) {
      console.warn('BottomBar: onItemClick callback is recommended for handling navigation');
    }
  }, [data, onItemClick]);

  return (
    <ThemeWrapper isMobile={true}>
      <BottomBarContainer 
        className={`${computedClassName} vms_bottombar_container`} 
        style={additionalStyle}
        role="navigation"
        aria-label="Bottom navigation"
        {...otherProps}
      >
        {bottomBarItems}
      </BottomBarContainer>
    </ThemeWrapper>
  );
});

// Component display name for debugging
BottomBar.displayName = 'BottomBar';

BottomBar.defaultProps = {
  additionalClassName: null,
  additionalStyle: null,
  alignment: "vertical",
};

BottomBar.propTypes = {
  /**
   * Array of Data
   */
  data: PropTypes.arrayOf(PropTypes.shape({
    selectedImage: PropTypes.string,
    image: PropTypes.string,
    title: PropTypes.string.isRequired,
  })).isRequired,
  /**
   * Property to append additional css class
   */
  additionalClassName: PropTypes.arrayOf(PropTypes.string),
  /**
   * Property to set the `inline style` object
   */
  additionalStyle: PropTypes.object,
  /**
   * function which will call on click
   */
  onItemClick: PropTypes.func,
  /**
   * Index of selected item
   */
  index: PropTypes.number,
  /**
   * Alignment of view horizontal or vertical
   */
  alignment: PropTypes.oneOf(["vertical", "horizontal"]),
};

export { BottomBar };
