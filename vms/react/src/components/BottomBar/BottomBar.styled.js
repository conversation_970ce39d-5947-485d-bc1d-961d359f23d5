import styled from "styled-components";

export const BottomBarWrapper = styled.div`
  width: 100%;
  margin: 0 auto;
`;

export const BottomBarContainer = styled.div`
  display: flex;
  width: 100%;
  height: 56px;
`;

export const BottomBarItem = styled.div`
  background-color: #000;
  width: 100%;
  cursor: pointer;
  text-align: center;
  justify-content: center;
  display: flex;
`;

export const BottomBarItemContainer = styled.div`
  flex-direction: ${({ alignment }) =>
    alignment == "horizontal" ? "row" : "column"};
  justify-content: center;
  display: flex;
`;

export const BottomBarIcon = styled.div`
  width: 50px;
  margin-bottom: 2px;
  width: 24px;
  height: 24px;
  justify-content: center;
  align-self: center;
`;

export const BottomItemText = styled.span`
  font-size: 10px;
  line-height: 16px;
  letter-spacing: 0.3px;
  text-align: center;
  font-weight: 400;
  font-family: ${({ theme }) => theme?.typography?.fontFamily || ""};
  justify-content: center;
  align-self: center;
 color: ${({ isSelected }) =>
    (isSelected  ? "#f26b6a" : "#939598")};
`;
