import * as React from "react";
import { TitleBandHeader } from "./TitleBandHeader";
import { IconButton } from "../IconButton/IconButton";
const nameStyle = {
  fontFamily: "Montserrat",
  fontSize: 14,
  fontWeight: "bold",
  lineHeight: 1.26,
  width: "100%",
};

const roomStyle = {
  fontFamily: "Montserrat",
  fontSize: 14,
  fontWeight: "normal",
  lineHeight: 1.29,
  paddingTop: 4,
  width: "100%",
};

const bookingDateStyle = {
  fontFamily: "Montserrat",
  fontSize: 12,
  fontWeight: "normal",
  lineHeight: 1.33,
  paddingTop: 4,
};

const priceStyle = {
  fontFamily: "Montserrat",
  fontSize: 19,
  letterSpacing: 0.12,
  fontWeight: "bold",
  lineHeight: "normal",
};

const totalCostStyle = {
  fontSize: 12,
  letterSpacing: -0.6,
  fontWeight: "normal",
  paddingLeft: 10,
};

export default {
  title: "VMS_REACT/Title Band Header",
  component: TitleBandHeader,
};

export const LeftButtonItemAndTitle = () => (
  <div style={{ padding: 0, margin: 0 }}>
    <div additionalContainerStyle={{ margin: 0, padding: 0 }}>
      <TitleBandHeader
        title="Screen Title"
        onLeftItemClick={() => {
          alert("Left item clicked");
        }}
      />
    </div>
  </div>
);

LeftButtonItemAndTitle.story = {
  name: "Default",
};

export const LeftButtonItemTitleAndSubTitle = () => (
  <div style={{ padding: 0 }}>
    <h4 style={{ margin: 0, padding: 0 }}>
      Left button item, Title and Sub-title
    </h4>
    <TitleBandHeader
      title="Title"
      subTitle="12 Feb - 13 Feb 2020"
      onLeftItemClick={() => {
        alert("Left item clicked");
      }}
    />
  </div>
);

LeftButtonItemTitleAndSubTitle.story = {
  name: "Default with subtitle",
};

export const LeftButtonItemTitleSubTitleAndRightItem = () => (
  <div style={{ padding: 0, margin: 0 }}>
    <h4 style={{ margin: 0, padding: 0 }}>
      Left button item, Title, Sub-title and Right item
    </h4>
    <TitleBandHeader
      title="Title"
      subTitle="Sub title"
      onLeftItemClick={() => {
        alert("Left item clicked");
      }}
      rightItem={
        <IconButton
          mode="other"
          imageSrc="https://img.icons8.com/ios/25/000000/cancel--v1.png"
          onClick={() => {
            alert("Right item clicked");
          }}
        />
      }
    />
  </div>
);

LeftButtonItemTitleSubTitleAndRightItem.story = {
  name: "With right item",
};

export const WithChildComponent = () => (
  <div style={{ padding: 0, margin: 0 }}>
    <h4
      style={{
        margin: 0,
        padding: 0,
      }}
    >
      With right item and child component
    </h4>
    <TitleBandHeader
      title="Title"
      subTitle="Sub title"
      leftItemStyle={{ padding: 0 }}
      onLeftItemClick={() => {
        alert("Left item clicked");
      }}
      rightItem={
        <IconButton
          mode="other"
          imageSrc="https://img.icons8.com/ios/25/000000/search--v1.png"
          onClick={() => {
            alert("Right item clicked");
          }}
        />
      }
    >
      <div style={{ padding: 23 }}>
        some titleWolf vinyl hella, jean shorts disrupt skateboard master
        cleanse hashtag iPhone. Pop-up bicycle rights Brooklyn ics.Wolf vinyl
        hella, jean shorts disrupt skateboard master cleanse hashtag iPhone.
        Pop-up bicycle rights Brooklyn ics. some titleWolf vinyl hella, jean
        shorts disrupt skateboard master cleanse hashtag iPhone. Pop-up bicycle
        rights Brooklyn ics.Wolf vinyl hella, jean shorts disrupt skateboard
        master cleanse hashtag iPhone. Pop-up bicycle rights Brooklyn ics. some
        titleWolf vinyl hella, jean shorts disrupt skateboard master cleanse
        hashtag iPhone. Pop-up bicycle rights Brooklyn ics.Wolf vinyl hella,
        jean shorts disrupt skateboard master cleanse hashtag iPhone. Pop-up
        bicycle rights Brooklyn ics..
      </div>
    </TitleBandHeader>
  </div>
);

WithChildComponent.story = {
  name: "With right item and child component",
};

export const WithExtraChildComponent = () => (
  <div style={{ padding: 0, margin: 0 }}>
    <h4 style={{ margin: 0, padding: 0 }}>Max title and subtitle length</h4>
    <TitleBandHeader
      title="some titleWolf vinyl hella, jean shorts disrupt skateboard master cleanse hashtag iPhone. Pop-up bicycle rights Brooklyn ics.Wolf vinyl hella, jean shorts disrupt skateboard master cleanse hashtag iPhone. Pop-up bicycle rights Brooklyn ics. some titleWolf vinyl hella, jean shorts disrupt skateboard master cleanse hashtag iPhone.Pop-up bicycle rights Brooklyn ics.Wolf vinyl hella, jean shorts disrupt skateboardmaster cleanse hashtag iPhone. Pop-up bicycle rights Brooklyn ics. some titleWolf vinyl hella, jean shorts disrupt skateboard master cleanse hashtag iPhone. Pop-up bicycle rights Brooklyn ics.Wolf vinyl hella, jean shorts disrupt skateboard master cleanse hashtag iPhone. Pop-up bicycle rights Brooklyn ics"
      subTitle="some titleWolf vinyl hella, jean shorts disrupt skateboard master cleanse hashtag iPhone. Pop-up bicycle rights Brooklyn ics.Wolf vinyl hella, jean shorts disrupt skateboard master cleanse hashtag iPhone. Pop-up bicycle rights Brooklyn ics. some titleWolf vinyl hella, jean shorts disrupt skateboard master cleanse hashtag iPhone.Pop-up bicycle rights Brooklyn ics.Wolf vinyl hella, jean shorts disrupt skateboardmaster cleanse hashtag iPhone. Pop-up bicycle rights Brooklyn ics. some titleWolf vinyl hella, jean shorts disrupt skateboard master cleanse hashtag iPhone. Pop-up bicycle rights Brooklyn ics.Wolf vinyl hella, jean shorts disrupt skateboard master cleanse hashtag iPhone. Pop-up bicycle rights Brooklyn ics"
      onLeftItemClick={() => {
        alert("Left item clicked");
      }}
      additionalClassName={["custom-title-band-header"]}
      rightItem={
        <IconButton
          mode="other"
          imageSrc="https://img.icons8.com/ios/25/000000/search--v1.png"
          onClick={() => {
            alert("Right item clicked");
          }}
        />
      }
    >
      <div style={{ padding: 23 }}>
        some titleWolf vinyl hella, jean shorts disrupt skateboard master
        cleanse hashtag iPhone. Pop-up bicycle rights Brooklyn ics.Wolf vinyl
        hella, jean shorts disrupt skateboard master cleanse hashtag iPhone.
        Pop-up bicycle rights Brooklyn ics. some titleWolf vinyl hella, jean
        shorts disrupt skateboard master cleanse hashtag iPhone. Pop-up bicycle
        rights Brooklyn ics.Wolf vinyl hella, jean shorts disrupt skateboard
        master cleanse hashtag iPhone. Pop-up bicycle rights Brooklyn ics. some
        titleWolf vinyl hella, jean shorts disrupt skateboard master cleanse
        hashtag iPhone. Pop-up bicycle rights Brooklyn ics.Wolf vinyl hella,
        jean shorts disrupt skateboard master cleanse hashtag iPhone. Pop-up
        bicycle rights Brooklyn ics..
      </div>
    </TitleBandHeader>
  </div>
);

WithExtraChildComponent.story = {
  name: "Max title and subtitle length",
};

export const WithCustomComponent = () => (
  <div style={{ padding: 0, margin: 0 }}>
    <h4 style={{ margin: 0, padding: 0 }}>With children components</h4>
    <TitleBandHeader
      title="Welcome to My Page"
      subTitle="12 Feb - 13 Feb 2020"
      onLeftItemClick={() => {
        alert("Left item clicked");
      }}
      rightItem={() => {
        return (
          <IconButton
            mode="other"
            imageSrc="https://img.icons8.com/ios/25/000000/search--v1.png"
            onClick={() => {
              alert("Right item clicked");
            }}
          />
        );
      }}
    >
      <div
        style={{
          padding: 23,
          display: "inline-block",
          width: "100%",
        }}
      >
        <div style={nameStyle}>The Westin garden city mumbai </div>
        <div style={roomStyle}>1 Classic room </div>
        <div style={bookingDateStyle}>10 Apr, 2019 - 11 Apr 2019 </div>
        <div className="BookingPriceMobile">
          <span style={priceStyle}>&#8377; 1499</span>
          <span style={totalCostStyle}>Total cost</span>
        </div>
      </div>
    </TitleBandHeader>
  </div>
);

WithCustomComponent.story = {
  name: "With children components",
};

export const WithCustomTitleComponent = () => (
  <div style={{ padding: 0, margin: 0 }}>
    <h4
      style={{
        margin: 0,
        padding: 0,
      }}
    >
      With Custom Title Component
    </h4>
    <TitleBandHeader
      title="Welcome to My Page"
      subTitle="12 Feb - 13 Feb 2020"
      onLeftItemClick={() => {
        alert("Left item clicked");
      }}
      rightItem={
        <IconButton
          mode="other"
          imageSrc="https://img.icons8.com/ios/25/000000/search--v1.png"
          onClick={() => {
            alert("Right item clicked");
          }}
        />
      }
      customTitleView={
        <div
          style={{
            display: "inline-block",
            width: "100%",
          }}
        >
          <div style={nameStyle}>The Westin garden city mumbai </div>
          <div style={roomStyle}>1 Classic room </div>
          <div style={bookingDateStyle}>10 Apr, 2019 - 11 Apr 2019 </div>
          <div className="BookingPriceMobile">
            <span style={priceStyle}>&#8377; 1499</span>
            <span style={totalCostStyle}>Total cost</span>
          </div>
        </div>
      }
    ></TitleBandHeader>
  </div>
);

WithCustomTitleComponent.story = {
  name: "With Custom Title component",
};
