import React, { memo, useCallback, useMemo } from "react";
import PropTypes from "prop-types";
import {
  BackImage,
  Container,
  LeftItem,
  RightItem,
  SubTitleText,
  TitleBandContainer,
  TitleBandMainContainer,
  TitleContainer,
  TitleText,
  RightIcon,
} from "./TitleBandHeader.styled";
import { IconButton } from "../IconButton/IconButton";
import { useClassName } from "../../hooks/useClassName";
import { ThemeWrapper } from "../Theme/ThemeContext";

// Utility function (placed before main component)
const isNonNull = (prop) => prop !== null && prop !== undefined;

const TitleBandHeader = memo((props) => {
  // 1. PROP DESTRUCTURING & VALIDATION
  const {
    mode = "backArrow",
    showLeftItem = true,
    title,
    subTitle,
    className,
    style,
    onLeftItemClick,
    rightItem,
    children,
    customTitleView,
    imageSrc,
    imageAlt = "backIcon",
    leftItemImagePath,
    leftItemStyle,
    leftItemClassName,
    titleItemClassName,
    titleStyle,
    subTitleItemClassName,
    subTitleStyle,
    rightItemClassName,
    rightItemStyle,
    additionalClassName,
    additionalStyle,
    isMobile,
    isMobileView,
    ...otherProps
  } = props;

  // 2. PERFORMANCE OPTIMIZATIONS
  const computedLeftItemClassName = useMemo(() => 
    useClassName(props, leftItemClassName), 
    [props, leftItemClassName]
  );

  const computedRightItemClassName = useMemo(() => 
    useClassName(props, rightItemClassName), 
    [props, rightItemClassName]
  );

  const computedContainerClassName = useMemo(() => 
    useClassName(props, additionalClassName), 
    [props, additionalClassName]
  );

  const computedTitleClassName = useMemo(() => 
    useClassName(props, titleItemClassName), 
    [props, titleItemClassName]
  );

  const computedSubTitleClassName = useMemo(() => 
    useClassName(props, subTitleItemClassName), 
    [props, subTitleItemClassName]
  );

  const hasCustomTitleView = useMemo(() => 
    isNonNull(customTitleView), 
    [customTitleView]
  );

  const hasTitle = useMemo(() => isNonNull(title), [title]);
  const hasSubTitle = useMemo(() => isNonNull(subTitle), [subTitle]);
  const hasRightItem = useMemo(() => isNonNull(rightItem), [rightItem]);

  const iconButtonImageSrc = useMemo(() => {
    if (leftItemImagePath) {
      return mode === "other" ? imageSrc : leftItemImagePath;
    }
    return "";
  }, [leftItemImagePath, mode, imageSrc]);

  // 3. EVENT HANDLING with useCallback
  const handleLeftItemClick = useCallback(() => {
    if (isNonNull(onLeftItemClick)) {
      onLeftItemClick();
    }
  }, [onLeftItemClick]);

  const handleKeyDown = useCallback((event) => {
    if ((event.key === 'Enter' || event.key === ' ') && showLeftItem) {
      event.preventDefault();
      handleLeftItemClick();
    }
  }, [showLeftItem, handleLeftItemClick]);

  // 4. CONDITIONAL RENDERING (memoized)
  const leftItemElement = useMemo(() => {
    if (!showLeftItem) return null;

    return (
      <LeftItem
        style={leftItemStyle}
        className={`${computedLeftItemClassName} vms_titleband_left_icon`}
        onClick={handleLeftItemClick}
        onKeyDown={handleKeyDown}
        tabIndex={0}
        role="button"
        aria-label={imageAlt}
      >
        <IconButton
          mode={mode}
          imageSrc={iconButtonImageSrc}
          onClick={handleLeftItemClick}
          alt={imageAlt}
        />
      </LeftItem>
    );
  }, [
    showLeftItem,
    leftItemStyle,
    computedLeftItemClassName,
    handleLeftItemClick,
    handleKeyDown,
    imageAlt,
    mode,
    iconButtonImageSrc
  ]);

  const titleContent = useMemo(() => {
    if (hasCustomTitleView) {
      return customTitleView;
    }

    return (
      <>
        {hasTitle && (
          <TitleText 
            style={titleStyle} 
            className={`${computedTitleClassName} vms_titleband_titletext`}
          >
            {title}
          </TitleText>
        )}
        {hasSubTitle && (
          <SubTitleText
            style={subTitleStyle}
            className={`${computedSubTitleClassName} vms_titleband_subtitletext`}
          >
            {subTitle}
          </SubTitleText>
        )}
      </>
    );
  }, [
    hasCustomTitleView,
    customTitleView,
    hasTitle,
    title,
    titleStyle,
    computedTitleClassName,
    hasSubTitle,
    subTitle,
    subTitleStyle,
    computedSubTitleClassName
  ]);

  const rightItemElement = useMemo(() => {
    if (!hasRightItem) return null;

    return (
      <RightItem
        style={rightItemStyle}
        className={`${computedRightItemClassName} vms_titleband_right_item`}
      >
        <RightIcon>{rightItem}</RightIcon>
      </RightItem>
    );
  }, [hasRightItem, rightItem, rightItemStyle, computedRightItemClassName]);

  // 5. ERROR HANDLING
  React.useEffect(() => {
    if (showLeftItem && !isNonNull(onLeftItemClick)) {
      console.warn('TitleBandHeader: onLeftItemClick callback should be provided when showLeftItem is true');
    }
    if (!hasTitle && !hasSubTitle && !hasCustomTitleView) {
      console.warn('TitleBandHeader: Either title, subTitle, or customTitleView should be provided');
    }
  }, [showLeftItem, onLeftItemClick, hasTitle, hasSubTitle, hasCustomTitleView]);

  return (
    <ThemeWrapper isMobile={isMobile || isMobileView || false}>
      <Container 
        className={`${computedContainerClassName} vms_titleband_main`} 
        style={additionalStyle}
        {...otherProps}
      >
        <TitleBandMainContainer 
          className={`${className || ''} vms_titleband_mainContainer`} 
          style={style}
        >
          <TitleBandContainer 
            className="vms_titleband_container"
            role="banner"
          >
            {leftItemElement}

            <TitleContainer className="vms_titleband_titlecontainer">
              {titleContent}
            </TitleContainer>

            {rightItemElement}
          </TitleBandContainer>
        </TitleBandMainContainer>
        {children}
      </Container>
    </ThemeWrapper>
  );
});

TitleBandHeader.displayName = 'TitleBandHeader';

TitleBandHeader.defaultProps = {
  showLeftItem: true,
  mode: "backArrow",
};

TitleBandHeader.propTypes = {
  /**
   *prop to set left image using path or url
   */
  showLeftItem: PropTypes.bool,

  /**
   *prop to set left image using path or url
   */
  leftItemImagePath: PropTypes.string,

  /**
   *prop to set click event to left item
   */
  onLeftItemClick: PropTypes.func,

  /**
   *prop to set image style to left item
   */
  leftItemStyle: PropTypes.object,

  /**
   *prop to set additional classes to left container
   */
  leftItemClassName: PropTypes.arrayOf(PropTypes.string),

  /**
   *prop to set title
if custom title view is provided then this will be ignored.
   */
  title: PropTypes.string,

  /**
   *Class applied to title container
   */
  titleItemClassName: PropTypes.arrayOf(PropTypes.string),
  /**
   *prop to set title style
   */
  titleStyle: PropTypes.object,

  /**
   *	
prop to set sub-title
if custom title view is provided then this will be ignored.
   */

  subTitle: PropTypes.string,

  /**
   *Class applied to sub title container
   */
  subTitleItemClassName: PropTypes.arrayOf(PropTypes.string),
  /**
   *prop to set sub-title style
   */
  subTitleStyle: PropTypes.object,
  /**
   *prop to set custom right item
   */
  rightItem: PropTypes.node,

  /**
   *Class applied to child container
   */
  rightItemClassName: PropTypes.arrayOf(PropTypes.string),
  /**
   *prop to set Right Item style
   */
  rightItemStyle: PropTypes.object,

  /**
   *prop to set custom or child component
   */
  children: PropTypes.node,

  /**
   *Custom Title View, if custom title view is provided then title and subTitle will be ignored.
   */
  customTitleView: PropTypes.node,

  /**
   * Mode will be used to display default icons. If not provided backArrow will be considered as default. other option doesn't have any default image.
   */
  mode: PropTypes.oneOf(["other", "backArrow", "crossIcon"]),

  /**
   * Custom Image of Icon Button
   */
  imageSrc: PropTypes.string,

  /**
   * Alternate Text for image
   */
  imageAlt: PropTypes.string,

  /**
   * Property to append additional css class
   */
  additionalClassName: PropTypes.arrayOf(PropTypes.string),

  /**
   * Property to set the `inline style` object on button
   */
  additionalStyle: PropTypes.object,
};

export { TitleBandHeader };
