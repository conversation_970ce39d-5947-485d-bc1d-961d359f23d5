import styled from "styled-components";

export const Container = styled.div`
  display: block;
`;

export const TitleBandMainContainer = styled.div`
  background: #fff;
  color: #000;
  margin-top: 17px;
  margin-bottom: 19px;
`;

export const TitleBandContainer = styled.div`
  display: flex;
  align-items: center;
`;

export const LeftItem = styled.li`
  margin-left: 16px;
  list-style: none;
`;
export const RightItem = styled.li`
  display: flex;
  align-items: center;
  position: absolute;
  right: 37px;
  cursor: pointer;
`;

export const TitleContainer = styled.li`
  margin-left: 24px;
  max-width: 90%;
  color: #4d4d4f;
  font-weight: ${({ theme }) => theme?.typography?.text?.fontWeight || ""};
  font-family: ${({ theme }) => theme?.typography?.fontFamily || ""};
  list-style: none;
  margin-right: 50px;
`;

export const TitleText = styled.div`
  font-size: 14px;
  line-height: 20px;
  letter-spacing: 0.25px;
  & {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
`;

export const SubTitleText = styled.div`
  padding-top: 4px;
  font-size: 12px;
  line-height: 16px;
  letter-spacing: 0.3px;
  & {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
`;

export const BackImage = styled.img`
  background-repeat: no-repeat;
  width: 24px;
  height: 24px;
  background-size: 24px 24px !important;
`;

export const RightIcon = styled.a`
  display: inline-block;
  background-size: 20px 18px !important;
  background-repeat: no-repeat !important;
  background-position: center !important;
  width: 24px;
  height: 24px;
  text-decoration: none;
  outline: none;
`;
