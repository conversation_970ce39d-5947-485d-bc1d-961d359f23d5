import * as React from 'react';
import { ProgressTracker } from "./ProgressTracker";
import { Button } from "../Button/Button";
import stepIconActive from "../../assets/images/Breadcrumb/customSeperator.svg"
import stepIconInactive from "../../assets/images/Breadcrumb/seperator.svg"
import completedStepIcon from "../../assets/images/ToggleButton/ButtonOn.svg"

export default {
  title: "VMS_REACT/ProgressTracker",
  component: ProgressTracker,
  parameters: {
    controls: {
      exclude: /.*/g,
    },
  },
};

const content = (
  <div style={{ marginTop: 5, fontFamily: 'Montserrat', fontSize: '12px' }}>
    This is Current Active Step
  </div>
);

const CustomTitle = (props) => {
  return (
    <div style={{ wordBreak: 'break-all' }}>
      <div
        style={{
          color: 'Green',
          fontFamily: 'Montserrat',
          fontSize: '16px',
          fontWeight: 'bold',
          display: 'inline-block',
        }}
      >
        {props.title}
        <div
          style={{
            marginLeft: '5px',
            color: 'red',
            fontFamily: 'Montserrat',
            fontSize: '14px',
            fontWeight: 'normal',
            display: 'inline-block',
          }}
        >
          {props.subTitle}
        </div>
      </div>
      <div style={{ color: 'black', fontFamily: 'Montserrat', fontSize: '12px' }}>
        {props.content}
      </div>
    </div>
  );
};

const steps = [
  {
    title: 'Step 1',
    subTitle: '12/06/2020',
    content: content,
  },
  {
    title: 'Step 2',
    subTitle: '15/06/2020',
    content: content,
  },
  {
    title: 'Step 3',
    subTitle: '18/06/2020',
    content: content,
  },
  {
    title: 'Step 4',
    subTitle: '21/06/2020',
    content: content,
  },
  {
    title: 'Step 5',
    subTitle: '24/06/2020',
    content: content,
  },
];

const onlyTitlesSteps = [
  {
    title: 'Step 1',
  },
  {
    title: 'Step 2',
  },
  {
    title: 'Step 3',
  },
  {
    title: 'Step 4',
  },
  {
    title: 'Step 5',
  },
];

const withTitleAndSubTitleSteps = [
  {
    title: 'Step 1',
    subTitle: '12/06/2020',
    separator: '@',
    stepNumber:1
  },
  {
    title: 'Step 2',
    subTitle: '15/06/2020',
    separator: '!',
    stepNumber:2
   
  },
  {
    title: 'Step 3',
    subTitle: '18/06/2020',
    separator: '#',
    stepNumber:3
    
  },
  {
    title: 'Step 4',
    subTitle: '21/06/2020',
    separator: '$',
    stepNumber:4
    
  },
  {
    title: 'Step 5',
    subTitle: '24/06/2020',
    separator: '–',
  },
];

export const TwoSteps = () => {
  const [currentActiveStep, setActiveStep] = React.useState(0);
  return (
      <div>
        <h4>2 Steps</h4>
          <ProgressTracker
            steps={withTitleAndSubTitleSteps.slice(0, 2)}
            activeStep={currentActiveStep}
          />
        <div style={{ textAlign: 'center' }}>
          <h3>Buttons In Application</h3>
          <Button
          id="2-btn-nxt"
            buttonType="tertiary"
            disabled={currentActiveStep > 0}
            onClick={() => setActiveStep(currentActiveStep + 1)}
            additionalClassName={["StepperButton"]}
          >
            {' '}
            Next{' '}
          </Button>
          <Button
          id="2-btn-prv"

            buttonType="tertiary"
            disabled={currentActiveStep < 0}
            onClick={() => setActiveStep(currentActiveStep - 1)}
            additionalClassName={["StepperButton"]}
          >
            {' '}
            Back{' '}
          </Button>
        </div>
      </div>
  );
};

TwoSteps.story = {
  name: 'Two Steps',
};

export const ThreeSteps = () => {
  const [currentActiveStep, setActiveStep] = React.useState(0);
  return (
      <div className={`parent`}>
        <h4>3 Steps</h4>
          <ProgressTracker
            steps={withTitleAndSubTitleSteps.slice(0, 3)}
            activeStep={currentActiveStep}
          />
        <div style={{ textAlign: 'center' }}>
          <h3>Buttons In Application</h3>
          <Button
          id="3-btn-nxt"

            buttonType="tertiary"
            disabled={currentActiveStep > 1}
            onClick={() => setActiveStep(currentActiveStep + 1)}
            additionalClassName={["StepperButton"]}
          >
            {' '}
            Next{' '}
          </Button>
          <Button
          id="3-btn-prv"

            buttonType="tertiary"
            disabled={currentActiveStep < 0}
            onClick={() => setActiveStep(currentActiveStep - 1)}
            additionalClassName={["StepperButton"]}
          >
            {' '}
            Back{' '}
          </Button>
        </div>
      </div>
  );
};

ThreeSteps.story = {
  name: 'Three Steps',
};

export const FourSteps = () => {
  const [currentActiveStep, setActiveStep] = React.useState(0);
  return (
      <div className={`parent`}>
        <h4>4 Steps</h4>
          <ProgressTracker
            steps={withTitleAndSubTitleSteps.slice(0, 4)}
            activeStep={currentActiveStep}
            completedStepIcon={completedStepIcon}
            // stepIconActive={stepIconActive}
            // stepIconInActive={stepIconInactive}
            onStepClick={(index) => setActiveStep(index)}
          />
        <div style={{ textAlign: 'center' }}>
          <h3>Buttons In Application</h3>
          <Button
          id="4-btn-nxt"

            buttonType="tertiary"
            disabled={currentActiveStep > 2}
            onClick={() => setActiveStep(currentActiveStep + 1)}
            additionalClassName={["StepperButton"]}
          >
            {' '}
            Next{' '}
          </Button>
          <Button
          id="4-btn-prv"

            buttonType="tertiary"
            disabled={currentActiveStep < 0}
            onClick={() => setActiveStep(currentActiveStep - 1)}
            additionalClassName={["StepperButton"]}
          >
            {' '}
            Back{' '}
          </Button>
        </div>
      </div>
  );
};

FourSteps.story = {
  name: 'Four Steps',
};

export const FiveSteps = () => {
  const [currentActiveStep, setActiveStep] = React.useState(0);
  return (
      <div className={`parent`}>
        <h5>5 Steps</h5>
          <ProgressTracker steps={withTitleAndSubTitleSteps} activeStep={currentActiveStep} />
        <div style={{ textAlign: 'center' }}>
          <h3>Buttons In Application</h3>
          <Button
          id="5-btn-nxt"

            buttonType="tertiary"
            disabled={currentActiveStep > 3}
            onClick={() => setActiveStep(currentActiveStep + 1)}
            additionalClassName={["StepperButton"]}
          >
            {' '}
            Next{' '}
          </Button>
          <Button
          id="5-btn-prv"

            buttonType="tertiary"
            disabled={currentActiveStep < 0}
            onClick={() => setActiveStep(currentActiveStep - 1)}
            additionalClassName={["StepperButton"]}
          >
            {' '}
            Back{' '}
          </Button>
        </div>
      </div>
  );
};

FiveSteps.story = {
  name: 'Five Steps',
};

export const WithOnlyTitles = () => {
  const [currentActiveStep, setActiveStep] = React.useState(0);
  return (
      <div className={`parent`}>
        <h5>3 Steps with only titles</h5> 
          <ProgressTracker steps={onlyTitlesSteps.slice(0, 3)} activeStep={currentActiveStep} />
        <div style={{ textAlign: 'center' }}>
          <h3>Buttons In Application</h3>
          <Button
            buttonType="tertiary"
            disabled={currentActiveStep > 1}
            onClick={() => setActiveStep(currentActiveStep + 1)}
            additionalClassName={["StepperButton"]}
          >
            {' '}
            Next{' '}
          </Button>
          <Button
            buttonType="tertiary"
            disabled={currentActiveStep < 0}
            onClick={() => setActiveStep(currentActiveStep - 1)}
            additionalClassName={["StepperButton"]}
          >
            {' '}
            Back{' '}
          </Button>
        </div>
      </div>
  );
};

WithOnlyTitles.story = {
  name: 'With Only Titles',
};

export const WithTitleSubTItleAndContent = () => {
  const [currentActiveStep, setActiveStep] = React.useState(0);
  return (
      <div className={`parent`}>
        <h4>3 Steps with titles, sub titles and content</h4> 
          <ProgressTracker steps={steps.slice(0, 3)} activeStep={currentActiveStep} />
        <div style={{ textAlign: 'center' }}>
          <h3>Buttons In Application</h3>
          <Button
            buttonType="tertiary"
            disabled={currentActiveStep > 1}
            onClick={() => setActiveStep(currentActiveStep + 1)}
            additionalClassName={["StepperButton"]}
          >
            {' '}
            Next{' '}
          </Button>
          <Button
            buttonType="tertiary"
            disabled={currentActiveStep < 0}
            onClick={() => setActiveStep(currentActiveStep - 1)}
            additionalClassName={["StepperButton"]}
          >
            {' '}
            Back{' '}
          </Button>
        </div>
      </div>
  );
};

WithTitleSubTItleAndContent.story = {
  name: 'With Title, Sub Title and Content',
};

export const CustomTitleView = () => {
  const [currentActiveStep, setActiveStep] = React.useState(0);
  return (
      <div className={`parent`}>
        <h5>3 Steps with Custom Title View</h5>
          <ProgressTracker
            steps={[
              {
                title: '',
                customTitle: (
                  <CustomTitle
                    title={'First Step'}
                    subTitle="07/07/2020"
                    content="First Step content"
                  />
                ),
              },
              {
                title: '',
                customTitle: (
                  <CustomTitle
                    title={'Second Step'}
                    subTitle="09/07/2020"
                    content="Second Step content"
                  />
                ),
              },
              {
                title: '',
                customTitle: (
                  <CustomTitle
                    title={'Third Step'}
                    subTitle="11/07/2020"
                    content="Third Step content"
                  />
                ),
              },
            ]}
            activeStep={currentActiveStep}
            onStepClick={(index) => console.log('onStepClick : ', index)}
          />
        <div style={{ textAlign: 'center' }}>
          <h3>Buttons In Application</h3>
          <Button
            buttonType="tertiary"
            disabled={currentActiveStep > 1}
            onClick={() => setActiveStep(currentActiveStep + 1)}
            additionalClassName={["StepperButton"]}
          >
            {' '}
            Next{' '}
          </Button>
          <Button
            buttonType="tertiary"
            disabled={currentActiveStep < 0}
            onClick={() => setActiveStep(currentActiveStep - 1)}
            additionalClassName={["StepperButton"]}
          >
            {' '}
            Back{' '}
          </Button>
        </div>
      </div>
  );
};

CustomTitleView.story = {
  name: 'Custom Title View',
};

export const StepClickCallback = () => {
  const [currentActiveStep, setActiveStep] = React.useState(0);
  return (
      <div className={`parent`}>
        <h5>3 Steps with Step Click Callback</h5> 
          <ProgressTracker
            steps={steps.slice(0, 3)}
            activeStep={currentActiveStep}
            onStepClick={(index) => setActiveStep(index)}
          />
      </div>
  );
};

StepClickCallback.story = {
  name: 'Step Click Callback',
};


export const MoWebTwoSteps = () => {
  const [currentActiveStep, setActiveStep] = React.useState(0);
  return (
      <div className={`parent`}>
        <h5>2 Steps" </h5>
          <ProgressTracker
            isMobile={true}
            steps={withTitleAndSubTitleSteps.slice(0, 2)}
            activeStep={currentActiveStep}
          />
        <div style={{ textAlign: 'center' }}>
          <h3>Buttons In Application</h3>
          <Button
            buttonType="tertiary"
            disabled={currentActiveStep > 0}
            onClick={() => setActiveStep(currentActiveStep + 1)}
            additionalClassName={["StepperButton"]}
          >
            {' '}
            Next{' '}
          </Button>
          <Button
            buttonType="tertiary"
            disabled={currentActiveStep < 0}
            onClick={() => setActiveStep(currentActiveStep - 1)}
            additionalClassName={["StepperButton"]}
          >
            {' '}
            Back{' '}
          </Button>
        </div>
      </div>
  );
};

MoWebTwoSteps.story = {
  name: 'MoWeb Two Steps',
  parameters: { viewport: { defaultViewport: 'iphonex' } },
};

export const MoWebThreeSteps = () => {
  const [currentActiveStep, setActiveStep] = React.useState(0);
  return (
      <div className={`parent`}>
        <h4>3 Steps</h4>
          <ProgressTracker
            isMobile={true}
            steps={withTitleAndSubTitleSteps.slice(0, 3)}
            activeStep={currentActiveStep}
          />
        <div style={{ textAlign: 'center' }}>
          <h3>Buttons In Application</h3>
          <Button
            buttonType="tertiary"
            disabled={currentActiveStep > 1}
            onClick={() => setActiveStep(currentActiveStep + 1)}
            additionalClassName={["StepperButton"]}
          >
            {' '}
            Next{' '}
          </Button>
          <Button
            buttonType="tertiary"
            disabled={currentActiveStep < 0}
            onClick={() => setActiveStep(currentActiveStep - 1)}
            additionalClassName={["StepperButton"]}
          >
            {' '}
            Back{' '}
          </Button>
        </div>
      </div>
  );
};

MoWebThreeSteps.story = {
  name: 'MoWeb Three Steps',
  parameters: { viewport: { defaultViewport: 'iphonex' } },
};

export const MoWebFourSteps = () => {
  const [currentActiveStep, setActiveStep] = React.useState(0);
  return (
      <div className={`parent`}>
        <h5>4 Steps</h5> 
          <ProgressTracker
            isMobile={true}
            steps={withTitleAndSubTitleSteps.slice(0, 4)}
            activeStep={currentActiveStep}
          />
        <div style={{ textAlign: 'center' }}>
          <h3>Buttons In Application</h3>
          <Button
            buttonType="tertiary"
            disabled={currentActiveStep > 2}
            onClick={() => setActiveStep(currentActiveStep + 1)}
            additionalClassName={["StepperButton"]}
          >
            {' '}
            Next{' '}
          </Button>
          <Button
            buttonType="tertiary"
            disabled={currentActiveStep < 0}
            onClick={() => setActiveStep(currentActiveStep - 1)}
            additionalClassName={["StepperButton"]}
          >
            {' '}
            Back{' '}
          </Button>
        </div>
      </div>
  );
};

MoWebFourSteps.story = {
  name: 'MoWeb Four Steps',
  parameters: { viewport: { defaultViewport: 'iphonex' } },
};

export const MoWebFiveSteps = () => {
  const [currentActiveStep, setActiveStep] = React.useState(0);
  return (
      <div className={`parent`}>
        <h5>5 Steps</h5>
          <ProgressTracker
            isMobile={true}
            steps={withTitleAndSubTitleSteps}
            activeStep={currentActiveStep}
          />
        <div style={{ textAlign: 'center' }}>
          <h3>Buttons In Application</h3>
          <Button
            buttonType="tertiary"
            disabled={currentActiveStep > 3}
            onClick={() => setActiveStep(currentActiveStep + 1)}
            additionalClassName={["StepperButton"]}
          >
            {' '}
            Next{' '}
          </Button>
          <Button
            buttonType="tertiary"
            disabled={currentActiveStep < 0}
            onClick={() => setActiveStep(currentActiveStep - 1)}
            additionalClassName={["StepperButton"]}
          >
            {' '}
            Back{' '}
          </Button>
        </div>
      </div>
  );
};

MoWebFiveSteps.story = {
  name: 'MoWeb Five Steps',
  parameters: { viewport: { defaultViewport: 'iphonex' } },
};

export const MoWebWithOnlyTitles = () => {
  const [currentActiveStep, setActiveStep] = React.useState(0);
  return (
      <div className={`parent`}>
        <h5>3 Steps with only titles</h5>
          <ProgressTracker
            isMobile={true}
            steps={onlyTitlesSteps.slice(0, 3)}
            activeStep={currentActiveStep}
          />
        <div style={{ textAlign: 'center' }}>
          <h3>Buttons In Application</h3>
          <Button
            buttonType="tertiary"
            disabled={currentActiveStep > 1}
            onClick={() => setActiveStep(currentActiveStep + 1)}
            additionalClassName={["StepperButton"]}
          >
            {' '}
            Next{' '}
          </Button>
          <Button
            buttonType="tertiary"
            disabled={currentActiveStep < 0}
            onClick={() => setActiveStep(currentActiveStep - 1)}
            additionalClassName={["StepperButton"]}
          >
            {' '}
            Back{' '}
          </Button>
        </div>
      </div>
  );
};

MoWebWithOnlyTitles.story = {
  name: 'MoWeb With Only Titles',
  parameters: { viewport: { defaultViewport: 'iphonex' } },
};

export const MoWebWithTitleSubTItleAndContent = () => {
  const [currentActiveStep, setActiveStep] = React.useState(0);
  return (
      <div className={`parent`}>
        <h5>3 Steps with titles, sub titles and content</h5>
          <ProgressTracker
            isMobile={true}
            steps={steps.slice(0, 3)}
            activeStep={currentActiveStep}
          />
        <div style={{ textAlign: 'center' }}>
          <h3>Buttons In Application</h3>
          <Button
            buttonType="tertiary"
            disabled={currentActiveStep > 1}
            onClick={() => setActiveStep(currentActiveStep + 1)}
            additionalClassName={["StepperButton"]}
          >
            {' '}
            Next{' '}
          </Button>
          <Button
            buttonType="tertiary"
            disabled={currentActiveStep < 0}
            onClick={() => setActiveStep(currentActiveStep - 1)}
            additionalClassName={["StepperButton"]}
          >
            {' '}
            Back{' '}
          </Button>
        </div>
      </div>
  );
};

MoWebWithTitleSubTItleAndContent.story = {
  name: 'MoWeb With Title, Sub Title and Content',
  parameters: { viewport: { defaultViewport: 'iphonex' } },
};

export const MoWebCustomTitleView = () => {
  const [currentActiveStep, setActiveStep] = React.useState(0);
  return (
      <div className={`parent`}>
        <h5>3 Steps with Custom Title View</h5>
          <ProgressTracker
            isMobile={true}
            steps={[
              {
                title: '',
                customTitle: (
                  <CustomTitle
                    title={'First Step'}
                    subTitle="07/07/2020"
                    content="First Step content"
                  />
                ),
              },
              {
                title: '',
                customTitle: (
                  <CustomTitle
                    title={'Second Step'}
                    subTitle="09/07/2020"
                    content="Second Step content"
                  />
                ),
              },
              {
                title: '',
                customTitle: (
                  <CustomTitle
                    title={'Third Step'}
                    subTitle="11/07/2020"
                    content="Third Step content"
                  />
                ),
              },
            ]}
            activeStep={currentActiveStep}
            onStepClick={(index) => console.log('onStepClick : ', index)}
          />
        <div style={{ textAlign: 'center' }}>
          <h3>Buttons In Application</h3>
          <Button
            buttonType="tertiary"
            disabled={currentActiveStep > 1}
            onClick={() => setActiveStep(currentActiveStep + 1)}
            additionalClassName={["StepperButton"]}
          >
            {' '}
            Next{' '}
          </Button>
          <Button
            buttonType="tertiary"
            disabled={currentActiveStep < 0}
            onClick={() => setActiveStep(currentActiveStep - 1)}
            additionalClassName={["StepperButton"]}
          >
            {' '}
            Back{' '}
          </Button>
        </div>
      </div>
  );
};

MoWebCustomTitleView.story = {
  name: 'MoWeb Custom Title View',
  parameters: { viewport: { defaultViewport: 'iphonex' } },
};

export const MoWebStepClickCallback = () => {
  const [currentActiveStep, setActiveStep] = React.useState(0);
  return (
      <div className={`parent`}>
        <h5>3 Steps with Step Click Callback</h5>
          <ProgressTracker
            isMobile={true}
            steps={steps.slice(0, 3)}
            activeStep={currentActiveStep}
            onStepClick={(index) => setActiveStep(index)}
          />
      </div>
  );
};

MoWebStepClickCallback.story = {
  name: 'MoWeb Step Click Callback',
  parameters: { viewport: { defaultViewport: 'iphonex' } },
};
