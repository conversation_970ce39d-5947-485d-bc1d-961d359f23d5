import React from "react";
import PropTypes from "prop-types";
import { ProgressTrackerDesktop } from "./Desktop/ProgressTrackerDesktop";
import { ProgressTrackerMobile } from "./Mobile/ProgressTrackerMobile";
import { ThemeWrapper } from "../Theme/ThemeContext";

const ProgressTracker = (props) => {
  const { steps, activeStep, activeStepColor, additionalStyle, isMobile, isMobileView, onStepClick, stepIconActive, stepIconInActive, completedStepIcon } = props;
  return (
    <ThemeWrapper isMobile={isMobile || isMobileView || false}>
      {isMobile ? (
        <ProgressTrackerMobile
          steps={steps}
          activeStep={activeStep}
          additionalStyle={additionalStyle}
          onStepClick={onStepClick}
          additionalClassName={props.additionalClassName}
          activeStepColor={activeStepColor}
          stepIconActive={stepIconActive}
          stepIconInActive={stepIconInActive}
          completedStepIcon={completedStepIcon}
        />
      ) : (
        <ProgressTrackerDesktop
          steps={steps}
          activeStep={activeStep}
          additionalStyle={additionalStyle}
          onStepClick={onStepClick}
          additionalClassName={props.additionalClassName}
          activeStepColor={activeStepColor}
          stepIconActive={stepIconActive}
          stepIconInActive={stepIconInActive}
          completedStepIcon={completedStepIcon}
        />
      )}
    </ThemeWrapper>
  );
};

ProgressTracker.defaultProps = {
  additionalStyle: null,
  additionalClassName: null,
  isMobile: false,
  activeStep: 1,
};

ProgressTracker.propTypes = {
  /**
   * Steps Array
   * 
   * { <br/>  
    title - Title String <br/> 
    subTitle - Sub Title String. It will be only displayed when the step in current active or completed <br/> 
    titleProps - Title String props <br/> 
    subTitleProps - Sub Title String props <br/> 
    customTitle - Custom title view. In case of this @property title, @property titleProps, @property subTitle, @property subTitleProps will be ignored <br/> 
    content - Content which will be displayed below sub title. It will be only displayed when the step in current active or completed <br/> 
    separator - incase of @property isMobile true then it will be displayed between title and sub title. default value – <br/> 
}
   */
  steps: PropTypes.arrayOf(
    PropTypes.shape({
      title: PropTypes.string,
      subTitle: PropTypes.string,
      titleProps: PropTypes.string,
      subTitleProps: PropTypes.string,
      customTitle: PropTypes.oneOfType([PropTypes.node, PropTypes.string]),
      content: PropTypes.oneOfType([PropTypes.node, PropTypes.string]),
      separator: PropTypes.string
    })
  ).isRequired,
  /**
   * Current Active step index. Note: index start from 0.Default value 1.
   */
  activeStep: PropTypes.number,

  /**
   * It will give you index of step when user have clicked on that step's dot
    (clickedIndex: number) => void
   */
  onStepClick: PropTypes.func,
  /**
   * Classes to add additional styling in the parent container
   */
  additionalClassName: PropTypes.arrayOf(PropTypes.string),
  /**
   * Inline styles to add additional styling in the parent container
   */
  additionalStyle: PropTypes.object,

  /**
   * To enable mobile view. Default value false.
   */
  isMobile: PropTypes.bool,
  /**
     * Add active step color
     */
  activeStepColor: PropTypes.string,
  /**
     * Add any type of icon to the step
     */
  stepIconActive: PropTypes.string,
  stepIconInActive: PropTypes.string,
};

export { ProgressTracker };
