import React, { memo, useMemo } from "react";
import PropTypes from "prop-types";
import { ProgressTrackerDesktop } from "./Desktop/ProgressTrackerDesktop";
import { ProgressTrackerMobile } from "./Mobile/ProgressTrackerMobile";
import { ThemeWrapper } from "../Theme/ThemeContext";

// Utility function (placed before main component)
const isNonNull = (prop) => prop !== null && prop !== undefined;

const ProgressTracker = memo((props) => {
  // 1. PROP DESTRUCTURING & VALIDATION
  const {
    steps = [],
    activeStep = 1,
    activeStepColor,
    additionalStyle,
    additionalClassName,
    isMobile,
    isMobileView,
    onStepClick,
    stepIconActive,
    stepIconInActive,
    completedStepIcon,
    ...otherProps
  } = props;

  // 2. PERFORMANCE OPTIMIZATIONS
  const isMobileDevice = useMemo(
    () => isMobile || isMobileView || false,
    [isMobile, isMobileView]
  );

  const commonProps = useMemo(() => ({
    steps,
    activeStep,
    additionalStyle,
    onStepClick,
    additionalClassName,
    activeStepColor,
    stepIconActive,
    stepIconInActive,
    completedStepIcon,
    ...otherProps
  }), [
    steps,
    activeStep,
    additionalStyle,
    onStepClick,
    additionalClassName,
    activeStepColor,
    stepIconActive,
    stepIconInActive,
    completedStepIcon,
    otherProps
  ]);

  // 3. ERROR HANDLING
  React.useEffect(() => {
    if (!steps || steps.length === 0) {
      console.warn('ProgressTracker: steps prop is required and should contain at least one step');
    }

    if (activeStep < 0 || activeStep > steps.length) {
      console.warn(`ProgressTracker: activeStep (${activeStep}) should be between 0 and ${steps.length}`);
    }

    steps?.forEach((step, index) => {
      if (!step.title && !step.customTitle) {
        console.warn(`ProgressTracker: Step at index ${index} is missing both title and customTitle properties`);
      }
    });
  }, [steps, activeStep]);

  return (
    <ThemeWrapper isMobile={isMobileDevice}>
      {isMobileDevice ? (
        <ProgressTrackerMobile {...commonProps} />
      ) : (
        <ProgressTrackerDesktop {...commonProps} />
      )}
    </ThemeWrapper>
  );
});

// Component display name for debugging
ProgressTracker.displayName = "ProgressTracker";

ProgressTracker.defaultProps = {
  steps: [],
  activeStep: 1,
  additionalStyle: null,
  additionalClassName: null,
  isMobile: false,
  isMobileView: false,
};

ProgressTracker.propTypes = {
  /**
   * Steps Array
   * 
   * { <br/>  
    title - Title String <br/> 
    subTitle - Sub Title String. It will be only displayed when the step in current active or completed <br/> 
    titleProps - Title String props <br/> 
    subTitleProps - Sub Title String props <br/> 
    customTitle - Custom title view. In case of this @property title, @property titleProps, @property subTitle, @property subTitleProps will be ignored <br/> 
    content - Content which will be displayed below sub title. It will be only displayed when the step in current active or completed <br/> 
    separator - incase of @property isMobile true then it will be displayed between title and sub title. default value – <br/> 
}
   */
  steps: PropTypes.arrayOf(
    PropTypes.shape({
      title: PropTypes.string,
      subTitle: PropTypes.string,
      titleProps: PropTypes.string,
      subTitleProps: PropTypes.string,
      customTitle: PropTypes.oneOfType([PropTypes.node, PropTypes.string]),
      content: PropTypes.oneOfType([PropTypes.node, PropTypes.string]),
      separator: PropTypes.string
    })
  ).isRequired,
  /**
   * Current Active step index. Note: index start from 0.Default value 1.
   */
  activeStep: PropTypes.number,

  /**
   * It will give you index of step when user have clicked on that step's dot
    (clickedIndex: number) => void
   */
  onStepClick: PropTypes.func,
  /**
   * Classes to add additional styling in the parent container
   */
  additionalClassName: PropTypes.arrayOf(PropTypes.string),
  /**
   * Inline styles to add additional styling in the parent container
   */
  additionalStyle: PropTypes.object,

  /**
   * To enable mobile view. Default value false.
   */
  isMobile: PropTypes.bool,

  /**
   * Whether component is in mobile view
   */
  isMobileView: PropTypes.bool,

  /**
   * Add active step color
   */
  activeStepColor: PropTypes.string,

  /**
   * Add any type of icon to the step
   */
  stepIconActive: PropTypes.string,

  /**
   * Icon for inactive steps
   */
  stepIconInActive: PropTypes.string,

  /**
   * Icon for completed steps
   */
  completedStepIcon: PropTypes.string,
};

export { ProgressTracker };
