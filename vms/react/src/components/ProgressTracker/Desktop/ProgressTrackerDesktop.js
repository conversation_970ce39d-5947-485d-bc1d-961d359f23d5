import React from "react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Step<PERSON>abelContainer,
  StepStyle,
  SubTitleText,
  TitleText,
} from "./ProgressTrackerDesktop.styled";
import { useClassName } from "../../../hooks/useClassName";

const isNonNull = (prop) => {
  return prop !== null && prop !== undefined;
};

const ProgressTrackerDesktop = (props) => {
  const { steps, activeStep, additionalStyle, isMobile, onStepClick,activeStepColor, stepIconActive, stepIconInActive, completedStepIcon } = props;
  const AdditioanalClassName = useClassName(props, props.additionalClassName);
  return (
    <Container
      className={`${AdditioanalClassName} vms_ProgressTracker_Container`}
      style={additionalStyle}
      isMobile={isMobile}
    >
      {steps &&
        steps.length > 0 &&
        steps.map((item, index) => {
          const isCompleted =
            activeStep === 0 ? false : activeStep >= index ? true : false;
          const isShowLable =
            activeStep < 0 ? false : activeStep >= index ? true : false;
          const isMiddle =
            index === 0 || index + 1 === steps.length ? false : true;
          return (
            <StepStyle
              key={index}
              isCompleted={isCompleted}
              isActive={activeStep === index ? true : false}
              isFirstStep={index == 0 ? true : false}
              isLastStep={index + 1 === steps.length ? true : false}
              isMiddle={isMiddle}
              checkIsCompleted={activeStep > index}
              // style={{ width: `${!isMobile ? `calc(${100 / steps.length}%)` :''} ` }}
              style={{
                width: `${!isMobile ? `calc(${100 / steps.length}%)` : ""} `,
                height: `${isMobile ? 'calc("100%")' : ""}`,
              }}
              activeStepColor={activeStepColor}
              className={`vms_ProgressTracker_Step ${activeStep === index ? "vms_ProgressTracker_Step_active" : ""} ${activeStep > index ? "vms_ProgressTracker_Step_completed" : ""}`}
            >
             {isNonNull(stepIconActive || stepIconInActive || completedStepIcon) ? 
             <div 
                onClick={() => onStepClick(index)}
                className="vms_ProgressTracker_customStepIcon_container"
             >
              {activeStep > index ? <>{completedStepIcon != "" && completedStepIcon != null  ? <img src={completedStepIcon}  alt="vms_Completed_step_icon_completed" className="vms_ProgressTracker_customStepIcon_completed"/> : <div className="vms_progress_completed_design"></div>}</> :
              activeStep == index ? <>{stepIconActive != "" && stepIconActive != null ? <img src={stepIconActive}  alt="vms_step_icon_active" className="vms_ProgressTracker_customStepIcon_active"/> : <div className="vms_progress_active_design">{item.stepNumber}</div>}</>
              :<>{stepIconInActive != "" && stepIconInActive != null ? <img src={stepIconInActive}  alt="vms_step_icon_active" className="vms_ProgressTracker_customStepIcon_active"/> : <div className="vms_progress_inactive_design">{item.stepNumber}</div>}</>}
             </div>
               : <StepDot
                onClick={() => onStepClick(index)}
                isActive={activeStep == index ? true : false}
                className="vms_ProgressTracker_StepDot"
                activeStepColor={activeStepColor}
              />}
              
              <StepLabelContainer className="vms_ProgressTracker_StepLabelContainer"
              onClick={() => onStepClick(index)}
              >
                {item.customTitle ? (
                  <>{item.customTitle}</>
                ) : (
                  <>
                    <LabelConatiner
                      isMiddle={
                        index === 0 || index + 1 === steps.length ? false : true
                      }
                      className={`vms_ProgressTracker_LabelContainer ${activeStep === index ? "vms_ProgressTracker_Label_active" : ""} ${activeStep > index ? "vms_ProgressTracker_Label_completed" : ""}`}
                      isLastStep={index + 1 === steps.length ? true : false}
                    >
                      <TitleText
                        className="vms_ProgressTracker_TitleText"
                        isShowLable={isShowLable}
                      >
                        {item.title}
                      </TitleText>
                    </LabelConatiner>
                    <LabelConatiner
                      isMiddle={
                        index === 0 || index + 1 === steps.length ? false : true
                      }
                      className={`vms_ProgressTracker_LabelContainer ${activeStep === index ? "vms_ProgressTracker_Label_active" : ""} ${activeStep > index ? "vms_ProgressTracker_Label_completed" : ""}`}
                      isLastStep={index + 1 === steps.length ? true : false}
                      onClick={() => onStepClick(index)}
                    >
                      {isShowLable && (
                        <SubTitleText
                          className="vms_ProgressTracker_SubText"
                          isShowLable={isShowLable}
                        >
                          {item.subTitle}
                        </SubTitleText>
                      )}
                    </LabelConatiner>
                  </>
                )}
                {item.content && isShowLable && (
                  <div className="vms_ProgressTracker_Content">
                    {item.content}
                  </div>
                )}
              </StepLabelContainer>
            </StepStyle>
          );
        })}
    </Container>
  );
};

export { ProgressTrackerDesktop };
