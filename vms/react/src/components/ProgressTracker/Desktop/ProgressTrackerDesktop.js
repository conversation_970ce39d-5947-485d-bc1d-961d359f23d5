import React, { memo, useCallback, useMemo } from "react";
import {
  <PERSON><PERSON><PERSON>,
  LabelConatiner,
  StepDot,
  StepLabelContainer,
  StepStyle,
  SubTitleText,
  TitleText,
} from "./ProgressTrackerDesktop.styled";
import { useClassName } from "../../../hooks/useClassName";

// Utility function (placed before main component)
const isNonNull = (prop) => prop !== null && prop !== undefined;

const ProgressTrackerDesktop = memo((props) => {
  // 1. PROP DESTRUCTURING & VALIDATION
  const {
    steps = [],
    activeStep = 1,
    additionalStyle,
    additionalClassName,
    isMobile,
    onStepClick,
    activeStepColor,
    stepIconActive,
    stepIconInActive,
    completedStepIcon,
    ...otherProps
  } = props;

  // 2. PERFORMANCE OPTIMIZATIONS
  const computedClassName = useMemo(
    () => useClassName(props, additionalClassName),
    [props, additionalClassName]
  );

  const hasCustomIcons = useMemo(
    () => isNonNull(stepIconActive) || isNonNull(stepIconInActive) || isNonNull(completedStepIcon),
    [stepIconActive, stepIconInActive, completedStepIcon]
  );

  // 3. EVENT HANDLING with useCallback
  const handleStepClick = useCallback(
    (index) => {
      if (isNonNull(onStepClick)) {
        onStepClick(index);
      }
    },
    [onStepClick]
  );
  // 4. CONDITIONAL RENDERING (memoized)
  const stepElements = useMemo(() => {
    if (!steps || steps.length === 0) {
      return null;
    }

    return steps.map((item, index) => {
      const isCompleted = activeStep === 0 ? false : activeStep >= index;
      const isShowLabel = activeStep < 0 ? false : activeStep >= index;
      const isMiddle = index !== 0 && index + 1 !== steps.length;
      const isActive = activeStep === index;
      const isFirst = index === 0;
      const isLast = index + 1 === steps.length;

      return (
        <StepStyle
          key={`step-${index}`}
          isCompleted={isCompleted}
          isActive={isActive}
          isFirstStep={isFirst}
          isLastStep={isLast}
          isMiddle={isMiddle}
          checkIsCompleted={activeStep > index}
          style={{
            width: !isMobile ? `calc(${100 / steps.length}%)` : "",
            height: isMobile ? 'calc("100%")' : "",
          }}
          activeStepColor={activeStepColor}
          className={`vms_ProgressTracker_Step ${isActive ? "vms_ProgressTracker_Step_active" : ""} ${activeStep > index ? "vms_ProgressTracker_Step_completed" : ""}`}
          role="tab"
          aria-selected={isActive}
          aria-label={`Step ${index + 1}: ${item.title}`}
          tabIndex={0}
          onKeyDown={(e) => {
            if (e.key === 'Enter' || e.key === ' ') {
              e.preventDefault();
              handleStepClick(index);
            }
          }}
        >
          {hasCustomIcons ? (
            <div
              onClick={() => handleStepClick(index)}
              className="vms_ProgressTracker_customStepIcon_container"
              role="button"
              tabIndex={0}
              aria-label={`Step ${index + 1} icon`}
            >
              {activeStep > index ? (
                <>
                  {isNonNull(completedStepIcon) ? (
                    <img
                      src={completedStepIcon}
                      alt="Completed step icon"
                      className="vms_ProgressTracker_customStepIcon_completed"
                    />
                  ) : (
                    <div className="vms_progress_completed_design"></div>
                  )}
                </>
              ) : activeStep === index ? (
                <>
                  {isNonNull(stepIconActive) ? (
                    <img
                      src={stepIconActive}
                      alt="Active step icon"
                      className="vms_ProgressTracker_customStepIcon_active"
                    />
                  ) : (
                    <div className="vms_progress_active_design">{item.stepNumber}</div>
                  )}
                </>
              ) : (
                <>
                  {isNonNull(stepIconInActive) ? (
                    <img
                      src={stepIconInActive}
                      alt="Inactive step icon"
                      className="vms_ProgressTracker_customStepIcon_inactive"
                    />
                  ) : (
                    <div className="vms_progress_inactive_design">{item.stepNumber}</div>
                  )}
                </>
              )}
            </div>
          ) : (
            <StepDot
              onClick={() => handleStepClick(index)}
              isActive={isActive}
              className="vms_ProgressTracker_StepDot"
              activeStepColor={activeStepColor}
            />
          )}

          <StepLabelContainer
            className="vms_ProgressTracker_StepLabelContainer"
            onClick={() => handleStepClick(index)}
            role="button"
            tabIndex={0}
            aria-label={`Step ${index + 1} label`}
          >
            {item.customTitle ? (
              <>{item.customTitle}</>
            ) : (
              <>
                <LabelConatiner
                  isMiddle={isMiddle}
                  className={`vms_ProgressTracker_LabelContainer ${isActive ? "vms_ProgressTracker_Label_active" : ""} ${activeStep > index ? "vms_ProgressTracker_Label_completed" : ""}`}
                  isLastStep={isLast}
                >
                  <TitleText
                    className="vms_ProgressTracker_TitleText"
                    isShowLable={isShowLabel}
                  >
                    {item.title}
                  </TitleText>
                </LabelConatiner>
                <LabelConatiner
                  isMiddle={isMiddle}
                  className={`vms_ProgressTracker_LabelContainer ${isActive ? "vms_ProgressTracker_Label_active" : ""} ${activeStep > index ? "vms_ProgressTracker_Label_completed" : ""}`}
                  isLastStep={isLast}
                >
                  {isShowLabel && item.subTitle && (
                    <SubTitleText
                      className="vms_ProgressTracker_SubText"
                      isShowLable={isShowLabel}
                    >
                      {item.subTitle}
                    </SubTitleText>
                  )}
                </LabelConatiner>
              </>
            )}
            {item.content && isShowLabel && (
              <div className="vms_ProgressTracker_Content">
                {item.content}
              </div>
            )}
          </StepLabelContainer>
        </StepStyle>
      );
    });
  }, [steps, activeStep, isMobile, hasCustomIcons, stepIconActive, stepIconInActive, completedStepIcon, activeStepColor, handleStepClick]);

  return (
    <Container
      className={`${computedClassName} vms_ProgressTracker_Container`}
      style={additionalStyle}
      isMobile={isMobile}
      role="tablist"
      aria-label="Progress tracker"
      {...otherProps}
    >
      {stepElements}
    </Container>
  );
});

// Component display name for debugging
ProgressTrackerDesktop.displayName = "ProgressTrackerDesktop";

export { ProgressTrackerDesktop };
