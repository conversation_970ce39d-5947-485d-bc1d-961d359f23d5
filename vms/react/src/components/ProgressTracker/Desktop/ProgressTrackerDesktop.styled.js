import styled from "styled-components";

export const Container = styled.div`
  display: flex;
  justify-content: space-between;
  width: 100%; ;
`;

export const StepStyle = styled.div`
  position: relative;
  text-align: ${({ isLastStep, isMiddle }) =>
    isMiddle ? "center" : isLastStep ? "right" : "left"};

  :before {
    ${({ isFirstStep, isCompleted, isMiddle,activeStepColor }) =>
      !isFirstStep
        ? ` 
    position: absolute;
    top: 9px;
    content: '';
    ${isMiddle ? "left: 1px" : ""};
    ${!isMiddle ? "right:12px" : ""};
    ${isMiddle ? "width:calc(50% - 10px)" : "width: calc(100% - 10px)"};
    ${isCompleted ? `border: dashed 1px ${activeStepColor ? activeStepColor :"#48a842"}` : "border: dashed 1px #d1d3d4"}
    `
        : ``};
  }
  :after {
    ${({ isLastStep, isCompleted, isMiddle, checkIsCompleted,activeStepColor }) =>
      !isLastStep
        ? ` 
    position: absolute;
    top: 9px;
    ${!isMiddle ? "left: 12px" : ""};
    ${isMiddle ? "right:1px" : ""};
    content: '';
    ${isMiddle ? "width:calc(50% - 10px)" : "width: calc(100% - 10px)"};
    ${
      isMiddle
        ? checkIsCompleted
          ? `border: dashed 1px ${activeStepColor ? activeStepColor :"#48a842"}`
          : "border: dashed 1px #d1d3d4"
        : isCompleted
        ? `border: dashed 1px ${activeStepColor ? activeStepColor :"#48a842"}`
        : "border: dashed 1px #d1d3d4"
    }
    `
        : ``};
  }
`;

export const StepDot = styled.div`
  width: 10px;
  height: 10px;
  background-color: ${({ isActive,activeStepColor }) => (isActive ? (activeStepColor ? activeStepColor : "#48a842") : "#d1d3d4")};
  border-radius: 50%;
  display: inline-block;
`;

export const StepLabelContainer = styled.div`
  word-break: break-all;
  margin-top: 8px;
`;
export const LabelConatiner = styled.div`
  justify-content: ${({ isLastStep, isMiddle }) =>
    isMiddle ? "center" : isLastStep ? "flex-end" : "flex-start"};
  display: flex;
`;

export const TitleText = styled.span`
  color: ${({ isShowLable }) => (isShowLable ? "#4d4d4f" : "#939598")};
  font-stretch: normal;
  font-style: normal;
  text-align: left;
  font-weight: ${({ isShowLable, theme }) =>
    isShowLable ? 700 : theme?.typography?.text?.fontWeight};
  font-family: ${({ theme }) => theme?.typography?.fontFamily || ""};
  font-size: 12px;
  line-height: 16px;
  letter-spacing: 0.4px;
  justify-content: center;
  align-self: center;
`;
export const SubTitleText = styled.div`
  margin-top: 5px;
  font-stretch: normal;
  font-style: normal;
  color: #939598;
  text-align: left;
  font-weight: ${({ theme }) => theme?.typography?.text?.fontWeight || ""};
  font-family: ${({ theme }) => theme?.typography?.fontFamily || ""};
  font-size: 12px;
  line-height: 16px;
  letter-spacing: 0.4px;
  justify-content: center;
  align-self: center;
`;

export const SubTitleContiner = styled.div`
  justify-content: flex-start;
  display: flex;
`;
