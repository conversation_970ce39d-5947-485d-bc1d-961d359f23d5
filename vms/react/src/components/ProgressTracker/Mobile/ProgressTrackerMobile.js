import React, { memo, useCallback, useMemo } from "react";
import {
  Con<PERSON>er,
  LabelConatiner,
  SeparatorDiv,
  StepDot,
  StepLabelContainer,
  StepStyle,
  SubTitleText,
  TitleText,
} from "./ProgressTrackerMobile.styled";
import { useClassName } from "../../../hooks/useClassName";

// Utility function (placed before main component)
const isNonNull = (prop) => prop !== null && prop !== undefined;

const ProgressTrackerMobile = memo((props) => {
  // 1. PROP DESTRUCTURING & VALIDATION
  const {
    steps = [],
    activeStep = 1,
    additionalStyle,
    additionalClassName,
    isMobile,
    onStepClick,
    activeStepColor,
    stepIconActive,
    stepIconInActive,
    completedStepIcon,
    ...otherProps
  } = props;

  // 2. PERFORMANCE OPTIMIZATIONS
  const computedClassName = useMemo(
    () => useClassName(props, additionalClassName),
    [props, additionalClassName]
  );

  const hasCustomIcons = useMemo(
    () => isNonNull(stepIconActive) || isNonNull(stepIconInActive) || isNonNull(completedStepIcon),
    [stepIconActive, stepIconInActive, completedStepIcon]
  );

  // 3. EVENT HANDLING with useCallback
  const handleStepClick = useCallback(
    (index) => {
      if (isNonNull(onStepClick)) {
        onStepClick(index);
      }
    },
    [onStepClick]
  );
  // 4. CONDITIONAL RENDERING (memoized)
  const stepElements = useMemo(() => {
    if (!steps || steps.length === 0) {
      return null;
    }

    return steps.map((item, index) => {
      const isCompleted = activeStep === 0 ? false : activeStep >= index;
      const isShowLabel = activeStep < 0 ? false : activeStep >= index;
      const isMiddle = index !== 0 && index + 1 !== steps.length;
      const isActive = activeStep === index;
      const isFirst = index === 0;
      const isLast = index + 1 === steps.length;

      return (
        <StepStyle
          key={`mobile-step-${index}`}
          isCompleted={isCompleted}
          isActive={isActive}
          isFirstStep={isFirst}
          isLastStep={isLast}
          isMiddle={isMiddle}
          checkIsCompleted={activeStep > index}
          style={{ height: `calc(${100 / (steps.length - 1)}%)` }}
          activeStepColor={activeStepColor}
          className="vms_ProgressTracker_Step"
          role="tab"
          aria-selected={isActive}
          aria-label={`Step ${index + 1}: ${item.title}`}
        >
          {isNonNull(stepIconActive || stepIconInActive || completedStepIcon) ?
            <div
              onClick={() => onStepClick(index)}
              className="vms_ProgressTracker_customStepIcon_container"
            >

              {activeStep > index ? <>{completedStepIcon != "" ? <img src={completedStepIcon} alt="vms_Completed_step_icon_completed" className="vms_ProgressTracker_customStepIcon_completed" /> : <div className="vms_progress_completed_design"></div>}</> :
                activeStep >= index ? <>{stepIconActive != "" ? <img src={stepIconActive} alt="vms_step_icon_active" className="vms_ProgressTracker_customStepIcon_active" /> : <div className="vms_progress_active_design"></div>}</>
                  : <>{stepIconInActive != "" ? <img src={stepIconInActive} alt="vms_step_icon_active" className="vms_ProgressTracker_customStepIcon_active" /> : <div className="vms_progress_inactive_design"></div>}</>}
            </div>
            : <StepDot
              onClick={() => onStepClick(index)}
              activeStepColor={activeStepColor}
              isActive={activeStep >= index ? true : false}
              className="vms_ProgressTracker_StepDot"
            />}
          <StepLabelContainer className="vms_ProgressTracker_StepLabelContainer">
            {item.customTitle ? (
              <>{item.customTitle}</>
            ) : (
              <div className="vms_ProgressTracker_mobileTitle">
                <LabelConatiner
                  isMiddle={
                    index === 0 || index + 1 === steps.length ? false : true
                  }
                  className="vms_ProgressTracker_LabelContainer"
                  isLastStep={index + 1 === steps.length ? true : false}
                >
                  <TitleText
                    className="vms_ProgressTracker_TitleText"
                    isShowLable={isShowLable}
                  >
                    {item.title}
                  </TitleText>
                </LabelConatiner>
                {item.separator && isShowLable && (
                  <SeparatorDiv className="vms_ProgressTracker_Separator">{item.separator}</SeparatorDiv>
                )}
                {isShowLable && (
                  <LabelConatiner
                    isMiddle={
                      index === 0 || index + 1 === steps.length
                        ? false
                        : true
                    }
                    className="vms_ProgressTracker_LabelContainer"
                    isLastStep={index + 1 === steps.length ? true : false}
                  >
                    {isShowLable && (
                      <SubTitleText
                        className="vms_ProgressTracker_SubText"
                        isShowLable={isShowLable}
                      >
                        {item.subTitle}
                      </SubTitleText>
                    )}
                  </LabelConatiner>
                )}
              </div>
            )}
            {item.content && isShowLabel && (
              <div className="vms_ProgressTracker_Content">
                {item.content}
              </div>
            )}
          </StepLabelContainer>
        </StepStyle>
      );
    });
  }, [steps, activeStep, hasCustomIcons, stepIconActive, stepIconInActive, completedStepIcon, activeStepColor, handleStepClick]);

  return (
    <Container
      className={`${computedClassName} vms_ProgressTracker_Container`}
      style={additionalStyle}
      isMobile={isMobile}
      role="tablist"
      aria-label="Progress tracker mobile"
      {...otherProps}
    >
      {stepElements}
    </Container>
  );
});

// Component display name for debugging
ProgressTrackerMobile.displayName = "ProgressTrackerMobile";

export { ProgressTrackerMobile };

export { ProgressTrackerMobile };
