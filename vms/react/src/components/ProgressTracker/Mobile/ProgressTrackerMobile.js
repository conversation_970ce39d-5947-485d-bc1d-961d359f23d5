import React from "react";
import {
  Con<PERSON>er,
  LabelConatiner,
  SeparatorDiv,
  StepDot,
  StepLabelContainer,
  StepStyle,
  SubTitleText,
  TitleText,
} from "./ProgressTrackerMobile.styled";
import { useClassName } from "../../../hooks/useClassName";

const isNonNull = (prop) => {
  return prop !== null && prop !== undefined;
};


const ProgressTrackerMobile = (props) => {
  const { steps, activeStep, additionalStyle, isMobile, onStepClick,activeStepColor, stepIconActive, stepIconInActive, completedStepIcon  } = props;
  const AdditioanalClassName = useClassName(props, props.additionalClassName);
  return (
    <Container
      className={`${AdditioanalClassName} vms_ProgressTracker_Container`}
      style={additionalStyle}
      isMobile={isMobile}
    >
      {steps &&
        steps.length > 0 &&
        steps.map((item, index) => {
          const isCompleted =
            activeStep === 0 ? false : activeStep >= index ? true : false;
          const isShowLable =
            activeStep < 0 ? false : activeStep >= index ? true : false;
          const isMiddle =
            index === 0 || index + 1 === steps.length ? false : true;
          return (
            <StepStyle
              isCompleted={isCompleted}
              isActive={activeStep === index ? true : false}
              isFirstStep={index == 0 ? true : false}
              isLastStep={index + 1 === steps.length ? true : false}
              isMiddle={isMiddle}
              checkIsCompleted={activeStep > index}
              style={{ height: `${`calc(${100 / (steps.length - 1)}%)`} ` }}
              activeStepColor={activeStepColor}
              className="vms_ProgressTracker_Step"
              key={index}
            >
            { isNonNull(stepIconActive || stepIconInActive || completedStepIcon) ? 
             <div 
                onClick={() => onStepClick(index)}
                className="vms_ProgressTracker_customStepIcon_container"
             >

              {activeStep > index ? <>{completedStepIcon != "" ? <img src={completedStepIcon}  alt="vms_Completed_step_icon_completed" className="vms_ProgressTracker_customStepIcon_completed"/> : <div className="vms_progress_completed_design"></div>}</> :
              activeStep >= index ? <>{stepIconActive != "" ? <img src={stepIconActive}  alt="vms_step_icon_active" className="vms_ProgressTracker_customStepIcon_active"/> : <div className="vms_progress_active_design"></div>}</>
              :<>{stepIconInActive != "" ? <img src={stepIconInActive}  alt="vms_step_icon_active" className="vms_ProgressTracker_customStepIcon_active"/> : <div className="vms_progress_inactive_design"></div>}</>}
             </div>
               : <StepDot
                onClick={() => onStepClick(index)}
                activeStepColor={activeStepColor}
                isActive={activeStep >= index ? true : false}
                className="vms_ProgressTracker_StepDot"
              />}
              <StepLabelContainer className="vms_ProgressTracker_StepLabelContainer">
                {item.customTitle ? (
                  <>{item.customTitle}</>
                ) : (
                  <div className="vms_ProgressTracker_mobileTitle">
                    <LabelConatiner
                      isMiddle={
                        index === 0 || index + 1 === steps.length ? false : true
                      }
                      className="vms_ProgressTracker_LabelContainer"
                      isLastStep={index + 1 === steps.length ? true : false}
                    >
                      <TitleText
                        className="vms_ProgressTracker_TitleText"
                        isShowLable={isShowLable}
                      >
                        {item.title}
                      </TitleText>
                    </LabelConatiner>
                    {item.separator && isShowLable && (
                      <SeparatorDiv className="vms_ProgressTracker_Separator">{item.separator}</SeparatorDiv>
                    )}
                    {isShowLable && (
                      <LabelConatiner
                        isMiddle={
                          index === 0 || index + 1 === steps.length
                            ? false
                            : true
                        }
                        className="vms_ProgressTracker_LabelContainer"
                        isLastStep={index + 1 === steps.length ? true : false}
                      >
                        {isShowLable && (
                          <SubTitleText
                            className="vms_ProgressTracker_SubText"
                            isShowLable={isShowLable}
                          >
                            {item.subTitle}
                          </SubTitleText>
                        )}
                      </LabelConatiner>
                    )}
                  </div>
                )}
                {item.content && isShowLable && (
                  <div className="vms_ProgressTracker_Content">
                    {item.content}
                  </div>
                )}
              </StepLabelContainer>
            </StepStyle>
          );
        })}
    </Container>
  );
};

export { ProgressTrackerMobile };
