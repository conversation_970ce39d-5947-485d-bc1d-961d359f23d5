import styled from "styled-components";

export const Container = styled.div`
  display: flex;
  justify-content: space-between;
  width: 100%;
  flex-direction: column;
`;

export const StepStyle = styled.div`
  position: relative;
  padding-bottom: 16px;
  :after {
    ${({ isLastStep, isCompleted, isMiddle, checkIsCompleted,activeStepColor }) =>
      !isLastStep
        ? ` 
    position: absolute;
    top: 17px;
    left: 4px;
    content: '';
    height: calc(100% - 14px);
    width: unset;

    ${
      isMiddle
        ? checkIsCompleted
          ? `border: dashed 1px ${activeStepColor ? activeStepColor :"#48a842"}`
          : "border: dashed 1px #d1d3d4"
        : isCompleted
        ? `border: dashed 1px ${activeStepColor ? activeStepColor :"#48a842"}`
        : "border: dashed 1px #d1d3d4"
    }
    `
        : `min-height: inherit;`};
  }
`;

export const StepDot = styled.div`
  position: absolute;
  top: 6px;
  z-index: 100;
  width: 10px;
  height: 10px;
  background-color: ${({ isActive,activeStepColor }) => (isActive ? (activeStepColor ? activeStepColor : "#48a842") : "#d1d3d4")};
  border-radius: 50%;
  display: inline-block;
`;

export const StepLabelContainer = styled.div`
  word-break: break-all;
  margin-top: 0px;
  padding-left: 25px;
`;
export const LabelConatiner = styled.div`
  /* justify-content: ${({ isLastStep, isMiddle }) =>
    isMiddle ? "center" : isLastStep ? "flex-end" : "flex-start"}; */
  justify-content: flex-start;
  display: inline-block;
  /* display: flex; */
`;

export const TitleText = styled.span`
  color: ${({ isShowLable }) => (isShowLable ? "#4d4d4f" : "#939598")};
  font-stretch: normal;
  font-style: normal;
  text-align: left;
  font-weight: ${({ isShowLable }) => (isShowLable ? 700 : 400)};
  font-family: ${({ theme }) => theme?.typography?.fontFamily || ""};  
  font-size: 12px;
  line-height: 16px;
  letter-spacing: 0.4px;
  justify-content: center;
  align-self: center;
`;
export const SubTitleText = styled.div`
  margin-top: 5px;
  font-stretch: normal;
  font-style: normal;
  color: #939598;
  text-align: left;
  font-weight: ${({ theme }) => theme?.typography?.text?.fontWeight || ""};
  font-family: ${({ theme }) => theme?.typography?.fontFamily || ""};
  font-size: 12px;
  line-height: 16px;
  letter-spacing: 0.4px;
  justify-content: center;
  align-self: center;
`;
export const SeparatorDiv = styled.div`
  margin: 0px 5px;
  display: inline-block;
  font-weight: ${({ theme }) => theme?.typography?.text?.fontWeight || ""};
  font-family: ${({ theme }) => theme?.typography?.fontFamily || ""};
  font-size: 12px;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.33;
  letter-spacing: 0.3px;
  color: #939598;
`;

export const SubTitleContiner = styled.div`
  justify-content: flex-start;
  display: flex;
`;
