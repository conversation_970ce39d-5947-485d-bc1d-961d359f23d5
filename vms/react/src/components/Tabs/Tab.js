import React, { Fragment, useRef, memo, useCallback, useMemo } from "react";
import {
  StyledTabButton,
  ActiveTabIndicator,
  TabLabelIconWrapper,
  TabIcon,
} from "./Tab.styled";
import PropTypes from "prop-types";
import { ThemeWrapper } from "../Theme/ThemeContext";
import { useClassName } from "../../hooks/useClassName";

// Utility function (placed before main component)
const isNonNull = (prop) => prop !== null && prop !== undefined;

export const Tab = memo((props) => {
  // 1. PROP DESTRUCTURING & VALIDATION
  const {
    onTabClick,
    value,
    innerRef,
    activeTabValue,
    onClick,
    tabIndex,
    orientation = "horizontal",
    label,
    icon,
    iconPosition = "top",
    textColor = "primary",
    indicatorColor = "primary",
    additionalClassName,
    additionalStyle,
    isMobile = false,
    isMobileView = false,
    id,
    ...otherProps
  } = props;

  // 2. REFS
  const tabRef = useRef();

  // 3. PERFORMANCE OPTIMIZATIONS
  const computedClassName = useMemo(
    () => useClassName(props, additionalClassName),
    [props, additionalClassName]
  );

  const isActive = useMemo(() => activeTabValue === value, [
    activeTabValue,
    value,
  ]);

  const isMobileDevice = useMemo(
    () => isMobile || isMobileView,
    [isMobile, isMobileView]
  );

  // 4. EVENT HANDLERS with useCallback
  const tabClickHandler = useCallback(
    (e) => {
      try {
        if (isNonNull(onTabClick)) {
          onTabClick(e, value, tabIndex);
        } else if (isNonNull(onClick)) {
          onClick(value);
        }

        // Smooth scroll to tab
        if (tabRef.current) {
          tabRef.current.scrollIntoView({
            behavior: "smooth",
            block: "center",
            inline: "center",
          });
        }
      } catch (error) {
        console.error("Tab: Error in click handler", error);
      }
    },
    [onTabClick, onClick, value, tabIndex]
  );

  const handleKeyDown = useCallback(
    (event) => {
      if (event.key === "Enter" || event.key === " ") {
        event.preventDefault();
        tabClickHandler(event);
      }
    },
    [tabClickHandler]
  );

  // 5. MEMOIZED COMPONENTS
  const iconElement = useMemo(() => {
    if (!icon) return null;

    if (React.isValidElement(icon)) {
      return icon;
    }

    if (typeof icon === "string") {
      return (
        <img
          src={icon}
          className="vms_tab_icon"
          alt=""
          loading="lazy"
        />
      );
    }

    return null;
  }, [icon]);

  const labelContent = useMemo(
    () => (
      <TabLabelIconWrapper
        iconPosition={iconPosition}
        className="vms_tab_label"
      >
        {iconElement}
        {label}
      </TabLabelIconWrapper>
    ),
    [iconElement, label, iconPosition]
  );

  const activeIndicator = useMemo(
    () => (
      <ActiveTabIndicator
        isActive={isActive}
        orientation={orientation}
        indicatorColor={indicatorColor}
        className={`vms_tab_indicator ${isActive ? "vms_tab_active_indicator" : ""
          }`}
      />
    ),
    [isActive, orientation, indicatorColor]
  );

  // 6. ERROR HANDLING
  React.useEffect(() => {
    if (!label) {
      console.warn("Tab: label prop is required");
    }
  }, [label]);

  return (
    <ThemeWrapper isMobile={isMobileDevice}>
      <Fragment>
        <StyledTabButton
          id={id || null}
          orientation={orientation}
          ref={tabRef}
          onClick={tabClickHandler}
          onKeyDown={handleKeyDown}
          isActive={isActive}
          textColor={textColor}
          className={`${computedClassName} vms_tab`}
          style={additionalStyle}
          role="tab"
          aria-selected={isActive}
          aria-controls={id ? `${id}-panel` : null}
          tabIndex={isActive ? 0 : -1}
          {...otherProps}
        >
          {labelContent}
          {activeIndicator}
        </StyledTabButton>
      </Fragment>
    </ThemeWrapper>
  );
});

// Set display name for debugging
Tab.displayName = "Tab";

Tab.defaultProps = {
  iconPosition: "top",
};

Tab.propTypes = {
  /**
   * When a tab is clicked
   */
  onClick: PropTypes.func,
  /**
   * The icon to display
   */
  icon: PropTypes.oneOfType([PropTypes.string, PropTypes.element]),
  /**
   * 	The label element
   */
  label: PropTypes.node.isRequired,
  /**
   * The position of the icon relative to the label. Default is `top`
   */
  iconPosition: PropTypes.oneOf(["bottom", "top", "left", "right"]),
  /**
   * 	You can provide your own value. Otherwise, we fallback to the child position index.
   */
  value: PropTypes.any,
  /**
   * Property to append additional css class
   */
  additionalClassName: PropTypes.arrayOf(PropTypes.string),

  /**
   * Property to set the `inline style` object
   */
  additionalStyle: PropTypes.object,
};
