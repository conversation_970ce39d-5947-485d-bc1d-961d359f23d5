import styled from "styled-components";

export const StyledTabButton = styled.button`
  color: ${({ isActive, theme, textColor }) =>
    isActive === true
      ? theme.palette[textColor][theme.mode]
      : `rgba(0, 0, 0, 0.6)`};
  display: inline-flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  background-color: transparent;
  outline: 0px;
  border: 0px;
  margin: 0px;
  cursor: pointer;
  user-select: none;
  vertical-align: middle;
  text-decoration: none;
  font-weight: 700;
  text-transform: uppercase;
  max-width: 360px;
  position: relative;
  min-height: 48px;
  flex-shrink: 0;
  padding: 9px 16px;
  overflow: hidden;
  white-space: normal;
  text-align: center;
  flex-direction: ${({ orientation }) =>
    orientation == "horizontal" ? "row" : "column"};
`;

export const ActiveTabIndicator = styled.div`
  position: absolute;
  bottom: 0px;
  transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  background-color: ${({ theme, isActive, indicatorColor }) =>
    isActive === true
      ? theme.palette[indicatorColor][theme.mode]
      : "transparent"};
  right: 0px;
  top: ${({ orientation }) => orientation == "vertical" && "0"};
  height: 48px;
  height: ${({ orientation }) => (orientation == "vertical" ? "48px" : "3px")};
  width: ${({ orientation }) => (orientation == "vertical" ? "4px" : "100%")};
`;

export const TabLabelIconWrapper = styled.div`
  display: flex;
  vertical-align: middle;
  align-items: center;
  justify-content: center;
  gap: 6px;
  flex-direction: ${({ iconPosition }) =>
    iconPosition == "top"
      ? "column"
      : iconPosition == "bottom"
      ? "column-reverse"
      : iconPosition == "left"
      ? "row"
      : "row-reverse"};
  position: relative;
`;

export const TabIcon = styled.svg`
  color: ${({ theme, color }) => theme.palette[color][theme.mode]};
`;
