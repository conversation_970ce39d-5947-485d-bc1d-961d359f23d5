import { useState } from "react";
import { Tab } from "./Tab";
import { Tabs } from "./Tabs";
import { useTheme } from "styled-components";

export default {
  title: "VMS_REACT/Tabs",
  component: Tabs,
  argTypes: {
    additionalStyle: { control: { type: "" } },
    additionalClassName: { control: { type: "" } },
    children: { control: { type: "" } },
  },
};

const tabLabels = [
  "ONE",
  "TWO",
  "THREE",
  "FOUR",
  "FIVE",
  "SIX",
  "Seven",
  "Eight",
  "Nine",
  "Ten",
  "Eleven",
  "Twelve",
  "Thirteen",
];
export const TabsDefault = (props) => {
  const [activeTabName, setActiveTabName] = useState("ONE");
  return (
    <div
      id="Tabs-Default"
      style={{
        flexGrow: "1",
        backgroundColor: "rgb(255, 255, 255)",
        display: "flex",
        flexDirection: props.orientation == "horizontal" ? "column" : "row",
      }}
    >
      <div>
        <Tabs
          {...props}
          onChange={(event, value) => {
            setActiveTabName(tabLabels[value]);
          }}
          scrollButtons
        >
          {tabLabels.map((tabName) => {
            return <Tab key={tabName} label={`Tab-${tabName}`} />;
          })}
        </Tabs>
      </div>
      <div style={{ marginTop: "30px", marginLeft: "30px" }}>
        {`Tab Item ${activeTabName} Content`}
      </div>
    </div>
  );
};
TabsDefault.args = {
  orientation: "horizontal",
  id:"tabs-id"
};
export const VerticalTab = (props) => {
  const [activeTabName, setActiveTabName] = useState("ONE");
  return (
    <div
      id="Tabs-Default"
      style={{
        height: "300px",
        flexGrow: "1",
        backgroundColor: "rgb(255, 255, 255)",
        display: "flex",
        flexDirection: "row",
      }}
    >
      <div>
        <Tabs
        id={"vertical-id"}
          {...props}
          onChange={(event, value) => {
            setActiveTabName(tabLabels[value]);
          }}
          orientation="vertical"
          // value={activeTabName}
        >
          {tabLabels.map((tabName) => {
            return <Tab key={tabName} label={`Tab-${tabName}`} />;
          })}
        </Tabs>
      </div>
      <span
        style={{ marginTop: "30px", marginLeft: "30px", fontWeight: "600" }}
      >
        {`Tab Item ${activeTabName} Content`}
      </span>
    </div>
  );
};

export const TabsWithIcon = (props) => {
  const icons = [<PhoneIcon />, <NearbyIcon />, <FavoriteIcon />];
  return (
    <div
      id="Tabs-Default"
      style={{
        // height: "72px",
        flexGrow: "1",
        backgroundColor: "rgb(255, 255, 255)",
        display: "flex",
        // width: "770px",
      }}
    >
      <Tabs {...props}>
        {tabLabels.map((ele, index) => {
          return (
            <Tab
            id={"icon-id"+`-${index}`}
              key={ele}
              label={`Tab-${ele}`}
              icon={icons[Math.floor(Math.random() * icons.length)]}
            />
          );
        })}
      </Tabs>
    </div>
  );
};
TabsWithIcon.args = {
  orientation: "horizontal",
};

export const TabsWithIconPosition = (props) => {
  const icons = [
    <PhoneIcon />,
    <NearbyIcon />,
    <FavoriteIcon />,
    <NearbyIcon />,
  ];
  const iconPositions = ["left", "top", "right", "bottom"];
  return (
    <div
      id="Tabs-Default"
      style={{
        flexGrow: "1",
        backgroundColor: "rgb(255, 255, 255)",
        display: "flex",
        width: "390px",
      }}
    >
      <Tabs {...props}>
        {iconPositions.map((ele, index) => {
          return (
            <Tab
            id={"pos-id-"+`${index}`}
              key={ele}
              label={`${ele}`}
              iconPosition={iconPositions[index]}
              icon={icons[index]}
            />
          );
        })}
      </Tabs>
    </div>
  );
};
TabsWithIcon.args = {
  orientation: "horizontal",
};

export const TabMobileDefault = (props) => {
  return (
    <Tabs isMobile={true} textColor={"primary"}>
      {["Recent", "Favorites", "Nearby"].map((ele,index) => {
        return (
          <Tab
          id={"mob-id-"+`${index}`}
            key={ele}
            label={ele}
            icon={
              ele == "Recent" ? (
                <PhoneIcon />
              ) : ele == "Favorites" ? (
                <FavoriteIcon />
              ) : (
                <NearbyIcon />
              )
            }
          />
        );
      })}
    </Tabs>
  );
};
TabMobileDefault.parameters = {
  viewport: { defaultViewport: "iphone5" },
};

const PhoneIcon = () => {
  return (
    <svg
      focusable="false"
      viewBox="0 0 24 24"
      width={20}
      height={20}
      color={"inherit"}
    >
      <path
        fill="currentColor"
        d="M6.62 10.79c1.44 2.83 3.76 5.14 6.59 6.59l2.2-2.2c.27-.27.67-.36 1.02-.24 1.12.37 2.33.57 ********** 0 1 .45 1 1V20c0 .55-.45 1-1 1-9.39 0-17-7.61-17-17 0-.55.45-1 1-1h3.5c.55 0 1 .45 1 1 0 1.25.2 2.45.57 **********.03.74-.25 1.02l-2.2 2.2z"
      ></path>
    </svg>
  );
};

const FavoriteIcon = () => {
  return (
    <svg
      focusable="false"
      viewBox="0 0 24 24"
      width={20}
      height={20}
      color="inherit"
    >
      <path
        fill="currentColor"
        d="m12 21.35-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"
      ></path>
    </svg>
  );
};

const NearbyIcon = () => {
  return (
    <svg
      focusable="false"
      viewBox="0 0 24 24"
      width={20}
      height={20}
      color="inherit"
    >
      <path
        fill="currentColor"
        d="M12 2c-4.97 0-9 4.03-9 9 0 4.17 2.84 7.67 6.69 8.69L12 22l2.31-2.31C18.16 18.67 21 15.17 21 11c0-4.97-4.03-9-9-9zm0 2c1.66 0 3 1.34 3 3s-1.34 3-3 3-3-1.34-3-3 1.34-3 3-3zm0 14.3c-2.5 0-4.71-1.28-6-3.22.03-1.99 4-3.08 6-3.08 1.99 0 5.97 1.09 6 3.08-1.29 1.94-3.5 3.22-6 3.22z"
      ></path>
    </svg>
  );
};
