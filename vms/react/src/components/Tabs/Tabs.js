import React, { useEffect, useRef, useState, memo, useCallback, useMemo } from "react";
import { Tab } from "./Tab";
import {
  TabLeftRightButton,
  TabScroller,
  StyledTabs,
  TabWrapper,
  Svg,
} from "./Tabs.styled";
import PropTypes from "prop-types";
import { ThemeWrapper } from "../Theme/ThemeContext";
import { useClassName } from "../../hooks/useClassName";

// Utility function (placed before main component)
const isNonNull = (prop) => prop !== null && prop !== undefined;

export const Tabs = memo((props) => {
  // 1. PROP DESTRUCTURING & VALIDATION
  const {
    children,
    textColor = "primary",
    orientation = "horizontal",
    scrollButtons = "auto",
    isMobile = false,
    isMobileView = false,
    allowScrollButtonsMobile = false,
    onChange,
    value = 0,
    indicatorColor = "primary",
    additionalClassName,
    additionalStyle,
    id,
    ...otherProps
  } = props;

  // 2. STATE MANAGEMENT
  const [activeTabValue, setActiveTabValue] = useState(value);
  const [showDownBtn, setShowDownBtn] = useState(true);
  const [showUpBtn, setShowUpBtn] = useState(false);
  const [showButtons, setShowButtons] = useState(true);

  // 3. REFS
  const tabsRef = useRef();

  // 4. PERFORMANCE OPTIMIZATIONS
  const computedClassName = useMemo(() => 
    useClassName(props, additionalClassName), 
    [props, additionalClassName]
  );

  const isVertical = useMemo(() => orientation === "vertical", [orientation]);

  const shouldShowButtons = useMemo(() => {
    if (isMobile && allowScrollButtonsMobile) {
      return true;
    }
    
    if (scrollButtons === true) {
      return true;
    }

    if (scrollButtons === "auto" && !isMobile && tabsRef.current) {
      if (isVertical) {
        return tabsRef.current.clientHeight < tabsRef.current.scrollHeight;
      } else {
        return tabsRef.current.clientWidth < tabsRef.current.scrollWidth;
      }
    }

    return false;
  }, [isMobile, allowScrollButtonsMobile, scrollButtons, isVertical]);

  // 5. EVENT HANDLERS with useCallback
  const tabClickHandler = useCallback((event, value, tabIndex) => {
    setActiveTabValue(value);
    if (isNonNull(onChange)) {
      try {
        onChange(event, value);
      } catch (error) {
        console.error("Tabs: Error in onChange callback", error);
      }
    }
  }, [onChange]);

  const upBtnClickHandler = useCallback((e) => {
    if (!tabsRef.current) return;

    let scrollValue = null;
    
    if (isVertical) {
      const clientHeight = tabsRef.current.clientHeight;
      const currentScrollHeight = tabsRef.current.scrollTop;
      scrollValue = {
        top: Math.max(0, currentScrollHeight - clientHeight),
      };
    } else {
      const clientWidth = tabsRef.current.clientWidth;
      const currentScrollWidth = tabsRef.current.scrollLeft;
      scrollValue = {
        left: Math.max(0, currentScrollWidth - clientWidth),
      };
    }
    
    if (scrollValue) {
      tabsRef.current.scrollTo({
        ...scrollValue,
        behavior: "smooth",
      });
    }
  }, [isVertical]);

  const downBtnClickHandler = useCallback((e) => {
    if (!tabsRef.current) return;

    let scrollValue = null;
    
    if (isVertical) {
      const clientHeight = tabsRef.current.clientHeight;
      const currentScrollHeight = tabsRef.current.scrollTop;
      scrollValue = {
        top: clientHeight + currentScrollHeight,
      };
    } else {
      const clientWidth = tabsRef.current.clientWidth;
      const currentScrollWidth = tabsRef.current.scrollLeft;
      scrollValue = {
        left: clientWidth + currentScrollWidth,
      };
    }
    
    if (scrollValue) {
      tabsRef.current.scrollTo({
        ...scrollValue,
        behavior: "smooth",
      });
    }
  }, [isVertical]);

  const handleScroll = useCallback((e) => {
    const target = e.target;
    let bottom = false;
    let left = false;
    
    if (isVertical) {
      bottom = target.scrollHeight - target.scrollTop === target.clientHeight;
      left = target.scrollTop > 1;
    } else {
      bottom = target.scrollWidth <= Math.round(target.clientWidth + target.scrollLeft);
      left = target.scrollLeft > 1;
    }
    
    setShowDownBtn(!bottom);
    setShowUpBtn(left);
  }, [isVertical]);

  const handleKeyDown = useCallback((event) => {
    if (!Array.isArray(children)) return;

    const currentIndex = children.findIndex((child, index) => {
      const tabValue = isNonNull(child.props.value) ? child.props.value : index;
      return tabValue === activeTabValue;
    });

    let newIndex = currentIndex;

    if (event.key === 'ArrowLeft' && !isVertical) {
      newIndex = Math.max(0, currentIndex - 1);
    } else if (event.key === 'ArrowRight' && !isVertical) {
      newIndex = Math.min(children.length - 1, currentIndex + 1);
    } else if (event.key === 'ArrowUp' && isVertical) {
      newIndex = Math.max(0, currentIndex - 1);
    } else if (event.key === 'ArrowDown' && isVertical) {
      newIndex = Math.min(children.length - 1, currentIndex + 1);
    } else if (event.key === 'Home') {
      newIndex = 0;
    } else if (event.key === 'End') {
      newIndex = children.length - 1;
    }

    if (newIndex !== currentIndex) {
      event.preventDefault();
      const newValue = isNonNull(children[newIndex].props.value) 
        ? children[newIndex].props.value 
        : newIndex;
      tabClickHandler(event, newValue, newIndex);
    }
  }, [children, activeTabValue, isVertical, tabClickHandler]);

  // 6. EFFECTS
  useEffect(() => {
    setActiveTabValue(value);
  }, [value]);

  useEffect(() => {
    setShowButtons(shouldShowButtons);
  }, [shouldShowButtons]);

  // 7. ERROR HANDLING
  React.useEffect(() => {
    if (!children || (Array.isArray(children) && children.length === 0)) {
      console.warn('Tabs: children prop is required and should contain Tab elements');
    }
    if (!id) {
      console.warn('Tabs: id prop is recommended for accessibility');
    }
  }, [children, id]);

  // 8. MEMOIZED COMPONENTS
  const tabElements = useMemo(() => {
    if (!children) return null;

    const childrenArray = Array.isArray(children) ? children : [children];
    
    return childrenArray.map(({ props: childProps }, tabIndex) => (
      <Tab
        id={id ? `${id}-${tabIndex}` : `tab-${tabIndex}`}
        orientation={orientation}
        key={`Tab-Index-${tabIndex}`}
        {...childProps}
        value={isNonNull(childProps.value) ? childProps.value : tabIndex}
        onTabClick={tabClickHandler}
        activeTabValue={activeTabValue}
        tabIndex={tabIndex}
        label={childProps.label}
        indicatorColor={indicatorColor}
        textColor={textColor}
        additionalClassName={childProps.additionalClassName}
        additionalStyle={childProps.additionalStyle}
      />
    ));
  }, [children, id, orientation, tabClickHandler, activeTabValue, indicatorColor, textColor]);

  const leftButton = useMemo(() => {
    if (!showButtons) return null;
    
    return (
      <TabLeftRightButton
        isVisible={showUpBtn}
        onClick={showUpBtn ? upBtnClickHandler : null}
        onKeyDown={showUpBtn ? handleKeyDown : null}
        orientation={orientation}
        className="vms_tabs_left_button"
        tabIndex={showUpBtn ? 0 : -1}
        role="button"
        aria-label={isVertical ? "Scroll up" : "Scroll left"}
        aria-disabled={!showUpBtn}
      >
        <LeftIcon
          color={indicatorColor}
          orientation={orientation}
          className="vms_tabs_left_icon"
        />
      </TabLeftRightButton>
    );
  }, [showButtons, showUpBtn, upBtnClickHandler, handleKeyDown, orientation, indicatorColor, isVertical]);

  const rightButton = useMemo(() => {
    if (!showButtons) return null;
    
    return (
      <TabLeftRightButton
        isVisible={showDownBtn}
        onClick={showDownBtn ? downBtnClickHandler : null}
        onKeyDown={showDownBtn ? handleKeyDown : null}
        orientation={orientation}
        className="vms_tabs_right_button"
        tabIndex={showDownBtn ? 0 : -1}
        role="button"
        aria-label={isVertical ? "Scroll down" : "Scroll right"}
        aria-disabled={!showDownBtn}
      >
        <RightIcon
          color={indicatorColor}
          orientation={orientation}
          className="vms_tabs_right_icon"
        />
      </TabLeftRightButton>
    );
  }, [showButtons, showDownBtn, downBtnClickHandler, handleKeyDown, orientation, indicatorColor, isVertical]);

  return (
    <ThemeWrapper isMobile={isMobile || isMobileView || false}>
      <StyledTabs
        orientation={orientation}
        className={`${computedClassName} vms_tabs`}
        style={additionalStyle}
        role="tablist"
        aria-orientation={orientation}
        onKeyDown={handleKeyDown}
        {...otherProps}
      >
        {leftButton}
        <TabScroller
          ref={tabsRef}
          onScroll={handleScroll}
          orientation={orientation}
          className="vms_tabs_scroller"
          role="presentation"
        >
          <TabWrapper
            orientation={orientation}
            className="vms_tabs_tab_wrapper"
            role="presentation"
          >
            {tabElements}
          </TabWrapper>
        </TabScroller>
        {rightButton}
      </StyledTabs>
    </ThemeWrapper>
  );
});

Tabs.displayName = 'Tabs';

Tabs.defaultProps = {
  orientation: "horizontal",
  scrollButtons: "auto",
  isMobile: false,
  allowScrollButtonsMobile: false,
  value: 0,
  textColor: "primary",
  indicatorColor: "primary",
};

Tabs.propTypes = {
  /**
   * The component orientation (layout flow direction). Default is horizontal
   */
  orientation: PropTypes.oneOf(["horizontal", "vertical"]),

  /**
   * 	Callback fired when the tab changes
   * `
   * function(event: React.SyntheticEvent, value: any) => void
   * `
   */
  onChange: PropTypes.func,
  /**
   * Determine behavior of scroll buttons when tabs are set to scroll: - auto will only present them when not all the items are visible. - true will always present them. - false will never present them.
   * By default the scroll buttons are hidden on mobile. This behavior can be disabled with allowScrollButtonsMobile.
   */
  scrollButtons: PropTypes.oneOf([true, false, "auto"]),
  /**
   * Child must be of Type `Tab` element only
   */
  children: PropTypes.oneOfType([
    PropTypes.element,
    PropTypes.arrayOf(PropTypes.element),
  ]),
  /**
   * Theme Color to apply. Default `primary`
   */
  textColor: PropTypes.oneOf(["primary", "secondary"]),
  /**
   * Is Mobile-View. Default is `false`
   */
  isMobile: PropTypes.bool,
  /**
   * If true, the scroll buttons aren't forced hidden on mobile.
   * By default the scroll buttons are hidden on mobile and takes precedence over scrollButtons.
   */
  allowScrollButtonsMobile: PropTypes.bool,
  /**
   * The value of the currently selected Tab
   */
  value: PropTypes.any,
  /**
   * Determines the color of the indicator
   * Default is `primary
   */
  indicatorColor: PropTypes.oneOf(["primary", "secondary"]),
  /**
   * Property to append additional css class
   */
  additionalClassName: PropTypes.arrayOf(PropTypes.string),

  /**
   * Property to set the `inline style` object
   */
  additionalStyle: PropTypes.object,
};

// Memoized icon components
const LeftIcon = memo(({ color, orientation, className }) => (
  <Svg
    color={color}
    orientation={orientation}
    focusable="false"
    viewBox="0 0 24 24"
    aria-hidden="true"
    className={className}
    role="img"
  >
    <path
      fill="currentColor"
      d="M15.41 16.09l-4.58-4.59 4.58-4.59L14 5.5l-6 6 6 6z"
    />
  </Svg>
));

const RightIcon = memo(({ color, orientation, className }) => (
  <Svg
    color={color}
    orientation={orientation}
    focusable="false"
    viewBox="0 0 24 24"
    aria-hidden="true"
    className={className}
    role="img"
  >
    <path
      fill="currentColor"
      d="M8.59 16.34l4.58-4.59-4.58-4.59L10 5.75l6 6-6 6z"
    />
  </Svg>
));

LeftIcon.displayName = 'LeftIcon';
RightIcon.displayName = 'RightIcon';
