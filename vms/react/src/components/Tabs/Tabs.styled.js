import styled from "styled-components";

export const StyledTabs = styled.div`
  overflow: hidden;
  min-height: 48px;
  display: flex;
  flex-direction: ${({ orientation }) =>
    orientation == "horizontal" ? "row" : "column"};
  border-right: ${({ orientation }) =>
    orientation == "vertical"
      && `1px solid #0000001f`};
  /* border-bottom: ${({ orientation }) =>
    orientation == "horizontal" && `1px solid #0000001f`}; */
  height: 100%;
  width: 100%;
`;

export const TabScroller = styled.div`
  position: relative;
  display: inline-block;
  flex: 1 1 auto;
  white-space: nowrap;
  overflow: ${({ orientation }) =>
    orientation == "vertical" ? `hidden auto` : `auto hidden`};
  ::-webkit-scrollbar {
    display: none;
  }
  -ms-overflow-style: none;
  scrollbar-width: none;
`;

export const TabLeftRightButton = styled.div`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  position: relative;
  background-color: transparent;
  outline: 0px;
  border: 0px;
  margin: 0px;
  border-radius: 0px;
  padding: 0px;
  cursor: pointer;
  user-select: none;
  vertical-align: middle;
  appearance: none;
  text-decoration: none;
  color: inherit;
  width: ${({ orientation }) => orientation == "vertical" && `100%`};
  flex-shrink: 0;
  height: 40px;
  opacity: ${({ isVisible }) => (isVisible ? "0.8" : "0")};
  transition: opacity 0.5s ease;
`;

export const TabIcon = styled.img`
  margin-bottom: 6px;
  user-select: none;
  width: 1em;
  height: 1em;
  transition: fill 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
`;

export const TabWrapper = styled.div`
  display: flex;
  flex-direction: ${({ orientation }) =>
    orientation == "horizontal" ? "row" : "column"};
`;

export const Svg = styled.svg`
  width: 24px;
  height: 24px;
  color: ${({ theme, color }) => theme.palette[color][theme.mode]};
  transform: ${({ orientation }) =>
    orientation == "vertical" && "rotate(90deg)"};
`;
