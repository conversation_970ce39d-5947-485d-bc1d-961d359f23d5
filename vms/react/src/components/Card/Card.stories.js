import React from "react";
import { Card } from "./Card";

export default {
  title: "VMS_REACT/Card",
  component: Card,
  argTypes: {
    customTitleView: { control: { type: "" } },
    additionalStyle: { control: { type: "" } },
    additionalClassName: { control: { type: "" } },
    titleClassName: {control: { type: "" }},
    children: { control: { type: "" } },
  },
};

const renderChildren = () => {
  return (
    <div style={{ margin: "0 auto", textAlign: "center", padding: 50 }}>
      Lorem Ipsum has been the industry's standard dummy text ever since the
      1500s, when an unknown printer took a galley of type and scrambled it to
      make a type specimen book.
    </div>
  );
};
const Template = (args) => <Card {...args}>{renderChildren()}</Card>;

const CustomTitle = (args) => (
  <Card
    {...args}
    customTitleView={
      <div
        style={{
          display: "flex",
          justifyContent: "space-between",
          padding: 20,
        }}
      >
        <img
          src={"https://img.icons8.com/pastel-glyph/24/000000/plus.png"}
          width={24}
          height={24}
        />
        <p
          style={{
            margin: 0,
            fontSize: 24,
            lineHeight: "24px",
            fontFamily: "Montserrat",
            fontWeight: 700,
          }}
        >
          {" "}
          Card Title{" "}
        </p>
        <button
          style={{
            padding: 0,
            margin: 0,
            border: "none",
            background: "#fff",
            color: "#03868b",
          }}
        >
          {" "}
          CLOSE{" "}
        </button>
      </div>
    }
  >
    {renderChildren()}
  </Card>
);

const CustomStyle = (args) => (
  <Card
    {...args}
    additionalStyle={{
      width: 300,
      height: 200,
      backgroundColor: "lightgoldenrodyellow",
    }}
  >
    <div style={{ margin: "0 auto", textAlign: "center", padding: 20 }}>
      Lorem Ipsum has been the industry's standard dummy text ever since the
      1500s, when an unknown printer took a galley of type and scrambled it to
      make a type specimen book.
    </div>
  </Card>
);

export const OnlyText = Template.bind({});
OnlyText.args = {
  title:"",
  showTitleSeparatorLine:true,
  cardBorder:true
};

export const WithTitle = Template.bind({});
WithTitle.args = {
  title: "Card Title",
  showTitleSeparatorLine:true,
  cardBorder:true
};

export const TitleWithoutSeparator = Template.bind({});
TitleWithoutSeparator.args = {
  title: "Card Title",
  showTitleSeparatorLine: false,
  cardBorder:true
};

export const CardWithoutBorder = Template.bind({});
CardWithoutBorder.args = {
  title: "Card Title",
  cardBorder: false,
  showTitleSeparatorLine: true,
};

// export const TitleWithIcon = Template.bind({});
// TitleWithIcon.args = {
//   title: "Card Title",
//   showTitleSeparatorLine:true,
//   cardBorder:true
// };

export const CustomTitleView = CustomTitle.bind({});
CustomTitleView.args = {
  title: "Card Title",
  showTitleSeparatorLine:true,
  cardBorder:true
};

export const CustomStyling = CustomStyle.bind({});
CustomStyling.args = {
  title: "Card Title",
  showTitleSeparatorLine:true,
  cardBorder:true
};
