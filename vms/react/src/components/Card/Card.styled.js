import styled from "styled-components";

export const StyledCard = styled.div`
  border-radius: 3px;
  background-color: #ffffff;
  box-shadow: 0 2px 4px 0 rgb(0 0 0 / 9%);
  border: ${({ cardBorder }) => (cardBorder ? "solid 1px #f4f4f4" : "")};
`;

export const StyledTitleContainer = styled.div`
  padding: 16px 24px;
  display: flex;
  justify-content: flex-start;
  border: ${({ cardBorder,showSep }) => (cardBorder && showSep  ? "solid 1px #f4f4f4" : "")};
  border-bottom: ${({ cardBorder,showSep}) => (showSep ? "solid 1px #ebebec" : "")};
  text-align: left;
`;

export const TitleText = styled.span`
  font-weight: 700;
  font-family: ${({ theme }) => theme?.typography?.fontFamily || ""};
  font-size: 19px;
  line-height: 24px;
  letter-spacing: 0.3px;
  justify-content: center;
  align-self: center;
  color: #4d4d4f;
`;

export const Divider = styled.div`
  border-bottom: solid 1px #ebebec !important;
`;
