import React, { memo, useMemo } from "react";
import PropTypes from "prop-types";
import {
  Divider,
  StyledCard,
  StyledTitleContainer,
  TitleText,
} from "./Card.styled";
import { useClassName } from "../../hooks/useClassName";
import { ThemeWrapper } from "../Theme/ThemeContext";

// Utility function (placed before main component)
const isNonNull = (prop) => prop !== null && prop !== undefined;

const Card = memo((props) => {
  // 1. PROP DESTRUCTURING & VALIDATION
  const {
    children,
    title,
    showTitleSeparatorLine = true,
    cardBorder = true,
    customTitleView,
    additionalStyle,
    additionalClassName,
    titleClassName,
    isMobile,
    isMobileView,
    ...otherProps
  } = props;

  // 2. PERFORMANCE OPTIMIZATIONS
  const computedClassName = useMemo(
    () => useClassName(props, additionalClassName),
    [props, additionalClassName]
  );

  const computedTitleClassName = useMemo(
    () => useClassName(props, titleClassName),
    [props, titleClassName]
  );

  const hasCustomTitle = useMemo(
    () => isNonNull(customTitleView),
    [customTitleView]
  );
  const hasTitle = useMemo(() => isNonNull(title), [title]);

  // 4. CONDITIONAL RENDERING (memoized)
  const titleContent = useMemo(() => {
    if (hasCustomTitle) {
      return (
        <>
          {customTitleView}
          <Divider />
        </>
      );
    }

    if (hasTitle) {
      return (
        <StyledTitleContainer
          cardBorder={cardBorder}
          className={`${computedTitleClassName} vms_card_title`}
          showSep={showTitleSeparatorLine}
        >
          <TitleText className={`${computedTitleClassName} vms_card_text`}>
            {title}
          </TitleText>
        </StyledTitleContainer>
      );
    }

    return null;
  }, [
    hasCustomTitle,
    hasTitle,
    customTitleView,
    title,
    cardBorder,
    computedTitleClassName,
    showTitleSeparatorLine,
  ]);

  // 5. ERROR HANDLING
  React.useEffect(() => {
    if (hasCustomTitle && hasTitle) {
      console.warn(
        "Card: customTitleView will override title prop. Use only one of them."
      );
    }
  }, [hasCustomTitle, hasTitle]);

  return (
    <ThemeWrapper isMobile={isMobile || isMobileView || false}>
      <StyledCard
        className={`${computedClassName} vms_card_container`}
        style={additionalStyle}
        cardBorder={cardBorder}
        role="region"
        aria-label={title}
        {...otherProps}
      >
        {titleContent}
        {children}
      </StyledCard>
    </ThemeWrapper>
  );
});

// Component display name for debugging
Card.displayName = "Card";

Card.defaultProps = {
  showTitleSeparatorLine: true,
  cardBorder: true,
};
Card.propTypes = {
  /**
   * Title. If you pass this value then @property customTitleView will be ignored
   */
  title: PropTypes.string,

  /**
   *  It will display separator line below title. Default value true
   */
  showTitleSeparatorLine: PropTypes.bool,

  /**
   * It will add border to card container . Default value true
   */
  cardBorder: PropTypes.bool,
  /**
   * Body of the card component
   */
  children: PropTypes.oneOfType([PropTypes.node, PropTypes.string]),

  // titleProps:PropTypes

  /**
   * Custom title view. Please don't pass value of @property title with it.
   */
  customTitleView: PropTypes.oneOfType([PropTypes.node]),

  /**
   * Property to append additional css class
   */
  additionalClassName: PropTypes.arrayOf(PropTypes.string),

  /**
   * Property to append additional css class to Title container
   */
  titleClassName: PropTypes.arrayOf(PropTypes.string),

  /**
   * Property to set the `inline style` object on Breadcrumb
   */
  additionalStyle: PropTypes.object,
};

export { Card };
