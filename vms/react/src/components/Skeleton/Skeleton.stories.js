import React, { Fragment, useEffect, useState } from "react";
import { Carousel } from "../Carousel/Carousel";
import { Skeleton } from "./Skeleton";

export default {
  title: "VMS_REACT/Skeleton",
  component: Skeleton,
  argTypes: {
    additionalStyle: { control: { type: "" } },
    additionalClassName: { control: { type: "" } },
  },
};

export const Rectangular = (props) => {
  return (
    <Skeleton {...props} width={500} height={40} variant={"rectangular"} />
  );
};

export const Rounded = (props) => {
  return (
    <Skeleton width={500} height={40} variant={"rounded"} animation={"pulse"} />
  );
};

export const Circular = (props) => {
  return (
    <Skeleton width={60} height={60} variant={"circular"} animation={"pulse"} />
  );
};

export const InferringDimension = (props) => {
  return (
    <Skeleton variant={"rectangular"}>
      <div style={{ width: "50px", height: "40px" }} animation={"pulse"} />
    </Skeleton>
  );
};
InferringDimension.parameters = {
  docs: {
    description: {
      story:
        "In addition to accepting width and height props, the component can also infer the dimensions. But when it comes to other components, you may not want to repeat the width and height. In these instances, you can pass children and it will infer its width and height from them",
    },
  },
};

export const Animations = (props) => {
  const animations = ["pulse", "wave", "none"];
  return (
    <div style={{ display: "flex", flexDirection: "column", gap: "10px" }}>
      {animations.map((animation) => {
        return (
          <div key={animation}>
            <span style={{ padding: "30px 0px" }}>
              <span style={{ color: "#FF0303", fontWeight: "bold" }}>
                Note:
              </span>{" "}
              Animation <b>{animation}</b>
            </span>
            <Skeleton width={"500px"} height={"40px"} animation={animation} />
          </div>
        );
      })}
    </div>
  );
};
Animations.parameters = {
  docs: {
    description: {
      story:
        "By default, the skeleton pulsates, but you can change the animation to a wave or disable it entirely",
    },
  },
};

export const PulsateExample = (props) => {
  const viewDetails = [
    {
      image:
        "https://i.ytimg.com/vi/pLqipJNItIo/hqdefault.jpg?sqp=-oaymwEYCNIBEHZIVfKriqkDCwgBFQAAiEIYAXAB&rs=AOn4CLBkklsyaw9FxDmMKapyBYCn9tbPNQ",
      title: "Don Diablo @ Tomorrowland Main Stage 2019 | Official…",
      subtile: "Don Diablo",
      subtitle1: "396k views • a week ago",
    },
    {
      image:
        "https://i.ytimg.com/vi/_Uu12zY01ts/hqdefault.jpg?sqp=-oaymwEZCPYBEIoBSFXyq4qpAwsIARUAAIhCGAFwAQ==&rs=AOn4CLCpX6Jan2rxrCAZxJYDXppTP4MoQA",
      title: "Queen - Greatest Hits",
      subtile: "Queen Official",
      subtitle1: "40M views • 3 years ago",
    },
    {
      image:
        "https://i.ytimg.com/vi/kkLk2XWMBf8/hqdefault.jpg?sqp=-oaymwEYCNIBEHZIVfKriqkDCwgBFQAAiEIYAXAB&rs=AOn4CLB4GZTFu1Ju2EPPPXnhMZtFVvYBaw",
      title: "Calvin Harris, Sam Smith - Promises (Official Video)",
      subtile: "Calvin Harris",
      subtitle1: "130M views • 10 months ago",
    },
  ];
  return (
    <div>
      <div
        id="skeleton"
        style={{
          display: "flex",
          width: "60%",
          gap: "20px",
          overflow: "hidden",
          height: "200px",
        }}
      >
        {viewDetails.map((viewDetail, index) => {
          return (
            <div
              key={viewDetail.title}
              style={{
                display: "flex",
                flexDirection: "column",
                width: "210px",
                height: "118px",
                gap: "10px",
              }}
            >
              <Skeleton width={210} height={118} />
              <Skeleton width={210} height={20} />
              <Skeleton width={105} height={20} />
            </div>
          );
        })}
      </div>
      <div
        id="view"
        style={{
          display: "flex",
          width: "60%",
          gap: "20px",
          overflow: "hidden",
          height: "250px",
        }}
      >
        {viewDetails.map((viewDetail, index) => {
          return (
            <div
              key={viewDetail.title}
              style={{
                display: "flex",
                flexDirection: "column",
                width: "210px",
                height: "118px",
              }}
            >
              <img width={210} height={118} src={viewDetail.image} />
              <div
                style={{
                  display: "flex",
                  flexDirection: "column",
                  gap: "8px",
                }}
              >
                <p>{viewDetail.title}</p>
                <span style={{ color: "#00000099", fontSize: "16px" }}>
                  {viewDetail.subtile}
                </span>
                <span style={{ color: "#00000099", fontSize: "14px" }}>
                  {viewDetail.subtitle1}
                </span>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export const WaveExample = (props) => {
    const [loading,setLoading] = useState(true);
    let timer1 = setTimeout(() => setLoading(false), 2000);
    useEffect(() => {
        return () => {
          clearTimeout(timer1);
        }
      },[loading]);
  return (
    <div style={{ display: "flex", flexDirection: "row", gap: "20px" }}>
      <div style={{ width: "35%" }}>
        <CardViewContent loading={loading} />
      </div>
      {/* <div style={{ width: "35%" }}>
        <CardViewContent loading={false} />
      </div> */}
    </div>
  );
};
const CardViewContent = ({ loading }) => {
  return (
    <div
      id="card"
      style={{
        backgroundColor: "#fff",
        color: "rgba(0, 0, 0, 0.87)",
        boxShadow:
          "0px 2px 1px -1px rgb(0 0 0 / 20%), 0px 1px 1px 0px rgb(0 0 0 / 14%), 0px 1px 3px 0px rgb(0 0 0 / 12%",
        borderRadius: "4px",
      }}
    >
      <div
        id="card-header"
        style={{ display: "flex", alignItems: "center", padding: "16px" }}
      >
        <div style={{ flex: "0 0 auto", marginRight: "16px" }}>
          {loading ? (
            <Skeleton
              animation="wave"
              variant="circular"
              width={50}
              height={60}
            />
          ) : (
            <img
              width={40}
              height={40}
              style={{ borderRadius: "50%" }}
              src="https://pbs.twimg.com/profile_images/877631054525472768/Xp5FAPD5_reasonably_small.jpg"
            />
          )}
        </div>
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            flex: "1 1 auto",
          }}
        >
          {loading ? (
            <Skeleton
              animation="wave"
              height={10}
              width="80%"
              additionalStyle={{ marginBottom: 6 }}
            />
          ) : (
            <span>Ted</span>
          )}
          {loading ? (
            <Skeleton animation="wave" height={10} width="40%" />
          ) : (
            <span>5 hours ago</span>
          )}
        </div>

        <div style={{ display: "flex", flex: "0 0 auto" }}>
          {loading ? null : <ActionIcon />}
        </div>
      </div>
      {loading ? (
        <Skeleton animation="pulse" variant="rectangular" height={180} />
      ) : (
        <div id="card-body">
          <img
            height={180}
            style={{
              width: "100%",
              backgroundPosition: "center",
              objectFit: "cover",
              backgroundSize: "cover",
              backgroundRepeat: "no-repeat",
            }}
            src="https://pi.tedcdn.com/r/talkstar-photos.s3.amazonaws.com/uploads/72bda89f-9bbf-4685-910a-2f151c4f3a8a/NicolaSturgeon_2019T-embed.jpg?w=512"
            alt="Nicola Sturgeon on a TED talk stage"
          />
        </div>
      )}
      <div id="card-bottom" style={{ padding: "16px", paddingBottom: "24px" }}>
        {loading ? (
          <Fragment>
            <Skeleton
              animation="wave"
              height={10}
              additionalStyle={{ marginBottom: 6 }}
            />
            <Skeleton animation="wave" height={10} width="80%" />
          </Fragment>
        ) : (
          <p style={{ color: "#00000099" }}>
            Why First Minister of Scotland Nicola Sturgeon thinks GDP is the
            wrong measure of a country's success:
          </p>
        )}
      </div>
    </div>
  );
};
const ActionIcon = () => {
  return (
    <svg
      width={"1em"}
      height={"1em"}
      focusable="false"
      aria-hidden="true"
      viewBox="0 0 24 24"
      data-testid="MoreVertIcon"
    >
      <path d="M12 8c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm0 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"></path>
    </svg>
  );
};
