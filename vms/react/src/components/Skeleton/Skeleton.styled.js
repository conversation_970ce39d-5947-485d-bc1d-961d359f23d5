import styled, { keyframes } from "styled-components";

const keyFramePulse = keyframes`
0% {
    opacity: 1;
}
50% {
    opacity: 0.4;
}
100% {
    opacity: 1;
}
`;

const waveAnimation = keyframes`
0% {
    -webkit-transform: translateX(-100%);
    -moz-transform: translateX(-100%);
    -ms-transform: translateX(-100%);
    transform: translateX(-100%);
  }

50% {
  -webkit-transform: translateX(100%);
  -moz-transform: translateX(100%);
  -ms-transform: translateX(100%);
  transform: translateX(100%);
}

100% {
  -webkit-transform: translateX(100%);
  -moz-transform: translateX(100%);
  -ms-transform: translateX(100%);
  transform: translateX(100%);
}
`;

export const SkeletonPulse = styled.span`
  display: block;
  width: ${({ width, isChildren }) =>
    isChildren === false && Number.isInteger(width) ? `${width}px` : width};
  height: ${({ height, isChildren }) =>
    isChildren === true
      ? `auto`
      : Number.isInteger(height)
      ? `${height}px`
      : height};
  border-radius: ${({ theme, variant }) =>
    variant === "rounded"
      ? theme.shape.borderRadius
      : variant === "circular"
      ? "50%"
      : `0px`};
  background-color: #0000001c;
  animation-name: ${keyFramePulse};
  animation-play-state: running;
  animation-duration: 1.5s;
  animation-timing-function: ease-in-out;
  animation-delay: 0.5s;
  animation-iteration-count: infinite;
  animation-direction: normal;
  animation-fill-mode: none;
  ${({ isChildren }) => isChildren === true && `max-width: fit-content`};
  margin-top: 0;
  margin-bottom: 0;
`;

export const SkeletonWave = styled.span`
  display: block;
  transform-origin: 0 55%;
  transform: scale(1, 0.6);
  width: ${({ width, isChildren }) =>
    isChildren === false && Number.isInteger(width) ? `${width}px` : width};
  height: ${({ height, isChildren }) =>
    isChildren === true
      ? `auto`
      : Number.isInteger(height)
      ? `${height}px`
      : height};
  border-radius: ${({ theme, variant }) =>
    variant === "rounded"
      ? theme.shape.borderRadius
      : variant === "circular"
      ? "50%"
      : `0px`};
  background-color: #0000001c;
  position: relative;
  overflow: hidden;
  margin-top: 0;
  margin-bottom: 0;
  ::before {
  }
  ::after {
    animation-name: ${waveAnimation};
    animation-duration: 1.6s;
    animation-timing-function: linear;
    animation-delay: 0.5s;
    animation-iteration-count: infinite;
    animation-direction: normal;
    animation-fill-mode: none;
    animation-play-state: running;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(0, 0, 0, 0.04),
      transparent
    );
    transform: translateX(-100%);
    content: "";
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    top: 0;
  }
`;

export const SkeletonNone = styled.span`
  display: block;
  transform-origin: 0 55%;
  transform: scale(1, 0.6);
  width: ${({ width, isChildren }) =>
    isChildren === false && Number.isInteger(width) ? `${width}px` : width};
  height: ${({ height, isChildren }) =>
    isChildren === true
      ? `auto`
      : Number.isInteger(height)
      ? `${height}px`
      : height};
  border-radius: ${({ theme, variant }) =>
    variant === "rounded"
      ? theme.shape.borderRadius
      : variant === "circular"
      ? "50%"
      : `0px`};
  background-color: #0000001c;
  margin-top: 0;
  margin-bottom: 0;
`;
