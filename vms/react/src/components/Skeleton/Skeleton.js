import React, { Fragment, memo, useMemo } from "react";
import PropTypes from "prop-types";
import { useClassName } from "../../hooks/useClassName";
import { SkeletonWave, SkeletonPulse, SkeletonNone } from "./Skeleton.styled";
import { ThemeWrapper } from "../Theme/ThemeContext";

// Utility function (placed before main component)
const isNonNull = (prop) => prop !== null && prop !== undefined;

export const Skeleton = memo((props) => {
  // 1. PROP DESTRUCTURING & VALIDATION
  const {
    animation = "pulse",
    variant = "rectangular",
    height,
    width,
    additionalClassName,
    additionalStyle,
    children,
    isMobile,
    isMobileView,
    ...otherProps
  } = props;

  // 2. PERFORMANCE OPTIMIZATIONS
  const computedClassName = useMemo(
    () => useClassName(props, additionalClassName),
    [props, additionalClassName]
  );

  const hasChildren = useMemo(() => isNonNull(children), [children]);

  const skeletonProps = useMemo(
    () => ({
      className: `${computedClassName} vms_skeleton`,
      style: additionalStyle,
      height,
      width,
      animation,
      variant,
      isChildren: hasChildren,
      ...otherProps,
    }),
    [
      computedClassName,
      additionalStyle,
      height,
      width,
      animation,
      variant,
      hasChildren,
      otherProps,
    ]
  );

  // 4. CONDITIONAL RENDERING (memoized)
  const skeletonContent = useMemo(() => {
    const SkeletonComponent = {
      pulse: SkeletonPulse,
      wave: SkeletonWave,
      none: SkeletonNone,
    }[animation] || SkeletonPulse;

    return (
      <SkeletonComponent {...skeletonProps}>
        {children}
      </SkeletonComponent>
    );
  }, [animation, skeletonProps, children]);

  // 5. ERROR HANDLING
  React.useEffect(() => {
    if (!["pulse", "wave", "none"].includes(animation)) {
      console.warn(
        `Skeleton: Invalid animation "${animation}". Using "pulse" as fallback.`
      );
    }
    if (!["circular", "rectangular", "rounded"].includes(variant)) {
      console.warn(
        `Skeleton: Invalid variant "${variant}". Using "rectangular" as fallback.`
      );
    }
  }, [animation, variant]);

  return (
    <ThemeWrapper isMobile={isMobile || isMobileView || false}>
      <Fragment>{skeletonContent}</Fragment>
    </ThemeWrapper>
  );
});

// Component display name for debugging
Skeleton.displayName = "Skeleton";

Skeleton.defaultProps = {
  animation: "pulse",
  variant: "rectangular",
  height: null,
  width: null,
  additionalClassName: null,
  additionalStyle: null,
  children: null,
};

Skeleton.propTypes = {
  /**
   * The animation. If `none ` the animation effect is disabled.
   */
  animation: PropTypes.oneOf(["pulse", "wave", "none"]),
  /**
   * The type of content that will be rendered.
   */
  variant: PropTypes.oneOf(["circular", "rectangular", "rounded"]),
  /**
   * Height of the skeleton.
   * **Note:** px will be considered for number type
   */
  height: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
  /**
   * Width of the skeleton
   * **Note:** px will be considered for number type
   */
  width: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
  /**
   * Optional children to infer width and height from.
   */
  children: PropTypes.node,
  /**
   * Property to append additional css class
   */
  additionalClassName: PropTypes.arrayOf(PropTypes.string),
  /**
   * Property to set the `inline style` object on skeleton
   */
  additionalStyle: PropTypes.object,
};
