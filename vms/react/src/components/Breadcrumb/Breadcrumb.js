import React, { memo, useCallback, useMemo } from "react";
import PropTypes from "prop-types";
import seperatorsvg from "../../assets/images/Breadcrumb/seperator.svg";
import { StyledBreadcrumb, StyledBreadcrumbElement, StyledSeperator } from "./Breadcrumb.styled";
import { useClassName } from "../../hooks/useClassName";
import { ThemeWrapper } from "../Theme/ThemeContext";

// Utility function (placed before main component)
const isNonNull = (prop) => prop !== null && prop !== undefined;

/**
 * Breadcrumb navigation component
 */
const Breadcrumb = memo((props) => {
  // 1. PROP DESTRUCTURING & VALIDATION
  const {
    breadcrumbData = [],
    customSeparator,
    additionalClassName,
    additionalStyle,
    isMobile,
    isMobileView,
    ...otherProps
  } = props;

  // 2. PERFORMANCE OPTIMIZATIONS
  const computedClassName = useMemo(() => 
    useClassName(props, additionalClassName), 
    [props, additionalClassName]
  );

  const separatorSrc = useMemo(() => 
    customSeparator || seperatorsvg, 
    [customSeparator]
  );

  // 3. EVENT HANDLING with useCallback
  const handleBreadcrumbClick = useCallback((data, index) => {
    if (data.isReactRoute || data.path) {
      window.open(`${data.path}`, "_self");
    }
  }, []);

  const handleKeyDown = useCallback((event, data, index) => {
    if (event.key === 'Enter' || event.key === ' ') {
      handleBreadcrumbClick(data, index);
    }
  }, [handleBreadcrumbClick]);

  // 4. CONDITIONAL RENDERING (memoized)
  const breadcrumbItems = useMemo(() => {
    if (!breadcrumbData || breadcrumbData.length === 0) {
      return null;
    }

    return breadcrumbData.map((data, index) => {
      const isLast = index + 1 === breadcrumbData.length;
      const isClickable = data.isReactRoute || data.path;

      if (isLast) {
        return (
          <StyledBreadcrumbElement
            key={index}
            className="vms_breadcrumb_li_active"
            onClick={() => handleBreadcrumbClick(data, index)}
            role={isClickable ? "button" : "text"}
            tabIndex={isClickable ? 0 : -1}
            onKeyDown={(e) => handleKeyDown(e, data, index)}
            aria-current="page"
          >
            {data.caption}
          </StyledBreadcrumbElement>
        );
      }

      return (
        <React.Fragment key={index}>
          <StyledBreadcrumbElement
            className="vms_breadcrumb_li"
            onClick={() => handleBreadcrumbClick(data, index)}
            role={isClickable ? "button" : "text"}
            tabIndex={isClickable ? 0 : -1}
            onKeyDown={(e) => handleKeyDown(e, data, index)}
          >
            {data.caption}
          </StyledBreadcrumbElement>
          <StyledSeperator 
            src={separatorSrc} 
            alt="Breadcrumb separator"
            role="presentation"
          />
        </React.Fragment>
      );
    });
  }, [breadcrumbData, separatorSrc, handleBreadcrumbClick, handleKeyDown]);

  // 5. ERROR HANDLING
  React.useEffect(() => {
    if (!breadcrumbData || breadcrumbData.length === 0) {
      console.warn('Breadcrumb: breadcrumbData prop is required and should contain at least one item');
    }
    
    breadcrumbData?.forEach((item, index) => {
      if (!item.caption) {
        console.warn(`Breadcrumb: Item at index ${index} is missing required caption property`);
      }
    });
  }, [breadcrumbData]);

  return (
    <ThemeWrapper isMobile={isMobile || isMobileView || false}>
      <StyledBreadcrumb
        className={`${computedClassName} vms_breadcrumb`}
        style={additionalStyle}
        role="navigation"
        aria-label="Breadcrumb navigation"
        {...otherProps}
      >
        {breadcrumbItems}
      </StyledBreadcrumb>
    </ThemeWrapper>
  );
});

// Component display name for debugging
Breadcrumb.displayName = 'Breadcrumb';



Breadcrumb.propTypes = {

  /**
  * Array of items for Breadcrumb Data
  */
  breadcrumbData: PropTypes.arrayOf(
    PropTypes.shape({
      caption: PropTypes.string,
      path: PropTypes.string,
      isReactRoute: PropTypes.bool.isRequired
    })
  ),

  /**
  * Property to add custom seperator 
  */
  customSeparator: PropTypes.string,

  /**
 * Property to append additional css class
 */
  additionalClassName: PropTypes.arrayOf(PropTypes.string),

  /**
   * Property to set the `inline style` object on Breadcrumb
   */
  additionalStyle: PropTypes.object,
}

export { Breadcrumb };