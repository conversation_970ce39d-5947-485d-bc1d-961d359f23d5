import styled, { css, keyframes } from "styled-components";

function _fontWeight(theme) {
  return theme?.typography?.text?.fontWeight || "";
}
function _fontFamily(theme) {
  return theme?.typography?.fontFamily || "";
}
function _fontSize(theme) {
  return theme?.typography?.text?.fontSize?.desktop || "";
}
function _lineHeight(theme) {
  return theme?.typography?.lineHeight || "";
}
function _letterSpacing(theme) {
  return theme?.typography?.letterSpacing || "";
}

export const StyledBreadcrumb = styled.ul`
  width:100%;
  font-family: ${({ theme }) => _fontFamily(theme)};
  font-size: ${({ theme }) => _fontSize(theme)};
  font-weight: ${({ theme }) => _fontWeight(theme)};
  font-style: normal;
  font-stretch: normal;
  line-height: ${({ theme }) => _lineHeight(theme)};
  letter-spacing: ${({ theme }) => _letterSpacing(theme)};
  display:flex;
  flex-direction:row;
  min-height: 40px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  padding: 0;
  color: #009fff;
  &:first-child {
      padding-left:16px
    };
`


export const StyledBreadcrumbElement = styled.li`
  width:auto;
  font-family: ${({ theme }) => _fontFamily(theme)};
  font-size: ${({ theme }) => _fontSize(theme)};
  font-weight: ${({ theme }) => _fontWeight(theme)};
  font-style: normal;
  font-stretch: normal;
  line-height: ${({ theme }) => _lineHeight(theme)};
  letter-spacing: ${({ theme }) => _letterSpacing(theme)};
  display: inline-block;
  position: relative;
  border-left: none;
  border-right: none;
  cursor: default;
  padding: 12px;
  
  &:last-child {
      color: grey
    }
`

export const StyledSeperator = styled.img`
  color: ${({ theme }) => theme.palette.primary.main};
  background-color: transparent;
  fill: ${({ theme }) => theme.palette.primary.main};
  stroke: ${({ theme }) => theme.palette.primary.main};
`;
