import React from "react";

import { Breadcrumb } from "./Breadcrumb";

export default {
  title: "VMS_REACT/Breadcrumb",
  component: Breadcrumb,
  argTypes: {
    additionalStyle: { control: { type: "" } },
    additionalClassName: { control: { type: "" } },
    customSeparator: { control: { type: "" } },
    breadcrumbData:{ control: { type: "" } },
  },
};

const Template = (args) => <Breadcrumb {...args}>Breadcrumb</Breadcrumb>;
export const Default = Template.bind({});
Default.args = {
  breadcrumbData: [{
    caption: "About Vernost",
    path: "/",
    isReactRoute: false
  },
  {
    caption: "About the Company",
    path: "/",
    isReactRoute: false
  },
],
};

export const CustomCss = Template.bind({});
CustomCss.args = {
  breadcrumbData: [{
    caption: "About Vernost",
    path: "/",
    isReactRoute: false
  },
  {
    caption: "About the Company",
    path: "/",
    isReactRoute: false
  },
],
additionalStyle:{
  color:"red"
}


};