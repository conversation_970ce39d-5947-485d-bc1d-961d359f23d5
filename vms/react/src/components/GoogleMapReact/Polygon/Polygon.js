import PropTypes from "prop-types";
import React, { Fragment, useEffect } from "react";
import { useMap } from "../utils/hooks/useMap";

export const Polygon = ({
  paths,
  strokeColor,
  strokeOpacity,
  strokeWeight,
  fillColor,
  fillOpacity,
  visible,
  zIndex,
}) => {
  const { map } = useMap();

  useEffect(() => {
    const polygonObj = new google.maps.Polygon({
      map: map,
      paths: paths,
      strokeColor: strokeColor,
      strokeOpacity: strokeOpacity,
      strokeWeight: strokeWeight,
      fillColor: fillColor,
      fillOpacity: fillOpacity,
      visible: visible,
      zIndex: zIndex,
    });
  }, [map]);

  return <Fragment></Fragment>;
};

Polygon.defaultProps = {
  visible: true,
};

Polygon.propTypes = {
  /**
   * The ordered sequence of coordinates that designates a closed loop. Unlike polylines, a polygon may consist of one or more paths. As a result, the paths property may specify one or more arrays of coordinates. Paths are closed automatically; do not repeat the first vertex of the path as the last vertex. Simple polygons may be defined using a single array of s. More complex polygons may specify an array of arrays. Any simple arrays are converted into s. Inserting or removing s from the will automatically update the polygon on the map.
   */
  paths: PropTypes.arrayOf(
    PropTypes.shape({
      lat: PropTypes.number,
      lng: PropTypes.number,
    })
  ).isRequired,

  /**
   * The stroke color. All CSS3 colors are supported except for extended named colors.
   */
  strokeColor: PropTypes.string,

  /**
   * The stroke opacity between 0.0 and 1.0
   */
  strokeOpacity: PropTypes.number,

  /**
   * The stroke width in pixels.
   */
  strokeWeight: PropTypes.number,

  /**
   * The fill color. All CSS3 colors are supported except for extended named colors.
   */
  fillColor: PropTypes.string,

  /**
   * The fill opacity between 0.0 and 1.0
   */
  fillOpacity: PropTypes.number,

  /**
   * Whether this polygon is visible on the map.
   * @default true
   */
  visible: PropTypes.bool,

  /**
   * The zIndex compared to other polys.
   */
  zIndex: PropTypes.number,
};
