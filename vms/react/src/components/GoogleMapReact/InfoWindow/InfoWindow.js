import PropTypes from "prop-types";
import React, { Fragment, useEffect, useRef } from "react";
import { createRoot } from "react-dom/client";
import { useMap } from "../utils/hooks/useMap";

export const InfoWindow = ({
  children,
  position,
  pixelOffset,
  maxWidth,
  minWidth,
  ariaLabel,
  zIndex,
}) => {
  const { map } = useMap();
  const markerRef = useRef();
  const rootRef = useRef();

  useEffect(() => {
    if (!rootRef.current) {
      const container = document.createElement("div");
      rootRef.current = createRoot(container);
      const infoWindoObj = new google.maps.InfoWindow({
        content: container,
        position: position,
        pixelOffset: pixelOffset,
        maxWidth: maxWidth,
        minWidth: minWidth,
        ariaLabel: ariaLabel,
        zIndex: zIndex,
      });
      markerRef.current = infoWindoObj;
    }
  }, []);

  useEffect(() => {
    rootRef.current.render(children);
    markerRef.current.open({ map });
  }, [map, children]);

  return <Fragment></Fragment>;
};

InfoWindow.defaultProps = {
  pixelOffset: null,
  maxWidth: null,
  minWidth: null,
  ariaLabel: null,
};

InfoWindow.propTypes = {
  /**
   * The LatLng at which to display this InfoWindow
   */
  position: PropTypes.shape({
    lat: PropTypes.number,
    lng: PropTypes.number,
  }).isRequired,

  /**
   * The offset, in pixels, of the tip of the info window from the point on the map at whose geographical coordinates the info window is anchored.
   * If an InfoWindow is opened with an anchor, the will be calculated from the anchor's property.
   */
  pixelOffset: PropTypes.shape({
    width: PropTypes.number,
    height: PropTypes.number,
  }),

  /**
   * All InfoWindows are displayed on the map in order of their zIndex, with higher values displaying in front of InfoWindows with lower values. By default, InfoWindows are displayed according to their latitude, with InfoWindows of lower latitudes appearing in front of InfoWindows at higher latitudes. InfoWindows are always displayed in front of markers
   */
  zIndex: PropTypes.number,

  /**
   * Maximum width of the InfoWindow, regardless of content's width
   */
  maxWidth: PropTypes.number,

  /**
   * Minimum width of the InfoWindow, regardless of content's width
   */
  minWidth: PropTypes.number,

  /**
   * AriaLabel to assign to the InfoWindow.
   */
  ariaLabel: PropTypes.string,
};
