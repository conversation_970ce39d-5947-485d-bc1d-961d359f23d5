import { Meta, <PERSON>vas } from "@storybook/addon-docs";
import packageJson from "../../../../../../package.json";

<Meta title="VMS_REACT/Google-Map/Overview Document" />

# Google-Map React

## Description

In all the google map react components we are internally using JavaScript API's of google-map. <a target="_blank" href='https://developers.google.com/maps/documentation/javascript/examples'>For more detail on javascript google-map API's</a>

## Usage

<ul>
  <li>Wrap the component with a div. Note: It is mandatory to set width and height of the div otherwise googl-map will not be visible</li>
</ul>

<pre>
  <code>
    {`
import { GoogleMapWrapper, GoogleMap } from '${packageJson.name}/dist/GoogleMapReact'

function App() {
  return (
    <div style={{height:'80vh', width:'100%'}}>
    <GoogleMapWrapper  
        apiKey={"<your api key>"}
        libraries={["marker"]}>
      <GoogleMap>
        Here you can place various other Google-Map component. Please refer each stories for more details
      </GoogleMap>
    </GoogleMapWrapper>
    <div>
  );
}

`}
  </code>
</pre>
