import React, { Fragment, useState } from "react";
import { GoogleMapWrapper } from "../GoogleMapWrapper/GoogleMapWrapper";
import { Button } from "../../Button/Button";
import styles from "./GoogleMap.stories.module.css";
import { InfoWindow } from "../InfoWindow/InfoWindow";
import { GoogleMap } from "../GoogleMap/GoogleMap";
import { AdvanceMarker } from "../AdvanceMarker/AdvanceMarker";
import { Carousel } from "../../Carousel/Carousel";
import { RatingBar } from "../../RatingBar/RatingBar";
import { Theme } from "../../Theme/ThemeContext";
import { Polygon } from "../Polygon/Polygon";
import { Overlay } from "../../ModalOverlay/ModalOverlay.styled";
import { Skeleton } from "../../Skeleton/Skeleton";

export default {
  title: "VMS_REACT/Google-Map",
  component: GoogleMapWrapper,
  subcomponents: {
    GoogleMapWrapper,
    GoogleMap,
    AdvanceMarker,
    InfoWindow,
    Polygon,
  },
  argTypes: {
    additionalStyle: { control: { type: "" } },
    additionalClassName: { control: { type: "" } },
  },
};

const MarkerCustomView = ({
  highlightedMarker,
  markerInfo,
  onMouseEnter,
  onMouseLeave,
}) => {
  const onMouseEnterHandler = (e) => {
    e.stopPropagation();
    onMouseEnter(markerInfo);
  };
  const onMouseLeaveHandler = (e) => {
    e.stopPropagation();
    onMouseLeave(null);
  };

  const isHighligted = highlightedMarker?.id == markerInfo.id;
  return (
    <Fragment>
      <Theme>
        <div
          className={`${styles.marker} ${isHighligted ? styles.markerHiglighted : ``
            }`}
          onMouseEnter={onMouseEnterHandler}
        >
          <div
            className={`${styles.ce89cb} ${styles.S2WJKd} ${isHighligted ? styles.S2WJKdHigh : ``
              }`}
          >
            <div className={styles.P3iUhe}>
              <div
                className={`${styles.icon} ${isHighligted ? styles.iconHighlighted : ``
                  }`}
              >
                <svg
                  className={styles.hj759e}
                  height="18px"
                  viewBox="0 0 24 24"
                  width="18px"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path d="M0 0h24v24H0V0z" fill="none"></path>
                  <path
                    className={`${styles.dJ0wg} ${isHighligted ? styles.dJ0wgHigh : ``
                      }`}
                    d="M7 14c1.66 0 3-1.34 3-3S8.66 8 7 8s-3 1.34-3 3 1.34 3 3 3zm12-7h-8v8H3V5H1v15h2v-3h18v3h2v-9c0-2.21-1.79-4-4-4z"
                  ></path>
                </svg>
              </div>
              {markerInfo.hotelDetails.price}
            </div>
          </div>
          <div className={styles.P5LfNc}>
            <svg
              className={`${styles.P5LfNc} ${styles.ggVq7c}`}
              height="10"
              viewBox="0 0 19 10"
              width="19"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                className={`${styles.PojWnc} ${isHighligted ? styles.PojWncHigh : ``
                  }`}
                clipRule="evenodd"
                d="M19 0H0V1C4.96608 1 6.48058 4.29808 7.56473 6.65901C8.16909 7.97511 8.63973 9 9.5 9C10.36 9 10.8296 7.97578 11.4327 6.66029C12.5153 4.29927 14.028 1 19 1V0Z"
                fillRule="evenodd"
              ></path>
              <path
                className={styles.cSrhvb}
                clipRule="evenodd"
                d="M4.69717 2.34115C3.53936 1.5261 2.02621 1 0 1L0 2C1.84101 2 3.14853 2.4739 4.12153 3.15885C5.10147 3.84869 5.77723 4.77728 6.31171 5.74226C6.57907 6.22496 6.80787 6.71064 7.02314 7.17795C7.05209 7.24079 7.08089 7.30353 7.10958 7.36602C7.29114 7.76148 7.46816 8.14704 7.64789 8.48485C7.85468 8.87352 8.08687 9.24233 8.37256 9.51681C8.66993 9.80251 9.04121 10 9.5 10C9.95883 10 10.33 9.80247 10.6273 9.51666C10.9128 9.24211 11.1447 8.87323 11.3512 8.48457C11.5304 8.14712 11.707 7.762 11.888 7.367C11.9169 7.30409 11.9458 7.24094 11.9749 7.17769C12.1899 6.71041 12.4183 6.22475 12.6854 5.7421C13.2194 4.77721 13.8949 3.84869 14.8751 3.1589C15.8484 2.47397 17.1569 2 19 2V1C16.9719 1 15.4578 1.52603 14.2996 2.3411C13.1483 3.15131 12.3833 4.22279 11.8105 5.2579C11.5242 5.77525 11.2827 6.28959 11.0664 6.75981C11.0362 6.82545 11.0066 6.89001 10.9775 6.95348C10.7951 7.35123 10.6324 7.70599 10.468 8.01543C10.2761 8.37677 10.1036 8.63289 9.93415 8.79584C9.77641 8.94753 9.64204 9 9.5 9C9.35792 9 9.22337 8.94749 9.06537 8.79569C8.89569 8.63267 8.72296 8.37648 8.53072 8.01515C8.3659 7.70537 8.20275 7.35015 8.01982 6.95187C7.99087 6.88884 7.96143 6.82473 7.9314 6.75955C7.7148 6.28936 7.47302 5.77504 7.18649 5.25774C6.61322 4.22272 5.84804 3.15131 4.69717 2.34115Z"
                fillRule="evenodd"
              ></path>
            </svg>
          </div>
          {highlightedMarker?.id == markerInfo.id && (
            <div
              id="hotel detailinfo"
              className={styles.hotelDetailInfoView}
              style={{
                position: "absolute",
              }}
              onMouseLeave={onMouseLeaveHandler}
            >
              <div
                style={{
                  display: "flex",
                  flexDirection: "column",
                  gap: "15px",
                  overflow: "auto",
                }}
              >
                <div id="carousel-div">
                  <Carousel
                    responsive={{
                      desktop: {
                        items: 1,
                        partialVisibilityGutter: 0,
                      },
                    }}
                    autoPlay
                    arrows={false}
                    deviceType="desktop"
                    showDots={true}
                  >
                    {markerInfo.hotelDetails.hotelimages.map((image, index) => {
                      return (
                        <div key={`hotel-img-${index}`}>
                          <img src={image.imsu} height={90} width={"100%"} />
                        </div>
                      );
                    })}
                  </Carousel>
                </div>
                <div
                  style={{ display: "flex", flexDirection: "row", gap: "5px" }}
                >
                  <div style={{ alignItems: "center" }}>
                    <RatingBar
                      ratings={markerInfo.hotelDetails.ratings}
                      componentsToShow="ratingBox"
                    />
                  </div>
                  <div>
                    <b>{`Reviews (${markerInfo.hotelDetails.reviews.length})`}</b>
                  </div>
                </div>
                <div>
                  <b>{markerInfo.hotelDetails.name}</b>
                </div>
              </div>
            </div>
          )}
        </div>
      </Theme>
    </Fragment>
  );
};

const MarkerCustomViewMobile = ({
  selectedMarker,
  markerInfo,
  onModalOpen,
  onMouseEnter,
  onModalClose,
}) => {
  const onModelOpenHandler = (e) => {
    e.stopPropagation();
    onModalOpen(markerInfo);
  };
  const onModalCloseHandler = (e) => {
    e.stopPropagation();
    onModalClose(null);
  };
  const isHighligted = selectedMarker?.id == markerInfo.id;
  return (
    <Fragment>
      <Theme>
        <div
          className={`${styles.marker} ${isHighligted ? styles.markerHiglighted : ``
            }`}
          onClick={onModelOpenHandler}
        >
          <div
            className={`${styles.ce89cb} ${styles.S2WJKd} ${isHighligted ? styles.S2WJKdHigh : ``
              }`}
          >
            <div className={styles.P3iUhe}>
              <div
                className={`${styles.icon} ${isHighligted ? styles.iconHighlighted : ``
                  }`}
              >
                <svg
                  className={styles.hj759e}
                  height="18px"
                  viewBox="0 0 24 24"
                  width="18px"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path d="M0 0h24v24H0V0z" fill="none"></path>
                  <path
                    className={`${styles.dJ0wg} ${isHighligted ? styles.dJ0wgHigh : ``
                      }`}
                    d="M7 14c1.66 0 3-1.34 3-3S8.66 8 7 8s-3 1.34-3 3 1.34 3 3 3zm12-7h-8v8H3V5H1v15h2v-3h18v3h2v-9c0-2.21-1.79-4-4-4z"
                  ></path>
                </svg>
              </div>
              {markerInfo.hotelDetails.price}
            </div>
          </div>
          <div className={styles.P5LfNc}>
            <svg
              className={`${styles.P5LfNc} ${styles.ggVq7c}`}
              height="10"
              viewBox="0 0 19 10"
              width="19"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                className={`${styles.PojWnc} ${isHighligted ? styles.PojWncHigh : ``
                  }`}
                clipRule="evenodd"
                d="M19 0H0V1C4.96608 1 6.48058 4.29808 7.56473 6.65901C8.16909 7.97511 8.63973 9 9.5 9C10.36 9 10.8296 7.97578 11.4327 6.66029C12.5153 4.29927 14.028 1 19 1V0Z"
                fillRule="evenodd"
              ></path>
              <path
                className={styles.cSrhvb}
                clipRule="evenodd"
                d="M4.69717 2.34115C3.53936 1.5261 2.02621 1 0 1L0 2C1.84101 2 3.14853 2.4739 4.12153 3.15885C5.10147 3.84869 5.77723 4.77728 6.31171 5.74226C6.57907 6.22496 6.80787 6.71064 7.02314 7.17795C7.05209 7.24079 7.08089 7.30353 7.10958 7.36602C7.29114 7.76148 7.46816 8.14704 7.64789 8.48485C7.85468 8.87352 8.08687 9.24233 8.37256 9.51681C8.66993 9.80251 9.04121 10 9.5 10C9.95883 10 10.33 9.80247 10.6273 9.51666C10.9128 9.24211 11.1447 8.87323 11.3512 8.48457C11.5304 8.14712 11.707 7.762 11.888 7.367C11.9169 7.30409 11.9458 7.24094 11.9749 7.17769C12.1899 6.71041 12.4183 6.22475 12.6854 5.7421C13.2194 4.77721 13.8949 3.84869 14.8751 3.1589C15.8484 2.47397 17.1569 2 19 2V1C16.9719 1 15.4578 1.52603 14.2996 2.3411C13.1483 3.15131 12.3833 4.22279 11.8105 5.2579C11.5242 5.77525 11.2827 6.28959 11.0664 6.75981C11.0362 6.82545 11.0066 6.89001 10.9775 6.95348C10.7951 7.35123 10.6324 7.70599 10.468 8.01543C10.2761 8.37677 10.1036 8.63289 9.93415 8.79584C9.77641 8.94753 9.64204 9 9.5 9C9.35792 9 9.22337 8.94749 9.06537 8.79569C8.89569 8.63267 8.72296 8.37648 8.53072 8.01515C8.3659 7.70537 8.20275 7.35015 8.01982 6.95187C7.99087 6.88884 7.96143 6.82473 7.9314 6.75955C7.7148 6.28936 7.47302 5.77504 7.18649 5.25774C6.61322 4.22272 5.84804 3.15131 4.69717 2.34115Z"
                fillRule="evenodd"
              ></path>
            </svg>
          </div>
        </div>
      </Theme>
    </Fragment>
  );
};
const InfoWindoCustomView = ({ markerInfo }) => {
  return (
    <div
      id="hotel detailinfo"
      //className={styles.hotelDetailInfoView}
      style={{
        //position: "absolute",
        height: "200px",
        width: "200px",
      }}
    >
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          gap: "15px",
          overflow: "auto",
        }}
      >
        <div id="carousel-div">
          <Carousel
            responsive={{
              desktop: {
                items: 1,
                partialVisibilityGutter: 0,
              },
            }}
            autoPlay
            arrows={false}
            deviceType="desktop"
            showDots={true}
          // customTransition="all 1s ease-in-out"
          >
            {markerInfo.hotelDetails.hotelimages.map((image, index) => {
              return (
                <div key={`hotel-img-${index}`}>
                  <img src={image.imsu} height={90} width={"100%"} />
                </div>
              );
            })}
          </Carousel>
        </div>
        <div style={{ display: "flex", flexDirection: "row", gap: "5px" }}>
          <div style={{ alignItems: "center" }}>
            <RatingBar
              ratings={markerInfo.hotelDetails.ratings}
              componentsToShow="ratingBox"
            />
          </div>
          <div>
            <b>{`Reviews (${markerInfo.hotelDetails.reviews.length})`}</b>
          </div>
        </div>
        <div>
          <b>{markerInfo.hotelDetails.name}</b>
        </div>
        <div>
          <span>{markerInfo.hotelDetails.price}</span>
        </div>
      </div>
    </div>
  );
};
const hotelData = [
  {
    id: 1,
    hotelDetails: {
      location: {
        latitude: 19.1085,
        longitude: 72.8247,
      },
      name: "Novotel Mumbai Juhu Beach Hotel",
      price: "₹9,000",
      ratings: 5,
      reviews: [{ review_rating: 5, review_comment: "Nice Staff" }],
      hotelimages: [
        {
          imsu: "https://i.travelapi.com/hotels/1000000/50000/49300/49261/a652d6bb_z.jpg",
        },
        {
          imsu: "https://i.travelapi.com/hotels/1000000/50000/49300/49261/c55ec772_z.jpg",
        },
        {
          imsu: "https://i.travelapi.com/hotels/1000000/50000/49300/49261/f79fe014_z.jpg",
        },
        {
          imsu: "https://i.travelapi.com/hotels/1000000/50000/49300/49261/b02a5c04_z.jpg",
        },
        {
          imsu: "https://i.travelapi.com/hotels/1000000/50000/49300/49261/de8ffd6c_z.jpg",
        },
      ],
    },
  },
  {
    id: 2,
    hotelDetails: {
      location: {
        latitude: 19.1031,
        longitude: 72.8767,
      },
      name: "Jw Marriott Mumbai Sahar",
      price: "₹7,500",
      ratings: 3.9,
      reviews: [{ review_rating: 5, review_comment: "Nice Staff" }],
      hotelimages: [
        {
          imsu: "https://i.travelapi.com/hotels/10000000/9790000/9784600/9784580/2a61cd38_z.jpg",
        },
        {
          imsu: "https://i.travelapi.com/hotels/10000000/9790000/9784600/9784580/8e94ae2f_z.jpg",
        },
        {
          imsu: "https://i.travelapi.com/hotels/10000000/9790000/9784600/9784580/90f7dcc9_z.jpg",
        },
        {
          imsu: "https://i.travelapi.com/hotels/10000000/9790000/9784600/9784580/f46369db_z.jpg",
        },
        {
          imsu: "https://i.travelapi.com/hotels/10000000/9790000/9784600/9784580/b5248663_z.jpg",
        },
      ],
    },
  },
  {
    id: 3,
    hotelDetails: {
      location: {
        latitude: 19.0439,
        longitude: 72.8187,
      },
      name: "Taj Lands End",
      price: "₹7,580",
      ratings: 4.5,
      reviews: [{ review_rating: 5, review_comment: "Nice Staff" }],
      hotelimages: [
        {
          imsu: "https://i.travelapi.com/hotels/1000000/910000/905500/905407/076666ed_z.jpg",
        },
        {
          imsu: "https://i.travelapi.com/hotels/1000000/910000/905500/905407/3070a8ea_z.jpg",
        },
        {
          imsu: "https://i.travelapi.com/hotels/1000000/910000/905500/905407/c4233afb_z.jpg",
        },
        {
          imsu: "https://i.travelapi.com/hotels/1000000/910000/905500/905407/80358f0d_z.jpg",
        },
        {
          imsu: "https://i.travelapi.com/hotels/1000000/910000/905500/905407/ee0faf6b_z.jpg",
        },
      ],
    },
  },
  {
    id: 4,
    hotelDetails: {
      location: {
        latitude: 18.935,
        longitude: 72.8241,
      },
      name: "InterContinental Marine Drive Mumbai, an IHG Hotel",
      price: "₹5,500",
      ratings: 3.5,
      reviews: [{ review_rating: 5, review_comment: "Nice Staff" }],
      hotelimages: [
        {
          imsu: "https://i.travelapi.com/hotels/1000000/920000/916300/916201/47775203_z.jpg",
        },
        {
          imsu: "https://i.travelapi.com/hotels/1000000/920000/916300/916201/94c2b7b0_z.jpg",
        },
        {
          imsu: "https://i.travelapi.com/hotels/1000000/920000/916300/916201/345fab49_z.jpg",
        },
        {
          imsu: "https://i.travelapi.com/hotels/1000000/920000/916300/916201/62b37e47_z.jpg",
        },
        {
          imsu: "https://i.travelapi.com/hotels/1000000/920000/916300/916201/ecc06c88_z.jpg",
        },
      ],
    },
  },
  {
    id: 5,
    hotelDetails: {
      location: {
        latitude: 19.0855,
        longitude: 72.8834,
      },
      name: "FabHotel Prime Phoenix International",
      price: "₹4,272",
      ratings: 2.3,
      reviews: [{ review_rating: 2, review_comment: "Nice Staff" }],
      hotelimages: [
        {
          imsu: "https://i.travelapi.com/hotels/37000000/36020000/36017900/36017801/c1bc6bfd_z.jpg",
        },
        {
          imsu: "https://i.travelapi.com/hotels/37000000/36020000/36017900/36017801/291177ab_z.jpg",
        },
        {
          imsu: "https://i.travelapi.com/hotels/37000000/36020000/36017900/36017801/6b763060_z.jpg",
        },
        {
          imsu: "https://i.travelapi.com/hotels/37000000/36020000/36017900/36017801/596e7a00_z.jpg",
        },
        {
          imsu: "https://i.travelapi.com/hotels/37000000/36020000/36017900/36017801/19e53fdb_z.jpg",
        },
      ],
    },
  },
];

const onMapLoadView = (status) => {
  if (status === "FAILURE") {
    return <div>error</div>;
  }
  return <Skeleton />;
};



export const MapWithAdvanceMarker = (props) => {
  const [highlightedMarker, setHighlightedMarker] = useState(null);

  const onMarkerEnterHandler = (marker) => {
    setHighlightedMarker(marker);
  };
  const onMarkerLeaveHandler = () => {
    if (highlightedMarker) {
      setHighlightedMarker(null);
    }
  };

  return (
    <div style={{ height: "80vh" }}>
      <GoogleMapWrapper
        apiKey={"AIzaSyDfrRmIrcvHdlGrr5ToL8j6MfOq2S2AfiU"}
        libraries={["marker"]}
        onLoadStatus={onMapLoadView}
      >
        <GoogleMap
          zoom={10}
          center={{
            lat: 19.07609,
            lng: 72.877426,
          }}
        >
          {hotelData.map((markerInfo) => {
            return (
              <Fragment key={markerInfo.id}>
                <AdvanceMarker
                  position={{
                    lat: markerInfo.hotelDetails.location.latitude,
                    lng: markerInfo.hotelDetails.location.longitude,
                  }}
                  zIndex={highlightedMarker?.id == markerInfo.id ? 999 : 0}
                >
                  <MarkerCustomView
                    highlightedMarker={highlightedMarker}
                    markerInfo={markerInfo}
                    onMouseEnter={onMarkerEnterHandler}
                    onMouseLeave={onMarkerLeaveHandler}
                  />
                </AdvanceMarker>
              </Fragment>
            );
          })}
        </GoogleMap>
      </GoogleMapWrapper>
    </div>
  );
};

export const MapWithAdvanceMarkerMobile = (props) => {
  const [selectedMarker, setSelectedMarker] = useState(null);

  const onModalOpen = (marker) => {
    setSelectedMarker(marker);
  };
  const onModalClose = (e) => {
    e.stopPropagation();
    setSelectedMarker(null);
  };

  return (
    <>
      <div style={{ height: "80vh", position: "relative", width: "100%" }}>
        <GoogleMapWrapper
          apiKey={"AIzaSyDfrRmIrcvHdlGrr5ToL8j6MfOq2S2AfiU"}
          libraries={["marker"]}
          onLoadStatus={onMapLoadView}
        >
          <GoogleMap
            zoom={9}
            center={{
              lat:
                selectedMarker == null
                  ? 19.07609
                  : selectedMarker.hotelDetails.location.latitude,
              lng:
                selectedMarker == null
                  ? 72.877426
                  : selectedMarker.hotelDetails.location.longitude,
            }}
          >
            {hotelData.map((markerInfo) => {
              return (
                <Fragment key={markerInfo.id}>
                  <AdvanceMarker
                    position={{
                      lat: markerInfo.hotelDetails.location.latitude,
                      lng: markerInfo.hotelDetails.location.longitude,
                    }}
                    zIndex={selectedMarker?.id == markerInfo.id ? 999 : 0}
                  >
                    <MarkerCustomViewMobile
                      selectedMarker={selectedMarker}
                      markerInfo={markerInfo}
                      onModalOpen={onModalOpen}
                      onModalClose={onModalClose}
                    />
                  </AdvanceMarker>
                </Fragment>
              );
            })}
          </GoogleMap>
          {selectedMarker != null && (
            <div
              style={{
                zIndex: 9999,
                height: "200px",
                width: "100%",
                backgroundColor: "transparent",
                bottom: "19vh",
                display: "block",
                position: "fixed",
              }}
            >
              <Overlay
                style={{ bottom: "18vh", backgroundColor: "transparent" }}
                onClick={onModalClose}
              />
              <Carousel
                responsive={{
                  mobile: {
                    items: 1.2,
                    partialVisibilityGutter: 10,
                  },
                }}
                deviceType="mobile"
                showDots={false}
                arrows={false}
                swipeable={true}
                draggable={true}
                additionalClassName={[
                  styles.carouselMobile,
                  styles.carouselMobileOne,
                ]}
                itemClass={styles.carouselItemMobile}
                afterChange={(value) => {
                  const newSelectedMarker = hotelData[value];
                  setSelectedMarker(newSelectedMarker);
                }}
              >
                {hotelData.map((markerInfo, index) => {
                  return (
                    <div
                      key={`carousel-child-${index + 1}`}
                      id={`carousel-child-${index + 1}`}
                      style={{
                        padding: "2px",
                        height: "100%",
                      }}
                    >
                      <div
                        id={`Card-${index}`}
                        style={{
                          boxShadow:
                            "rgba(0, 0, 0, 0.02) 0px 1px 3px 0px, rgba(27, 31, 35, 0.15) 0px 0px 0px 1px",
                          display: "flex",
                          flexDirection: "column",
                          borderRadius: "10px",
                          backgroundColor: "#fff",
                          height: "100%",
                        }}
                      >
                        <div
                          id="outer-custom-wrap"
                          style={{ padding: "0px", height: "100%" }}
                        >
                          <Carousel
                            responsive={{ desktop: { items: 1 } }}
                            deviceType="desktop"
                            showDots={true}
                            arrows={false}
                            id="inner"
                            autoPlay={true}
                            additionalStyle={{ borderRadius: "5px" }}
                          >
                            {markerInfo.hotelDetails.hotelimages.map(
                              (image, index) => {
                                return (
                                  <div
                                    key={`hotel-img-${index}`}
                                    style={{
                                      scale: "0.9",
                                      borderRadius: "20px",
                                    }}
                                  >
                                    <img
                                      style={{ borderRadius: "10px" }}
                                      src={image.imsu}
                                      height={100}
                                      width={"100%"}
                                    />
                                  </div>
                                );
                              }
                            )}
                          </Carousel>
                        </div>
                        <div
                          style={{
                            textAlign: "center",
                            height: "100%",
                          }}
                        >
                          <div
                            style={{
                              display: "flex",
                              flexDirection: "row",
                              gap: "5px",
                              padding: "5px",
                              justifyContent: "center",
                            }}
                          >
                            <div>
                              <RatingBar
                                ratings={markerInfo.hotelDetails.ratings}
                                componentsToShow="ratingBox"
                                isMobile={true}
                              />
                            </div>
                            <div>
                              <span>{markerInfo.hotelDetails.name}</span>
                            </div>
                          </div>
                          <div
                            style={{
                              display: "flex",
                              width: "100%",
                              justifyContent: "center",
                              bottom: "3px",
                            }}
                          >
                            <Button
                              buttonType="tertiary"
                              isMobile={true}
                              additionalStyle={{
                                width: "auto",
                                padding: "2px",
                                height: "24PX",
                              }}
                            >
                              view more
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </Carousel>
            </div>
          )}
        </GoogleMapWrapper>
      </div>
    </>
  );
};

MapWithAdvanceMarkerMobile.parameters = {
  viewport: { defaultViewport: "iphone5" },
};

export const MapWithPolygon = (props) => {
  const getPaths = () => {
    const pathsToReturn = [];
    hotelData.forEach((markerInfo) => {
      pathsToReturn.push({
        lat: markerInfo.hotelDetails.location.latitude,
        lng: markerInfo.hotelDetails.location.longitude,
      });
    });
    return pathsToReturn;
  };
  const paths = getPaths();
  return (
    <div style={{ height: "80vh" }}>
      <GoogleMapWrapper
        apiKey={"AIzaSyDfrRmIrcvHdlGrr5ToL8j6MfOq2S2AfiU"}
        libraries={["marker"]}
        onLoadStatus={onMapLoadView}
      >
        <GoogleMap
          zoom={10}
          center={{
            lat: 19.07609,
            lng: 72.877426,
          }}
        >
          {hotelData.map((markerInfo) => {
            return (
              <Fragment key={markerInfo.id}>
                <AdvanceMarker
                  position={{
                    lat: markerInfo.hotelDetails.location.latitude,
                    lng: markerInfo.hotelDetails.location.longitude,
                  }}
                >
                  <img
                    src="https://upload.wikimedia.org/wikipedia/commons/a/aa/Google_Maps_icon_%282020%29.svg"
                    width={35}
                    height={35}
                  />
                </AdvanceMarker>
              </Fragment>
            );
          })}
          <Polygon
            paths={paths}
            strokeColor="#FF0000"
            strokeOpacity={0.8}
            strokeWeight={2}
            fillColor="#383838"
            fillOpacity={0.35}
          />
        </GoogleMap>
      </GoogleMapWrapper>
    </div>
  );
};

export const MapWithInfoWindow = (props) => {
  const [selectedMarker, setSelectedMarker] = useState(null);
  const markerClickHandler = (markerInfo) => {
    setSelectedMarker(markerInfo);
  };
  return (
    <div style={{ height: "80vh" }}>
      <GoogleMapWrapper
        apiKey={'AIzaSyDfrRmIrcvHdlGrr5ToL8j6MfOq2S2AfiU'}
        libraries={["marker"]}
        onLoadStatus={onMapLoadView}
      >
        <GoogleMap
          zoom={10}
          center={{
            lat: 19.07609,
            lng: 72.877426,
          }}
        >
          <Fragment>
            {hotelData.map((markerInfo, index) => {
              return (
                <Fragment key={markerInfo.id}>
                  <AdvanceMarker
                    position={{
                      lat: markerInfo.hotelDetails.location.latitude,
                      lng: markerInfo.hotelDetails.location.longitude,
                    }}
                    onClick={(e) => {
                      console.log("marker clicked ", markerInfo);
                      markerClickHandler(markerInfo);
                    }}
                  >
                    <img
                      src="https://upload.wikimedia.org/wikipedia/commons/a/aa/Google_Maps_icon_%282020%29.svg"
                      width={35}
                      height={35}
                    />
                  </AdvanceMarker>
                  {selectedMarker != null &&
                    selectedMarker.id == markerInfo.id && (
                      <InfoWindow
                        position={{
                          lat: selectedMarker.hotelDetails.location.latitude,
                          lng: selectedMarker.hotelDetails.location.longitude,
                        }}
                        pixelOffset={{ height: -35 }}
                      >
                        <InfoWindoCustomView markerInfo={selectedMarker} />
                      </InfoWindow>
                    )}
                </Fragment>
              );
            })}
          </Fragment>
        </GoogleMap>
      </GoogleMapWrapper>
    </div>
  );
};
