/* HTML marker styles */
.marker {
  position: absolute;
  transform: scale(1);
  transition: transform 150ms;
  background-color: #ffffff;
  pointer-events: visible;
  height: 100%;
}

.ce89cb {
  transform: translateX(-50%) translateY(-100%) translateY(-9.3px);
  border-radius: 12px;
  cursor: pointer;
  min-width: 40px;
  position: absolute;
  text-align: center;
  white-space: nowrap;
  word-break: keep-all;
  align-items: center;
  display: flex;
  justify-content: center;
  height: 23px;
  /* z-index: 1; */
}

.S2WJKd {
  border-radius: 16px;
  background-color: #fff;
  box-shadow: 0 0 0 1px #9aa0a6;
  color: #3c4043;
}

.S2WJKdHigh {
  background-color: #f06292;
  color: #fff;
}

.P3iUhe {
  align-items: center;
  display: flex;
  margin: 0 4px;
  white-space: nowrap;
  word-break: keep-all;
}

.icon {
  background-color: #f06292;
  align-items: center;
  border-radius: 50%;
  display: flex;
  height: 17px;
  justify-content: center;
  margin-right: 5px;
  width: 17px;
}

.iconHighlighted {
  background-color: #fff;
}

.hj759e {
  transform: scale(0.7);
}

.P5LfNc {
  vertical-align: top;
  position: absolute;
  transform: translateX(-50%) translateY(-100%);
  /* z-index: 3; */
}

.dJ0wg {
  fill: #fff;
}

.dJ0wgHigh {
  fill: #f06292;
}

.PojWnc {
  fill: #fff;
  stroke: none;
}

.PojWncHigh {
  fill: #f06292;
}

.cSrhvb {
  fill: #9aa0a6;
}

.hotelDetailInfoView {
  position: absolute;
  background-color: #fff;
  border: 1px solid #dadce0;
  border-radius: 8px;
  cursor: pointer;
  /* max-height: 60px; */
  padding: 11px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 176px;
  min-height: 40px;
  z-index: 1;
  top: -35px;

  /* left: -98px;
  top: -99px; */
  /* transform: translateX(-50%) translateY(-100%); */
  transform: translate3d(-50%, -100%, 0);
}

.carouselItemMobile {
  background-color: transparent !important;

}

.carouselMobile {
  z-index: 999;
  height: inherit !important;
}

.carouselMobile>div {
  background-color: transparent !important;
}

.carouselMobileOne>div>ol {
  background-color: transparent !important;
}