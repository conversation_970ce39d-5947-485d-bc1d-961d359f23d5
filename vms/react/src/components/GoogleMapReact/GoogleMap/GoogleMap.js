import PropTypes from "prop-types";
import React, { Fragment, useEffect, useRef } from "react";
import { useClassName } from "../../../hooks/useClassName";
import { useMap } from "../utils/hooks/useMap";

export const GoogleMap = (props) => {
  const {
    mapId,
    zoom,
    center,
    streetViewControl,
    fullscreenControl,
    zoomControl,
    scaleControl,
    disableDefaultUI,
    mapTypeControl,
    additionalClassName,
    additionalStyle,
    children,
  } = props;
  const ref = useRef();
  const { map, setMap } = useMap();

  const className = useClassName(props, additionalClassName);

  useEffect(() => {
    const mapObj = new window.google.maps.Map(ref.current, {
      center: center,
      zoom: zoom,
      mapId: mapId,
      streetViewControl: streetViewControl,
      fullscreenControl: fullscreenControl,
      zoomControl: zoomControl,
      scaleControl: scaleControl,
      disableDefaultUI: disableDefaultUI,
      mapTypeControl: mapTypeControl,
    });
    mapObj.setCenter;
    setMap(mapObj);
  }, []);

  useEffect(() => {
    if (map != null) {
      map.setCenter(center);
    }
  }, [center]);

  return (
    <Fragment>
      <div
        ref={ref}
        className={`${className} vms_reactgooglemap_container`}
        style={{
          width: "100%",
          height: "100%",
          ...additionalStyle,
        }}
      />
      {children}
    </Fragment>
  );
};

GoogleMap.defaultProps = {
  mapId: "vms_react_map",
  additionalClassName: null,
  additionalStyle: null,
  streetViewControl: false,
  fullscreenControl: false,
  zoomControl: false,
  scaleControl: false,
  disableDefaultUI: true,
  mapTypeControl: false,
};

GoogleMap.propTypes = {
  /**
   * The initial Map center.
   */
  center: PropTypes.shape({
    lat: PropTypes.number,
    lng: PropTypes.number,
  }).isRequired,

  /**
   * The initial Map zoom level. Valid zoom values are numbers from zero up to the supported maximum zoom level.
   * Larger zoom values correspond to a higher resolution.
   */
  zoom: PropTypes.number.isRequired,

  /**
   * The Map ID of the map. This parameter cannot be set or changed after a map is instantiated.
   */
  mapId: PropTypes.string,

  /**
   * The initial enabled/disabled state of the Street View Pegman control.
   * This control is part of the default UI, and should be set to when displaying a map type on which the Street View road overlay should not appear (e.g. a non-Earth map type).
   * @default false
   */
  streetViewControl: PropTypes.bool,

  /**
   * The enabled/disabled state of the Fullscreen control.
   */
  fullscreenControl: PropTypes.bool,

  /**
   * The enabled/disabled state of the Zoom control.
   */
  zoomControl: PropTypes.bool,

  /**
   * The initial enabled/disabled state of the Scale control.
   */
  scaleControl: PropTypes.bool,

  /**
   * Enables/disables all default UI buttons. May be overridden individually. Does not disable the keyboard controls, which are separately controlled by the {@link google.maps.MapOptions.keyboardShortcuts} option. Does not disable gesture controls, which are separately controlled by the * google.maps.MapOptions.gestureHandling option.
   */
  disableDefaultUI: PropTypes.bool,

  /**
   * The initial enabled/disabled state of the Map type control.
   */
  mapTypeControl: PropTypes.bool,

  /**
   * Property to append additional css class
   */
  additionalClassName: PropTypes.arrayOf(PropTypes.string),
  /**
   * Property to set the `inline style` object on button
   */
  additionalStyle: PropTypes.object,
};
