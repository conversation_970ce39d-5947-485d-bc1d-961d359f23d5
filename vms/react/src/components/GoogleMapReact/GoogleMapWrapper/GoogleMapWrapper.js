import PropTypes from "prop-types";
import React from "react";
import { Wrapper, Status } from "@googlemaps/react-wrapper";
import { MapProvider } from "../utils/context/MapContext";

export const GoogleMapWrapper = ({
  children,
  apiKey,
  libraries,
  onLoadStatus,
}) => {
  return (
    <Wrapper
      id="vms_react_map_wrapper"
      libraries={libraries}
      apiKey={apiKey}
      render={onLoadStatus}
      version="beta"
    >
      <MapProvider>{children}</MapProvider>
    </Wrapper>
  );
};

GoogleMapWrapper.defaultProps = {
  libraries: ["marker"],
};

GoogleMapWrapper.propTypes = {
  /**
   * api-key is the unique identifier for map component.
   * Every application should have a different api-key
   */
  apiKey: PropTypes.string.isRequired,

  /**
   * When loading the Maps JavaScript API via the URL you may optionally load additional libraries through use of the libraries URL parameter. Libraries are modules of code that provide additional functionality to the main Maps JavaScript API but are not loaded unless you specifically request them
   * Currently only marker is supported
   * @default ["marker"]
   */
  libraries: PropTypes.arrayOf(PropTypes.oneOf(["marker"])).isRequired,

  /**
   * Function that gives status of Map. User can accordingly define view for each status
   * @type {Status}
   * status value can be any of one given string LOADING, FAILURE, SUCCESS
   * function(status) => {}
   */
  onLoadStatus: PropTypes.func,
};
