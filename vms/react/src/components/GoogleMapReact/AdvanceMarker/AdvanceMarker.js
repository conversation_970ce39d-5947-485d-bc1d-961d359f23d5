import PropTypes from "prop-types";
import React, { Fragment, useEffect, useRef } from "react";
import { createRoot } from "react-dom/client";
import { useMap } from "../utils/hooks/useMap";

export const AdvanceMarker = ({ children, position, onClick, zIndex }) => {
  const { map } = useMap();
  const markerRef = useRef();
  const rootRef = useRef();

  useEffect(() => {
    if (!rootRef.current) {
      const container = document.createElement("div");
      container.id = `adv-marker-${position.lat}-${position.lng}`;
      rootRef.current = createRoot(container);
      let collisionBehavior = google.maps.CollisionBehavior.REQUIRED;
      markerRef.current = new google.maps.marker.AdvancedMarkerView({
        position: position,
        content: container,
        collisionBehavior: collisionBehavior,
      });

      let onClickListener = null;
      if (onClick != null) {
        onClickListener = markerRef.current.addListener("click", onClick);
      }

      return () => {
        if (onClickListener != null) {
          onClickListener.remove();
        }
      };
    }
  }, []);

  useEffect(() => {
    rootRef.current.render(children);
    markerRef.current.position = position;
    if (zIndex != null) {
      markerRef.current.zIndex = zIndex;
    }
    markerRef.current.map = map;
  }, [map, position, children, zIndex]);

  return <Fragment></Fragment>;
};
AdvanceMarker.defaultProps = {
  zIndex: null,
  onClick: null,
};
AdvanceMarker.propTypes = {
  /**
   * Sets the AdvanceMarker's position on the map
   */
  position: PropTypes.shape({
    lat: PropTypes.number,
    lng: PropTypes.number,
  }).isRequired,

  /**
   * All AdvanceMarker's are displayed on the map in order of their zIndex, with higher values displaying in front of AdvanceMarker's with lower values.
   * By default, AdvanceMarker's are displayed according to their vertical position on screen, with lower AdvanceMarker's appearing in front of AdvanceMarker's farther up the screen.
   */
  zIndex: PropTypes.number,

  /**
   * When AdvanceMarker is clicked, this function will be triggered
   *
   * function(event) => {}
   */
  onClick: PropTypes.func,
};
