import React, { useState, useEffect, memo, useCallback, useMemo } from "react";
import PropTypes from "prop-types";
import {
  InputCheckbox as StyledInputCheckbox,
  Indicator,
  Label,
  LinkText,
} from "./InputCheckbox.styled";
import { useClassName } from "../../hooks/useClassName";
import { Link } from "../Link/Link";
import { ThemeWrapper } from "../Theme/ThemeContext";

// Utility function (placed before main component)
const isNonNull = (prop) => prop !== null && prop !== undefined;

const InputCheckbox = memo((props) => {
  // 1. PROP DESTRUCTURING & VALIDATION
  const {
    id,
    checked,
    onChange,
    onClick,
    disabled = false,
    normallabel,
    label,
    value,
    boldLabelOnCheckboxSelection = true,
    partialSelection = false,
    type = "primary",
    islinkwithtext = "false",
    labelStyle,
    labelClassName,
    containerStyle,
    containerClassName,
    isMobile,
    isMobileView,
    ...otherProps
  } = props;

  // 2. STATE MANAGEMENT
  const [check, setCheck] = useState(checked);
  const [labelData, setLabelData] = useState(label);

  // 3. PERFORMANCE OPTIMIZATIONS
  const labelclassName = useMemo(() => 
    useClassName(props, labelClassName), 
    [props, labelClassName]
  );

  const containerclassName = useMemo(() => 
    useClassName(props, containerClassName), 
    [props, containerClassName]
  );

  const isControlled = useMemo(() => isNonNull(checked), [checked]);

  // 4. EVENT HANDLING with useCallback
  const handleChange = useCallback((event) => {
    if (disabled) return;

    if (!isControlled) {
      setCheck(event.target.checked);
    }
    
    if (isNonNull(onChange)) {
      onChange(event);
    }
  }, [disabled, isControlled, onChange]);

  const handleClick = useCallback((event) => {
    if (disabled) return;
    
    if (isNonNull(onClick)) {
      onClick(event);
    }
  }, [disabled, onClick]);

  const handleKeyDown = useCallback((event) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      handleChange({ target: { checked: !check } });
    }
  }, [handleChange, check]);

  // 5. EFFECTS
  useEffect(() => {
    setCheck(isNonNull(checked) ? checked : false);
  }, [checked]);

  useEffect(() => {
    setLabelData(label);
  }, [label]);

  // 6. CONDITIONAL RENDERING (memoized)
  const normalLabelElement = useMemo(() => {
    if (!isNonNull(normallabel)) return null;
    
    return <>{normallabel}</>;
  }, [normallabel]);

  const linkWithTextElement = useMemo(() => {
    if (islinkwithtext === "false" || !labelData || labelData.length === 0) return null;

    return (
      <div className="vms_inputcheckbox_linktext">
        {labelData.map((e, i) => (
          <LinkText
            key={i}
            boldLabelOnCheckboxSelection={boldLabelOnCheckboxSelection}
            checked={check}
          >
            {e.isLink ? (
              <Link
                target={e.openLinkInNewTab ? "_blank" : "_self"}
                onClick={() => e.functionName && e.functionName()}
                href={e.href}
              >
                {e.label}
              </Link>
            ) : (
              <span>{e.label}</span>
            )}
          </LinkText>
        ))}
      </div>
    );
  }, [islinkwithtext, labelData, boldLabelOnCheckboxSelection, check]);

  // 7. ERROR HANDLING
  React.useEffect(() => {
    if (!id) {
      console.warn('InputCheckbox: id prop is required for accessibility');
    }
  }, [id]);

  return (
    <ThemeWrapper isMobile={isMobile || isMobileView || false}>
      <div
        className={`${containerclassName} vms_inputcheckbox_container`}
        style={containerStyle}
      >
        <div style={{ display: "flex" }} className={"vms_inputcheckbox_maindiv"}>
          <Label
            htmlFor={id}
            className={`${labelclassName} vms_inputcheckbox_label`}
            style={labelStyle}
            disabled={disabled}
            boldLabelOnCheckboxSelection={boldLabelOnCheckboxSelection}
            checked={check}
            id={id ? `${id}-label` : null}
            role="label"
            onClick={!disabled ? handleClick : undefined}
            onKeyDown={handleKeyDown}
            tabIndex={disabled ? -1 : 0}
          >
            {normalLabelElement}
            <StyledInputCheckbox
              id={id}
              type="checkbox"
              className={"vms_inputcheckbox_input"}
              varientType={type}
              value={value}
              disabled={disabled}
              checked={check}
              partialSelection={partialSelection}
              onChange={handleChange}
              aria-checked={check}
              aria-disabled={disabled}
              aria-labelledby={id ? `${id}-label` : undefined}
              {...otherProps}
            />
          </Label>
        </div>
        {linkWithTextElement}
      </div>
    </ThemeWrapper>
  );
});

// Component display name for debugging
InputCheckbox.displayName = 'InputCheckbox';

InputCheckbox.defaultProps = {
  labelStyle: null,
  labelClassName: null,
  containerStyle: null,
  containerClassName: null,
  disabled: false,
  // checked: false,
  boldLabelOnCheckboxSelection: true,
  partialSelection: false,
  type: "primary",
  label: null,
  islinkwithtext: "false",
  onChange: () => { },
  onClick: () => { },
};

InputCheckbox.propTypes = {
  /**
    
  * Unique ID for the field. Required for web accessibility
   */
  id: PropTypes.string,
  /**
   * Variations of Button Type
   */
  type: PropTypes.oneOf(["primary", "secondary"]),
  /**
   * Label of checkbox
   */
  labelComponent: PropTypes.oneOfType([PropTypes.node, PropTypes.string]),
  /**
   * change event for the checkbox
   */
  onChange: PropTypes.func,

  /**
   * onClick event for the checkbox
   */
  onClick: PropTypes.func,

  /**
   * property to make checkbox disabled for editing
   */
  disabled: PropTypes.bool,

  /**
   * used to represent default state of checkbox
   */
  checked: PropTypes.bool,

  /**
   * Set a value for check box
   */
  value: PropTypes.string,

  /**
   * To enable partial selection
   */
  partialSelection: PropTypes.bool,

  /**
   * To make label font bold when checkbox selected
Default value true
   */
  boldLabelOnCheckboxSelection: PropTypes.bool,

  /**
   * Classes to add additional styling in the label
   */
  labelClassName: PropTypes.arrayOf(PropTypes.string),
  /**
   * Property to set the `style` object on label
   */
  labelStyle: PropTypes.object,

  /**
   * Classes to add additional styling in the checkbox Container
   */
  containerClassName: PropTypes.arrayOf(PropTypes.string),
  /**
   * Property to set the `style` object on label checkbox Container
   */
  containerStyle: PropTypes.object,
  /**
   * Label for the field
   */
  normallabel: PropTypes.string,

  /**
   * label having Link and Text to display
   */
  label: PropTypes.arrayOf(PropTypes.string),

  /**
   * flag to check text is having Link and Text Or only text
   */
  islinkwithtext: PropTypes.string,
};

export { InputCheckbox };
