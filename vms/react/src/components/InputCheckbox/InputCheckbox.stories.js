import React, { useState } from "react";

import { InputCheckbox } from "./InputCheckbox";
import inputsvgIcon from "../../assets/images/variantofautocomplete/onwordflaghome.svg";

export default {
  title: "VMS_REACT/Input CheckBox",
  component: InputCheckbox,
  argTypes: {
    id: { control: { type: "" } },
    labelStyle: { control: { type: "" } },
    labelClassName: { control: { type: "" } },
    containerStyle: { control: { type: "" } },
    containerClassName: { control: { type: "" } },
    onChange: { action: "clicked" },
  },
};

const open = () => {
  alert("agree term and condition pop-up");
};
const open1 = () => {
  alert("policy pop-up");
};

const inputCheckboxArray = [
  {
    Labelindex: "1",
    isLink: false,
    label: "We will ",
    functionName: null,
  },
  {
    Labelindex: "2",
    isLink: true,
    label: "agree term and condition ",
    functionName: open,
    href: "https://www.google.com",
  },
  {
    Labelindex: "3",
    isLink: false,
    label: "of your ",
    functionName: null,
  },
  {
    Labelindex: "4",
    isLink: true,
    label: "policy ",
    functionName: open1,
  },
];

const inputCheckboxArray_OpenLink_NewTab = [
  {
    Labelindex: "1",
    isLink: false,
    label: "We will ",
    functionName: null,
  },
  {
    Labelindex: "2",
    isLink: true,
    label: "agree term and condition ",
    functionName: open,
    openLinkInNewTab: true,
    href: "https://www.google.com",
  },
  {
    Labelindex: "3",
    isLink: false,
    label: "of your ",
    functionName: null,
  },
  {
    Labelindex: "4",
    isLink: true,
    label: "policy ",
    functionName: open1,
  },
];

const Template = (args) => {
  const [checked, setChecked] = useState(false);
  return (
    <InputCheckbox
      value={"Checkbox"}
      checked={checked}
      type="primary"
      partialSelection={false}
      boldLabelOnCheckboxSelection={false}
      onChange={(e, val) => {
        setChecked(val);
      }}
      normallabel="Checkbox"
      id={"checkbox-1"}
    />
  );
};

const LinkTemplate = (args) => {
  const [checked, setChecked] = useState(false);
  return (
    <InputCheckbox
      id="chkTermsLink"
      checked={checked}
      onChange={(e, val) => {
        setChecked(val);
      }}
      boldLabelOnCheckboxSelection={false}
      label={inputCheckboxArray}
      value="terms"
      islinkwithtext="true"
    />
  );
};

const openNewTemplate = (args) => {
  const [checked, setChecked] = useState(false);
  return (
    <InputCheckbox
      id="chkTermsLink"
      checked={checked}
      onChange={(e, val) => {
        setChecked(val);
      }}
      boldLabelOnCheckboxSelection={false}
      label={inputCheckboxArray_OpenLink_NewTab}
      value="terms"
      islinkwithtext="true"
    />
  );
};

export const Default = Template.bind({});
// Default.args = {
//   checked: false,
//   disabled: false,
//   partialSelection: false,
//   boldLabelOnCheckboxSelection: false,
//   normallabel: "Checkbox",
// };

export const TextWithLink = LinkTemplate.bind({});

export const TextWithLinkToInNewTab = openNewTemplate.bind({});

export const LabelWithLabelComponent = (props) => {
  const [selected, setSelected] = useState(false);
  return (
    <InputCheckbox
    id="ic-label-id"
      checked={selected}
      onClick={(e)=>{
        setSelected(!selected);
      }}
      labelComponent={
        <div style={{ display: "flex" }}>
          <div>
            <img src={inputsvgIcon} width={"54px"} height={"24px"}/>
          </div>
          <div>Coins</div>
          <div style={{borderRight:"2px solid red", margin:"0px 5px", height:"24px"}}/>
          <div>1000 coins available</div>
        </div>
      }
    />
  );
};
