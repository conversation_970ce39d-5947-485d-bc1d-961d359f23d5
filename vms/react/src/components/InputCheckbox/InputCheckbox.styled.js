import styled from "styled-components";
export function getFontWeight(theme, boldLabelOnCheckboxSelection, checked) {
  return boldLabelOnCheckboxSelection || checked
    ? "bold"
    : theme?.typography?.text.fontWeight || "";
}

export const InputCheckbox = styled.input`
  height: 0;
  width: 0;
  opacity: 0;
  z-index: -1;
  float: left;
`;

export const Label = styled.label`
  position: relative;
  display: inline-block;
  cursor: ${(props) => (props.disabled ? "not-allowed" : "pointer")};
  /* margin: 0.6em 1em; */
  font-family: ${({ theme }) => theme?.typography?.fontFamily || ""};
  font-size: ${({ theme }) => theme?.typography?.text?.fontSize || ""};
  font-weight: ${({ theme, boldLabelOnCheckboxSelection, checked }) =>
    getFontWeight(theme, boldLabelOnCheckboxSelection, checked)};
  line-height: ${({ theme }) => theme?.typography?.lineHeight || ""};
  letter-spacing: ${({ theme }) => theme?.typography?.letterSpacing || ""};
`;

export const Indicator = styled.div`
  position: relative;
  border: ${({ checked }) => (checked ? "" : "2px solid #939598")};
  border-radius: 3px;
  width: 18px;
  height: 18px;
  box-sizing: border-box;
  background-color: ${({ theme, checked, type }) =>
    checked
      ? theme?.palette?.[type]?.main
      : theme?.palette?.[type]?.contrastText};
  float: left;
  margin: 0px 15px 0px 0px;

  ${InputCheckbox}:not(:disabled):checked & {
    background: #d1d1d1;
  }

  ${InputCheckbox}:checked + &::after {
    left: 6px;
    top: 2px;
    width: 4px;
    height: 10px;
    border: solid #fff;
    border-width: ${({ partialSelection }) =>
      partialSelection ? "0px 2px 0px 0px" : "0px 2px 2px 0px"};
    transform: ${({ partialSelection }) =>
      partialSelection ? "rotate(90deg)" : "rotate(45deg)"};
    position: absolute;
    content: "";
  }

  &::disabled {
    cursor: not-allowed;
  }
`;
export const LinkText = styled.span`
  color: #4d4d4f;
  height: 20px;
  font-weight: ${({ theme, boldLabelOnCheckboxSelection, checked }) =>
    getFontWeight(theme, boldLabelOnCheckboxSelection, checked)};
  font-family: ${({ theme }) => theme?.typography?.fontFamily || ""};
  font-size: ${({ theme }) => theme?.typography?.text?.fontSize || ""};
  font-stretch: normal;
  font-style: normal;
  line-height: 1.43;
  letter-spacing: 0.3px;
`;
