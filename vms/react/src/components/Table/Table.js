import React, { useMemo, useState, useCallback, memo } from "react";
import PropTypes from "prop-types";
import {
  StyledTable,
  StyledTHead,
  StyledTBody,
  StyledTH,
  StyledTD,
  StyledTR,
  TableContainer,
  SearchInput,
  SortButton,
  TableWrapper
} from "./Table.styled";
import { useClassName } from "../../hooks/useClassName";
import { ThemeWrapper } from "../Theme/ThemeContext";

// Utility function (placed before main component)
const isNonNull = (prop) => prop !== null && prop !== undefined;

/**
 * Table UI component for displaying tabular data
 */
const Table = memo((props) => {
  // 1. PROP DESTRUCTURING & VALIDATION
  const {
    headers = [],
    data = [],
    orientation = 'horizontal',
    colSpanConfig = {},
    rowSpanConfig = {},
    numRows,
    numCols,
    additionalClassName,
    additionalStyle,
    emptyCell = '',
    headerClassName = '',
    cellClassName = '',
    onCellClick,
    isMobile,
    isMobileView,
    tableType = 'primary',
    showBorder = true,
    striped = false,
    hover = false,
    id,
    showSearch = false,
    searchPlaceholder = 'Search...',
    sortable = false,
    sortableColumns = null, // Array of column indices that should be sortable
    defaultSortOrder = 'asc',
    onSort,
    searchValue: propSearchValue,
    onSearchChange,
    ...otherProps
  } = props;

  // 2. STATE MANAGEMENT
  const [internalSearchValue, setInternalSearchValue] = useState('');
  const [sortConfig, setSortConfig] = useState({ key: null, direction: defaultSortOrder });

  // 3. PERFORMANCE OPTIMIZATIONS
  const computedClassName = useMemo(
    () => useClassName(props, additionalClassName),
    [props, additionalClassName]
  );

  const isMobileDevice = useMemo(
    () => isMobile || isMobileView || false,
    [isMobile, isMobileView]
  );

  const currentSearchValue = useMemo(() =>
    propSearchValue !== undefined ? propSearchValue : internalSearchValue,
    [propSearchValue, internalSearchValue]
  );

  // Helper function to check if a specific column is sortable
  const isColumnSortable = useCallback((columnIndex) => {
    // If sortableColumns is specified, only those columns are sortable
    if (Array.isArray(sortableColumns)) {
      return sortableColumns.includes(columnIndex);
    }
    // If sortableColumns is not specified and sortable is true, all columns are sortable
    return sortable;
  }, [sortable, sortableColumns]);

  // 4. EVENT HANDLING with useCallback
  const handleSearchChange = useCallback((e) => {
    const value = e.target.value;

    if (propSearchValue !== undefined && isNonNull(onSearchChange)) {
      // Controlled mode
      onSearchChange(value);
    } else {
      // Uncontrolled mode
      setInternalSearchValue(value);
    }
  }, [propSearchValue, onSearchChange]);

  const handleSort = useCallback((columnIndex) => {
    if (!isColumnSortable(columnIndex)) return;

    const direction = sortConfig.key === columnIndex && sortConfig.direction === 'asc' ? 'desc' : 'asc';
    const newSortConfig = { key: columnIndex, direction };

    setSortConfig(newSortConfig);

    if (isNonNull(onSort)) {
      onSort(columnIndex, direction);
    }
  }, [isColumnSortable, sortConfig, onSort]);

  const handleCellClick = useCallback((rowIndex, colIndex, cellData) => {
    if (isNonNull(onCellClick)) {
      onCellClick(rowIndex, colIndex, cellData);
    }
  }, [onCellClick]);

  // 5. UTILITY FUNCTIONS (memoized)
  const getCellKey = useCallback((rowIndex, colIndex) => `${rowIndex}-${colIndex}`, []);

  const shouldSkipCell = useCallback((rowIndex, colIndex, spannedCells) => {
    return spannedCells.has(getCellKey(rowIndex, colIndex));
  }, [getCellKey]);

  const calculateSpannedCells = useCallback((totalRows, totalCols) => {
    const spannedCells = new Set();

    // Process rowspan
    Object.entries(rowSpanConfig).forEach(([key, span]) => {
      const [rowIndex, colIndex] = key.split('-').map(Number);
      for (let i = 1; i < span; i++) {
        if (rowIndex + i < totalRows) {
          spannedCells.add(getCellKey(rowIndex + i, colIndex));
        }
      }
    });

    // Process colspan
    Object.entries(colSpanConfig).forEach(([key, span]) => {
      const [rowIndex, colIndex] = key.split('-').map(Number);
      for (let i = 1; i < span; i++) {
        if (colIndex + i < totalCols) {
          spannedCells.add(getCellKey(rowIndex, colIndex + i));
        }
      }
    });

    return spannedCells;
  }, [rowSpanConfig, colSpanConfig, getCellKey]);

  const isVisuallyLastCell = useCallback((rowIndex, colIndex, totalCols, spannedCells, colSpanConfig) => {
    // Find the rightmost visible cell in this row
    let rightmostVisibleCol = -1;

    for (let col = 0; col < totalCols; col++) {
      const cellKey = getCellKey(rowIndex, col);
      if (!shouldSkipCell(rowIndex, col, spannedCells)) {
        const span = colSpanConfig[cellKey] || 1;
        rightmostVisibleCol = Math.max(rightmostVisibleCol, col + span - 1);
      }
    }

    // Check if current cell + its colspan covers the rightmost position
    const currentSpan = colSpanConfig[getCellKey(rowIndex, colIndex)] || 1;
    return (colIndex + currentSpan - 1) >= rightmostVisibleCol;
  }, [getCellKey, shouldSkipCell]);

  const rowHasSpanning = useCallback((rowIndex, totalCols, colSpanConfig, rowSpanConfig) => {
    for (let col = 0; col < totalCols; col++) {
      const cellKey = getCellKey(rowIndex, col);
      if ((colSpanConfig[cellKey] && colSpanConfig[cellKey] > 1) ||
        (rowSpanConfig[cellKey] && rowSpanConfig[cellKey] > 1)) {
        return true;
      }
    }
    return false;
  }, [getCellKey]);

  const isRowAffectedBySpanning = useCallback((rowIndex, totalCols, totalRows, colSpanConfig, rowSpanConfig) => {
    // Check if current row has its own spanning
    if (rowHasSpanning(rowIndex, totalCols, colSpanConfig, rowSpanConfig)) {
      return true;
    }

    // Check if any previous rows have rowspans that affect this row
    for (let prevRow = 0; prevRow < rowIndex; prevRow++) {
      for (let col = 0; col < totalCols; col++) {
        const prevCellKey = getCellKey(prevRow, col);
        const rowSpan = rowSpanConfig[prevCellKey];
        if (rowSpan && rowSpan > 1) {
          // Check if this rowspan extends to current row
          if (prevRow + rowSpan > rowIndex) {
            return true;
          }
        }
      }
    }

    return false;
  }, [rowHasSpanning, getCellKey]);

  const getCellValue = useCallback((data, rowIndex, colIndex) => {
    if (Array.isArray(data[rowIndex])) {
      return data[rowIndex][colIndex] ?? emptyCell;
    } else if (typeof data[rowIndex] === 'object' && data[rowIndex] !== null) {
      const keys = Object.keys(data[rowIndex]);
      return data[rowIndex][keys[colIndex]] ?? emptyCell;
    }
    return emptyCell;
  }, [emptyCell]);

  const isImageFile = useCallback((value) => {
    if (typeof value !== 'string') return false;
    const imageExtensions = ['.png', '.jpg', '.jpeg', '.gif', '.svg', '.webp', '.bmp', '.ico'];
    const lowercaseValue = value.toLowerCase();
    return imageExtensions.some(ext => lowercaseValue.endsWith(ext));
  }, []);

  const processCellValue = useCallback((cellValue) => {
    if (React.isValidElement(cellValue)) {
      return cellValue;
    }

    if (isImageFile(cellValue)) {
      return (
        <img
          src={cellValue}
          alt={cellValue}
          style={{ maxWidth: '100px', maxHeight: '100px' }}
          title={cellValue}
        />
      );
    }

    return cellValue;
  }, [isImageFile]);

  const extractTextContent = useCallback((element) => {
    if (element == null) return '';

    if (typeof element === 'string' || typeof element === 'number') {
      return element.toString();
    }

    if (React.isValidElement(element)) {
      if (element.props && element.props.children) {
        if (typeof element.props.children === 'string') {
          return element.props.children;
        }
        if (Array.isArray(element.props.children)) {
          return element.props.children.map(child => extractTextContent(child)).join(' ');
        }
        return extractTextContent(element.props.children);
      }
      return '';
    }

    return element.toString();
  }, []);

  const matchesSearch = useCallback((row, searchTerm) => {
    if (!searchTerm) return true;

    const searchLower = searchTerm.toLowerCase();

    if (Array.isArray(row)) {
      return row.some(cell => {
        const textContent = extractTextContent(cell);
        return textContent && textContent.toLowerCase().includes(searchLower);
      });
    } else if (typeof row === 'object' && row !== null) {
      return Object.values(row).some(cell => {
        const textContent = extractTextContent(cell);
        return textContent && textContent.toLowerCase().includes(searchLower);
      });
    }

    return false;
  }, [extractTextContent]);

  // 6. DATA PROCESSING (memoized)
  const filteredData = useMemo(() => {
    if (!showSearch || !currentSearchValue) {
      return data;
    }

    return data.filter(row => matchesSearch(row, currentSearchValue));
  }, [data, showSearch, currentSearchValue, matchesSearch]);

  const processedData = useMemo(() => {
    // Apply sorting if needed
    if (sortConfig.key !== null && !onSort) {
      // Only sort internally if no external sort handler
      const sorted = [...filteredData].sort((a, b) => {
        const aValue = getCellValue(filteredData, filteredData.indexOf(a), sortConfig.key);
        const bValue = getCellValue(filteredData, filteredData.indexOf(b), sortConfig.key);

        const aText = extractTextContent(aValue);
        const bText = extractTextContent(bValue);

        if (sortConfig.direction === 'asc') {
          return aText.localeCompare(bText);
        } else {
          return bText.localeCompare(aText);
        }
      });
      return sorted;
    }

    return filteredData;
  }, [filteredData, sortConfig, onSort, getCellValue, extractTextContent]);

  // 7. ERROR HANDLING
  React.useEffect(() => {
    if (headers.length === 0 && data.length > 0) {
      console.warn('Table: headers prop is recommended when data is provided');
    }

    if (sortableColumns && !Array.isArray(sortableColumns)) {
      console.warn('Table: sortableColumns should be an array of column indices');
    }
  }, [headers, data, sortableColumns]);

  const sortData = useCallback((data, columnIndex, direction) => {
    if (columnIndex === null) return data;

    return [...data].sort((a, b) => {
      let aVal, bVal;

      if (Array.isArray(a)) {
        aVal = a[columnIndex];
        bVal = b[columnIndex];
      } else if (typeof a === 'object' && a !== null) {
        const keys = Object.keys(a);
        aVal = a[keys[columnIndex]];
        bVal = b[keys[columnIndex]];
      } else {
        return 0;
      }

      if (aVal == null && bVal == null) return 0;
      if (aVal == null) return direction === 'asc' ? 1 : -1;
      if (bVal == null) return direction === 'asc' ? -1 : 1;

      const aText = extractTextContent(aVal);
      const bText = extractTextContent(bVal);

      const aNum = parseFloat(aText);
      const bNum = parseFloat(bText);
      const isANumeric = !isNaN(aNum) && isFinite(aNum) && aText.trim() !== '';
      const isBNumeric = !isNaN(bNum) && isFinite(bNum) && bText.trim() !== '';

      if (isANumeric && isBNumeric) {
        return direction === 'asc' ? aNum - bNum : bNum - aNum;
      } else {
        const comparison = aText.localeCompare(bText);
        return direction === 'asc' ? comparison : -comparison;
      }
    });
  }, [extractTextContent]);

  const processedTableData = useMemo(() => {
    let processedData = [...data];
    let processedHeaders = [...headers];

    if (currentSearchValue) {
      processedData = processedData.filter(row => matchesSearch(row, currentSearchValue));
    }
    if (sortable && sortConfig.key !== null) {
      processedData = sortData(processedData, sortConfig.key, sortConfig.direction);
    }

    if (numRows && processedData.length > numRows) {
      processedData = processedData.slice(0, numRows);
    }

    if (numCols) {
      if (Array.isArray(processedData[0])) {
        processedData = processedData.map(row =>
          Array.isArray(row) ? row.slice(0, numCols) : row
        );
      }
      processedHeaders = processedHeaders.slice(0, numCols);
    }

    const totalRows = processedData.length;
    const totalCols = processedHeaders.length ||
      (Array.isArray(processedData[0]) ? processedData[0].length :
        typeof processedData[0] === 'object' ? Object.keys(processedData[0]).length : 0);

    const spannedCells = calculateSpannedCells(totalRows + 1, totalCols); // +1 for header

    return {
      data: processedData,
      headers: processedHeaders,
      totalRows,
      totalCols,
      spannedCells
    };
  }, [data, headers, numRows, numCols, colSpanConfig, rowSpanConfig, currentSearchValue, sortConfig, sortable, matchesSearch, sortData]);

  const renderHorizontalTable = () => {
    const { data: processedData, headers: processedHeaders, spannedCells } = processedTableData;

    return (
      <StyledTable
        tableType={tableType}
        showBorder={showBorder}
        striped={striped}
        hover={hover}
        orientation="horizontal"
      >
        {processedHeaders.length > 0 && (
          <StyledTHead>
            <StyledTR>
              {processedHeaders.map((header, colIndex) => {
                const cellKey = getCellKey(0, colIndex);

                if (shouldSkipCell(0, colIndex, spannedCells)) {
                  return null;
                }

                const colSpan = colSpanConfig[cellKey] || 1;
                const rowSpan = rowSpanConfig[cellKey] || 1;
                const isSortable = isColumnSortable(colIndex) && colSpan === 1 && rowSpan === 1; // Check if column is sortable and non-spanned
                const isLastVisible = isVisuallyLastCell(0, colIndex, processedTableData.totalCols, spannedCells, colSpanConfig);
                const hasRowSpanning = isRowAffectedBySpanning(0, processedTableData.totalCols, processedTableData.totalRows, colSpanConfig, rowSpanConfig);

                return (
                  <StyledTH
                    key={`header-${colIndex}`}
                    className={`${headerClassName} vms_table_header ${isSortable ? 'sortable' : ''} ${isLastVisible && hasRowSpanning ? 'force-right-border' : ''}`}
                    colSpan={colSpan > 1 ? colSpan : undefined}
                    rowSpan={rowSpan > 1 ? rowSpan : undefined}
                    tableType={tableType}
                    sortable={isSortable}
                    sortDirection={sortConfig.key === colIndex ? sortConfig.direction : null}
                    isVisuallyLast={isLastVisible}
                    hasRowSpanning={hasRowSpanning}
                    onClick={isSortable ? () => handleSort(colIndex) : (onCellClick ? () => onCellClick(0, colIndex, header) : undefined)}
                  >
                    <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                      <span>{header}</span>
                      {isSortable && (
                        <SortButton sortDirection={sortConfig.key === colIndex ? sortConfig.direction : null}>
                          <span>⇅</span>
                        </SortButton>
                      )}
                    </div>
                  </StyledTH>
                );
              })}
            </StyledTR>
          </StyledTHead>
        )}
        <StyledTBody>
          {processedData.map((row, rowIndex) => (
            <StyledTR key={`row-${rowIndex}`} striped={striped} rowIndex={rowIndex}>
              {Array.from({ length: processedTableData.totalCols }).map((_, colIndex) => {
                const cellKey = getCellKey(rowIndex + 1, colIndex); // +1 because header is row 0

                if (shouldSkipCell(rowIndex + 1, colIndex, spannedCells)) {
                  return null;
                }

                const cellValue = getCellValue(processedData, rowIndex, colIndex);
                const colSpan = colSpanConfig[cellKey] || 1;
                const rowSpan = rowSpanConfig[cellKey] || 1;
                const isLastVisible = isVisuallyLastCell(rowIndex + 1, colIndex, processedTableData.totalCols, spannedCells, colSpanConfig);
                const hasRowSpanning = isRowAffectedBySpanning(rowIndex + 1, processedTableData.totalCols, processedTableData.totalRows, colSpanConfig, rowSpanConfig);

                return (
                  <StyledTD
                    key={`cell-${rowIndex}-${colIndex}`}
                    className={`${cellClassName} vms_table_cell ${isLastVisible && hasRowSpanning ? 'force-right-border' : ''}`}
                    colSpan={colSpan > 1 ? colSpan : undefined}
                    rowSpan={rowSpan > 1 ? rowSpan : undefined}
                    tableType={tableType}
                    hover={hover}
                    isVisuallyLast={isLastVisible}
                    hasRowSpanning={hasRowSpanning}
                    onClick={onCellClick ? () => onCellClick(rowIndex + 1, colIndex, cellValue) : undefined}
                  >
                    {processCellValue(cellValue)}
                  </StyledTD>
                );
              })}
            </StyledTR>
          ))}
        </StyledTBody>
      </StyledTable>
    );
  };

  // Render vertical table (rotated layout)
  const renderVerticalTable = () => {
    const { data: processedData, headers: processedHeaders, spannedCells } = processedTableData;

    return (
      <StyledTable
        tableType={tableType}
        showBorder={showBorder}
        striped={striped}
        hover={hover}
        orientation="vertical"
      >
        <StyledTBody>
          {processedHeaders.map((header, headerIndex) => (
            <StyledTR key={`vertical-row-${headerIndex}`} striped={striped} rowIndex={headerIndex}>
              <StyledTH
                className={`${headerClassName} vms_table_header`}
                tableType={tableType}
                onClick={onCellClick ? () => onCellClick(headerIndex, 0, header) : undefined}
              >
                {header}
              </StyledTH>
              {processedData.map((row, dataIndex) => {
                const cellKey = getCellKey(headerIndex, dataIndex + 1); // +1 because header is col 0

                if (shouldSkipCell(headerIndex, dataIndex + 1, spannedCells)) {
                  return null;
                }

                const cellValue = getCellValue(processedData, dataIndex, headerIndex);
                const colSpan = colSpanConfig[cellKey] || 1;
                const rowSpan = rowSpanConfig[cellKey] || 1;

                return (
                  <StyledTD
                    key={`vertical-cell-${headerIndex}-${dataIndex}`}
                    className={`${cellClassName} vms_table_cell`}
                    colSpan={colSpan > 1 ? colSpan : undefined}
                    rowSpan={rowSpan > 1 ? rowSpan : undefined}
                    tableType={tableType}
                    hover={hover}
                    onClick={onCellClick ? () => onCellClick(headerIndex, dataIndex + 1, cellValue) : undefined}
                  >
                    {processCellValue(cellValue)}
                  </StyledTD>
                );
              })}
            </StyledTR>
          ))}
        </StyledTBody>
      </StyledTable>
    );
  };

  return (
    <ThemeWrapper isMobile={isMobile || isMobileView || false}>
      <TableWrapper
        id={id}
        className={`${className} vms_table_wrapper`}
        style={additionalStyle}
      >
        {showSearch && (
          <SearchInput
            type="text"
            placeholder={searchPlaceholder}
            value={currentSearchValue}
            onChange={handleSearchChange}
            className="vms_table_search"
            tableType={tableType}
          />
        )}
        <TableContainer
          className="vms_table_container"
          isMobile={isMobile || isMobileView}
        >
          {orientation === 'vertical' ? renderVerticalTable() : renderHorizontalTable()}
        </TableContainer>
      </TableWrapper>
    </ThemeWrapper>
  );
};

Table.defaultProps = {
  headers: [],
  data: [],
  orientation: 'horizontal',
  colSpanConfig: {},
  rowSpanConfig: {},
  numRows: null,
  numCols: null,
  additionalClassName: null,
  additionalStyle: null,
  emptyCell: '',
  headerClassName: '',
  cellClassName: '',
  onCellClick: null,
  isMobile: false,
  isMobileView: false,
  tableType: 'primary',
  showBorder: true,
  striped: false,
  hover: false,
  id: null,
  showSearch: false,
  searchPlaceholder: 'Search...',
  sortable: false,
  sortableColumns: null,
  defaultSortOrder: 'asc',
  onSort: null,
  searchValue: undefined,
  onSearchChange: null
};

Table.propTypes = {
  /**
   * Array of column/row headers
   */
  headers: PropTypes.arrayOf(PropTypes.string),

  /**
   * 2D array or array of objects representing table content
   */
  data: PropTypes.arrayOf(
    PropTypes.oneOfType([
      PropTypes.arrayOf(PropTypes.any),
      PropTypes.object
    ])
  ),

  /**
   * Layout direction: "horizontal" or "vertical"
   */
  orientation: PropTypes.oneOf(['horizontal', 'vertical']),

  /**
   * Object specifying colspan values for specific cells (by row-col index)
   * Format: { "row-col": spanValue }
   */
  colSpanConfig: PropTypes.object,

  /**
   * Object specifying rowspan values for specific cells (by row-col index)
   * Format: { "row-col": spanValue }
   */
  rowSpanConfig: PropTypes.object,

  /**
   * Maximum number of rows to display
   */
  numRows: PropTypes.number,

  /**
   * Maximum number of columns to display
   */
  numCols: PropTypes.number,

  /**
   * Additional CSS classes to apply
   */
  additionalClassName: PropTypes.arrayOf(PropTypes.string),

  /**
   * Inline styles to apply
   */
  additionalStyle: PropTypes.object,

  /**
   * Fallback content for empty cells
   */
  emptyCell: PropTypes.any,

  /**
   * CSS class for header cells
   */
  headerClassName: PropTypes.string,

  /**
   * CSS class for data cells
   */
  cellClassName: PropTypes.string,

  /**
   * Callback function when a cell is clicked
   * (rowIndex, colIndex, cellValue) => void
   */
  onCellClick: PropTypes.func,

  /**
   * Enable mobile view
   */
  isMobile: PropTypes.bool,

  /**
   * Enable mobile view (alias)
   */
  isMobileView: PropTypes.bool,

  /**
   * Table theme type
   */
  tableType: PropTypes.oneOf(['primary', 'secondary']),

  /**
   * Show table borders
   */
  showBorder: PropTypes.bool,

  /**
   * Enable striped rows
   */
  striped: PropTypes.bool,

  /**
   * Enable hover effects
   */
  hover: PropTypes.bool,

  /**
   * Table ID
   */
  id: PropTypes.string,

  /**
   * Show search input above table
   */
  showSearch: PropTypes.bool,

  /**
   * Placeholder text for search input
   */
  searchPlaceholder: PropTypes.string,

  /**
   * Enable column sorting
   */
  sortable: PropTypes.bool,

  /**
   * Array of column indices that should be sortable. If not provided, all columns are sortable when sortable=true
   * Example: [0, 2, 3] makes only the 1st, 3rd, and 4th columns sortable
   */
  sortableColumns: PropTypes.arrayOf(PropTypes.number),

  /**
   * Default sort order
   */
  defaultSortOrder: PropTypes.oneOf(['asc', 'desc']),

  /**
   * Callback when column is sorted
   * (columnIndex, direction) => void
   */
  onSort: PropTypes.func,

  /**
   * Controlled search value
   */
  searchValue: PropTypes.string,

  /**
   * Callback when search value changes
   * (searchValue) => void
   */
  onSearchChange: PropTypes.func,

  /**
   * Whether component is in mobile view
   */
  isMobileView: PropTypes.bool,
};

// Component display name for debugging
Table.displayName = "Table";

export { Table };
