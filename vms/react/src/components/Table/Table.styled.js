import styled from "styled-components";

function _fontWeight(theme) {
  return theme?.typography?.text?.fontWeight || "";
}

function _fontFamily(theme) {
  return theme?.typography?.fontFamily || "";
}

function _fontSize(theme) {
  return theme?.typography?.text?.fontSize || "";
}

function _lineHeight(theme) {
  return theme?.typography?.lineHeight || "";
}

function _letterSpacing(theme) {
  return theme?.typography?.letterSpacing || "";
}

function _borderRadius(theme) {
  return theme?.shape?.borderRadius || "3px";
}

function _getBorderColor(theme, tableType) {
  return theme?.palette?.[tableType]?.[theme.mode] || "#ebebec";
}

function _getHeaderBackground(theme, tableType) {
  return theme?.palette?.[tableType]?.contrastText || "#f4f4f4";
}

export const TableWrapper = styled.div`
  width: 100%;
  font-family: ${({ theme }) => _fontFamily(theme)};
  font-size: ${({ theme }) => _fontSize(theme)};
  font-weight: ${({ theme }) => _fontWeight(theme)};
  line-height: ${({ theme }) => _lineHeight(theme)};
  letter-spacing: ${({ theme }) => _letterSpacing(theme)};
`;

export const SearchInput = styled.input`
  width: 100%;
  max-width: 300px;
  padding: 8px 12px;
  margin-bottom: 16px;
  border: 1px solid ${({ theme, tableType }) => _getBorderColor(theme, tableType)};
  border-radius: ${({ theme }) => _borderRadius(theme)};
  font-family: ${({ theme }) => _fontFamily(theme)};
  font-size: ${({ theme }) => _fontSize(theme)};
  font-weight: ${({ theme }) => _fontWeight(theme)};
  line-height: ${({ theme }) => _lineHeight(theme)};
  letter-spacing: ${({ theme }) => _letterSpacing(theme)};
  color: #4d4d4f;
  background-color: #fff;
  outline: none;
  transition: border-color 0.2s ease;
  box-sizing: border-box;
  pointer-events: auto;
  user-select: text;
  
  &:focus {
    border-color: ${({ theme }) => theme?.palette?.primary?.main || '#03868b'};
    box-shadow: 0 0 0 2px ${({ theme }) => (theme?.palette?.primary?.main || '#03868b') + '20'};
  }
  
  &::placeholder {
    color: ${({ theme }) => theme?.palette?.text?.secondary || '#999'};
    font-family: ${({ theme }) => _fontFamily(theme)};
    font-size: ${({ theme }) => _fontSize(theme)};
  }
`;

export const SortButton = styled.span`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  margin-left: 4px;
  cursor: pointer;
  opacity: ${({ sortDirection }) => sortDirection ? 1 : 0.6};
  transition: opacity 0.2s ease;
  
  &:hover {
    opacity: 1;
  }
  
  span {
    font-size: 12px;
    color: ${({ theme, sortDirection }) => 
      sortDirection ? (theme?.palette?.primary?.main || '#03868b') : 'currentColor'};
    transform: ${({ sortDirection }) => 
      sortDirection === 'desc' ? 'rotate(180deg)' : 'none'};
    transition: transform 0.2s ease, color 0.2s ease;
  }
`;

export const TableContainer = styled.div`
  width: 100%;
  overflow-x: ${({ isMobile }) => isMobile ? 'auto' : 'visible'};
  font-family: ${({ theme }) => _fontFamily(theme)};
  font-size: ${({ theme }) => _fontSize(theme)};
  font-weight: ${({ theme }) => _fontWeight(theme)};
  line-height: ${({ theme }) => _lineHeight(theme)};
  letter-spacing: ${({ theme }) => _letterSpacing(theme)};
`;

export const StyledTable = styled.table`
  width: 100%;
  border-collapse: collapse;
  border-spacing: 0;
  border-radius: ${({ theme }) => _borderRadius(theme)};
  border: ${({ showBorder, theme, tableType }) => 
    showBorder ? `1px solid ${_getBorderColor(theme, tableType)}` : 'none'};
  background-color: ${({ theme }) => theme?.palette?.background?.paper || '#fff'};
  
  ${({ orientation }) => orientation === 'vertical' && `
    th:first-child {
      background-color: ${({ theme, tableType }) => _getHeaderBackground(theme, tableType)};
      font-weight: bold;
    }
  `}
`;

export const StyledTHead = styled.thead`
  background-color: ${({ theme, tableType }) => _getHeaderBackground(theme, tableType)};
`;

export const StyledTBody = styled.tbody``;

export const StyledTR = styled.tr`
  ${({ striped, rowIndex, theme }) => 
    striped && rowIndex % 2 === 1 && `
      background-color: ${theme?.palette?.action?.hover || '#f9f9f9'};
    `}
  
  &:hover {
    background-color: ${({ theme }) => theme?.palette?.action?.hover || '#f5f5f5'};
  }
`;

export const StyledTH = styled.th`
  padding: 12px 16px;
  text-align: left;
  font-weight: bold;
  font-family: ${({ theme }) => _fontFamily(theme)};
  font-size: ${({ theme }) => _fontSize(theme)};
  line-height: ${({ theme }) => _lineHeight(theme)};
  letter-spacing: ${({ theme }) => _letterSpacing(theme)};
  color: ${({ theme }) => theme?.palette?.text?.primary || '#4d4d4f'};
  border-bottom: ${({ theme, tableType }) => `1px solid ${_getBorderColor(theme, tableType)}`};
  border-right: ${({ theme, tableType }) => `1px solid ${_getBorderColor(theme, tableType)}`};
  background-color: ${({ theme, tableType }) => _getHeaderBackground(theme, tableType)};
  cursor: ${({ onClick, sortable }) => (onClick || sortable) ? 'pointer' : 'default'};
  user-select: none;
  position: relative;
  
  &:last-child {
    border-right: none;
  }
  
  /* JavaScript-controlled class to force right border when needed */
  &.force-right-border {
    border-right: ${({ theme, tableType }) => `1px solid ${_getBorderColor(theme, tableType)}`} !important;
  }
  
  &:hover {
    background-color: ${({ onClick, theme }) => 
      onClick ? theme?.palette?.action?.hover || '#e0e0e0' : 'inherit'};
  }
    
`;

export const StyledTD = styled.td`
  padding: 12px 16px;
  text-align: left;
  font-family: ${({ theme }) => _fontFamily(theme)};
  font-size: ${({ theme }) => _fontSize(theme)};
  font-weight: ${({ theme }) => _fontWeight(theme)};
  line-height: ${({ theme }) => _lineHeight(theme)};
  letter-spacing: ${({ theme }) => _letterSpacing(theme)};
  color: ${({ theme }) => theme?.palette?.text?.primary || '#4d4d4f'};
  border-bottom: ${({ theme, tableType }) => `1px solid ${_getBorderColor(theme, tableType)}`};
  border-right: ${({ theme, tableType }) => `1px solid ${_getBorderColor(theme, tableType)}`};
  cursor: ${({ onClick }) => onClick ? 'pointer' : 'default'};
  
  /* Standard border removal for last cell, but maintain border if row is affected by spanning */
  &:last-child {
    border-right: none;
  }
  
  /* JavaScript-controlled class to force right border when needed */
  &.force-right-border {
    border-right: ${({ theme, tableType }) => `1px solid ${_getBorderColor(theme, tableType)}`} !important;
  }

  
  /* Special handling for rowspan cells - ensure they maintain right borders */
  &[rowspan] {
    border-left: ${({ theme, tableType }) => `1px solid ${_getBorderColor(theme, tableType)}`};
    border-bottom: ${({ theme, tableType }) => `1px solid ${_getBorderColor(theme, tableType)}`};
  }
  
  /* Ensure cells that come after rowspan cells also have proper borders */
  &[rowspan] + td {
    border-left: ${({ theme, tableType }) => `1px solid ${_getBorderColor(theme, tableType)}`};
  }
  
  ${({ hover, onClick, theme }) => 
    (hover || onClick) && `
      &:hover {
        background-color: ${theme?.palette?.action?.hover || '#f5f5f5'};
      }
    `}
  
  /* Remove bottom border for cells in the last row */
  tr:last-child & {
    border-bottom: none;
  }
`;
