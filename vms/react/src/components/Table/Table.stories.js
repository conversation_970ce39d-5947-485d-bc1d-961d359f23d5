import React from "react";
import { Table } from "./Table";
import { <PERSON> } from "../Link/Link";
import backArrowIcon from "../../assets/images/IconButton/backArrow.svg";
import crossIcon from "../../assets/images/IconButton/crossIcon.svg";

export default {
  title: "VMS_REACT/Table",
  component: Table,
  argTypes: {
    additionalStyle: { control: { type: "" } },
    additionalClassName: { control: { type: "" } },
    headers: { control: { type: "" } },
    data: { control: { type: "" } },
    colSpanConfig: { control: { type: "" } },
    rowSpanConfig: { control: { type: "" } },
    onCellClick: { control: { type: "" } },
  },
};

// Sample data for stories
const sampleHeaders = ["Name", "Age", "City", "Occupation"];
const sampleData = [
  ["<PERSON>", "30", "New York", "Engineer"],
  ["<PERSON>", "25", "Los Angeles", "<PERSON>"],
  ["<PERSON>", "35", "Chicago", "<PERSON>"],
  ["<PERSON> Brown", "28", "<PERSON>", "Developer"],
];

const sampleObjectData = [
  { name: "<PERSON>e", age: 30, city: "New York", occupation: "Engineer" },
  { name: "Jane <PERSON>", age: 25, city: "Los Angeles", occupation: "Designer" },
  { name: "Bob Johnson", age: 35, city: "Chicago", occupation: "Manager" },
  { name: "Alice Brown", age: 28, city: "Houston", occupation: "Developer" },
];

const Template = (args) => <Table {...args} />;

export const Default = Template.bind({});
Default.args = {
  headers: sampleHeaders,
  data: sampleData,
  orientation: "horizontal",
  showBorder: true,
};

export const VerticalOrientation = Template.bind({});
VerticalOrientation.args = {
  headers: sampleHeaders,
  data: sampleData,
  orientation: "vertical",
  showBorder: true,
};

export const WithObjectData = Template.bind({});
WithObjectData.args = {
  headers: ["Name", "Age", "City", "Occupation"],
  data: sampleObjectData,
  orientation: "horizontal",
  showBorder: true,
};

export const WithColspan = Template.bind({});
WithColspan.args = {
  headers: ["Name", "Age", "City", "Actions"],
  data: [
    ["John Doe", "30", "New York", "Edit"],
    ["Jane Smith", "25", "Los Angeles", "Edit"],
    ["Combined Summary", "", "", "View All"], // This row will have colspan
  ],
  orientation: "horizontal",
  colSpanConfig: {
    "3-1": 2, // Last data row, second column spans 2 columns (Age + City)
  },
  showBorder: true,
};

export const HeaderColspanExample = Template.bind({});
HeaderColspanExample.args = {
  headers: ["Employee", "Personal Info","", "Performance", ""],
  data: [
    ["John Doe", "30", "New York", "100", "A"],
    ["Jane Smith", "25", "Los Angeles","95", "B"],
    ["Bob Johnson", "35", "Chicago","110", "A+"],
  ],
  orientation: "horizontal",
  colSpanConfig: {
    "0-1": 2, // Header row: "Personal Info" spans Age and City columns
    "0-3": 2, // Header row: "Performance" spans Score and Grade columns
  },
  showBorder: true,
  striped: true,
};

export const WithRowspan = Template.bind({});
WithRowspan.args = {
  headers: ["Department", "Name", "Age", "Role"],
  data: [
    ["Engineering", "John Doe", "30", "Senior Engineer"],
    ["", "Jane Smith", "25", "Junior Engineer"],
    ["Marketing", "Bob Johnson", "35", "Manager"],
    ["", "Alice Brown", "28", "Coordinator"],
  ],
  orientation: "horizontal",
  rowSpanConfig: {
    "1-0": 2, // First data row, first column spans 2 rows
    "3-0": 2, // Third data row, first column spans 2 rows
    "1-3": 2, // Second data row, first column spans 2 rows
    "1-2": 2, // Second data row, second column spans 2 rows
  },
  showBorder: true,
  showSearch: true,
};

export const StripedTable = Template.bind({});
StripedTable.args = {
  headers: sampleHeaders,
  data: sampleData,
  orientation: "horizontal",
  striped: true,
  showBorder: true,
};

export const HoverEffects = Template.bind({});
HoverEffects.args = {
  headers: sampleHeaders,
  data: sampleData,
  orientation: "horizontal",
  hover: true,
  showBorder: true,
};

export const HyperlinkTable = Template.bind({});
HyperlinkTable.args = {
  headers: ["Name", "Age", "City", "Profile"],
  data: [
    [
      <Link 
        href="https://linkedin.com/in/johndoe" 
        target="_blank" 
        type="primary"
      >
        John Doe
      </Link>, 
      "30", 
      "New York", 
      <Link 
        href="/profile/john-doe" 
        type="primary"
      >
        View Profile
      </Link>
    ],
    [
      <Link 
        href="https://linkedin.com/in/janesmith" 
        target="_blank" 
        type="primary"
      >
        Jane Smith
      </Link>, 
      "25", 
      "Los Angeles", 
      <Link 
        href="/profile/jane-smith" 
        type="primary"
      >
        View Profile
      </Link>
    ],
    [
      <Link 
        href="https://linkedin.com/in/bobjohnson" 
        target="_blank" 
        type="primary"
      >
        Bob Johnson
      </Link>, 
      "35", 
      "Chicago", 
      <Link 
        href="/profile/bob-johnson" 
        type="primary"
      >
        View Profile
      </Link>
    ],
    [
      <Link 
        href="https://linkedin.com/in/alicebrown" 
        target="_blank" 
        type="primary"
      >
        Alice Brown
      </Link>, 
      "28", 
      "Houston", 
      <Link 
        href="/profile/alice-brown" 
        type="primary"
      >
        View Profile
      </Link>
    ],
  ],
  orientation: "horizontal",
  showBorder: true,
  hover: true,
};

export const HyperlinkWithSearchTable = Template.bind({});
HyperlinkWithSearchTable.args = {
  headers: ["Employee", "Department", "Email", "Actions"],
  data: [
    [
      <Link href="/employee/1" type="primary">John Doe</Link>,
      "Engineering",
      <Link href="mailto:<EMAIL>" type="secondary"><EMAIL></Link>,
      <div style={{display: 'flex', gap: '8px'}}>
        <Link href="/edit/1" type="primary">Edit</Link>
        <Link href="/view/1" type="secondary">View</Link>
      </div>
    ],
    [
      <Link href="/employee/2" type="primary">Jane Smith</Link>,
      "Design",
      <Link href="mailto:<EMAIL>" type="secondary"><EMAIL></Link>,
      <div style={{display: 'flex', gap: '8px'}}>
        <Link href="/edit/2" type="primary">Edit</Link>
        <Link href="/view/2" type="secondary">View</Link>
      </div>
    ],
    [
      <Link href="/employee/3" type="primary">Bob Johnson</Link>,
      "Marketing",
      <Link href="mailto:<EMAIL>" type="secondary"><EMAIL></Link>,
      <div style={{display: 'flex', gap: '8px'}}>
        <Link href="/edit/3" type="primary">Edit</Link>
        <Link href="/view/3" type="secondary">View</Link>
      </div>
    ],
  ],
  orientation: "horizontal",
  showBorder: true,
  showSearch: true,
  searchPlaceholder: "Search employees...",
  sortable: true,
  hover: true,
  striped: true,
};

export const LimitedRowsAndColumns = Template.bind({});
LimitedRowsAndColumns.args = {
  headers: sampleHeaders,
  data: sampleData,
  orientation: "horizontal",
  numRows: 2,
  numCols: 3,
  showBorder: true,
};

export const NoBorders = Template.bind({});
NoBorders.args = {
  headers: sampleHeaders,
  data: sampleData,
  orientation: "horizontal",
  showBorder: false,
};

export const SecondaryTheme = Template.bind({});
SecondaryTheme.args = {
  headers: sampleHeaders,
  data: sampleData,
  orientation: "horizontal",
  tableType: "secondary",
  showBorder: true,
};

export const CustomStyling = Template.bind({});
CustomStyling.args = {
  headers: sampleHeaders,
  data: sampleData,
  orientation: "horizontal",
  showBorder: true,
  headerClassName: "custom-header",
  cellClassName: "custom-cell",
  additionalStyle: {
    maxWidth: "900px",
    margin: "20px auto",
    boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)",
  },
};

export const EmptyCellFallback = Template.bind({});
EmptyCellFallback.args = {
  headers: ["Name", "Age", "City", "Phone"],
  data: [
    ["John Doe", "30", "New York", "************"],
    ["Jane Smith", null, "Los Angeles", ""],
    ["Bob Johnson", "35", "", "555-0123"],
  ],
  orientation: "horizontal",
  emptyCell: "N/A",
  showBorder: true,
};

export const MobileView = Template.bind({});
MobileView.storyName = "MoWeb Default";
MobileView.parameters = {
  viewport: {
    defaultViewport: "iphone6",
  },
};
MobileView.args = {
  headers: sampleHeaders,
  data: sampleData,
  orientation: "horizontal",
  isMobile: true,
  showBorder: true,
};

export const ComplexSpanning = Template.bind({});
ComplexSpanning.args = {
  headers: ["Quarter", "Product A", "Product B", "Total"],
  data: [
    ["Q1", "100", "150", "250"],
    ["Q2", "120", "180", "300"],
    ["Total", "220", "330", "550"],
  ],
  orientation: "horizontal",
  colSpanConfig: {
    "3-0": 3, // Last row, first column spans 3 columns for "Total"
  },
  rowSpanConfig: {
    "0-3": 3, // Header "Total" spans all data rows
  },
  showBorder: true,
  striped: true,
};

export const WithSearch = Template.bind({});
WithSearch.args = {
  headers: sampleHeaders,
  data: sampleData,
  orientation: "horizontal",
  showBorder: true,
  showSearch: true,
  searchPlaceholder: "Search employees...",
};

export const WithSorting = Template.bind({});
WithSorting.args = {
  headers: sampleHeaders,
  data: sampleData,
  orientation: "horizontal",
  showBorder: true,
  sortable: true,
  hover: true,
};

export const WithSearchAndSorting = Template.bind({});
WithSearchAndSorting.args = {
  headers: sampleHeaders,
  data: sampleData,
  orientation: "horizontal",
  showBorder: true,
  showSearch: true,
  sortable: true,
  searchPlaceholder: "Search employees...",
  striped: true,
  hover: true,
  onSort: (columnIndex, direction) => {
    console.log(`Sorted column ${columnIndex} in ${direction} order`);
  },
};

export const WithColumnSpecificSorting = Template.bind({});
WithColumnSpecificSorting.args = {
  headers: ["Name", "Age", "City", "Occupation"],
  data: sampleData,
  orientation: "horizontal",
  showBorder: true,
  showSearch: true,
  sortable: true,
  sortableColumns: [0,2], // Only Name, Age, and Occupation columns are sortable
  searchPlaceholder: "Search employees...",
  striped: true,
  hover: true,
  onSort: (columnIndex, direction) => {
    console.log(`Sorted column ${columnIndex} in ${direction} order`);
  },
};

export const MixedContentWithSelectiveSorting = Template.bind({});
MixedContentWithSelectiveSorting.args = {
  headers: ["Employee", "Salary", "Actions", "Status", "Rating"],
  data: [
    [
      <Link href="/employee/1" type="primary">John Doe</Link>,
      "$75,000",
      <div style={{display: 'flex', gap: '8px'}}>
        <Link href="/edit/1" type="primary">Edit</Link>
        <Link href="/delete/1" type="secondary">Delete</Link>
      </div>,
      "Active",
      "4.5"
    ],
    [
      <Link href="/employee/2" type="primary">Jane Smith</Link>,
      "$85,000",
      <div style={{display: 'flex', gap: '8px'}}>
        <Link href="/edit/2" type="primary">Edit</Link>
        <Link href="/delete/2" type="secondary">Delete</Link>
      </div>,
      "Inactive",
      "4.2"
    ],
    [
      <Link href="/employee/3" type="primary">Bob Johnson</Link>,
      "$95,000",
      <div style={{display: 'flex', gap: '8px'}}>
        <Link href="/edit/3" type="primary">Edit</Link>
        <Link href="/delete/3" type="secondary">Delete</Link>
      </div>,
      "Active",
      "4.8"
    ],
    [
      <Link href="/employee/4" type="primary">Alice Brown</Link>,
      "$70,000",
      <div style={{display: 'flex', gap: '8px'}}>
        <Link href="/edit/4" type="primary">Edit</Link>
        <Link href="/delete/4" type="secondary">Delete</Link>
      </div>,
      "Active",
      "4.1"
    ],
  ],
  orientation: "horizontal",
  showBorder: true,
  showSearch: true,
  sortable: true,
  sortableColumns: [0, 1, 4],
  searchPlaceholder: "Search employees...",
  striped: true,
  hover: true,
  onSort: (columnIndex, direction) => {
    const columnNames = ['Employee', 'Salary', 'Actions', 'Status', 'Rating'];
    console.log(`Sorted column ${columnIndex} (${columnNames[columnIndex]}) in ${direction} order`);
  },
};



export const TableWithSVGIcons = Template.bind({});
TableWithSVGIcons.args = {
  headers: ["Task", "Status", "Actions"],
  data: [
    [
      "Complete project documentation",
      <div style={{display: 'flex', alignItems: 'center', gap: '6px'}}>
        <div style={{
          width: '10px', 
          height: '10px', 
          borderRadius: '50%', 
          backgroundColor: '#4CAF50'
        }}></div>
        "Completed"
      </div>,
      <div style={{display: 'flex', gap: '8px', alignItems: 'center'}}>
        <img 
          src={backArrowIcon}
          alt="Back" 
          style={{width: '16px', height: '16px', cursor: 'pointer'}}
          title="Go Back"
        />
        <img 
          src={crossIcon}
          alt="Delete" 
          style={{width: '16px', height: '16px', cursor: 'pointer'}}
          title="Delete Task"
        />
      </div>
    ],
    [
      "Review code changes",
      <div style={{display: 'flex', alignItems: 'center', gap: '6px'}}>
        <div style={{
          width: '10px', 
          height: '10px', 
          borderRadius: '50%', 
          backgroundColor: '#FF9800'
        }}></div>
        "In Progress"
      </div>,
      <div style={{display: 'flex', gap: '8px', alignItems: 'center'}}>
        <img 
          src={backArrowIcon}
          alt="Back" 
          style={{width: '16px', height: '16px', cursor: 'pointer'}}
          title="Go Back"
        />
        <img 
          src={crossIcon}
          alt="Delete" 
          style={{width: '16px', height: '16px', cursor: 'pointer'}}
          title="Delete Task"
        />
      </div>
    ],
    [
      "Fix reported bugs",
      <div style={{display: 'flex', alignItems: 'center', gap: '6px'}}>
        <div style={{
          width: '10px', 
          height: '10px', 
          borderRadius: '50%', 
          backgroundColor: '#F44336'
        }}></div>
        "Pending"
      </div>,
      <div style={{display: 'flex', gap: '8px', alignItems: 'center'}}>
        <img 
          src={backArrowIcon}
          alt="Back" 
          style={{width: '16px', height: '16px', cursor: 'pointer'}}
          title="Go Back"
        />
        <img 
          src={crossIcon}
          alt="Delete" 
          style={{width: '16px', height: '16px', cursor: 'pointer'}}
          title="Delete Task"
        />
      </div>
    ],
  ],
  orientation: "horizontal",
  showBorder: true,
  striped: true,
  hover: true,
  showSearch: true,
  searchPlaceholder: "Search tasks...",
  sortable: true,
};

export const TableWithAutoImageDetection = Template.bind({});
TableWithAutoImageDetection.args = {
  headers: ["Icon", "Name"],
  data: [
    [
      "https://promos.makemytrip.com/appfest/1x/ic_home_tertiary_where2go.png",
      "Where to go Icon"
    ],
    [
      "https://promos.makemytrip.com/appfest/1x/ic_home_tertiary_howtogo.png",
      "Location Icon"
    ],
    [
      "https://promos.makemytrip.com/appfest/1x/ic_home_tertiary_giftcards.png",
      "Gift Cards Icon"
    ],
    [
      "https://promos.makemytrip.com/appfest/1x/ic_home_tertiary_mice.png",
      "Mice Icon"
    ],
    [
      "Regular text without extension",
      "No Image Here"
    ],
  ],
  orientation: "horizontal",
  showBorder: true,
  striped: false,
  hover: true,
  showSearch: false,
  searchPlaceholder: "Search table...",
  sortable: true,
};





