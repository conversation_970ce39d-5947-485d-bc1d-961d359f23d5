import PropTypes from "prop-types";
import React, { Fragment } from "react";
import { WrapperDiv } from "./ReactGoogleMap.styled";
import {
  GoogleMap,
  Marker,
  Polygon,
  InfoWindow,
  useLoadScript,
} from "@react-google-maps/api";
import { useClassName } from "../../hooks/useClassName";
// import { ThemeWrapper } from "../Theme/ThemeContext";

const isNonNull = (prop) => {
  return prop !== null && prop !== undefined;
};

const containerStyle = {
  width: "100%",
  height: "100%",
  position: "relative",
  overflow: "hidden",
};

export const ReactGoogleMap = (props) => {
  const {
    id,
    apiKey,
    zoom,
    center,
    markers,
    activeMarker,
    activeMarkerIcon,
    defaultMarkerIcon,
    onMarkerClick,
    isInfoWindowVisible,
    domInformationPopup,
    polygonProps,
    additionalClassName,
    additionalStyle
  } = props;

  const className = useClassName(props, additionalClassName);

  const { isLoaded, loadError } = useLoadScript({
    id: "google-map-script",
    googleMapsApiKey: apiKey,
  });

  const getIcon = (markerElement) => {
    let icon = null;
    if (markerElement?.markerIcon) {
      let url =
        markerElement.name === activeMarker?.name
          ? activeMarkerIcon || markerElement.markerIcon
          : markerElement.markerIcon;
      icon = {
        url: url,
        size: { width: markerElement.size[0], height: markerElement.size[1] },
        scaledSize: {
          width: markerElement.size[0],
          height: markerElement.size[1],
        },
      };
    } else if (isNonNull(defaultMarkerIcon)) {
      let url =
        markerElement.name === activeMarker?.name
          ? activeMarkerIcon || defaultMarkerIcon
          : defaultMarkerIcon;
      icon = {
        url: url,
        size: { width: 24, height: 38 },
        scaledSize: { width: 24, height: 38 },
      };
    }
    return icon;
  };
  return (
    // <ThemeWrapper isMobile={isMobile || isMobileView || false}>
    <Fragment>
      <WrapperDiv id={id} className={`${className} vms_reactgooglemap_container`} style={additionalStyle}>
        {isLoaded === true && (
          <GoogleMap
            center={center}
            zoom={zoom}
            mapContainerStyle={containerStyle}
            className="vms_reactgooglemap_googlemap"
          >
            {isNonNull(markers) && markers?.length > 0 && (
              <Fragment>
                {markers.map((markerElement, index) => {
                  return (
                    <Marker
                      key={`marker-${index}`}
                      className="vms_reactgooglemap_marker"
                      position={{
                        lat: markerElement.lat,
                        lng: markerElement.lng,
                      }}
                      icon={getIcon(markerElement)}
                      title={markerElement.name}
                      onClick={(e) => {
                        if (isNonNull(onMarkerClick)) {
                          onMarkerClick(markerElement);
                        }
                      }}
                    >
                      {isInfoWindowVisible === true &&
                        isNonNull(activeMarker) &&
                        isNonNull(domInformationPopup) &&
                        activeMarker?.name == markerElement.name ? (
                        <InfoWindow
                          className="vms_reactgooglemap_inforwindow"
                          onCloseClick={() => {
                            if (isNonNull(onMarkerClick)) {
                              onMarkerClick(null);
                            }
                          }}
                        >
                          {domInformationPopup}
                        </InfoWindow>
                      ) : null}
                    </Marker>
                  );
                })}
              </Fragment>
            )}
            {isNonNull(polygonProps) && polygonProps?.paths && (
              <Polygon className="vms_reactgooglemap_polygon" paths={polygonProps.paths} />
            )}
          </GoogleMap>
        )}
        {isNonNull(loadError) && <p>{loadError}</p>}
      </WrapperDiv>
    </Fragment>
    // </ThemeWrapper>
  );
};
ReactGoogleMap.defaultProps = {
  domInformationPopup: null,
  activeMarker: null,
  markers: null,
  apiKey: null,
};

ReactGoogleMap.propTypes = {
  /**
   * Maps can have its unique identifier
   */
  id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  /**
   * api-key is the unique identifier for map component.
   * Every application should have a different api-key
   */
  apiKey: PropTypes.string.isRequired,
  /**
   * default zoom for the map
   */
  zoom: PropTypes.number.isRequired,
  /**
   * map will load w.r.t these cordinates
   */
  center: PropTypes.shape({
    lat: PropTypes.number.isRequired,
    lng: PropTypes.number.isRequired,
  }).isRequired,
  /**
   * array of markers is required for showing markers
   */
  markers: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.number.isRequired,
      name: PropTypes.string.isRequired,
      lat: PropTypes.number.isRequired,
      lng: PropTypes.number.isRequired,
      text: PropTypes.string,
      markerIcon: PropTypes.string,
      size: PropTypes.arrayOf(PropTypes.number),
    })
  ),
  /**
   * as name suggests, it is the currently active marker
   */
  activeMarker: PropTypes.shape({
    id: PropTypes.number.isRequired,
    name: PropTypes.string.isRequired,
    lat: PropTypes.number.isRequired,
    lng: PropTypes.number.isRequired,
    text: PropTypes.string,
    markerIcon: PropTypes.string,
    size: PropTypes.arrayOf(PropTypes.number),
  }),
  /**
   * marker icon for selected marker
   */
  activeMarkerIcon: PropTypes.string,
  /**
   * user can pass the default marker icon which will be shown if no markerIcon is passed
   */
  defaultMarkerIcon: PropTypes.string,
  /**
   * handle the click event on markers
   * (marker: any) => void
   */
  onMarkerClick: PropTypes.func,
  /**
   * Flag should be true to display the popup
   */
  isInfoWindowVisible: PropTypes.bool,
  /**
   * When isInfoWindowVisible is true then domInformationPopup is required for displaying information
   */
  domInformationPopup: PropTypes.node,
  /**
   * If user want to show polygon on map then polygon props should be passed.
   * NOTE: minimum 3 cordinates for polygon should be passed in paths for showing polygon
   */
  polygonProps: PropTypes.shape({
    paths: PropTypes.arrayOf(
      PropTypes.shape({
        lat: PropTypes.number.isRequired,
        lng: PropTypes.number.isRequired,
      })
    ),
  }),
  /**
   * Property to append additional css class
   */
  additionalClassName: PropTypes.arrayOf(PropTypes.string),
  /**
   * Property to set the `inline style` object on button
   */
  additionalStyle: PropTypes.object
};
