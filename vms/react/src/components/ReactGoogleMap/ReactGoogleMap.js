import PropTypes from "prop-types";
import React, { Fragment, memo, useCallback, useMemo } from "react";
import { WrapperDiv } from "./ReactGoogleMap.styled";
import {
  GoogleMap,
  Marker,
  Polygon,
  InfoWindow,
  useLoadScript,
} from "@react-google-maps/api";
import { useClassName } from "../../hooks/useClassName";
// import { ThemeWrapper } from "../Theme/ThemeContext";

// Utility function (placed before main component)
const isNonNull = (prop) => prop !== null && prop !== undefined;

// Static styles (moved outside component to prevent recreation)
const containerStyle = {
  width: "100%",
  height: "100%",
  position: "relative",
  overflow: "hidden",
};

export const ReactGoogleMap = memo((props) => {
  // 1. PROP DESTRUCTURING & VALIDATION
  const {
    id,
    apiKey,
    zoom = 10,
    center = { lat: 0, lng: 0 },
    markers = [],
    activeMarker,
    activeMarkerIcon,
    defaultMarkerIcon,
    onMarkerClick,
    isInfoWindowVisible = false,
    domInformationPopup,
    polygonProps,
    additionalClassName,
    additionalStyle,
    ...otherProps
  } = props;

  // 2. PERFORMANCE OPTIMIZATIONS
  const computedClassName = useMemo(
    () => useClassName(props, additionalClassName),
    [props, additionalClassName]
  );

  const { isLoaded, loadError } = useLoadScript({
    id: "google-map-script",
    googleMapsApiKey: apiKey,
  });

  // 3. UTILITY FUNCTIONS (memoized)
  const getIcon = useCallback((markerElement) => {
    if (!markerElement) return null;

    if (markerElement?.markerIcon) {
      const isActive = markerElement.name === activeMarker?.name;
      const url = isActive
        ? activeMarkerIcon || markerElement.markerIcon
        : markerElement.markerIcon;

      const size = markerElement.size || [24, 38];
      return {
        url: url,
        size: { width: size[0], height: size[1] },
        scaledSize: { width: size[0], height: size[1] },
      };
    }

    if (isNonNull(defaultMarkerIcon)) {
      const isActive = markerElement.name === activeMarker?.name;
      const url = isActive
        ? activeMarkerIcon || defaultMarkerIcon
        : defaultMarkerIcon;

      return {
        url: url,
        size: { width: 24, height: 38 },
        scaledSize: { width: 24, height: 38 },
      };
    }

    return null;
  }, [activeMarker, activeMarkerIcon, defaultMarkerIcon]);

  // 4. EVENT HANDLING with useCallback
  const handleMarkerClick = useCallback((markerElement) => {
    if (isNonNull(onMarkerClick)) {
      onMarkerClick(markerElement);
    }
  }, [onMarkerClick]);

  const handleInfoWindowClose = useCallback(() => {
    if (isNonNull(onMarkerClick)) {
      onMarkerClick(null);
    }
  }, [onMarkerClick]);

  // 5. CONDITIONAL RENDERING (memoized)
  const markersElements = useMemo(() => {
    if (!isNonNull(markers) || markers.length === 0) return null;

    return markers.map((markerElement, index) => {
      const isActiveMarker = activeMarker?.name === markerElement.name;
      const showInfoWindow = isInfoWindowVisible &&
        isNonNull(activeMarker) &&
        isNonNull(domInformationPopup) &&
        isActiveMarker;

      return (
        <Marker
          key={`marker-${markerElement.name || index}`}
          className="vms_reactgooglemap_marker"
          position={{
            lat: markerElement.lat,
            lng: markerElement.lng,
          }}
          icon={getIcon(markerElement)}
          title={markerElement.name}
          onClick={() => handleMarkerClick(markerElement)}
        >
          {showInfoWindow && (
            <InfoWindow
              className="vms_reactgooglemap_inforwindow"
              onCloseClick={handleInfoWindowClose}
            >
              {domInformationPopup}
            </InfoWindow>
          )}
        </Marker>
      );
    });
  }, [markers, activeMarker, isInfoWindowVisible, domInformationPopup, getIcon, handleMarkerClick, handleInfoWindowClose]);

  const polygonElement = useMemo(() => {
    if (!isNonNull(polygonProps) || !polygonProps?.paths) return null;

    return (
      <Polygon
        className="vms_reactgooglemap_polygon"
        paths={polygonProps.paths}
        {...polygonProps}
      />
    );
  }, [polygonProps]);

  // 6. ERROR HANDLING
  React.useEffect(() => {
    if (!apiKey) {
      console.warn('ReactGoogleMap: apiKey is required for Google Maps to work');
    }
    if (loadError) {
      console.error('ReactGoogleMap: Error loading Google Maps', loadError);
    }
  }, [apiKey, loadError]);

  if (loadError) {
    return (
      <WrapperDiv
        id={id}
        className={`${computedClassName} vms_reactgooglemap_container vms_reactgooglemap_error`}
        style={additionalStyle}
        {...otherProps}
      >
        <div>Error loading Google Maps</div>
      </WrapperDiv>
    );
  }

  if (!isLoaded) {
    return (
      <WrapperDiv
        id={id}
        className={`${computedClassName} vms_reactgooglemap_container vms_reactgooglemap_loading`}
        style={additionalStyle}
        {...otherProps}
      >
        <div>Loading Google Maps...</div>
      </WrapperDiv>
    );
  }

  return (
    <Fragment>
      <WrapperDiv
        id={id}
        className={`${computedClassName} vms_reactgooglemap_container`}
        style={additionalStyle}
        {...otherProps}
      >
        <GoogleMap
          center={center}
          zoom={zoom}
          mapContainerStyle={containerStyle}
          className="vms_reactgooglemap_googlemap"
        >
          {markersElements}
          {polygonElement}
        </GoogleMap>
      </WrapperDiv>
    </Fragment>
  );
});

// Component display name for debugging
ReactGoogleMap.displayName = "ReactGoogleMap";

ReactGoogleMap.defaultProps = {
  zoom: 10,
  center: { lat: 0, lng: 0 },
  markers: [],
  isInfoWindowVisible: false,
  domInformationPopup: null,
  activeMarker: null,
  apiKey: null,
};

ReactGoogleMap.propTypes = {
  /**
   * Maps can have its unique identifier
   */
  id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  /**
   * api-key is the unique identifier for map component.
   * Every application should have a different api-key
   */
  apiKey: PropTypes.string.isRequired,
  /**
   * default zoom for the map
   */
  zoom: PropTypes.number.isRequired,
  /**
   * map will load w.r.t these cordinates
   */
  center: PropTypes.shape({
    lat: PropTypes.number.isRequired,
    lng: PropTypes.number.isRequired,
  }).isRequired,
  /**
   * array of markers is required for showing markers
   */
  markers: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.number.isRequired,
      name: PropTypes.string.isRequired,
      lat: PropTypes.number.isRequired,
      lng: PropTypes.number.isRequired,
      text: PropTypes.string,
      markerIcon: PropTypes.string,
      size: PropTypes.arrayOf(PropTypes.number),
    })
  ),
  /**
   * as name suggests, it is the currently active marker
   */
  activeMarker: PropTypes.shape({
    id: PropTypes.number.isRequired,
    name: PropTypes.string.isRequired,
    lat: PropTypes.number.isRequired,
    lng: PropTypes.number.isRequired,
    text: PropTypes.string,
    markerIcon: PropTypes.string,
    size: PropTypes.arrayOf(PropTypes.number),
  }),
  /**
   * marker icon for selected marker
   */
  activeMarkerIcon: PropTypes.string,
  /**
   * user can pass the default marker icon which will be shown if no markerIcon is passed
   */
  defaultMarkerIcon: PropTypes.string,
  /**
   * handle the click event on markers
   * (marker: any) => void
   */
  onMarkerClick: PropTypes.func,
  /**
   * Flag should be true to display the popup
   */
  isInfoWindowVisible: PropTypes.bool,
  /**
   * When isInfoWindowVisible is true then domInformationPopup is required for displaying information
   */
  domInformationPopup: PropTypes.node,
  /**
   * If user want to show polygon on map then polygon props should be passed.
   * NOTE: minimum 3 cordinates for polygon should be passed in paths for showing polygon
   */
  polygonProps: PropTypes.shape({
    paths: PropTypes.arrayOf(
      PropTypes.shape({
        lat: PropTypes.number.isRequired,
        lng: PropTypes.number.isRequired,
      })
    ),
  }),
  /**
   * Property to append additional css class
   */
  additionalClassName: PropTypes.arrayOf(PropTypes.string),

  /**
   * Property to set the `inline style` object on button
   */
  additionalStyle: PropTypes.object,

  /**
   * Whether info window is visible
   */
  isInfoWindowVisible: PropTypes.bool,

  /**
   * DOM content for information popup
   */
  domInformationPopup: PropTypes.node,
};
