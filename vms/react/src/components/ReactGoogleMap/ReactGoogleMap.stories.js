import React, { Fragment, useState } from "react";
import { ReactGoogleMap } from "./ReactGoogleMap";
import { Button } from "../Button/Button";
export default {
  title: "VMS_REACT/ReactGoogleMap",
  component: ReactGoogleMap,
};

const zoom = 12;
const triangleCoords = [
  { lat: 18.566526, lng: 73.912239 },
  { lat: 18.516726, lng: 73.856255 },
  { lat: 18.51581, lng: 73.927193 },
  { lat: 18.55572, lng: 73.95274 },
];

const markers = [
  {
    id: 1,
    name: "Viman Nagar",
    text: "Viman Nagar",
    lat: 18.566526,
    lng: 73.912239,
    markerIcon:
      "https://upload.wikimedia.org/wikipedia/commons/thumb/8/88/Map_marker.svg/1200px-Map_marker.svg.png",
    hotelImgSrc:
      "https://lh5.googleusercontent.com/p/AF1QipOOU-bosnpeI0mJ1UMI0IXdG5BkztlNykyYzoup=w592-h404-n-k-no-v1",
    size: [24, 38],
  },
  {
    id: 3,
    name: "Mundhwa",
    lat: 18.516726,
    lng: 73.856255,
    markerIcon:
      "https://upload.wikimedia.org/wikipedia/commons/thumb/8/88/Map_marker.svg/1200px-Map_marker.svg.png",
    hotelImgSrc:
      "https://lh5.googleusercontent.com/proxy/o9qTqIox1Dy8vdbn8O-Ln0LJRhQJGCLtqfRifPSH2Nm6V3zOFlmkwcnrcTMrPA6Runj9JvjYewsQfT_b9E8Rb6MgnG6Bu7HcLdz1hTFIc6lkbvjBoPhwkeJgBDT30YzFFjNyc-ldxbcqn7WHCKsvdIQ5v6ZprA=w592-h404-n-k-no-v1",
    size: [25, 40],
  },
  {
    id: 4,
    name: "Eon",
    text: "Eon",
    lat: 18.55572,
    lng: 73.95274,
    markerIcon: "https://www.svgrepo.com/show/116159/location-marker.svg",
    hotelImgSrc:
      "https://lh5.googleusercontent.com/proxy/TCz5dS6Y9gOJc-Q9WT1zhXfcFTzvJtjtYM-1WaWLLNnMwpz1rUlwu0PlVbjhsiSJTN18amPJtFFx3IDUPDf3PFuA3UUdvtH5pJagMjna2oJ4sFEf_8FfcAa_AWX05vKT8dsnWweHTiRIHGlyLgZUzJ5eHjii7g=w592-h404-n-k-no-v1",
    size: [30, 40],
  },
  {
    id: 5,
    name: "Magarpatta",
    text: "Magarpatta",
    lat: 18.51581,
    lng: 73.927193,
    hotelImgSrc:
      "https://lh5.googleusercontent.com/proxy/p5xNZKgplCb2nzgecq12E45aZo73cpKgXcKYEThFXvSxSAaf_dTqKQ6Bywfpb8UEMtknPEzyvXOReQ09orIdjj5WuFB41JK9MIuE5OJjO36cnN6F26TaZsZpVhQtDoGSNCVzY7q61JhYUyrLw8QfIYza3p0bGQ=w592-h404-n-k-no-v1",
  },
];

export const Default = (args) => {
  // const key = "AIzaSyDfrRmIrcvHdlGrr5ToL8j6MfOq2S2AfiU";
  const [apiKey, setApiKey] = useState("");
  const [showMaps, setShowMaps] = useState(false);
  const [stateObj, setStateObj] = useState({
    marker: null,
    center: {
      lat: markers[0].lat,
      lng: markers[0].lng,
    },
    isInfoWindowVisible: false,
    domInformationPopup: null,
  });
  const markerHandler = (marker) => {
    if (marker !== null) {
      if (stateObj.marker !== marker) {
        setStateObj({
          ...stateObj,
          marker,
          isInfoWindowVisible: true,
          domInformationPopup: (
            <div>
              <div style={{ padding: "10px", width: "305px", height: "206px" }}>
                <img
                  src={marker.hotelImgSrc}
                  alt={marker.name}
                  style={{ width: "305px", height: "206px" }}
                />
              </div>
              <div style={{ padding: "10px" }}>
                <div style={{ fontSize: "14px", fontWeight: "600" }}>
                  {marker.name}
                </div>
                <div style={{ paddingTop: "10px" }}>
                  <span style={{ fontSize: "12px", fontWeight: "600" }}>
                    Rs. 11,413{" "}
                  </span>
                  <span style={{ paddingLeft: "5px", paddingRight: "5px" }}>
                    <s>Rs. 13,000</s>
                  </span>
                  <span style={{ fontSize: "12px", fontWeight: "600" }}>
                    per night{" "}
                  </span>
                </div>
                <div>{`${marker.name} - 5 Star Hotel`}</div>
                <div>Earn 6000 Points</div>
                <div>
                  <span
                    style={{
                      textDecoration: "underline",
                      cursor: "pointer",
                      color: "blue",
                    }}
                  >
                    Login
                  </span>
                  <span> to get it @ 10,000 or less</span>
                </div>
              </div>
            </div>
          ),
        });
      }
    } else {
      setStateObj({
        ...stateObj,
        marker: null,
        isInfoWindowVisible: false,
        domInformationPopup: null,
      });
    }
  };
  return (
    <Fragment>
      {showMaps === false ? (
        <div style={{ display: "flex", flexDirection: "column", gap: "20px" }}>
          <div>
            <span style={{ color: "red" }}>
              Please Enter API KEY to View Google Maps
            </span>
          </div>
          <div>
            <input
              value={apiKey}
              onChange={(e) => {
                setApiKey(e.target.value);
              }}
              placeholder="Enter API Key"
              style={{ padding: "10px", width: "300px", height: "24px" }}
            />
          </div>
          <div>
            <Button
              onClick={() => {
                if (apiKey.trim().length > 0) {
                  setShowMaps(true);
                } else {
                  setShowMaps(false);
                }
              }}
            >
              Submit
            </Button>
          </div>
        </div>
      ) : (
        <ReactGoogleMap
          {...args}
          id="gmaps"
          apiKey={apiKey}
          center={stateObj.center}
          zoom={zoom}
          markers={markers}
          isInfoWindowVisible={stateObj.isInfoWindowVisible}
          domInformationPopup={stateObj.domInformationPopup}
          activeMarker={stateObj.marker}
          onMarkerClick={markerHandler}
          polygonProps={{
            paths: triangleCoords,
          }}
        />
      )}
    </Fragment>
  );
};
Default.storyName = "Google Maps";
