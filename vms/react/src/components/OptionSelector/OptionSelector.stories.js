import React, { useState } from "react";

import { OptionSelector } from "./OptionSelector";

export default {
  title: "VMS_REACT/OptionSelector",
  component: OptionSelector,
  argTypes: {
    id: { control: { type: "" } },
    additionalStyle: { control: { type: "" } },
    additionalClassName: { control: { type: "" } },
    secondaryTextClassName: { control: { type: "" } },
    primaryTextClassName: { control: { type: "" } },
    options: { control: { type: "" } },
    selectedOptionsIndexes: { control: { type: "" } },
    arrowIcons: { control: { type: "" } },
    scollable: { control: { type: "" } },
    secondaryTextPrefix: { control: { type: "" } },
  },
};

const data = [
  {
    id: "0006",
    primaryText: "1",
    secondaryText: "00-03",
    isDisabled: false,
    images: {
      defaultImage:
        "https://www.intermilesresources.com/sharedresources/img/svg/fly.svg",
      selectedImage:
        "https://img.icons8.com/windows/32/ef4044/filled-minus-2-math.png",
      disabledImage: "https://img.icons8.com/material/24/d1d3d4/more.png",
    },
  },
  {
    id: "0612",
    primaryText: "2",
    secondaryText: "03-06",
    isDisabled: false,
    images: {
      defaultImage:
        "https://www.intermilesresources.com/sharedresources/img/svg/hotels.svg",
      selectedImage:
        "https://img.icons8.com/windows/32/ef4044/multiply-2--v1.png",
      disabledImage: "https://img.icons8.com/material/24/d1d3d4/more.png",
    },
  },
  {
    id: "1218",
    primaryText: "3",
    secondaryText: "06-09",
    isDisabled: false,
    images: {
      defaultImage:
        "https://www.intermilesresources.com/sharedresources/img/svg/shop.svg",
      selectedImage:
        "https://img.icons8.com/windows/32/ef4044/filled-plus-2-math.png",
      disabledImage: "https://img.icons8.com/material/24/d1d3d4/more.png",
    },
  },
  {
    id: "1801",
    primaryText: "4",
    secondaryText: "09-12",
    isDisabled: false,
    images: {
      defaultImage:
        "https://www.intermilesresources.com/sharedresources/img/svg/dine.svg",
      selectedImage: "https://img.icons8.com/material/24/ef4044/star.png",
      disabledImage: "https://img.icons8.com/material/24/d1d3d4/more.png",
    },
  },
  {
    id: "1802",
    primaryText: "5",
    secondaryText: "12-15",
    isDisabled: false,
    images: {
      defaultImage:
        "https://www.intermilesresources.com/sharedresources/img/svg/cards.svg",
      selectedImage: "https://img.icons8.com/material/24/ef4044/cards.png",
      disabledImage: "https://img.icons8.com/material/24/d1d3d4/more.png",
    },
  },
  {
    id: "1803",
    primaryText: "6",
    secondaryText: "12-15",
    isDisabled: true,
    images: {
      defaultImage:
        "https://www.intermilesresources.com/sharedresources/img/svg/cards.svg",
      selectedImage: "https://img.icons8.com/material/24/ef4044/cards.png",
      disabledImage: "https://img.icons8.com/material/24/d1d3d4/more.png",
    },
  },
  {
    id: "1804",
    primaryText: "7",
    secondaryText: "15-18",
    isDisabled: false,
    images: {
      defaultImage:
        "https://www.intermilesresources.com/sharedresources/img/svg/dine.svg",
      selectedImage: "https://img.icons8.com/material/24/ef4044/question.png",
      disabledImage: "https://img.icons8.com/material/24/d1d3d4/more.png",
    },
  },
];

const Template = (args) => <OptionSelector {...args}></OptionSelector>;

const SingleSelectionView = (args) => {
  
  const [selectedOptionIndex,setSelectedOptionIndex] = useState([]);
  return (
    <>
      <div
        style={{
          display: "flex",
        }}
      >
        <div
          style={{
            margin: "20px",
            width: "300px",
          }}
        >
          <h4>Without Scroll mode icon (width: 300px)</h4>
          <OptionSelector {...args} options={data}></OptionSelector>
        </div>
        <div
          style={{
            margin: "20px",
            width: "300px",
          }}
        >
          <h4>Without Scroll mode text(width: 300px)</h4>
          <OptionSelector
            options={data}
            {...args}
            scollable
            mode="icon"
          ></OptionSelector>
        </div>
      </div>
      <div
        style={{
          margin: "20px",
        }}
      >
        <h4>Value passed by Application</h4>
        <OptionSelector options={data} {...args} selectedOptionsIndexes={selectedOptionIndex} onOptionSelectionChange={(value)=>{
          setSelectedOptionIndex(value);
        }}></OptionSelector>
      </div>
    </>
  );
};

const WithPrefixView = (args) => {
  return (
    <>
      <div
        style={{
          margin: "0px",
        }}
      >
        <h4>Default Prefix</h4>
        <OptionSelector {...args} options={data}></OptionSelector>
      </div>
      <div
        style={{
          margin: "0px",
        }}
      >
        <h4>Custom Prefix</h4>
        <OptionSelector
          options={data}
          secondaryTextPrefix="$"
          {...args}
        ></OptionSelector>
      </div>
    </>
  );
};

const CustomStylingView = (args) => {
  return (
    <>
      <div
        style={{
          margin: "20px",
          width: "300px",
        }}
      >
        <h4>Default Prefix</h4>
        <OptionSelector {...args} options={data}></OptionSelector>
      </div>
      <div
        style={{
          margin: "20px",
          width: "300px",
        }}
      >
        <h4>Custom Prefix</h4>
        <OptionSelector
          options={data}
          secondaryTextPrefix="$"
          {...args}
        ></OptionSelector>
      </div>
    </>
  );
};

const MobView = (args) => {
  return (
    <>
      <div
        style={{
          margin: "0px",
        }}
      >
        <h4>Without Scroll mode icon</h4>
        <OptionSelector {...args} options={data}></OptionSelector>
      </div>

      <div
        style={{
          margin: "0px",
        }}
      >
        <h4>Without Scroll mode Text</h4>
        <OptionSelector {...args} mode="text" options={data}></OptionSelector>
      </div>

      <div
        style={{
          margin: "0px",
        }}
      >
        <h4>With Scroll mode icon</h4>
        <OptionSelector
          {...args}
          mode="icon"
          scollable
          options={data}
        ></OptionSelector>
      </div>

      <div
        style={{
          margin: "0px",
        }}
      >
        <h4>With Scroll mode Text</h4>
        <OptionSelector
          {...args}
          scollable
          mode="text"
          options={data}
        ></OptionSelector>
      </div>

      <div
        style={{
          margin: "0px",
        }}
      >
        <h4>Value passed by Application</h4>
        <OptionSelector
          {...args}
          scollable
          mode="text"
          selectedOptionsIndexes={[0, 2]}
          options={data}
        ></OptionSelector>
      </div>
      <div
        style={{
          margin: "0px",
        }}
      >
        <h4>Without Scroll mode Text With Icon</h4>
        <OptionSelector {...args} mode="textWithIcon" options={data}></OptionSelector>
      </div>
    </>
  );
};

export const SingleSelection = SingleSelectionView.bind({});
SingleSelection.args = {
  id: "SingleSelection",
  multipleSelect: false,
  showSecondaryTextPrefix: false,
};

export const MutipleSelection = SingleSelectionView.bind({});
MutipleSelection.args = {
  // id: "MutipleSelection",
  multipleSelect: true,
};

export const WithPrefix = WithPrefixView.bind({});
WithPrefix.args = {
  id: "WithPrefix",
  mode: "text",
  scollable: true,
  showSecondaryTextPrefix: true,
};

export const WithCustomStyling = CustomStylingView.bind({});
WithCustomStyling.args = {
  id: "WithCustom",
  mode: "text",
  scollable: true,
  showSecondaryTextPrefix: true,
  arrowIcons: {
    leftArrowIcon: "https://img.icons8.com/windows/24/ef4044/back-arrow.png",
    rightArrowIcon: "https://img.icons8.com/windows/24/ef4044/arrow.png",
  },
};

export const MobWebSingleSelection = MobView.bind({});
MobView.args = {
  id: "MobSingleView",
  isMobile: true,
};

export const MobWebMutipleSelection = MobView.bind({});
MobWebMutipleSelection.args = {
  id: "MobMutipleView",
  isMobile: true,
  multipleSelect: true,
};

export const MobWebWithPrefix = WithPrefixView.bind({});
MobWebWithPrefix.args = {
  id: "MobWithPrefix",
  isMobile: true,
  mode: "text",
  scollable: true,
  showSecondaryTextPrefix: true,
};
