import styled, { css } from "styled-components";
export const Container = styled.div`
  display: flex;
  justify-content: flex-start;
  flex-direction: row;
  margin-left: 1px;
  margin-right: 1px;
`;

export const ItemContainer = styled.div`
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  margin-top: 5px;
  margin-bottom: 5px;
  margin-right: 12px;

`;

export const Item = styled.button`
  width: ${({ isMobile }) =>isMobile ? "60px" : "44px"};
  height: ${({ isMobile }) =>isMobile ? "60px" : "4px"};
  display: flex;
  border-radius: 3px;
  align-items: center;
  justify-content: center;
  margin-bottom: ${({ isMobile }) =>isMobile ? "8px" : "10px"};
  background-color: #ffffff;
  outline: none;
  border: 1px solid #f4f4f4;
  box-shadow: 0 2px 4px 0 rgb(0 0 0 / 9%);
  padding: 0px;
  box-sizing: content-box;

  ${ItemContainer}.disabled & {
    cursor: not-allowed;
    border: 1px solid #ebebec;
    box-shadow: none;
  }

  ${ItemContainer}.selected & {
  border: ${({ theme, type, color }) => (`1px solid ${theme?.palette?.[type]?.[theme.mode]}`)};
  }
`;

export const SecondaryValueContainer = styled.div`
  display: flex;
  width: 100%;
  justify-content: center;
  align-items: baseline;
`;

export const SecondaryLabel = styled.span`
  font-size: 12px;
  line-height: 16px;
  letter-spacing: 0.4px;
  font-weight: 400;
  font-family: Montserrat;
  color: ${({ theme, type, isSelected }) => (isSelected ? theme?.palette?.[type]?.[theme.mode] : 'rgb(77, 77, 79)' )};
`;

export const WithoutScrollDiv = styled.div`
  width: 100%;
  flex-wrap: wrap;
  display: flex;
  align-items: flex-start;
`;

export const WithScrollDiv = styled.div`
  width: 100%;
  overflow-x: auto;
  display: flex;
  scroll-behavior: smooth;
  &::-webkit-scrollbar {
    width: 0;
    background: transparent;
  }
  &::-webkit-scrollbar-thumb {
    background: transparent;
  }
`;

export const LeftIconContainer = styled.div`
  width: 24px;
  height: 24px;
  display: inline-flex;
  text-align: center;
  margin: auto 5px;
`;

export const RightIconContainer = styled.div`
  width: 24px;
  height: 24px;
  display: inline-flex;
  text-align: center;
  margin: auto 5px;
`;

export const Icon = styled.div`
  width: 24px;
  height: 24px;
`;

export const Disabled = styled.div`
  cursor: not-allowed;
  border: 1px solid #ebebec;
  box-shadow: none;
`;

export const PrimaryLabel = styled.div`
color: ${({ isDisabled }) =>isDisabled ? "rgb(209, 211, 212)" : "rgb(77, 77, 79)"};
  text-align: center;
  font-weight: 400;
  font-family: Montserrat;
  font-size: 16px;
  line-height: 22px;
  letter-spacing: 0.3px;
  justify-content: center;
  align-self: center;
`;
