import React, { useState, useEffect, useRef, memo, useCallback, useMemo } from "react";
import PropTypes from "prop-types";
import {
  Container,
  Item,
  ItemContainer,
  SecondaryLabel,
  SecondaryValueContainer,
  WithoutScrollDiv,
  Disabled,
  LeftIconContainer,
  Icon,
  RightIconContainer,
  WithScrollDiv,
  PrimaryLabel,
} from "./OptionSelector.styled";
import classNames from "classnames";
import BackArrowSvg from "../../assets/images/DateRangeCalendar/backArrow.svg";
import ForwardArrowSvg from "../../assets/images/DateRangeCalendar/forwardArrow.svg";
import { useClassName } from "../../hooks/useClassName";
import { ThemeWrapper } from "../Theme/ThemeContext";

const OptionSelector = memo((props) => {
  // 1. PROP DESTRUCTURING & VALIDATION
  const {
    scollable = false,
    mode = "icon",
    selectedOptionsIndexes = [],
    showSecondaryTextPrefix = false,
    secondaryTextPrefix = "₹",
    arrowIcons,
    additionalStyle,
    additionalClassName,
    primaryTextClassName,
    secondaryTextClassName,
    isMobile,
    isMobileView,
    type = "primary",
    id,
    onOptionSelectionChange,
    options = [],
    multipleSelect = false,
    ...otherProps
  } = props;

  // 2. STATE MANAGEMENT
  const [optionData, setOptionData] = useState([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [activeIndex, setActiveIndex] = useState(0);
  const [changePosition, setChangePosition] = useState(false);

  // 3. REFS
  const maxScrollWidth = useRef(0);
  const scrollRef = useRef(null);
  
  // Create refs for each option - memoized
  const refs = useMemo(() => 
    options.reduce((acc, value) => {
      acc[value.id] = React.createRef();
      return acc;
    }, {}), 
    [options]
  );

  // 4. PERFORMANCE OPTIMIZATIONS
  const computedClassName = useMemo(() => 
    useClassName(props, additionalClassName), 
    [props, additionalClassName]
  );

  const primaryTextclassName = useMemo(() => 
    useClassName(props, primaryTextClassName), 
    [props, primaryTextClassName]
  );

  const secondaryTextclassName = useMemo(() => 
    useClassName(props, secondaryTextClassName), 
    [props, secondaryTextClassName]
  );

  // 5. UTILITY FUNCTIONS
  const isDisabled = useCallback((direction) => {
    if (direction === "prev") {
      return currentIndex <= 0;
    }

    if (direction === "next" && scrollRef.current !== null) {
      return (
        scrollRef.current.offsetWidth * currentIndex >= maxScrollWidth.current
      );
    }

    return false;
  }, [currentIndex]);

  const handleClick = useCallback((id) => {
    if (scollable && refs[id]?.current) {
      refs[id].current.scrollIntoView({
        behavior: "smooth",
        block: "start",
      });
    }
  }, [scollable, refs]);

  // 6. EVENT HANDLERS
  const selectOption = useCallback((evt, item, index) => {
    if (multipleSelect) {
      setChangePosition(true);
      setOptionData(prevState => {
        const newState = [...prevState];
        newState[index].selected = !newState[index].selected;
        
        // Get selected indexes
        const tempArray = [];
        newState.forEach((item, idx) => {
          if (item.selected) {
            tempArray.push(idx);
          }
        });
        
        // Trigger callback
        if (onOptionSelectionChange) {
          onOptionSelectionChange(tempArray);
        }
        
        return newState;
      });

      const nextElement = optionData[index];
      if (nextElement) {
        setActiveIndex(nextElement.id);
      }
    } else {
      setOptionData(prevState => {
        const newData = prevState.slice();
        newData.forEach((e, i) => {
          e.selected = i === index;
        });
        
        // Trigger callback
        if (onOptionSelectionChange) {
          onOptionSelectionChange(index);
        }
        
        return newData;
      });
    }
  }, [multipleSelect, optionData, onOptionSelectionChange]);

  const movePrev = useCallback(() => {
    if (currentIndex > 0) {
      setCurrentIndex(prevState => prevState - 1);
    }
  }, [currentIndex]);

  const moveNext = useCallback(() => {
    if (
      scrollRef.current !== null &&
      scrollRef.current.offsetWidth * currentIndex <= maxScrollWidth.current
    ) {
      setCurrentIndex(prevState => prevState + 1);
    }
  }, [currentIndex]);

  // 7. EFFECTS
  useEffect(() => {
    // Load initial data
    if (options?.length > 0) {
      const initialData = options.map((option, i) => ({
        ...option,
        selected: selectedOptionsIndexes?.includes(i) || false
      }));
      setOptionData(initialData);
    }
  }, [options]);

  useEffect(() => {
    // Update selection state when selectedOptionsIndexes changes
    setOptionData(prevState => {
      const updatedOptionsData = [...prevState];
      updatedOptionsData.forEach((e, i) => {
        const checkIsSelect = selectedOptionsIndexes?.includes(i);
        e.selected = checkIsSelect;
      });
      return updatedOptionsData;
    });
  }, [selectedOptionsIndexes]);

  useEffect(() => {
    // Handle scroll position
    if (scrollRef.current) {
      scrollRef.current.scrollLeft = scrollRef.current.offsetWidth * currentIndex;
    }
  }, [currentIndex]);

  useEffect(() => {
    // Handle active index change
    if (activeIndex !== 0) {
      handleClick(activeIndex);
    }
  }, [activeIndex, handleClick]);

  useEffect(() => {
    // Calculate max scroll width
    if (scrollRef.current) {
      maxScrollWidth.current = scrollRef.current.scrollWidth - scrollRef.current.offsetWidth;
    }
  }, []);

  // 8. RENDER FUNCTIONS (memoized)
  const renderItem = useMemo(() => {
    if (!optionData?.length) return null;

    return optionData.map((e, i) => (
      <ItemContainer
        className={`vms_optionselector_itemcontainer ${classNames({
          disabled: e.isDisabled,
          selected: e.selected,
        })}`}
        key={e.id}
        ref={refs[e.id]}
        isMobile={isMobile}
        onClick={(evt) => !e.isDisabled && selectOption(evt, e, i)}
        role="option"
        aria-selected={e.selected}
        aria-disabled={e.isDisabled}
        tabIndex={e.isDisabled ? -1 : 0}
        onKeyDown={(event) => {
          if ((event.key === 'Enter' || event.key === ' ') && !e.isDisabled) {
            event.preventDefault();
            selectOption(event, e, i);
          }
        }}
      >
        <Item
          id={id ? `${id}-${i}` : null}
          className="vms_optionselector_item"
          isMobile={isMobile}
          type={type}
          disabled={e.isDisabled}
          aria-label={`Option ${e.primaryText || ''} ${e.secondaryText || ''}`}
        >
          {mode === "text" ? (
            <PrimaryLabel
              className={`${primaryTextclassName} vms_optionselector_primarylabel`}
              isDisabled={e.isDisabled}
            >
              {e.primaryText}
            </PrimaryLabel>
          ) : mode === "textWithIcon" ? (
            <div>
              <img
                src={
                  e.isDisabled
                    ? e.images.disabledImage
                    : e.selected
                    ? e.images.selectedImage
                    : e.images.defaultImage
                }
                alt={`${e.primaryText} icon`}
              />
              <PrimaryLabel
                className={`${primaryTextclassName} vms_optionselector_primarylabel`}
                isDisabled={e.isDisabled}
              >
                {e.primaryText}
              </PrimaryLabel>
              <SecondaryValueContainer className="vms_optionselector_secondarylabelcontainer">
                <SecondaryLabel
                  className={`${secondaryTextclassName} vms_optionselector_secondarylabel`}
                  isDisabled={e.isDisabled}
                  type={type}
                  isSelected={e.selected}
                >
                  {showSecondaryTextPrefix
                    ? `${secondaryTextPrefix} ${e.secondaryText}`
                    : e.secondaryText}
                </SecondaryLabel>
              </SecondaryValueContainer>
            </div>
          ) : (
            <img
              src={
                e.isDisabled
                  ? e.images.disabledImage
                  : e.selected
                  ? e.images.selectedImage
                  : e.images.defaultImage
              }
              alt={`${e.primaryText || 'Option'} icon`}
            />
          )}
        </Item>
        {mode !== "textWithIcon" && (
          <SecondaryValueContainer className="vms_optionselector_secondarylabelcontainer">
            <SecondaryLabel
              className={`${secondaryTextclassName} vms_optionselector_secondarylabel`}
              isDisabled={e.isDisabled}
              type={type}
              isSelected={e.selected}
            >
              {showSecondaryTextPrefix
                ? `${secondaryTextPrefix} ${e.secondaryText}`
                : e.secondaryText}
            </SecondaryLabel>
          </SecondaryValueContainer>
        )}
      </ItemContainer>
    ));
  }, [
    optionData,
    refs,
    isMobile,
    selectOption,
    id,
    type,
    mode,
    primaryTextclassName,
    secondaryTextclassName,
    showSecondaryTextPrefix,
    secondaryTextPrefix
  ]);

  const withoutScrollView = useMemo(() => (
    <WithoutScrollDiv className="vms_optionselector_renderitem">
      {renderItem}
    </WithoutScrollDiv>
  ), [renderItem]);

  const withScrollView = useMemo(() => (
    <>
      {!isMobile && (
        <LeftIconContainer 
          className="vms_optionselector_lefticon_container" 
          onClick={movePrev}
          style={{ display: isDisabled("prev") ? "none" : "" }}
          role="button"
          aria-label="Previous options"
          tabIndex={isDisabled("prev") ? -1 : 0}
        >
          <Icon className="vms_optionselector_left_icon">
            <img
              src={arrowIcons?.leftArrowIcon || BackArrowSvg}
              alt="Previous"
            />
          </Icon>
        </LeftIconContainer>
      )}

      <WithScrollDiv 
        className="vms_optionselector_scrolldiv" 
        ref={scrollRef}
        role="listbox"
        aria-multiselectable={multipleSelect}
      >
        {renderItem}
      </WithScrollDiv>
      
      {!isMobile && (
        <RightIconContainer 
          className="vms_optionselector_righticon_container" 
          onClick={moveNext}
          style={{ display: isDisabled("next") ? "none" : "" }}
          role="button"
          aria-label="Next options"
          tabIndex={isDisabled("next") ? -1 : 0}
        >
          <Icon className="vms_optionselector_right_icon">
            <img
              src={arrowIcons?.rightArrowIcon || ForwardArrowSvg}
              alt="Next"
            />
          </Icon>
        </RightIconContainer>
      )}
    </>
  ), [
    isMobile,
    movePrev,
    moveNext,
    isDisabled,
    arrowIcons,
    renderItem,
    multipleSelect
  ]);

  // 9. ERROR HANDLING
  React.useEffect(() => {
    if (!id) {
      console.warn('OptionSelector: id prop is required for accessibility');
    }
    if (!options || options.length === 0) {
      console.warn('OptionSelector: options array is required and should not be empty');
    }
  }, [id, options]);

  return (
    <ThemeWrapper isMobile={isMobile || isMobileView || false}>
      <Container 
        id={id} 
        className={`${computedClassName} vms_optionselector_main_container`} 
        style={additionalStyle}
        role="group"
        aria-label="Option selector"
        {...otherProps}
      >
        {scollable ? withScrollView : withoutScrollView}
      </Container>
    </ThemeWrapper>
  );
});

// Component display name for debugging
OptionSelector.displayName = 'OptionSelector';

OptionSelector.defaultProps = {
  multipleSelect: false,
  mode: "icon",
  type: "primary",
  scollable: false,
  selectedOptionsIndexes: [],
  showSecondaryTextPrefix: false,
  secondaryTextPrefix: "₹",
  primaryTextClassName: null,
  secondaryTextClassName: null,
  additionalStyle: null,
  additionalClassName: null,
  isMobile: false,
};

OptionSelector.propTypes = {
  /**
   * This id will be appiled to scrollable div and used to scroll the div
   */
  id: PropTypes.string.isRequired,

  /**
   * If true then it will allow to select multiple options If false then only one option will be selected at a time default value false
   */
  multipleSelect: PropTypes.bool,

  /**
   *{
   id: string,
   primaryText?: string, // Only considered when mode is text
   secondaryText?: string,
   isDisabled?: boolean // To Disable option (optional),
   images?: { // Only considered when mode is icon
      defaultImage?: string; // when option is unselected this will display
      selectedImage: string; // when option is selected this will display
      disabledImage?: string; // when option is disabled this will display
   }
}

{ id: string; primaryText?: string; secondaryText?: string; isDisabled?: boolean; images?: { defaultImage: string; selectedImage?: string; disabledImage?: string; }; }[]
   */
  options: PropTypes.array.isRequired,
  /**
   * Primary value type. It can be icon or text Default value icon
   */
  mode: PropTypes.oneOf(["icon", "text"]),

  /**
   * If true then all options will be shown in horizontal scroll view If false then options will shown in rows as per screen and option size
   */
  scollable: PropTypes.bool,
  /**
   * indexes that will be selected by default Default value []
   */
  selectedOptionsIndexes: PropTypes.arrayOf(PropTypes.number),

  /**
   * Pass true to display secondary Text Prefix
Default value false
   */
  showSecondaryTextPrefix: PropTypes.bool,

  /**
   * Only applicable if @property showSecondaryTextPrefix is true
Secondary Text Prefix value
It will display in Roboto-light font
Default value ₹
   */
  secondaryTextPrefix: PropTypes.bool,

  /**
   *Custom icons path or base64
{ leftArrowIcon?: string; rightArrowIcon?: string; }
   */
  arrowIcons: PropTypes.shape({
    leftArrowIcon: PropTypes.string,
    rightArrowIcon: PropTypes.string,
  }),

  /**
   * Property to append additional conatiner css class
   */
  additionalClassName: PropTypes.arrayOf(PropTypes.string),
  /**
   * Property to set the conatiner `style` object on button
   */
  additionalStyle: PropTypes.object,

  /**
   * Property to append additional conatiner css class
   */
  primaryTextClassName: PropTypes.arrayOf(PropTypes.string),
  /**
   * Property to set the conatiner `style` object on button
   */
  secondaryTextClassName: PropTypes.arrayOf(PropTypes.string),
  /**
    *  return  All seleted Index
    */
  onOptionSelectionChange: PropTypes.func
};
export { OptionSelector };
