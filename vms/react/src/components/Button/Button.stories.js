import React from "react";
import { Button } from "./Button";

export default {
  title: "VMS_REACT/Button",
  component: Button,
  argTypes: {
    progress: { control: { type: "" } },
    additionalStyle: { control: { type: "" } },
    additionalClassName: { control: { type: "" } },
  },
};

const Template = (args) => <Button {...args}>Button</Button>;

export const Primary = Template.bind({});
Primary.args = {
  buttonType: "primary",
  disabled: false,
  isLoading: false,
  rippleEffect:false,
  id:"btn"
};

export const PrimaryProgress = Template.bind({});
PrimaryProgress.args = {
  buttonType: "primary",
  disabled: false,
  isLoading: false,
  // isLoading: false,
  isMobile: false,
  rippleEffect:false,
  id:"btn1"
};
export const Secondary = Template.bind({});
Secondary.args = {
  buttonType: "secondary",
  disabled: false,
  isLoading: false,
  isMobile: false,
  rippleEffect:false,
  id:"btn2"

};
export const PrimaryMobile = (args) => <Button id={"btn"} {...args}>Button</Button>;
PrimaryMobile.parameters = {
  viewport: {
    defaultViewport: "iphone5",
  },
};
PrimaryMobile.args = {
  buttonType: "primary",
  disabled: false,
  isLoading: false,
  isMobile: true,
};
