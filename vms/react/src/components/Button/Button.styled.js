import styled, { css, keyframes } from "styled-components";

function _fontWeight(theme) {
  return theme?.typography?.text?.fontWeight || "";
}
function _fontFamily(theme) {
  return theme?.typography?.fontFamily || "";
}
function _fontSize(theme) {
  return theme?.typography?.text?.fontSize || "";
}
function _lineHeight(theme) {
  return theme?.typography?.lineHeight || "";
}
function _letterSpacing(theme) {
  return theme?.typography?.letterSpacing || "";
}
// function _textTransform(theme) {
//   return theme?.typography?.button?.textTransform || "inherit";
// }
function _color(theme, buttonType) {
  switch (buttonType) {
    case "link":
    case "tertiary":
      return theme.palette.primary[theme.mode];
    case "secondary":
      return theme.palette.secondary["contrastText"];
    default:
      return theme.palette.primary["contrastText"];
  }
}
function _backgroundColor(theme, buttonType) {
  switch (buttonType) {
    case "link":
    case "tertiary":
      return "transparent";
    case "secondary":
      return theme.palette.secondary[theme.mode];
    default:
      return theme.palette.primary[theme.mode];
  }
}
function _border(theme, buttonType) {
  return buttonType === "tertiary"
    ? `1px solid ${theme.palette.primary[theme.mode]}`
    : `none`;
}
function _borderRadius(theme) {
  return theme.shape.borderRadius;
}
function _width(buttonType, isMobile) {
  if (buttonType === "link") {
    return null;
  } else if (isMobile) {
    return "100%";
  } else {
    return "300px";
  }
}

export const StyledButton = styled.button`
  width: ${({ buttonType, isMobile }) => _width(buttonType, isMobile)};
  height: ${({ buttonType }) => (buttonType !== "link" ? `48px` : `0px`)};
  font-family: ${({ theme }) => _fontFamily(theme)};
  font-size: ${({ theme }) => _fontSize(theme)};
  font-weight: ${({ theme }) => _fontWeight(theme)};
  font-style: normal;
  font-stretch: normal;
  line-height: ${({ theme }) => _lineHeight(theme)};
  letter-spacing: ${({ theme }) => _letterSpacing(theme)};
  text-align: center;
  color: ${({ theme, buttonType }) => _color(theme, buttonType)};
  border-radius: ${({ theme, buttonType }) =>
    buttonType !== "link" && _borderRadius(theme)};
  border: ${({ theme, buttonType }) => _border(theme, buttonType)};
  outline: 0;
  margin: 0px;
  padding: 0px;
  position: relative;
  background: ${({ isLoading }) =>
    isLoading
      ? `linear-gradient(to right, rgba(255, 255, 255, 0.2) 50%, transparent 20%)`
      : `none`};
  ${({ rippleEffect, disabled, isLoading }) =>
    rippleEffect &&
    !disabled &&
    !isLoading &&
    `
    background-position: center;
    transition: background 0.8s;
    &:hover {
      filter: brightness(100%);
      background-image: radial-gradient(circle,transparent 1%,rgba(255, 255, 255, 0.2) 1%);
      background-position: center;
      background-size: 15000%;
    }
    &:active {
      transition: background 0s;
      background-color: ${({ theme }) => _color(theme, buttonType)};
      background-size: 100%;
    }
    `};
  ${({ isLoading }) => isLoading && animation};
  background-size: 200% 100%;
  background-position: ${({ isLoading }) => isLoading && `right bottom`};
  cursor: ${({ isLoading, disabled }) =>
    isLoading || disabled ? `not-allowed` : `pointer`};
  background-color: ${({ theme, buttonType }) =>
    _backgroundColor(theme, buttonType)};
  opacity: ${({ disabled }) => (disabled ? `0.5` : `1`)};
`;

const isLoadingKeyFrame = keyframes`
from {
  background-position: right bottom;
}
to {
  background-position: left bottom;
}
`;
const animation = css`
  animation: ${isLoadingKeyFrame} 2s linear infinite;
`;

export const AccordionIcon = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
`;

export const AccordionSvg = styled.svg`
  color: ${({ theme }) => theme.palette.primary[theme.mode]};
  transform: ${({ isButtonExpanded }) =>
    isButtonExpanded === true ? `rotate(180deg)` : ""};
`;
