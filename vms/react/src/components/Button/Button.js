import React, { memo, useCallback, useMemo } from "react";
import PropTypes from "prop-types";
import { StyledButton, AccordionIcon, AccordionSvg } from "./Button.styled";
import { useClassName } from "../../hooks/useClassName";
import { ThemeWrapper } from "../Theme/ThemeContext";

// Utility function (placed before main component)
const isNonNull = (prop) => prop !== null && prop !== undefined;

/**
 * Button UI component for user interaction
 */
const Button = memo((props) => {
  // 1. PROP DESTRUCTURING & VALIDATION
  const {
    id,
    children,
    onClick,
    buttonType = "primary",
    disabled = false,
    isLoading = false,
    rippleEffect = false,
    isExpansionButton = false,
    isButtonExpanded,
    progress,
    additionalClassName,
    additionalStyle,
    className,
    isMobile,
    isMobileView,
    ...otherProps
  } = props;

  // 2. PERFORMANCE OPTIMIZATIONS
  const computedClassName = useMemo(() => 
    useClassName(props, additionalClassName), 
    [props, additionalClassName]
  );

  const isProgressMode = useMemo(() => 
    !isNaN(progress) && progress !== null, 
    [progress]
  );

  const buttonContent = useMemo(() => {
    if (isProgressMode) {
      return `Retry in ${progress}`;
    }
    return children;
  }, [isProgressMode, progress, children]);

  // 3. EVENT HANDLING with useCallback
  const handleClick = useCallback((event) => {
    if (disabled || isLoading) return;
    
    event?.stopPropagation();
    
    if (isNonNull(onClick)) {
      onClick(event);
    }
  }, [disabled, isLoading, onClick]);

  // 4. CONDITIONAL RENDERING (memoized)
  const expansionIcon = useMemo(() => {
    if (!isExpansionButton) return null;
    
    return (
      <AccordionIcon className="vms_accordion_expansionicon">
        <ExpansionIcon isButtonExpanded={isButtonExpanded} />
      </AccordionIcon>
    );
  }, [isExpansionButton, isButtonExpanded]);

  // 5. ERROR HANDLING
  React.useEffect(() => {
    if (isExpansionButton && !isNonNull(isButtonExpanded)) {
      console.warn('Button: isButtonExpanded prop should be provided when isExpansionButton is true');
    }
  }, [isExpansionButton, isButtonExpanded]);

  return (
    <ThemeWrapper isMobile={isMobile || isMobileView || false}>
      <StyledButton
        id={id}
        className={`${computedClassName} vms_button`}
        style={additionalStyle}
        disabled={disabled || isLoading}
        onClick={handleClick}
        buttonType={buttonType}
        isLoading={isLoading}
        isMobile={isMobile}
        rippleEffect={rippleEffect}
        aria-disabled={disabled || isLoading}
        aria-busy={isLoading}
        type="button"
        {...otherProps}
      >
        {buttonContent}
        {expansionIcon}
      </StyledButton>
    </ThemeWrapper>
  );
});

// Memoized SVG component
const ExpansionIcon = memo(({ isButtonExpanded }) => (
  <AccordionSvg
    isButtonExpanded={isButtonExpanded}
    xmlns="http://www.w3.org/2000/svg"
    width="15"
    height="8"
    viewBox="0 0 15 8"
    role="img"
    aria-hidden="true"
  >
    <g fill="none" fillRule="evenodd">
      <path d="M0 0H24V24H0z" transform="translate(-5 -8)" />
      <g stroke="currentColor" strokeLinecap="round">
        <path
          d="M0 7L7 0M0 7L2.994 9.994 7 14"
          transform="translate(-5 -8) matrix(0 -1 -1 0 19.5 15.5)"
        />
      </g>
    </g>
  </AccordionSvg>
));

// Component display names for debugging
Button.displayName = 'Button';
ExpansionIcon.displayName = 'ExpansionIcon';

Button.defaultProps = {
  buttonType: "primary",
  additionalClassName: null,
  additionalStyle: null,
  disabled: false,
  isLoading: false,
  rippleEffect: false,
  isExpansionButton: false,
};
Button.propTypes = {
  /**
   * Optional click handler
   */
   /**
    
  * Unique ID for the field. Required for web accessibility
   */
  id: PropTypes.string,
  /**
   * Variations of Button Type
   */
  onClick: PropTypes.func,
  /**
   * Variations of Button Type
   */
  buttonType: PropTypes.oneOf(["primary", "secondary", "link", "tertiary"]),
  /**
   * Property to disable the button
   */
  disabled: PropTypes.bool,
  /**
   * Property to show if some activity is going-on in the background, if true loading animation will appear on button
   */
  isLoading: PropTypes.bool,
  /**
   * Property to append additional css class
   */
  additionalClassName: PropTypes.arrayOf(PropTypes.string),
  /**
   * Property to set the `inline style` object on button
   */
  additionalStyle: PropTypes.object,
  /**
   * Property to show progress on button text. Example: if progress=30 then it will display `Retry in 30`
   */
  progress: PropTypes.number,
  /**
   * Mobile-View
   */
  isMobile: PropTypes.bool,
  /**
   * enables the Ripple effect animation Onclick
   */
  rippleEffect: PropTypes.bool,
  /**
   * This prop will be used for the childrens of the button. 
   * Mostly this will be title of the button but could be used for anything else too like: Image
   * `ReactNode`
   */
  children: PropTypes.node,
  isExpansionButton: PropTypes.bool,
};

export { Button };
