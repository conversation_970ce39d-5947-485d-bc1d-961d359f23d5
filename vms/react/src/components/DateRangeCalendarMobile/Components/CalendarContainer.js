import styled from "styled-components";

const Row = styled.div`
  margin: 0;
  padding: 0;
  flex-grow: 1;
  flex-basis: auto;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  align-items: flex-start;
  justify-content: ${(props) =>
    props.justifyContent ? props.justifyContent : ""};
`;

const Col = styled.div`
  flex-grow: 1;
  flex-basis: auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: ${(props) =>
    props.justifyContent ? props.justifyContent : "center"};
  text-align: ${(props) => props.textAlign};
`;

const CalendarContainer = styled.div`
  width: 100%;
  border: none;
  box-shadow: none;
  background-color: ${({ theme, type }) => theme?.palette?.[type]?.contrastText || ""};
`;

const PickerBodyContainer = styled.div`
display: ${(props) => (props.editting ? "block" : "none")};
box-shadow: none;
border: none;
background-color: transparent;
width:auto;
border-left: none;
overflow: scroll;
height: ${({ isMobile }) => (isMobile ? "70vh" : "200px")};
&::-webkit-scrollbar {
  width: 0;
  background: transparent;
}
&::-webkit-scrollbar-thumb {
  background: transparent;
}
`;

// const PickerBodyContainer = styled.div`
//   display: ${(props) => (props.editting ? "block" : "none")};
//   box-shadow: none;
//   border: none;
//   background-color: transparent;
//   border-left: ${(props) => (props.subView ? "1px solid #ebebec" : "none")};

//   width: 100%;
//   height: 200px;
//   overflow: scroll;
// `;

const PickerContainer = styled.div`
  background: transparent;
`;
const DateContainer = styled.div`
  display: flex;
`;

const Divider = styled.div`
  overflow: hidden;
  border-top: 1px solid #33435b73;
  width: 100%;
  margin: 0 0 24px;
  padding: 0;
  height: auto !important;
`;

const CalendarCloseButton = styled.div`
  margin: 0 auto;
  text-align: center;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
`;
const StickyWeekDay = styled.div`
  padding: 0;
  margin: 0 auto;
  width: 100%;
  border-bottom: 1px solid #ebebec;
  z-index: 1000;
  position: relative;
  background-color: white;
  display: flex;
`;

const StickyWeekDayLabel = styled.span`
  font-size: 12px;
  line-height: 1.4;
  letter-spacing: normal;
  text-align: center;
  color: #4d4d4f;
  font-weight: 400;
  font-family: ${({ theme }) => theme?.typography?.fontFamily || ""};
  padding: 0.7em 0.3em;
  flex-basis: calc(100% / 7);
  box-sizing: inherit;
  text-align: center;
`;

const InputContainer = styled.div`
  display: inline-block;
  min-width: 100%;
`;

const InputRighView = styled.div`
  float: right;
  width: 48%;
`;

const InputLefView = styled.div`
  float: left;
  width: 48%;
`;

const InputLabel = styled.div`
  font-weight: 400;
  font-family: ${({ theme }) => theme?.typography?.fontFamily || ""};
  font-size: ${({ theme }) => theme?.typography?.text?.fontSize || ""};
  font-stretch: normal;
  font-style: normal;
  line-height: 20px;
  letter-spacing: 0.3px;
  color: #4d4d4f;
  display: block;
  margin: 12px 0;
  position: relative;
  text-transform: uppercase;
`;

export {
  Col,
  Row,
  PickerContainer,
  PickerBodyContainer,
  CalendarContainer,
  Divider,
  CalendarCloseButton,
  StickyWeekDay,
  StickyWeekDayLabel,
  DateContainer,
  InputContainer,
  InputLefView,
  InputRighView,
  InputLabel,
};
