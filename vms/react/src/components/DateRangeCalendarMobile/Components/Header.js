import React from "react";
import styled from "styled-components";
import PropTypes from "prop-types";
import { Row } from "./CalendarContainer";
import { formatWithLocale } from "../../../hooks/calendarHooks";

const HeaderContainer = styled(Row)`
  position: relative;
  margin: 10px 0px 0px 0px;
  display: flex;
  align-items: center;
  width: 300px;
`;

const TitleButton = styled.div`
  text-align: center;
  font-weight: 700;
  font-family: ${({ theme }) => theme?.typography?.fontFamily || ""};
  color: #4d4d4f;
  font-size: ${({ theme }) => `${theme?.typography?.text?.fontSize}` || ""};
  padding: 0.6em 0;
  line-height: 1.43;
  letter-spacing: 0.3px;
`;

const Header = (props) => {
  const { selectedDate, formatMonthYear } = props;
  return (
    <HeaderContainer className={"vms_DateRangeCalendarMobile_HeaderContainer"}>
      <TitleButton className={"vms_DateRangeCalendarMobile_TitleButton"}>
        {`${formatWithLocale(selectedDate, formatMonthYear)}`}
      </TitleButton>
    </HeaderContainer>
  );
};

Header.defaultProps = {
  selectedDate: new Date(),
  formatMonthYear: "MMMM yyyy",
};

Header.propTypes = {
  selectedDate: PropTypes.instanceOf(Date),
  formatMonthYear: PropTypes.string,
};

export { Header };
