import React from "react";
import styled from "styled-components";
import Item from "./Item";
import HeaderWeek from "./HeaderWeek";
import PropTypes from "prop-types";
import {
  formatWithLocale,
  whetherDisabled,
  whetherSameMonth,
  getStartOfMonth,
  getEndOfMonth,
  getStartOfWeek,
  getEndOfWeek,
  addDay,
  setYears,
  whetherisToday,
  whetherSelected,
  whetherSameDay,
  whetherBetween,
  whetherSat,
  whetherSun,
  differenceCalendarDays,
  whetherBefore,
} from "../../../hooks/calendarHooks";
import { Divider } from "./CalendarContainer";

const DateTr = styled.tr`
  .selected {
    border-left: 5px solid transparent;
  }
  .disabled {
    color: ${(props) => props.disabledColor || "#d1d3d4"} !important;
    cursor: not-allowed;
    pointer-events: none;
  }
`;

const DateTable = styled.table`
  width: 100%;
  margin: 10px 0px 0px 0px;
  box-sizing: border-box;
  border-spacing: 0px;
`;

const DateTb = styled.tbody``;

const RangeTr = styled.tr`
  .disabled {
    cursor: not-allowed;
    color: ${(props) => props.disabledColor || "#d1d3d4"} !important;
    pointer-events: none;
  }
  .selected {
    color: #4d4d4f;
    font-weight: bold !important;
    background-color: #ddeede !important;
  }
  .saturday {
    color: #4d4d4f;
    background-color: #ddeede !important;
  }
  .sunday {
    color: #4d4d4f;
    background-color: #ddeede !important;
  }
  .start {
    color: #4d4d4f;
    background-color: #fff !important;
  }
  .between {
    color: #4d4d4f;
    background-color: #ddeede !important;
    /* border: 2px solid #fff !important; */
  }
  .end {
    color: #4d4d4f;
    background-color: #fff !important;
  }
  .today{
    color: #4d4d4f;
    background-color: red !important;
  }
`;

const DateCell = ({
  selectedDate,
  view,
  onItemClick,
  showConfirmButton,
  minDate,
  maxDate,
  isMobile,
  isDateChange,
  newSelectedDate,
  renderMonth,
  from,
  to,
  index,
  disabledColor,
  showTodayDate,
  todaysDateCss,
  maxBookingDays,
  initialFocusOn,
  disabledPreviousDate,
  disableDateList,
}) => {
  let cloneDate;
  let formattedDate;
  let itemPerRow;
  switch (view) {
    case "range": {
      const monthStart = getStartOfMonth(renderMonth);
      const monthEnd = getEndOfMonth(monthStart);
      const startDate = getStartOfWeek(monthStart);
      const endDate = getEndOfWeek(monthEnd);
      const userMinDate = minDate ? minDate : new Date();
      const userMaxDate = maxDate
        ? maxDate
        : new Date().setFullYear(new Date().getFullYear() + 1);
      const rows = [];
      let row = [];
      let i;

      cloneDate = startDate;
      itemPerRow = 7;

      while (cloneDate <= endDate) {
        for (i = 0; i < itemPerRow; i += 1) {
          formattedDate = formatWithLocale(cloneDate, "d");
          const disabled = whetherDisabled(
            cloneDate,
            monthStart,
            userMinDate,
            userMaxDate,
            null,
            null,
            null,
            null,
            disableDateList
          );
          const selected =
            !disabled && whetherSelected(cloneDate, selectedDate, from, to);
          const startCell = selected && whetherSameDay(cloneDate, from);
          const between = selected && whetherBetween(cloneDate, from, to);
          const endCell = selected && whetherSameDay(cloneDate, to);
          const selectedSat = selected && whetherSat(cloneDate);
          const selectedSun = selected && whetherSun(cloneDate);
          const sameMonth = whetherSameMonth(cloneDate, monthStart);
          const isTodayCell = whetherisToday(cloneDate);
          const isMaxBookingDays = differenceCalendarDays(cloneDate,addDay(from, 1)); 
          row.push(
            <Item
              key={cloneDate}
              value={cloneDate}
              formattedDate={formattedDate}
              onDateClick={onItemClick}
              showConfirmButton={showConfirmButton}
              disabledCell={(initialFocusOn === 'endDate' && isMaxBookingDays >= maxBookingDays)  ? true : (disabledPreviousDate ? (whetherBefore(cloneDate,from) ? true : disabled) : disabled)}
              selectedCell={selected}
              startCell={startCell}
              betweenCell={between}
              endCell={endCell}
              selectedSat={selectedSat}
              selectedSun={selectedSun}
              sameMonthCell={sameMonth}
              todayCell={isTodayCell}
              showTodayDate={showTodayDate}
              todaysDateCss={showTodayDate ? todaysDateCss:{}}
              rangeView
            />
          );
          cloneDate = addDay(cloneDate, 1);
        }
        rows.push(<RangeTr className="vms_DateRangeCalendarMobile_dateTableContainer_row" key={cloneDate} disabledColor={disabledColor}>{row}</RangeTr>);
        row = [];
      }
      return (
        <DateTable className="vms_DateRangeCalendarMobile_dateTableContainer" isMobile={isMobile}>
          <DateTb className="vms_DateRangeCalendarMobile_dateTableContainer_body">{rows}</DateTb>
        </DateTable>
      );
    }

    case "day": {
      const monthStart = getStartOfMonth(selectedDate);
      const monthEnd = getEndOfMonth(monthStart);
      const startDate = getStartOfWeek(monthStart);
      const endDate = getEndOfWeek(monthEnd);
      const rows = [];
      let row = [];
      let i;

      cloneDate = startDate;
      itemPerRow = 7;

      while (cloneDate <= endDate) {
        for (i = 0; i < itemPerRow; i += 1) {
          formattedDate = formatWithLocale(cloneDate, "d");
          const disabled = whetherDisabled(
            cloneDate,
            monthStart,
            minDate,
            maxDate,
            null,
            null,
            null,
            null,
            disableDateList
          );
          const checkTodayDate = isDateChange
            ? formatWithLocale(cloneDate, "dd.MM.yyyy") ==
              formatWithLocale(newSelectedDate, "dd.MM.yyyy")
              ? true
              : false
            : whetherisToday(cloneDate);

          const selected = !disabled && checkTodayDate;
          row.push(
            <Item
              key={cloneDate}
              value={cloneDate}
              formattedDate={formattedDate}
              onDateClick={(value) => onItemClick(value, true)}
              showConfirmButton={showConfirmButton}
              disabledCell={disabled}
              selectedCell={selected}
              view={view}
              showTodayDate={showTodayDate}
              todaysDateCss={showTodayDate ? todaysDateCss:{}}
            />
          );

          cloneDate = addDay(cloneDate, 1);
        }
        rows.push(<DateTr className="vms_DateRangeCalendarMobile_dateTableContainer_row" key={cloneDate} disabledColor={disabledColor}>{row}</DateTr>);
        row = [];
      }

      return (
        <>
          <DateTable className="vms_DateRangeCalendarMobile_dateTableContainer" isMobile={isMobile}>
            <HeaderWeek
              currentMonth={selectedDate}
              formatWeek="eeeee"
              view="day"
            />
            <DateTb className="vms_DateRangeCalendarMobile_dateTableContainer_body">{rows}</DateTb>
          </DateTable>
        </>
      );
    }
    default: {
      return undefined;
    }
  }
};

DateCell.defaultProps = {
  onScroll: () => {},
};

DateCell.propTypes = {
  onScroll: PropTypes.func,
  isDateChange: PropTypes.bool,
};

export { DateCell };
