import React from "react";
import PropTypes from "prop-types";
import { DateCell } from "./DateCell";
import { addMonth } from "../../../hooks/calendarHooks";
import styled from "styled-components";

const Row = styled.div`
  margin: 0;
  padding: 0;
  flex-grow: 1;
  flex-basis: auto;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  align-items: flex-start;
  justify-content: "center";
`;
const Col = styled.div`
  flex-grow: 1;
  flex-basis: auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
`;

const RangeCell = (props) => {
  const {
    view,
    onItemClick,
    showConfirmButton,
    minDate,
    maxDate,
    fromDate,
    toDate,
    selectedDate,
    index,
    disabledColor,
    showTodayDate,
    todaysDateCss,
    maxBookingDays,
    initialFocusOn,
    disabledPreviousDate,
    disableDateList,
  } = props;
  return (
    <>
      <DateCell
        selectedDate={selectedDate}
        renderMonth={selectedDate}
        view={view}
        onItemClick={onItemClick}
        showConfirmButton={showConfirmButton}
        minDate={minDate}
        maxDate={maxDate}
        from={fromDate}
        index={index}
        to={toDate}
        disabledColor={disabledColor}
        showTodayDate={showTodayDate}
        todaysDateCss={showTodayDate ? todaysDateCss:{}}
        maxBookingDays={maxBookingDays}
        initialFocusOn={initialFocusOn}
        disabledPreviousDate={disabledPreviousDate}
        disableDateList={disableDateList}
      />
    </>
  );
};

RangeCell.defaultProps = {
  view: "day",
  showConfirmButton: false,
  minDate: undefined,
  maxDate: undefined,
  fromDate: new Date(),
  toDate: new Date(),
  fromMonth: new Date(),
  toMonth: addMonth(new Date(), 1),
};

RangeCell.propTypes = {
  view: PropTypes.string,
  onItemClick: PropTypes.func.isRequired,
  showConfirmButton: PropTypes.bool,
  minDate: PropTypes.instanceOf(Date),
  maxDate: PropTypes.instanceOf(Date),
  fromDate: PropTypes.instanceOf(Date),
  toDate: PropTypes.instanceOf(Date),
  fromMonth: PropTypes.instanceOf(Date),
  toMonth: PropTypes.instanceOf(Date),
  // disableDateList: PropTypes.array
};

export { RangeCell };
