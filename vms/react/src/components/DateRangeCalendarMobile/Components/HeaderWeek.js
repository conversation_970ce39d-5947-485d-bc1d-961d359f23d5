import React from "react";
import styled from "styled-components";
import {
  formatWithLocale,
  getStartOfWeek,
  addDay,
} from "../../../hooks/calendarHooks";
const DaysContainer = styled.thead`
  width: 100%;
  margin-top: 18px;
  margin-bottom: 16px;
  padding-left: 10px !important;
  padding-right: 10px !important;
  box-sizing: border-box;
`;

const WeekDayCol = styled.th`
  font-size: ${({ theme }) => theme?.typography?.text?.fontSize || ""};
  line-height: 1.4;
  letter-spacing: normal;
  text-align: center;
  color: #4d4d4f;
  font-weight: 400;
  font-family: ${({ theme }) => theme?.typography?.fontFamily || ""};
`;
const WeekCol = styled.th`
  height: 2em;
  text-align: center;
  justify-content: center;
`;
const MonthCol = styled.th`
  height: 2em;
  text-align: center;
  justify-content: center;
`;

const HeaderWeek = (props) => {
  const days = [];
  const { currentMonth, formatWeek, view } = props;
  switch (view) {
    case "day": {
      const startDate = getStartOfWeek(currentMonth);
      for (let i = 0; i < 7; i += 1) {
        days.push(
          <WeekDayCol className="vms_DateRangeCalendarMobile_WeekdayCol" key={i} colSpan="1">
            {formatWithLocale(addDay(startDate, i), formatWeek)}
          </WeekDayCol>
        );
      }

      return (
        <DaysContainer className="vms_DateRangeCalendarMobile_HeaderweekConatiner">
          <tr className="vms_DateRangeCalendarMobile_WeekdateTr">{days}</tr>
        </DaysContainer>
      );
    }

    case "week": {
      return (
        <DaysContainer className="vms_DateRangeCalendarMobile_HeaderweekConatiner">
          <tr className="vms_DateRangeCalendarMobile_weekdatTr">
            <WeekCol className="vms_DateRangeCalendarMobile_WeekdayCol" colSpan="7">Week Number</WeekCol>
          </tr>
        </DaysContainer>
      );
    }
    case "month": {
      return (
        <DaysContainer className="vms_DateRangeCalendarMobile_HeaderweekConatiner">
          <tr className="vms_DateRangeCalendarMobile_weekdatTr">
            <MonthCol className="vms_DateRangeCalendarMobile_MonthCol" colSpan="7">Month Number</MonthCol>
          </tr>
        </DaysContainer>
      );
    }

    default: {
      return "";
    }
  }
};

export default HeaderWeek;
