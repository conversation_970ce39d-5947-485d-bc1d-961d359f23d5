import React from "react";
import styled from "styled-components";
import classNames from "classnames";
import StartDateSvg from "../../../assets/images/DateRangeCalendar/startDate.svg";
import EndDateSvg from "../../../assets/images/DateRangeCalendar/endDate.svg";
import "./style.css";

const ItemContainer = styled.td`
  height: 30px !important;
  line-height: 30px;
  border: 2px solid transparent !important;
  color: #4d4d4f !important;
  font-size: 12px;
  width: 41px;
  flex: 1 1var (--widthB);
`;

const Number = styled.div`
  text-align: center;
  font-size: 12px;
  line-height: 30px;
  font-weight: 400;
  user-select: ${({ sameMonthCell }) => (sameMonthCell ? "" : "none")};
  border: ${({disabledCell, isTodayCell,showTodayDate,todaysDateCss,theme }) => (disabledCell ? "" : showTodayDate &&  isTodayCell ? `${todaysDateCss && todaysDateCss.border ?  todaysDateCss.border  : `2px solid ${theme.palette.primary[theme.mode]}`}` : "")};
  background-color: ${({disabledCell, isTodayCell,showTodayDate,todaysDateCss }) => (disabledCell ? "" : showTodayDate && isTodayCell ? `${todaysDateCss && todaysDateCss.backgroundColor ?  todaysDateCss.backgroundColor  : '#fffff'}` : "")};
  height: ${({disabledCell, isTodayCell,showTodayDate,todaysDateCss }) => (disabledCell ? "" : showTodayDate && isTodayCell ? `${todaysDateCss && todaysDateCss.height ? todaysDateCss.height : '30px'}` : "")};
  border-radius: ${({disabledCell, isTodayCell,showTodayDate,todaysDateCss }) => (disabledCell ? "" : showTodayDate && isTodayCell ? `${todaysDateCss && todaysDateCss.borderRadius ? todaysDateCss.borderRadius :  '50%'}` : "")};
  width: ${({disabledCell, isTodayCell,showTodayDate,todaysDateCss }) => (disabledCell ? "" : showTodayDate && isTodayCell ? `${todaysDateCss && todaysDateCss.width ? todaysDateCss.width : '30px'}` : "")};
  ${ItemContainer}.selected.notrangeview & {
    opacity: 0;
    transition: 0.5s ease-out;
  }

  ${ItemContainer}.selected.between & {
    font-weight: 400;
  }

  ${ItemContainer}.selected & {
    color: #4d4d4f;
    text-align: center;
    font-weight: 600;
    font-size: 12px;
    font-family: ${({ theme }) => theme?.typography?.fontFamily || ""};
  }
  ${ItemContainer}:hover & {
    background: #f1f6de;
    text-align: center;
  }
`;

const SelectedNumber = styled.div`
  text-align: center;
  font-size: 12px;
  line-height: 30px;
  font-weight: 400;
  color: ${({ sameMonthCell }) => (sameMonthCell ? "#4d4d4f" : "#fff")};
  user-select: ${({ sameMonthCell }) => (sameMonthCell ? "" : "none")};

  ${ItemContainer}.selected.notrangeview & {
    opacity: 0;
    transition: 0.5s ease-out;
  }

  ${ItemContainer}.selected & {
    color: #4d4d4f;
    text-align: center;
    font-weight: 600;
    font-size: 12px;
    font-family: ${({ theme }) => theme?.typography?.fontFamily || ""};
  }
  ${ItemContainer}:hover & {
    background: "";
    text-align: center;
  }
`;

const Bg = styled.div`
  position: absolute;
  top: 0;
  font-weight: 700;
  line-height: 1;
  text-align: center;
  color: ${({ theme }) => "white"};
  width: 100%;
  opacity: 0;
  height: 1em;
  font-size: 2em;
  transition: 0.25s ease-out;

  ${ItemContainer}.disabled & {
    cursor: not-allowed;
  }
`;

const ImageContainer = styled.div`
  position: relative;
  text-align: center;
  color: #4d4d4f;
`;

const CenteredImage = styled.div`
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
`;

// With wrapper, target the generated className
const IconWrapperTargetClassname = styled.img``;

const Item = (props) => {
  const {
    value,
    onDateClick,
    showConfirmButton,
    formattedDate,
    selectedCell,
    disabledCell,
    startCell,
    betweenCell,
    endCell,
    selectedSat,
    selectedSun,
    rangeView,
    view,
    display,
    sameMonthCell,
    todayCell,
    showTodayDate,
    todaysDateCss
  } = props;
  const onDateClickHandler = () => {
    onDateClick(value, showConfirmButton);
  };
  return (
    <ItemContainer
      key={value}
      // className={classNames({
      //   disabled: disabledCell,
      //   selected: selectedCell,
      //   start: startCell,
      //   between: betweenCell,
      //   end: endCell,
      //   saturday: selectedSat,
      //   sunday: selectedSun,
      //   notRangeView: !rangeView,
      // })}
      className={`${classNames({
        disabled: disabledCell,
        selected: selectedCell,
        start: startCell,
        between: betweenCell,
        end: endCell,
        saturday: selectedSat,
        sunday: selectedSun,
        notRangeView: !rangeView,
        todayCell:todayCell,
      })} vms_DateRangeCalendarMobile_itemMainContainer`}
      display={display}
      startCell={startCell}
      endCell={endCell}
      onClick={!disabledCell ? onDateClickHandler : () => {}}
    >
      {selectedCell && (startCell || endCell) ? (
        <ImageContainer className="vms_DateRangeCalendarMobile_SelectedDateImage_Container">
          <img
            src={startCell ? StartDateSvg : EndDateSvg}
            alt="plus"
            className="imgselected"
          />
          <CenteredImage>
            <SelectedNumber
              className="vms_DateRangeCalendarMobile_SelectedDate"
              view={view}
              startCell={startCell}
              endCell={endCell}
            >
              {formattedDate}
            </SelectedNumber>
          </CenteredImage>
        </ImageContainer>
      ) : (
        <div style={{ display: `${sameMonthCell ? "" : "none"}` }}>
          <Number
            view={view}
            sameMonthCell={sameMonthCell}
            startCell={startCell}
            className="vms_DateRangeCalendarMobile_dateItem"
            endCell={endCell}
            isTodayCell={todayCell}
            showTodayDate={showTodayDate}
            todaysDateCss={todaysDateCss}
          >
            {formattedDate}
          </Number>
        </div>
      )}
      {rangeView ? (
        ""
      ) : (
        <Bg className="vms_DateRangeCalendarMobile_Bg">{formattedDate}</Bg>
      )}
    </ItemContainer>
  );
};

export default Item;
