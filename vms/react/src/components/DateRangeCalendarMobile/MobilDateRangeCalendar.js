import React, { useState, useEffect, useRef } from "react";
import PropTypes from "prop-types";
import { DateInput } from "./Components/DateInput";
import {
  CalendarContainer,
  PickerBodyContainer,
  PickerContainer,
  StickyWeekDay,
  StickyWeekDayLabel,
  InputContainer,
  InputRighView,
  InputLefView,
  Divider,
  CalendarCloseButton,
} from "./Components/CalendarContainer";
import { Header } from "./Components/Header";
import {
  addDay,
  addMonth,
  formatWithLocale,
  getNextYear,
  subMonth,
  whetherBefore,
  whetherSameDay,
  checkRangeIsValid,
  checkRangeErrorCode,
  whetherAfter,
  whetherSameMonth,
  getMonthDiff,
  getStartOfWeek,
  getStartOfMonth,
  getEndOfMonth,
  getEndOfWeek,
  whetherBetween,
  differenceCalendarDays,
} from "../../hooks/calendarHooks";
import { RangeCell } from "./Components/RangeCell";
import { useClassName } from "../../hooks/useClassName";
import { Button } from "../Button/Button";
import { ThemeWrapper } from "../Theme/ThemeContext";
import { addDays } from "date-fns";

const formatMonthYear = "MMMM yyyy";
let diff = 0;
const isNonNull = (prop) => {
  return prop !== null && prop !== undefined;
};

const DateRangeCalendarMobile = (props) => {
  const listInnerRef = useRef();
  const scrollRef = useRef(null);
  const className = useClassName(props, props.additionalClassName);
  const [editting, setEditting] = useState(props.openCalendar);
  const [mobileDateArray, setMobileArray] = useState([]);
  const [fromDate, setFromDate] = useState(props.startDate);
  const [toDate, setToDate] = useState(props.endDate);
  const [fromMonth, setFromMonth] = useState(new Date());
  const [toMonth, setToMonth] = useState(
    addMonth(new Date(), props.noofMonthView - 1)
  );
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [selectDateChange, setSelectedDateChange] = useState(false);
  const [whetherFirstInput, setWhetherFirstInput] = useState(true);
  const [whetherEndDateSelected, setWhetherEndDateSelected] = useState(false);
  const [initialFocusOn, setInitialFocusOn] = useState(props.initialFocusOn);
  const [initialresults, setInitialresults] = useState();

  const calculateNewRage = (fromDate, toDate) => {
    if (!whetherSameDay(fromDate, toDate) && !whetherBefore(fromDate, toDate)) {
      setFromDate(toDate);
      setToDate(fromDate);
    }
  };

  useEffect(() => {
    calculateNewRage(fromDate, toDate);
  }, []);

  useEffect(() => {
    if (editting) {
      let tempMonth = [];
      let addNewMonth = new Date();
      tempMonth.push(addNewMonth);
      diff = getMonthDiff(new Date(), props.startDate);
      const getDiiferenceInMonth = getMonthDiff(
        new Date(),
        props.maxDate ? props.maxDate : getNextYear(1)
      );
      for (let i = 1; i <= getDiiferenceInMonth; i++) {
        const getNewMonth = addMonth(addNewMonth, i);
        tempMonth.push(getNewMonth);
      }
      setMobileArray(tempMonth);
    }
  }, [editting]);

  useEffect(() => {
    setEditting(props.openCalendar);
  }, [props.openCalendar]);

  useEffect(() => {
    if (editting && mobileDateArray.length > 0) {
      scrollRef?.current?.scrollIntoView({
        behavior: "smooth",
        block: "start",
      });
    }
    if (props.isCancel) {
      setInitialresults({
        fromDate: props.startDate,
        toDate: props.endDate,
      });
    }
  }, [mobileDateArray]);

  useEffect(() => {
    setEditting(props.openCalendar);
    setFromDate(props.startDate);
    if (props.mode === "multiDateWithoutLoop") {
      setToDate(props.endDate === undefined ? props.startDate : props.endDate);
    } else {
      setToDate(props.endDate);
    }
    // setToDate(
    //   props.endDate
    // );
    setSelectedDate(
      props.currentSelection === "startDate"
        ? props.startDate
        : props.currentSelection === "endDate"
          ? props.endDate
          : new Date()
    );
    setInitialFocusOn(props.initialFocusOn);
  }, [props.openCalendar, props.currentSelection, props.startDate, props.endDate]);

  useEffect(() => {
    setWhetherFirstInput(props.initialFocusOn == "startDate")
    setWhetherEndDateSelected(props.endDate ? true : false)
  }, [props.openCalendar])

  const onItemClick = (date) => {
    const { mode } = props;
    if (initialFocusOn === "startDate" && whetherFirstInput) {
      setWhetherFirstInput(false);
      setFromDate(date);
      if (
        mode === "multiDateWithoutLoop" &&
        whetherEndDateSelected &&
        !whetherBetween(date, fromDate, toDate)
      ) {
        setToDate(date);
        setInitialFocusOn("startDate");
        setWhetherEndDateSelected(false);
        let result = {
          startDate: date,
          endDate: date,
          currentSelection: "startDate",
        };
        if (isNonNull(props.onItemClick)) {
          props.onItemClick(result);
        }
      } else {
        setToDate(whetherFirstInput ? date : toDate);
        setInitialFocusOn("endDate");
        // setWhetherEndDateSelected(true)
        let result = {
          startDate: date,
          endDate: whetherFirstInput ? date : toDate,
          currentSelection: "endDate",
        };
        if (isNonNull(props.onItemClick)) {
          props.onItemClick(result);
        }
      }
    } else {
      if (whetherBefore(date, fromDate)) {
        console.log("date is less than");
        setWhetherFirstInput(mode === "multiDateWithoutLoop" ? false : true);
        setFromDate(date);
        setToDate(date);
        setInitialFocusOn(
          mode === "multiDateWithoutLoop"
            ? !whetherBetween(date, fromDate, toDate)
              ? "startDate"
              : initialFocusOn
            : "endDate"
        );
        let result = {
          startDate: date,
          endDate: date,
          currentSelection:
            mode === "multiDateWithoutLoop"
              ? !whetherBetween(date, fromDate, toDate)
                ? "startDate"
                : initialFocusOn
              : "endDate",
        };
        if (isNonNull(props.onItemClick)) {
          props.onItemClick(result);
        }
        if (mode === "multiDateWithoutLoop") {
          setWhetherEndDateSelected(false);
        }
      } else {
        console.log("date is greater than");
        setWhetherFirstInput(mode === "multiDateWithoutLoop" ? false : true);
        setFromDate(fromDate);
        setToDate(date);
        setWhetherEndDateSelected(true);
        setInitialFocusOn(
          mode === "multiDateWithoutLoop"
            ? !whetherBetween(date, fromDate, toDate)
              ? "endDate"
              : initialFocusOn
            : "startDate"
        );
        let result = {
          startDate: fromDate,
          endDate: date,
          currentSelection:
            mode === "multiDateWithoutLoop"
              ? !whetherBetween(date, fromDate, toDate)
                ? "startDate"
                : initialFocusOn
              : "startDate",
        };
        if (isNonNull(props.onItemClick)) {
          props.onItemClick(result);
        }
      }
    }
  };

  const onScroll = (e) => {
    // if (listInnerRef.current) {
    //   const bottom =
    //     e.target.scrollTop + e.target.clientHeight >= e.target.scrollHeight - 1;
    //   let firstMonth = mobileDateArray?.length > 0 ? mobileDateArray[0] : [];
    //   let lastMonth =
    //     mobileDateArray?.length > 0
    //       ? mobileDateArray[Math.floor(mobileDateArray.length - 1)]
    //       : [];
    //   const checkMinDate = props.minDate ? props.minDate : new Date();
    //   const checkMaxDate = props.maxDate ? props.maxDate : getNextYear(1);
    //   const checkMaxDateInSameMonth = whetherSameMonth( lastMonth, checkMaxDate );
    //   const checkMinDateInSameMonth = whetherSameMonth( firstMonth, checkMinDate );
    //   if (bottom && !checkMaxDateInSameMonth) {
    //     let tempMonth = [];
    //     let addNewMonth = addMonth(lastMonth ? lastMonth : selectedDate, 1);
    //     tempMonth.push(addNewMonth);
    //     setMobileArray([...mobileDateArray, ...tempMonth]);
    //     setSelectedDate(addNewMonth);
    //   }
    //   if (e.target.scrollTop <= 10 && !checkMinDateInSameMonth) {
    //     let tempMonth = [];
    //     let subNewMonth = subMonth(firstMonth ? firstMonth : selectedDate, 1);
    //     tempMonth.push(subNewMonth);
    //     setMobileArray([...tempMonth, ...mobileDateArray]);
    //     setSelectedDate(subNewMonth);
    //   }
    // }
  };

  const OnButtonCloseClick = (e) => {
    const { minBookingDays, maxBookingDays, onDoneButtonClick } = props;
    let isValid = checkRangeIsValid(
      fromDate,
      toDate,
      minBookingDays,
      maxBookingDays
    );
    let result = {
      startDate: fromDate,
      endDate:
        mode === "multiDateWithoutLoop"
          ? whetherSameDay(fromDate, toDate) && whetherEndDateSelected
            ? toDate
            : whetherEndDateSelected
              ? toDate
              : whetherAfter(fromDate, toDate) ? toDate : undefined
          : whetherSameDay(fromDate, toDate)
            ? toDate
            : whetherEndDateSelected
              ? toDate
              : whetherAfter(fromDate, toDate) ? toDate : undefined,
      valid: isValid,
      datesInFormat: {
        format1: {
          startDate: formatWithLocale(fromDate, "yyyy-MM-dd'T'HH:mm:ss'Z'"),
          endDate: whetherSameDay(fromDate, toDate)
            ? formatWithLocale(toDate, "yyyy-MM-dd'T'HH:mm:ss'Z'")
            : whetherEndDateSelected
              ? formatWithLocale(toDate, "yyyy-MM-dd'T'HH:mm:ss'Z'")
              : "",
        },
        format2: {},
      },
    };
    if (whetherEndDateSelected) {
      result.datesInFormat.format2 = {
        startDate: formatWithLocale(fromDate, "dd-MM-yyyy"),
        endDate: formatWithLocale(toDate, "dd-MM-yyyy"),
      };
    }
    if (!isValid) {
      let errorCode = checkRangeErrorCode(
        fromDate,
        toDate,
        minBookingDays,
        maxBookingDays
      );
      if (errorCode !== 0) {
        result.errorCode = errorCode;
      }
    }
    setInitialFocusOn(props.initialFocusOn);
    setWhetherFirstInput(props.initialFocusOn === "startDate")
    onDoneButtonClick(result);
    setEditting(!editting);
  };

  const onCancelButton = (e) => {
    const { onCancelButtonClick } = props;
    let result = {
      startDate: fromDate,
      endDate: whetherEndDateSelected ? toDate : undefined,
      datesInFormat: {
        format1: {
          startDate: formatWithLocale(fromDate, "yyyy-MM-dd'T'HH:mm:ss'Z'"),
          endDate: whetherEndDateSelected
            ? formatWithLocale(toDate, "yyyy-MM-dd'T'HH:mm:ss'Z'")
            : "",
        },
        format2: {},
      },
    };
    setInitialFocusOn(props.initialFocusOn);
    setWhetherFirstInput(props.initialFocusOn === "startDate")
    onCancelButtonClick(result);
  };

  const changeInitialFocus = (focusName, selectedDate) => {
    const { mode } = props;
    if (mode === "multiDateWithoutLoop") {
      setInitialFocusOn(focusName);
      if (focusName == "startDate") {
        setWhetherFirstInput(true);
        // setWhetherEndDateSelected(focusName == "startDate" ? false : true)
      }
    }
  };

  const {
    maxBookingDays,
    isOverlay,
    minDate,
    maxDate,
    dateDisplayFormat,
    startDateLabelText,
    endDateLabelText,
    doneButtonText,
    doneButtonType,
    startDatePlaceholder,
    endDatePlaceholder,
    calendarType,
    customInputView,
    disabledColor,
    showTodayDate,
    todaysDateCss,
    showCancelButton,
    cancelButtonText,
    cancelButtonType,
    disabledPreviousDate,
    showDivider,
    mode,
  } = props;

  console.log("whetherEndDateSelected1410", whetherEndDateSelected, toDate, fromDate, mode);
  return (
    <ThemeWrapper isMobile={true}>
      <div className={`vms_DateRangeCalendarMobile_MainCalendarContainer`}>
        {editting && (
          <InputContainer
            className={`vms_DateRangeCalendarMobile_MainInputcontainer`}
          >
            {customInputView ? (
              <>{customInputView}</>
            ) : (
              <>
                <InputLefView
                  className={`vms_DateRangeCalendarMobile_LeftInputView ${initialFocusOn == "startDate" ? "active_Date" : ""
                    }`}
                >
                  <DateInput
                    editting={editting}
                    fromDate={fromDate}
                    toDate={toDate}
                    selectedDate={fromDate}
                    mode={props.mode}
                    calendarType={calendarType}
                    showSelectedDate={true}
                    placeholder={startDatePlaceholder}
                    inputBoxLabel={startDateLabelText}
                    formatDateInput={dateDisplayFormat}
                    inputClick={() => {
                      changeInitialFocus("startDate", fromDate)

                      isNonNull(props.dateInputClick && initialFocusOn == "startDate")
                      props.startInputClick()
                    }}
                  />
                </InputLefView>
                <InputRighView
                  className={`vms_DateRangeCalendarMobile_RightnputView ${initialFocusOn == "endDate" ? "active_Date" : ""
                    }`}
                >
                  <DateInput
                    editting={editting}
                    fromDate={fromDate}
                    calendarType={calendarType}
                    toDate={toDate}
                    showSelectedDate={whetherEndDateSelected}
                    selectedDate={
                      mode === "multiDateWithoutLoop"
                        ? whetherEndDateSelected
                          ? toDate
                          : ""
                        : toDate
                    }
                    placeholder={endDatePlaceholder}
                    mode={props.mode}
                    inputBoxLabel={endDateLabelText}
                    formatDateInput={dateDisplayFormat}
                    inputClick={() => {
                      changeInitialFocus("endDate", toDate)
                      isNonNull(props.dateInputClick && initialFocusOn == "endDate")
                      props.endInputClick()

                    }}
                    dateInputType="endDate"
                  />
                </InputRighView>
              </>
            )}
          </InputContainer>
        )}

        {editting && (
          <StickyWeekDay
            className={`vms_DateRangeCalendarMobile_WeekdayContainer`}
          >
            <StickyWeekDayLabel
              className={`vms_DateRangeCalendarMobile_WeekdayLabel`}
            >
              S
            </StickyWeekDayLabel>
            <StickyWeekDayLabel
              className={`vms_DateRangeCalendarMobile_WeekdayLabel`}
            >
              M
            </StickyWeekDayLabel>
            <StickyWeekDayLabel
              className={`vms_DateRangeCalendarMobile_WeekdayLabel`}
            >
              T
            </StickyWeekDayLabel>
            <StickyWeekDayLabel
              className={`vms_DateRangeCalendarMobile_WeekdayLabel`}
            >
              W
            </StickyWeekDayLabel>
            <StickyWeekDayLabel
              className={`vms_DateRangeCalendarMobile_WeekdayLabel`}
            >
              T
            </StickyWeekDayLabel>
            <StickyWeekDayLabel
              className={`vms_DateRangeCalendarMobile_WeekdayLabel`}
            >
              F
            </StickyWeekDayLabel>
            <StickyWeekDayLabel
              className={`vms_DateRangeCalendarMobile_WeekdayLabel`}
            >
              S
            </StickyWeekDayLabel>
          </StickyWeekDay>
        )}

        <PickerContainer
          className={`vms_DateRangeCalendarMobile_PickerContainer`}
          isMobile={true}
        >
          {editting && (
            <>
              <CalendarContainer
                type={calendarType}
                isMobile={false}
                className={`${className} vms_DateRangeCalendarMobile_CalendarContainer`}
                style={props.additionalStyle}
              >
                <PickerBodyContainer
                  ref={listInnerRef}
                  onScroll={(e) => onScroll(e)}
                  isMobile={true}
                  className={`vms_DateRangeCalendarMobile_PickerBodyContainer`}
                  // style={{ height: "220px" }}
                  editting={true}
                >
                  {mobileDateArray &&
                    mobileDateArray.length > 0 &&
                    mobileDateArray.map((date, i) => {
                      //if (whetherSameDay(getEndOfMonth(date), getEndOfMonth(fromDate))) {
                      return (
                        <div
                          key={i}
                          ref={
                            whetherSameDay(
                              getEndOfMonth(date),
                              getEndOfMonth(fromDate)
                            )
                              ? scrollRef
                              : null
                          }
                        >
                          <Header
                            selectedDate={date}
                            view={"mobile"}
                            formatMonthYear={formatMonthYear}
                            isMobile={true}
                          />

                          <RangeCell
                            view="range"
                            onItemClick={(e) => onItemClick(e)}
                            selectedDate={date}
                            fromDate={fromDate}
                            toDate={toDate}
                            index={i}
                            from={new Date()}
                            to={
                              maxBookingDays !== 30
                                ? addDay(new Date(), maxBookingDays)
                                : maxDate
                            }
                            minDate={minDate}
                            maxDate={maxDate}
                            fromMonth={fromMonth}
                            toMonth={toMonth}
                            whetherFirstInput={whetherFirstInput}
                            maxBookingDays={maxBookingDays}
                            disabledColor={disabledColor}
                            showTodayDate={showTodayDate}
                            todaysDateCss={showTodayDate ? todaysDateCss : {}}
                            initialFocusOn={initialFocusOn}
                            disabledPreviousDate={disabledPreviousDate}
                            disableDateList={props.disableDateList}
                          />
                          {mobileDateArray.length - 1 !== i && showDivider && (
                            <Divider />
                          )}
                        </div>
                      );
                      //}
                    })}
                </PickerBodyContainer>
              </CalendarContainer>
            </>
          )}
        </PickerContainer>
      </div>
      {editting && (
        <CalendarCloseButton
          className={`vms_DateRangeCalendarMobile_CloseMainContainer`}
        >
          <Button
            buttonType={doneButtonType}
            additionalStyle={{
              maxWidth: "100%",
              margin: "10px 0px 10px 0",
            }}
            additionalClassName={["vms_DateRangeCalendarMobile_done_button"]}
            onClick={(e) => OnButtonCloseClick(e)}
          >
            {doneButtonText}
          </Button>

          {showCancelButton && (
            <Button
              buttonType={cancelButtonType}
              additionalStyle={{
                maxWidth: "100%",
                margin: "10px 0px 10px 0",
              }}
              additionalClassName={[
                "vms_DateRangeCalendarMobile_Cancel_button",
              ]}
              onClick={(e) => onCancelButton(e)}
            >
              {cancelButtonText}
            </Button>
          )}
        </CalendarCloseButton>
      )}
    </ThemeWrapper>
  );
};

DateRangeCalendarMobile.defaultProps = {
  minDate: new Date(),
  maxDate: getNextYear(1),
  startDate: addDay(new Date(), 1),
  // endDate: addDay(new Date(), 2),
  onCloseButtonClick: () => { },
  dateDisplayFormat: "dd MMM yyyy",
  additionalClassName: null,
  additionalStyle: null,
  calendarType: "primary",
  minBookingDays: 1,
  maxBookingDays: differenceCalendarDays(
    new Date(
      new Date(new Date().setYear(new Date().getFullYear() + 1)).setDate(0)
    ),
    new Date()
  ),
  openCalendar: false,
  startDateLabelText: "Check In",
  endDateLabelText: "Check Out",
  doneButtonText: "Done",
  doneButtonType: "primary",
  initialFocusOn: "startDate",
  mode: "multiDateWithLoop",
  startDatePlaceholder: "Select checkin date",
  endDatePlaceholder: "Select chekout date",
  showCancelButton: false,
  cancelButtonText: "Cancel",
  cancelButtonType: "tertiary",
  isCancel: false,
  disabledPreviousDate: true,
  showDivider: true,
};

DateRangeCalendarMobile.propTypes = {
  /**
   * Variations of Calendar Type
   */
  calendarType: PropTypes.oneOf(["primary", "secondary"]),

  /**
   * Start Date label text. it will take Check In as default value.
   */
  startDateLabelText: PropTypes.string,
  /**
   * End Date label text. it will take Check Out as default value.
   */
  endDateLabelText: PropTypes.string,
  /**
   * Close button text. it will take Done as default value.
   */
  doneButtonText: PropTypes.string,
  /**
   *Close button type. it will take primary as default value.
   */

  doneButtonType: PropTypes.oneOf(["primary", "secondary", "link", "tertiary"]),
  /**
   * Range start Date.
Date
   */

  startDate: PropTypes.instanceOf(Date),

  /**
   * Range End Date.
Date
   */
  endDate: PropTypes.instanceOf(Date),

  /**
   * Variations of Open Button Type
   */
  initialFocusOn: PropTypes.oneOf(["startDate", "endDate"]),

  /**
   * If I want to open calendar pass true.
   */
  openCalendar: PropTypes.bool,

  /**
   * 	
Defines minimum date. Disabled earlier dates. it will take current Date as default value.

Date
   */
  minDate: PropTypes.instanceOf(Date),

  /**
   * Defines maximum date. Disabled later dates it will take current Date + 1 year as default value.
    Date
   */
  maxDate: PropTypes.oneOfType([PropTypes.instanceOf(Date)]),

  /**
   * 
    callback function for range changes when @property mode is multiDateRange.
it will return {
   startDate: Date,
   endDate: Date,
   valid: boolean,
   errorCode?: number (optional),
   datesInFormat: {
     format1 (ISO format): {
       startDate: string;
       endDate: string;
     };
     format2 (DD-MM-YYYY): {
       startDate: string;
       endDate: string;
     };
   };
}
if valid = false then it will return errorCode.
errorCode values
1 = When start and end Date same
2 = When selected range is less than minBookingDays
3 = When selected range is more than maxBookingDays

(result: DateRangeCalendarOutput) => void
   */
  onDoneButtonClick: PropTypes.func,

  /**
   * Displayed Date format . Default value dd MMM yyyy
Please refer https://date-fns.org/v2.12.0/docs/format for various allowed format

string
   */
  dateDisplayFormat: PropTypes.string,

  /**
   *Inline styles to add additional styling in the parent container
CSSProperties
   */
  additionalStyle: PropTypes.object,
  /**
   * Classes to add additional styling in the parent container
string[]
   */

  additionalClassName: PropTypes.arrayOf(PropTypes.string),
  /**
   * Calendar Mode
multiDateWithLoop - After selection of one date it will automatically move focus to other date input box
multiDateWithoutLoop - After selection of the start date it will automatically move focus to the end date input box. The focus will remain on the end date until the user explicitly clicks on the start date input box
"multiDateWithLoop" | "multiDateWithoutLoop"
* 
   */
  mode: PropTypes.oneOf(["multiDateWithLoop", "multiDateWithoutLoop"]),

  /**
   *  Minimum days differece between start and end date. it will take 1 as default value.
number
   */
  minBookingDays: PropTypes.number,
  /**
   *  Maximum days differece between start and end date. it will take 30 as default value.
number
 */
  maxBookingDays: PropTypes.number,

  /**
   *   Place holder text for start date
   */
  startDatePlaceholder: PropTypes.string,

  /**
   *   Place holder text for start date
   */
  endDatePlaceholder: PropTypes.string,

  /**
   *   Custom Input View
   */
  customInputView: PropTypes.oneOfType([PropTypes.node, PropTypes.string])
    .isRequired,
  /**
   * highlight Today's Date in calendare date
   */
  showTodayDate: PropTypes.bool,
  /**
   * custom css for today's date  it is applied when showTodayDate is true
   */
  todaysDateCss: PropTypes.shape({
    border: PropTypes.string,
    backgroundColor: PropTypes.string,
    height: PropTypes.string,
    borderRadius: PropTypes.string,
    width: PropTypes.string,
  }),

  /**disabled date color code date
   */
  disabledColor: PropTypes.string,

  /**
   * show  Cancel Button
   */
  showCancelButton: PropTypes.bool,
  /**
   * Cancel button text. it will take Cancel as default value.
   */
  cancelButtonText: PropTypes.string,

  /**
   *Cancel button type. it will take secondary as default value.
   */
  cancelButtonType: PropTypes.oneOf([
    "primary",
    "secondary",
    "link",
    "tertiary",
  ]),

  /**
   *Cancel button click
   */
  onCancelButtonClick: PropTypes.func,

  /**
   *disabled the previos date  when it selectd start end
   */
  disabledPreviousDate: PropTypes.bool,
   /**
   * Disable a list of dates in the format 'dd/MM/yyyy' from an array.
   */
   disableDateList : PropTypes.arrayOf(PropTypes.string),
};

export { DateRangeCalendarMobile };
