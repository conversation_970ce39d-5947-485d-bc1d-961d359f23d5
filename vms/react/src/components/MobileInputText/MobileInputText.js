import React, { useState, memo, useCallback, useMemo, useEffect } from "react";
import CountryDropDown from "./Components/CountryDropDown/CountryDropDown";
import Input from "./Components/Input/Input";
import PropTypes from "prop-types";
import {
  Container,
  ErrorMessage,
  ErrorText,
  IsdCode,
  IsdLabel,
  IsdPickerContainer,
  MobileNumber,
} from "./MobileInputText.styled";
import { ModalPopup } from "../ModalPopup/ModalPopup";
import propTypes from "prop-types";
import { countriesAllData } from "./CountryData";
import { useClassName } from "../../hooks/useClassName";
import { ThemeWrapper } from "../Theme/ThemeContext";

// Utility function (placed before main component)
const isNonNull = (prop) => prop !== null && prop !== undefined;

const MobileInputText = memo((props) => {
  // 1. PROP DESTRUCTURING & VALIDATION
  const {
    type = "text",
    placeholder,
    customCountryList,
    disabled = false,
    selectedIndex,
    mobileNumber,
    onValidationCallback = () => {},
    msgText,
    isdCodeErrorMsgText,
    listItems = "All",
    mobileInputTextStyle,
    countryIconStyle,
    countryInputStyle,
    onCountryChange = () => {},
    onMobileNumberChange = () => {},
    isMobile = false,
    isMobileView,
    isNumberValid,
    messageMode,
    inputlabel = "Mobile Number",
    additionalClassName,
    additionalStyle,
    filterBy = "code",
    maxLength,
    minLength,
    showIsdCodeErrorMsgText = true,
    showMobileNoErrorMsgText = true,
    isDropdownOpen = false,
    onCountryVisibilityChange,
    selectedValueFilterBy = "index",
    selectedValue,
    tabIndex = 1,
    inputTabIndex = 2,
    readOnly,
    onBlur,
    onCountrydDropDownBlur,
    onMobileNoBlur,
    autoComplete = "off",
    inputId = "phone",
    countryCodePlaceholder,
    onMobileNumberFocus,
    isdMaxLength,
    onCountryCodeSelect = () => {},
    ...otherProps
  } = props;

  // 2. STATE MANAGEMENT
  const [isValid, setIsValid] = useState(() => 
    isNumberValid !== undefined ? isNumberValid : true
  );
  const [errText, setErrText] = useState("");
  const [countryCode, setCountryCode] = useState("");

  // 3. PERFORMANCE OPTIMIZATIONS
  const computedClassName = useMemo(() => 
    useClassName(props, additionalClassName), 
    [props, additionalClassName]
  );

  const isMobileDevice = useMemo(() => 
    isMobile || isMobileView || false, 
    [isMobile, isMobileView]
  );

  const countryData = useMemo(() => 
    customCountryList || countriesAllData, 
    [customCountryList]
  );

  const countryPlaceholder = useMemo(() => 
    countryCodePlaceholder || "Enter Country Code", 
    [countryCodePlaceholder]
  );

  const mobilePlaceholder = useMemo(() => 
    placeholder || "Mobile Number", 
    [placeholder]
  );

  const listItemsData = useMemo(() => 
    listItems === "All" ? ["icon", "code", "country"] : listItems, 
    [listItems]
  );

  // 4. VALIDATION FUNCTIONS with useCallback
  const validateMobileNumber = useCallback((str) => {
    if (str === "") return { isValid: true, errorText: "" };
    
    const regexExp = /^[6-9]\d{9}$/gi;
    const checkNo = regexExp.test(str);
    
    let errorText = "";
    if (showMobileNoErrorMsgText && !checkNo && countryCode === '91') {
      errorText = msgText || "Please enter a valid mobile number";
    }
    
    return { isValid: checkNo, errorText };
  }, [showMobileNoErrorMsgText, msgText, countryCode]);

  const validateCountryCode = useCallback((str) => {
    if (!countryData?.length) return { isValid: false, errorText: "" };
    
    const filterData = countryData.filter((obj) => {
      if (filterBy === 'countryName') {
        return obj.value.toLowerCase().indexOf(str.toLowerCase()) > -1;
      } else {
        return obj.label.toLowerCase().indexOf(str.toLowerCase()) > -1;
      }
    });

    const isValidCode = filterData.length > 0;
    let errorText = "";
    
    if (showIsdCodeErrorMsgText && !isValidCode) {
      errorText = isdCodeErrorMsgText || "Please enter a valid isd code";
    }

    return { isValid: isValidCode, errorText, filterData };
  }, [countryData, filterBy, showIsdCodeErrorMsgText, isdCodeErrorMsgText]);

  // 5. EVENT HANDLING with useCallback
  const handleMobileNumberChange = useCallback((str) => {
    const validation = validateMobileNumber(str);
    
    setIsValid(validation.isValid);
    setErrText(validation.errorText);
    
    if (isNonNull(onValidationCallback)) {
      onValidationCallback(validation.isValid);
    }
    
    if (isNonNull(onMobileNumberChange)) {
      onMobileNumberChange(str);
    }
  }, [validateMobileNumber, onValidationCallback, onMobileNumberChange]);

  const handleCountryCodeChange = useCallback((str) => {
    const validation = validateCountryCode(str);
    
    setIsValid(validation.isValid);
    if (!validation.isValid) {
      setErrText(validation.errorText);
    }
    
    if (isNonNull(onCountryChange)) {
      onCountryChange(str, validation.isValid);
    }
  }, [validateCountryCode, onCountryChange]);

  const handleCountryCodeSelect = useCallback((str, item) => {
    setCountryCode(str);
    if (isNonNull(onCountryCodeSelect)) {
      onCountryCodeSelect(str, item);
    }
  }, [onCountryCodeSelect]);

  const handleCountryDropdownBlur = useCallback((e) => {
    if (isNonNull(onCountrydDropDownBlur)) {
      onCountrydDropDownBlur(e);
    }
  }, [onCountrydDropDownBlur]);

  const handleMobileNumberBlur = useCallback((e) => {
    if (isNonNull(onMobileNoBlur)) {
      onMobileNoBlur(e);
    }
  }, [onMobileNoBlur]);

  const handleMobileNumberFocus = useCallback((e) => {
    if (isNonNull(onMobileNumberFocus)) {
      onMobileNumberFocus(e);
    }
  }, [onMobileNumberFocus]);

  // 6. EFFECTS
  useEffect(() => {
    if (isNumberValid !== undefined) {
      setIsValid(isNumberValid);
    }
  }, [isNumberValid]);

  // 7. CONDITIONAL RENDERING (memoized)
  const errorMessageElement = useMemo(() => {
    if (isValid || !errText) return null;

    return (
      <ErrorMessage className="vms_MobileInput_ErrorMessageContainer">
        <ErrorText className="vms_MobileInput_ErrorMessageText">
          {errText}
        </ErrorText>
      </ErrorMessage>
    );
  }, [isValid, errText]);

  // 8. ERROR HANDLING
  useEffect(() => {
    if (!Array.isArray(countryData)) {
      console.warn('MobileInputText: customCountryList should be an array');
    }
    if (maxLength && minLength && maxLength < minLength) {
      console.warn(`MobileInputText: maxLength (${maxLength}) should be greater than minLength (${minLength})`);
    }
  }, [countryData, maxLength, minLength]);

  return (
    <ThemeWrapper isMobile={isMobileDevice}>
      <Container 
        className={`${computedClassName} vms_MobileInput_Container`} 
        style={additionalStyle}
      >
        <IsdLabel className="vms_MobileInput_IsdLabel">
          {inputlabel}
        </IsdLabel>
        
        <IsdPickerContainer className="vms_MobileInput_IsdPickerContainer">
          <IsdCode className="vms_MobileInput_IsdCode">
            <CountryDropDown
              data={countryData}
              disabled={disabled}
              tabIndex={tabIndex}
              selectedIndex={selectedIndex}
              listItemsData={listItemsData}
              inputProps={{ placeholder: countryPlaceholder }}
              countryIconStyle={countryIconStyle}
              countryInputStyle={countryInputStyle}
              isMobile={isMobile}
              messageMode={isValid ? null : (messageMode || 'error')}
              onCountryCodeChange={handleCountryCodeChange}
              onCountryCodeSelect={handleCountryCodeSelect}
              filterBy={filterBy}
              readOnly={readOnly}
              isDropdownOpen={isDropdownOpen}
              onCountryVisibilityChange={onCountryVisibilityChange}
              selectedValueFilterBy={selectedValueFilterBy}
              selectedValue={selectedValue}
              isdMaxLength={isdMaxLength}
              onBlur={handleCountryDropdownBlur}
              {...otherProps}
            />
          </IsdCode>
          
          <MobileNumber className="vms_MobileInput_MobileNumber">
            <Input
              type={type}
              value={mobileNumber || ""} 
              placeholder={mobilePlaceholder}
              disabled={disabled}
              mobileNumber={mobileNumber || ""}
              isMobile={isMobile}
              messageMode={isValid ? null : (messageMode || 'error')}
              mobileInputTextStyle={mobileInputTextStyle}
              onMobileNumberChange={handleMobileNumberChange}
              maxLength={maxLength}
              minLength={minLength}
              inputTabIndex={inputTabIndex}
              name="phone"
              autoComplete={autoComplete}
              inputId={inputId}
              msgText={msgText}
              onBlur={handleMobileNumberBlur}
              onFocus={handleMobileNumberFocus}
            />
          </MobileNumber>
        </IsdPickerContainer>
        
        {errorMessageElement}
      </Container>
    </ThemeWrapper>
  );
});

MobileInputText.displayName = 'MobileInputText';

MobileInputText.defaultProps = {
  type: "text",
  disabled: false,
  onMobileNumberChange: () => { },
  onCountryChange: () => { },
  onValidationCallback: () => { },
  onCountryCodeSelect: () => { },
  listItems: "All",
  mobileInputTextStyle: null,
  countryIconStyle: null,
  countryInputStyle: null,
  isMobile: false,
  messageMode: null,
  inputlabel: "Mobile Number",
  filterBy: 'code',
  showIsdCodeErrorMsgText: true,
  showMobileNoErrorMsgText: true,
  isDropdownOpen: false,
  selectedValueFilterBy: "index",
  tabIndex: 1,
  inputTabIndex:2,
  autoComplete: "off",
  inputId: "phone"
};

MobileInputText.propTypes = {
  /**
  * Returns / Sets the element's accept attribute, 
  * containing comma-separated list of file types that can be selected.
  */
  accept: PropTypes.string,
  /**
   * Returns / Sets the element's alt attribute, containing alternative text to use.
   */
  alt: PropTypes.string,
  /**
   * Sets the element's autocomplete attribute, 
   * indicating whether the value of the control can be automatically completed by the browser.
   */
  autoComplete: PropTypes.string,
  /**
  * Sets the element's autofocus attribute, which specifies that a form control 
  * should have input focus when the page loads, unless the user overrides it, 
  * for example by typing in a different control. Only one form element in a document can have the autofocus attribute
  */
  autoFocus: PropTypes.bool,
  capture: PropTypes.oneOfType([PropTypes.string, PropTypes.bool]),
  /**
  * Returns / Sets the current state of the element.
 */
  checked: PropTypes.bool,
  crossOrigin: PropTypes.string,
  form: PropTypes.string,
  formAction: PropTypes.string,
  formEncType: PropTypes.string,
  formMethod: PropTypes.string,
  formNoValidate: PropTypes.string,
  formTarget: PropTypes.string,
  height: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  /**
   * Returns / Sets the element's min attribute, containing the minimum (numeric or date-time) value for this item, 
   *  which must not be greater than its maximum (max attribute) value.
   */
  min: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  /**
    *  Sets the element's max attribute, containing the maximum (numeric or date-time) value for this item, 
    * which must not be less than its minimum (min attribute) value.
    */
  max: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  list: PropTypes.string,
  /**
   * Sets the element's maxlength attribute, 
   * containing the maximum number of characters that the value can have.
   */
  maxLength: PropTypes.number,
  /**
  *  Sets the element's minlength attribute, containing the minimum number of characters
  *  (in Unicode code points) that the value can have.
  */
  minLength: PropTypes.number,
  multiple: PropTypes.bool,
  /**
  * Name of the input element
  */
  name: PropTypes.string,
  pattern: PropTypes.string,
  /**
   * use this props to display a placeholder string in text input.
   */
  placeholder: PropTypes.string,
  /**
   * Indicates that the element is not editable, but is otherwise operable.
   */
  readOnly: PropTypes.bool,
  required: PropTypes.bool,
  size: propTypes.number,
  src: PropTypes.string,
  step: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  /**
    * 	
     Type of the input field. Allowed types: text, number, tel, email, password
    */
  type: PropTypes.oneOf(["number", "text", "tel", "email", "password"]),
  /**
   * Callback function when input onchange event is triggered
   */
  onChange: PropTypes.func,
  /**
  * Current field value
  */
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  defaultChecked: PropTypes.bool,
  defaultValue: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  suppressContentEditableWarning: PropTypes.bool,
  suppressHydrationWarning: PropTypes.bool,
  accessKey: PropTypes.string,
  /**
   * Custom class name that can be added to the component
   */
  className: PropTypes.string,
  /**
   * Unique ID for the field. Required for web accessibility
   */
  id: PropTypes.string,
  /**
    * 	
     Provide default mobile number
    */
  mobileNumber: PropTypes.any,
  /**
    * 	
    Handle Mobile number change event
    */
  onMobileNumberChange: PropTypes.func,
  /**
    * 	
    Handle country name/ISD code change event
    */
  onCountryChange: PropTypes.func,
  /**
    * 	
    Provide style to country input
    */
  countryInputStyle: PropTypes.object,
  /**
    * 	
    Provide style to country icon style
    */
  countryIconStyle: PropTypes.object,

  /**
    * 	
    Provide style to country icon style
    */
  popupCountryInputStyle: PropTypes.object,

  /**
    * 	
    Provide style to mobile input text style
    */
  mobileInputTextStyle: PropTypes.object,

  /**
    * 	
    Provide jsondata for listing
    */
  customCountryList: PropTypes.arrayOf(PropTypes.shape({
    "id": PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    "value": PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    "label": PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    "code": PropTypes.string,
    "icon": PropTypes.string
  })),
  /**
    * 	
    Provide selectedIndex to select default value
    */
  selectedIndex: PropTypes.number,

  /**
    * 	
    Provide custome label to component
    */
  mobileInputLabel: PropTypes.string,

  /**
    * 	
    Provide custome label to country inpute in modal
    */
  mobileCountryInputLabel: PropTypes.string,
  /**
    * 	
ISD Code error message text. Default Please enter a valid isd code. To change mobile number error message text please user msgText prop.
    */

  isdCodeErrorMsgText: PropTypes.string,
  /**
    * 	
   Message text content in input text.
    */

  msgText: PropTypes.string,

  /**
    * 	
  Provide items to be display in Option list, Default: All
("icon" | "code" | "country" | "All")[]
    */
  listItems: PropTypes.oneOf(["icon", "code", "country", "All"]),
  /**
    * 	
Inline style to add additional styling in the right view container
    */

  additionalRightViewStyle: PropTypes.object,

  /**
    * 	
Classes to add additional styling in the right view container
    */

  additionalRightViewClassName: PropTypes.arrayOf(PropTypes.string),

  /**
    * 	
Classes to add additional styling in the right view container
    */

  rightView: PropTypes.node,

  /**
    * 	
left content for input text. Note: Please give padding to input as same of left view

*/
  leftView: PropTypes.node,

  /**
    * 	
Message mode
"error" | "information" | "success" | "warning"*/
  messageMode: PropTypes.oneOf(["error", "information", "success", "warning"]),

  /**
    * 	
Label for the field
 */
  label: PropTypes.string,
  /**
    * 	
Secondary Label for the field
 */
  secondaryLabel: PropTypes.string,
  /**
    * 	
Placeholder text for mobile input text
 */
  placeHolderText: PropTypes.string,

  /**
    * 	
It will call on change of ISD code and on change of mobile number field
 */
  onValidationCallback: PropTypes.func,


  inputlabel: PropTypes.string,
  /**
    * 	
 All inputText and AutoCompleteDropDown  Props is Alllowed for Mobile Input Text  
    */
  Info: PropTypes.any,
  /**
     * 	
  filterBy the value based on the code
     */
  filterBy: PropTypes.oneOf(["code", "countryName", "none"]),

  /**
    * 	
  disply error isd code or not  
 */
  showIsdCodeErrorMsgText: propTypes.bool,


  /**
      * 	
    disply error Mobile No Error Message 
   */
  showMobileNoErrorMsgText: propTypes.bool,
  /**
      * 	
    onCountryCodeSelect it will return value and item 
   */

  onCountryCodeSelect: propTypes.func,


  /**
      * 	
    show to country dropdown open  or not by default it is false
   */
  isDropdownOpen: propTypes.bool,


  /**
    * Invoked every time the country dropdown  visibility changes (i.e. every time it is displayed/hidden)
    * (isDropdownOpen: Boolean) => void
    */
  onCountryVisibilityChange: propTypes.func,

  /**
      * 	
   based on the value it will the record by default it will filter by index
   */
  selectedValueFilterBy: propTypes.string,
  /**
      * 	
   based on the value it will the record by default it will filter by index
   */
  selectedValue: propTypes.string,

  inputId: propTypes.string,
  /**
      * 	
   country code placeholder
   */
  countryCodePlaceholder: propTypes.string,
  /**
      * 	
   isd code max length
   */
  isdMaxLength: propTypes.number,

};

export { MobileInputText };
