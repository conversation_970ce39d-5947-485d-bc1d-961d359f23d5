import styled from "styled-components";

export const Container = styled.div``;

export const DropDownContainer = styled.div`
  min-width: 120px;
  width: auto;
  margin-right: 10px;
  box-shadow: none;
  position: relative;
  /* width: 300px; */
  /* border: 1px solid #4d4d4f; */
  border-radius: 3px;
  /* width: 334px; */
`;

export const InputTextParentContainer = styled.div`
  max-width: unset;
  width: 100%;
  max-width: 320px;
`;

export const InputLabel = styled.label`
  font-weight: 400;
  font-family: Montserrat;
  font-size: 14px;
  font-stretch: normal;
  font-style: normal;
  line-height: 20px;
  letter-spacing: 0.3px;
  color: #4d4d4f;
  display: block;
  position: relative;
  text-transform: uppercase;
`;

export const StyledInput = styled.input`
  display: block;
  box-sizing: border-box;
  font-weight: 700;
  font-family: Montserrat;
  font-size: 14px;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.43;
  width: 100%;
  border-radius: 3px;
  letter-spacing: 0.3px;
  color: #4d4d4f;
  padding: 14px 16px;
  margin-top: 12px;
  text-overflow: ellipsis;
  height: 48px;
  border: solid 1px #4d4d4f;
  background-color: #fff;

  padding-left: 40px;

  padding-top: 14px;
  padding-right: 16px;
  padding-bottom: 14px;
  height: 46px;

  &:focus {
    outline: none;
    ${({ isMobile }) =>
      isMobile
        ? { "border-bottom": "1px solid #03868b" }
        : { border: "1px solid #03868b" }};
  }
  &::placeholder {
    font-weight: 400;
    font-family: Montserrat;
    font-size: 16px;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.43;
    border-radius: 3px;
    letter-spacing: 0.3px;
    color: #4d4d4f;
  }
`;

export const LeftView = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  top: 10px !important;
  left: 5px !important;
`;

export const LeftIcon = styled.div`
  width: 24px;
  height: 24px;
  background-size: contain;
  background-image: url("../../../assets/flags/IN.png");
`;

export const AutocompleteContainer = styled.div`
  border: 1px solid rgb(3, 134, 139);
  border-radius: 0px 0px 3px 3px;
  font-size: 90%;
  overflow: hidden auto;
  background-color: white;
  min-width: auto;
  width: 250px;
  box-shadow: rgb(0 0 0 / 9%) 0px 2px 4px 0px;
  position: absolute;
  top: auto;
  bottom: auto;
  z-index: 10;
  left: -1px;
`;

export const AutocompleteItem = styled.ul`
  margin: 0;
  list-style: none;
  padding-left: 0;
  border-width: 1px;
  border-color: #03868b;
  border-top: none;
  overflow: auto;
`;

export const RecentlySearchDiv = styled.div`
    width: 100%;
    max-height: 363px;
    background-color: #fff;
    border-top: none;
    left: 0px;
`;

export const StyledListItem = styled.li`
      /* background-color: rgba(248, 244, 244, 0.68); */
      padding-top: 5px;
      list-style: none;
`;

export const JpCountry = styled.div`
     display: inline-flex;
    align-items: center;
    padding-left: 8px;
`;

export const CountryFlag = styled.span`
         height: 30px;
    width: 30px;
    display: inline-block;
    background-size: contain;
    margin-right: 10px;
`;

export const CountryCodeName = styled.span`
          margin-right: 10px;
    display: inline-block;
    min-width: 50px;
    white-space: nowrap;
    max-width: 100px;
    overflow: hidden;
    text-overflow: ellipsis;
`;