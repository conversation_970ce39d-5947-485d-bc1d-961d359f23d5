import React, { useState, useRef, useEffect } from "react";
import { AutocompleteDropdown } from "../../../AutoCompleteDropDown/Autocomplete";
import { ModalPopup } from "../../../ModalPopup/ModalPopup";
import "./countryDropDown.css";
import { useOnClickOutside } from "../../../../hooks/outsideClickHandler";

const CountryDropDown = (props) => {
  const [inputValue, setValue] = useState("");
  const [selectedIcon, setSelectedIcon] = useState("");
  const [open, setOpen] = useState(false);
  const [openMobileViewModal, SetopenMobileViewModal] = useState(false);
  const {
    isMobileView = true,
    inputProps,
    disabled,
    data,
    selectedIndex,
    onCountryCodeChange,
    onCountryCodeSelect,
    listItemsData,
    countryIconStyle,
    countryInputStyle,
    isMobile,
    messageMode,
    filterBy,
    readOnly,
    rightView,
    isDropdownOpen,
    onCountryVisibilityChange,
    selectedValue,
    selectedValueFilterBy,
    isdMaxLength
    
  } = props;
  let autoCompleteDropdownref = useRef();

  useOnClickOutside(autoCompleteDropdownref, () => {
    setOpen(false)
    onCountryVisibilityChange(false)
  });

  let highlighted = false;
  const onChangeHandle = (val) => {
    const regexExp = /^[0-9-+()]*$/;
    const checkNo = regexExp.test(val);
    if(checkNo){
      console.log("onChangeHandlessdsd",checkNo)
      setSelectedIcon("");
      setValue(`${val}`);
      onCountryCodeChange(val);
    }
  };

  useEffect(() => {
    setOpen(isDropdownOpen);
    if (selectedValueFilterBy === 'index' && selectedIndex !== null && selectedIndex !== undefined) {
      const getSelectedIndexData = data.filter(
        (obj, index) => index === selectedIndex
      );
      if (getSelectedIndexData && getSelectedIndexData.length > 0) {
        setSelectedIcon(`${getSelectedIndexData[0].icon}`);
        setValue(`${getSelectedIndexData[0].label}`);
        // onCountryCodeChange(getSelectedIndexData[0].label);
      }
    }
    console.log("getSelectedIndexData outsid if",selectedValueFilterBy,selectedValue)

    if(selectedValueFilterBy !== 'index' && selectedValue){
      const getSelectedIndexData = data.filter((obj, index) => {
        return obj[selectedValueFilterBy] === selectedValue;
      });
      console.log("getSelectedIndexData inside if",getSelectedIndexData)
      if (getSelectedIndexData && getSelectedIndexData.length > 0) {
        setSelectedIcon(`${getSelectedIndexData[0].icon}`);
        setValue(`${getSelectedIndexData[0].label}`);
        // onCountryCodeChange(getSelectedIndexData[0].label);
      }
    }
  }, [selectedIndex,isDropdownOpen]);







  const onSelect = (value, item) => {
    // console.log("🚀 ~ onSelect ~ item:", item)
    setSelectedIcon(item.icon);
    setOpen(false);
    onCountryVisibilityChange(false)
    setValue(`${filterBy === 'countryName' ?  item.value : item.label}`);
    SetopenMobileViewModal(false);
    onCountryCodeSelect(item.value,item)
  };

  const getItemValue = (item) => {
    return  filterBy === 'countryName' ?  item.value : item.label;
  };

  const renderItem = (item) => {
    return (
      <div
        className="itemContainer vms_MobileInput_CountryDropDown_ItemContainer"
        key={item.id}
        onClick={(e) => onSelect(e.target.value, item)}
      >
        <li
          id={highlighted ? "selectedoption" : ""}
          role="option"
          data-activedescendant={highlighted}
          aria-selected={highlighted}
          className={!isMobileView ? "desktop-li vms_MobileInput_CountryDropDown_Item_li" : "desktop-li vms_MobileInput_CountryDropDown_Item_li"}
        >
          <div
            className="jp-country vms_MobileInput_CountryDropDown_Item_Country"
            aria-atomic="true"
            style={{ paddingLeft: "8px" }}
          >
            {listItemsData.includes("icon") && (
              <span
                className="img-icon-styling vms_MobileInput_CountryDropDown_Item_Country_Text"
                style={{
                  backgroundImage: `url(${item.icon})`,
                  ...countryIconStyle,
                }}
              />
            )}

            {listItemsData.includes("code") && (
              <span className="without-icon-li-city vms_MobileInput_CountryDropDown_Item_Country_Text">{`${item.label}`}</span>
            )}
            {listItemsData.includes("country") && (
              <span className="without-icon-li-city vms_MobileInput_CountryDropDown_Item_Country_Text">{item.value}</span>
            )}
          </div>
        </li>
      </div>
    );
  };

  const shouldItemRender = (item, value) => {
    console.log("shouldItemRender - item:", item, "value:", value, "filterBy:", filterBy); // Debug line
  
    if(filterBy === 'countryName'){
      return item.value.toLowerCase().indexOf(value.toLowerCase()) > -1;
    }else if(filterBy === 'code'){
      return item.label.toLowerCase().indexOf(value.toLowerCase()) > -1;
    }else if(filterBy === 'none'){
      return true;
    }
    
    // Add default fallback
    return true;
  };

  const onChangeCloseButton = () => {
    setValue("");
    setSelectedIcon("");
    let elementInput = document.getElementById("search-flyout-mobile-input");
    if (elementInput !== null) {
      elementInput.focus();
    }
  };

  let inputclass = "custom-input";
  if (selectedIcon) {
    inputclass = "custom-input padding-l-35";
  }

  // const AutocompleteDropdownComponent = () => {
  //   return (
  //   );
  // };
  return (
    <div
      ref={autoCompleteDropdownref}
      className="vms_MobileInput_CountryDropDown_Container"
      style={{ paddingRight: "10px" }}
    >
      <AutocompleteDropdown
        items={data}
        name="isd_code"
        getItemValue={getItemValue}
        renderItem={renderItem}
        open={open}
        value={open ?  inputValue :  inputValue ? `${inputValue}` : ''}
        shouldItemRender={shouldItemRender}
        // onBlur={()=>{
        //   console.log("blur is called")
        //   setOpen(false)
        // }}
        isDropdown={readOnly}
        tabIndex={-1}
        inputStyle={{
          // paddingLeft: selectedIcon ? "40px" : "",
          width: isMobile && openMobileViewModal ? "100%" : "119px",
          ...countryInputStyle,
        }}
        onChange={(e) => onChangeHandle(e.target.value)}
        onSelect={(val,item) => {onSelect(val,item)}}
        onBlur={(e)=>{
          if(props.onBlur){
            console.log("onblur event")
            props.onBlur(e)
          }
        }}
        // searchLabel={labelCityOfResidence}
        inputProps={{
          id: "search-flyout-mobile-input",
          disabled: disabled,
          autocomplete: "off",
          messageMode: messageMode,
          maxLength:isdMaxLength ? isdMaxLength : null,
          placeholderTextSize: "small",
          additionalLeftViewClassName: [`dropdown-input-left-view`],
          // className:`${selectedIcon ? 'input-left-icon' :'' }`,
          object: {
            paddingLeft: `${selectedIcon   && listItemsData.includes("icon") ? "50px" : "40px"}`,
          },
          inputMethods: {
            onFocus: () => {
              if (isMobile) {
                SetopenMobileViewModal(true);
              }
              setOpen(true);
              onCountryVisibilityChange(true)
            },
          },
          ...inputProps,
        }}
        leftView={

          <>
           {listItemsData?.includes("icon") ?
          <div className="iconDiv">
            {selectedIcon && listItemsData.includes("icon") && (
              <>
              <div
                className="custom-left-icon vms_MobileInput_CountryDropDown_LeftIcon"
                style={{
                  backgroundImage: `url(${selectedIcon})`,
                }}
              />
              </>
            )}
                
          </div>
      : null}
       {/* <div className="iconDiv">
       {inputValue && (
              <div className="plusIcon vms_MobileInput_CountryDropDown_PlusIcon">+</div>
            )
            }
       </div> */}
          </>
      }
        rightView={rightView}
        renderMenu={(children) => {
          console.log("renderMenu children:", children); // Add this debug line
          return (
            <>
              {open && (
                <div
                  className={`${
                    isMobile ? "mainConatinerMobile" : "mainConatiner"
                  } vms_MobileInput_CountryDropDown_MenuContainer`}
                >
                  <div>
                    <div id="ex1-label" />
                    <ul
                      role="listbox"
                      id="ex1-listbox"
                      aria-labelledby="ex1-label"
                      className={
                        !isMobileView ? "ul-style border-desktop-ul vms_MobileInput_CountryDropDown_ul" : "ul-style vms_MobileInput_CountryDropDown_ul"
                      }
                    >
                      <div className="mobile-recently-searched vms_MobileInput_CountryDropDown_menuItem">
                        {children}
                      </div>
                    </ul>
                  </div>
                </div>
              )}
            </>
          );
        }}
        showClearIcon={false}
        onClearIconClick={onChangeCloseButton}
        showSearchIcon={false}
        isMobileView={isMobile}
        showUpDownIcon={false}
        detachableFlyout={true}
        detachedFlyoutPosition="left"
        wrapperStyle={{
          width: isMobile && openMobileViewModal ? "100%" : "119px",
        }}
        menuStyle={{
          width: isMobile && openMobileViewModal ? "100%" : "250px",
        }}
      />

      <ModalPopup
        isMobileView
        isOpen={openMobileViewModal}
        onAfterClose={() => setOpen(false)}
        onRequestClose={() => SetopenMobileViewModal(!openMobileViewModal)}
      >
        <div style={{ padding: "10px" }}>
          <div className="isdCodeLabel vms_MobileInput_CountryDropDown_isdLable">ISD Code</div>
          <AutocompleteDropdown
            items={data}
            getItemValue={getItemValue}
            renderItem={renderItem}
            open={open}
            value={inputValue}
            shouldItemRender={shouldItemRender}
            // onBlur={()=>{
            //   console.log("blur is called")
            //   setOpen(false)
            // }}
            inputStyle={{
              // paddingLeft: selectedIcon ? "40px" : "",
              width: isMobile && openMobileViewModal ? "100%" : "119px",
              ...countryInputStyle,
            }}
            onFocus={() => {
              if (isMobile) {
                SetopenMobileViewModal(true);
              }
              setOpen(true);
              onCountryVisibilityChange(true)
            }}
            onBlur={(e)=>{
              if(props.onBlur){
                props.onBlur(e)
              }
            }}
            onChange={(e) => onChangeHandle(e.target.value)}
            onSelect={(item) => console.log("hhe", item)}
            // searchLabel={labelCityOfResidence}
            inputProps={{
              id: "search-flyout-mobile-input",
              disabled: disabled,
              autocomplete: "off",
              messageMode: messageMode,
              additionalLeftViewClassName: [`dropdown-input-left-view`],
              // className:`${selectedIcon ? 'input-left-icon' :'' }`,
              object: {
                paddingLeft: `${selectedIcon ? "40px" : ""}`,
              },
              inputMethods: {
                onFocus: () => {
                  if (isMobile) {
                    SetopenMobileViewModal(true);
                  }
                  setOpen(true);
                  onCountryVisibilityChange(true)
                },
              },
              ...inputProps,
            }}
            leftView={
              <>
                {selectedIcon && listItemsData.includes("icon") && (
                  <div
                    className="custom-left-icon vms_MobileInput_CountryDropDown_LeftIcon"
                    style={{
                      backgroundImage: `url(${selectedIcon})`,
                    }}
                  />
                )}
              </>
            }
            renderMenu={(children) => (
              <div
                className={`${
                  isMobile ? "mainConatinerMobile" : "mainConatiner"
                } vms_MobileInput_CountryDropDown_MenuContainer`}
              >
                <div>
                  <div id="ex1-label" />
                  <ul
                    role="listbox"
                    id="ex1-listbox"
                    aria-labelledby="ex1-label"
                    className={
                      !isMobileView ? "ul-style border-desktop-ul vms_MobileInput_CountryDropDown_ul" : "ul-style vms_MobileInput_CountryDropDown_ul"
                    }
                  >
                    <div className="mobile-recently-searched vms_MobileInput_CountryDropDown_menuItem">{children}</div>
                  </ul>
                </div>
              </div>
            )}
            showClearIcon={false}
            onClearIconClick={onChangeCloseButton}
            showSearchIcon={false}
            isMobileView={isMobile}
            showUpDownIcon={false}
            detachableFlyout={true}
            detachedFlyoutPosition="left"
            wrapperStyle={{
              width: isMobile && openMobileViewModal ? "100%" : "119px",
            }}
            menuStyle={{
              width: isMobile && openMobileViewModal ? "100%" : "250px",
            }}
          />
        </div>
      </ModalPopup>
    </div>
  );
};

export default CountryDropDown;
