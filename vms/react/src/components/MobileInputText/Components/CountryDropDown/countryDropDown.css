.img-icon-styling {
  height: 30px;
  width: 30px;
  display: inline-block;
  background-size: contain;
  margin-right: 10px;
}
.desktop-li {
  padding: 0px;
  list-style: none;
  border-bottom: solid 1px rgba(151, 151, 151, 0.2);
}
.leftIconClassName {
  top: 10px !important;
  left: 5px !important;
}
.itemContainer {
  background-color: transparent;
}
.itemContainer:hover {
  background: rgba(248, 244, 244, 0.68);
}
.iconDiv {
  display: flex;
}
.plusIcon {
    font-weight: 800;
    font-family: 'Montserrat',sans-serif;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.43;
    letter-spacing: 2px;
    margin: 2px
}

.jp-country {
  display: inline-flex;
  align-items: center;
  padding: 10px 16px;
  padding-left: 8px;
}

.mainConatiner {
  border: 1px solid rgb(3, 134, 139);
  border-radius: 0px 0px 3px 3px;
  font-size: 90%;
  overflow: hidden auto;
  background-color: white;
  min-width: auto;
  width: 250px;
  box-shadow: rgba(0, 0, 0, 0.09) 0px 2px 4px 0px;
  position: absolute;
  top: auto;
  bottom: auto;
  z-index: 10;
}

.mainConatinerMobile {
  border-style: none;
  border-radius: 0px 0px 3px 3px;
  font-size: 90%;
  overflow: hidden auto;
  background-color: white;
  min-width: auto;
  width: 100%;
  box-shadow: rgb(0 0 0 / 9%) 0px 2px 4px 0px;
  left: -1px;
  right: -1px;
  top: 100%;
  height: auto;
  position: unset;
  z-index: 10;
}
.ul-style {
  margin: 0;
  list-style: none;
  padding-left: 0;
}

.ul-style .border-desktop-ul {
  border-width: 1px;
  border-color: #03868b;
  border-top: none;
}
.without-icon-li-city{
  margin-right: 10px;
    display: inline-block;
    min-width: 50px;
    white-space: nowrap;
    max-width: 100px;
    overflow: hidden;
    text-overflow: ellipsis;
}

.mobile-recently-searched{
  max-height: 365px;
}
.mainConatiner::-webkit-scrollbar {
  width: 2px;
}
.mainConatiner::-webkit-scrollbar-thumb {
  background: #888;
}
.mainConatiner::-webkit-scrollbar-track {
  background: #f1f1f1;
}


.mainConatinerMobile::-webkit-scrollbar {
  width: 2px;
  height: 3px;
}
.mainConatinerMobile::-webkit-scrollbar-thumb {
  background: #888;
}
.mainConatinerMobile::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.isdCodeLabel{
  font-weight: 400;
    font-family: Montserrat;
    font-size: 14px;
    font-stretch: normal;
    font-style: normal;
    line-height: 20px;
    letter-spacing: 0.3px;
    color: #4d4d4f;
    display: block;
    position: relative;
    text-transform: uppercase;
    margin-bottom: 12px;
    line-height: 18px;
    letter-spacing: 0;
}
.input-left-icon {
  padding-left: 40px !important;
}
.custom-left-icon{
  width: 24px;
    height: 24px;
    background-size: contain;
}
.dropdown-input-left-view{
  top: 10px !important;
  left: 5px !important;
}