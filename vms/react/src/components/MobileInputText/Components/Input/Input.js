import React, { useState, useEffect } from "react";
import { InputText } from "../../../InputText/InputText";
import { InputContainer, InputLabel } from "./Input.styled";
const Input = (props) => {
  const {
    type,
    placeholder,
    mobileNumber,
    disabled,
    mobileInputTextStyle,
    isMobile,
    messageMode,
    maxLength,
    onMobileNumberChange = () => {},
    tabIndex,
    onBlur,
    inputId,
    autoComplete
  } = props;
  const [value, setValue] = useState();

  const onChange = (e) => {
    onMobileNumberChange(e.target.value);
    setValue(e.target.value);
  };

  useEffect(() => {
    if (mobileNumber) {
      setValue(mobileNumber);
      onMobileNumberChange(mobileNumber);
    }
  }, [mobileNumber]);
  function isInputNumber(evt) {
    var ch = String.fromCharCode(evt.which);
    // if (!/[0-9]/.test(ch)) {
    if (!/\d/.test(ch)) {
      evt.preventDefault();
    }
  }
  return (
    <InputContainer className="vms_MobileInput_InputContainer">
      <InputLabel className="vms_MobileInput_InputLable" style={{ disply: "none" }} />
      <div style={{ position: "relative" }}>
        <InputText
          id={inputId}
          type={type}
          name="phone"
          placeholder={placeholder}
          maxLength={maxLength ? maxLength : 17}
          autocomplete={autoComplete}
          value={value}
          messageMode={messageMode}
          isMobile={isMobile}
          disabled={disabled}
          additionalStyle={mobileInputTextStyle}
          onChange={(e) => onChange(e)}
          onKeyPress={(evt) => isInputNumber(evt)}
          tabIndex={tabIndex}
          onBlur={(e)=>onBlur(e)}
          {...props}
        />
      </div>
    </InputContainer>
  );
};

export default Input;
