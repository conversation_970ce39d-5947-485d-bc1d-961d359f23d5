import styled from "styled-components";

export const InputContainer = styled.div`
  max-width: 320px;
`;

export const InputLabel = styled.label`
  font-weight: 400;
  font-family: Montserrat;
  font-size: 14px;
  font-stretch: normal;
  font-style: normal;
  line-height: 20px;
  letter-spacing: 0.3px;
  color: #4d4d4f;
  display: block;
  position: relative;
  text-transform: uppercase;
`;

export const StyledInput = styled.input.attrs((props) => ({
  maxLength: 17,
}))`
  margin-top: 0px;
  display: block;
  box-sizing: border-box;
  font-weight: 700;
  font-family: Montserrat;
  font-size: 14px;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.43;
  width: 100%;
  border-radius: 3px;
  letter-spacing: 0.3px;
  color: #4d4d4f;
  padding: 14px 16px;
  margin-top: 12px;
  text-overflow: ellipsis;
  height: 48px;
  border: solid 1px #4d4d4f;
  background-color: #fff;
  &:focus {
    outline: none;
    ${({ isMobile }) =>
      isMobile
        ? { "border-bottom": "1px solid #03868b" }
        : { border: "1px solid #03868b" }};
  }
  &::placeholder {
    font-weight: 400;
    font-family: Montserrat;
    font-size: 16px;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.43;
    border-radius: 3px;
    letter-spacing: 0.3px;
    color: #4d4d4f;
  }
`;

// export const StyledInput = styled.input`
//   margin-top: 0px;
//   display: block;
//   box-sizing: border-box;
//   font-weight: 700;
//   font-family: Montserrat;
//   font-size: 14px;
//   font-stretch: normal;
//   font-style: normal;
//   line-height: 1.43;
//   width: 100%;
//   border-radius: 3px;
//   letter-spacing: 0.3px;
//   color: #4d4d4f;
//   padding: 14px 16px;
//   margin-top: 12px;
//   text-overflow: ellipsis;
//   height: 48px;
//   border: solid 1px #4d4d4f;
//   background-color: #fff;
//   &:focus {
//     outline: none;
//     ${({ isMobile }) =>
//       isMobile
//         ? { "border-bottom": "1px solid #03868b" }
//         : { border: "1px solid #03868b" }};
//   }
//   &::placeholder {
//     font-weight: 400;
//     font-family: Montserrat;
//     font-size: 16px;
//     font-stretch: normal;
//     font-style: normal;
//     line-height: 1.43;
//     border-radius: 3px;
//     letter-spacing: 0.3px;
//     color: #4d4d4f;
//   }
// `;
