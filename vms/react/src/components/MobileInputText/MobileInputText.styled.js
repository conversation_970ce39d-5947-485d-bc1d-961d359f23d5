import styled from "styled-components";

export const Container = styled.div``;

export const IsdLabel = styled.div`
  white-space: nowrap;
  width: auto;
  height: 18px;
  font-weight: 400;
  font-family: Montserrat;
  font-size: 14px;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.29;
  letter-spacing: normal;
  color: #4d4d4f;
  margin-bottom: 10px;
`;

export const IsdPickerContainer = styled.div`
  display: flex;
  align-items: flex-start;
`;

export const IsdCode = styled.div`
  flex: 0 1;
`;

export const MobileNumber = styled.div`
  flex: 1 1;
`;

export const ErrorMessage = styled.div``;
export const ErrorText = styled.span`
font-weight: 400;
    font-family: Montserrat;
    font-size: 14px;
    font-stretch: normal;
    font-style: normal;
    line-height: 20px;
    letter-spacing: 0.3px;
    position: relative;
    top: 8px;
    left: 16px;
    color: #e11b22;
`;

