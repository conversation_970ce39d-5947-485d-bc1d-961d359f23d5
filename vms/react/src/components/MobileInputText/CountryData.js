import AF from '../../assets/images/MobileInputText/Country_Flag/AF.png';
import AL from '../../assets/images/MobileInputText/Country_Flag/AL.png';
import DZ from '../../assets/images/MobileInputText/Country_Flag/DZ.png';
import AS from '../../assets/images/MobileInputText/Country_Flag/AS.png';
import AD from '../../assets/images/MobileInputText/Country_Flag/AD.png';
import AO from '../../assets/images/MobileInputText/Country_Flag/AO.png';
import AI from '../../assets/images/MobileInputText/Country_Flag/AI.png';
import AG from '../../assets/images/MobileInputText/Country_Flag/AG.png';
import AR from '../../assets/images/MobileInputText/Country_Flag/AR.png';
import AM from '../../assets/images/MobileInputText/Country_Flag/AM.png';
import AW from '../../assets/images/MobileInputText/Country_Flag/AW.png';
import AU from '../../assets/images/MobileInputText/Country_Flag/AU.png';
import AT from '../../assets/images/MobileInputText/Country_Flag/AT.png';
import AZ from '../../assets/images/MobileInputText/Country_Flag/AZ.png';
import BS from '../../assets/images/MobileInputText/Country_Flag/BS.png';
import BH from '../../assets/images/MobileInputText/Country_Flag/BH.png';
import BD from '../../assets/images/MobileInputText/Country_Flag/BD.png';
import BB from '../../assets/images/MobileInputText/Country_Flag/BB.png';
import BY from '../../assets/images/MobileInputText/Country_Flag/BY.png';
import BE from '../../assets/images/MobileInputText/Country_Flag/BE.png';
import BZ from '../../assets/images/MobileInputText/Country_Flag/BZ.png';
import BJ from '../../assets/images/MobileInputText/Country_Flag/BJ.png';
import BM from '../../assets/images/MobileInputText/Country_Flag/BM.png';
import BT from '../../assets/images/MobileInputText/Country_Flag/BT.png';
import BA from '../../assets/images/MobileInputText/Country_Flag/BA.png';
import BW from '../../assets/images/MobileInputText/Country_Flag/BW.png';
import BR from '../../assets/images/MobileInputText/Country_Flag/BR.png';
import IO from '../../assets/images/MobileInputText/Country_Flag/IO.png';
import BG from '../../assets/images/MobileInputText/Country_Flag/BG.png';
import BF from '../../assets/images/MobileInputText/Country_Flag/BF.png';
import BI from '../../assets/images/MobileInputText/Country_Flag/BI.png';
import KH from '../../assets/images/MobileInputText/Country_Flag/KH.png';
import CM from '../../assets/images/MobileInputText/Country_Flag/CM.png';
import CA from '../../assets/images/MobileInputText/Country_Flag/CA.png';
import CV from '../../assets/images/MobileInputText/Country_Flag/CV.png';
import KY from '../../assets/images/MobileInputText/Country_Flag/KY.png';
import CF from '../../assets/images/MobileInputText/Country_Flag/CF.png';
import TD from '../../assets/images/MobileInputText/Country_Flag/TD.png';
import CL from '../../assets/images/MobileInputText/Country_Flag/CL.png';
import CN from '../../assets/images/MobileInputText/Country_Flag/CN.png';
import CX from '../../assets/images/MobileInputText/Country_Flag/CX.png';
import CO from '../../assets/images/MobileInputText/Country_Flag/CO.png';
import KM from '../../assets/images/MobileInputText/Country_Flag/KM.png';
import CG from '../../assets/images/MobileInputText/Country_Flag/CG.png';
import CK from '../../assets/images/MobileInputText/Country_Flag/CK.png';
import CR from '../../assets/images/MobileInputText/Country_Flag/CR.png';
import HR from '../../assets/images/MobileInputText/Country_Flag/HR.png';
import CU from '../../assets/images/MobileInputText/Country_Flag/CU.png';
import CY from '../../assets/images/MobileInputText/Country_Flag/CY.png';
import CZ from '../../assets/images/MobileInputText/Country_Flag/CZ.png';
import DK from '../../assets/images/MobileInputText/Country_Flag/DK.png';
import DJ from '../../assets/images/MobileInputText/Country_Flag/DJ.png';
import DM from '../../assets/images/MobileInputText/Country_Flag/DM.png';
import DO from '../../assets/images/MobileInputText/Country_Flag/DO.png';
import EC from '../../assets/images/MobileInputText/Country_Flag/EC.png';
import EG from '../../assets/images/MobileInputText/Country_Flag/EG.png';
import SV from '../../assets/images/MobileInputText/Country_Flag/SV.png';
import GQ from '../../assets/images/MobileInputText/Country_Flag/GQ.png';
import ER from '../../assets/images/MobileInputText/Country_Flag/ER.png';
import EE from '../../assets/images/MobileInputText/Country_Flag/EE.png';
import ET from '../../assets/images/MobileInputText/Country_Flag/ET.png';
import FO from '../../assets/images/MobileInputText/Country_Flag/FO.png';
import FJ from '../../assets/images/MobileInputText/Country_Flag/FJ.png';
import FI from '../../assets/images/MobileInputText/Country_Flag/FI.png';
import FR from '../../assets/images/MobileInputText/Country_Flag/FR.png';
import GF from '../../assets/images/MobileInputText/Country_Flag/GF.png';
import PF from '../../assets/images/MobileInputText/Country_Flag/PF.png';
import GA from '../../assets/images/MobileInputText/Country_Flag/GA.png';
import GM from '../../assets/images/MobileInputText/Country_Flag/GM.png';
import GE from '../../assets/images/MobileInputText/Country_Flag/GE.png';
import DE from '../../assets/images/MobileInputText/Country_Flag/DE.png';
import GH from '../../assets/images/MobileInputText/Country_Flag/GH.png';
import GI from '../../assets/images/MobileInputText/Country_Flag/GI.png';
import GR from '../../assets/images/MobileInputText/Country_Flag/GR.png';
import GL from '../../assets/images/MobileInputText/Country_Flag/GL.png';
import GD from '../../assets/images/MobileInputText/Country_Flag/GD.png';
import GP from '../../assets/images/MobileInputText/Country_Flag/GP.png';
import GU from '../../assets/images/MobileInputText/Country_Flag/GU.png';
import GT from '../../assets/images/MobileInputText/Country_Flag/GT.png';
import GN from '../../assets/images/MobileInputText/Country_Flag/GN.png';
import GW from '../../assets/images/MobileInputText/Country_Flag/GW.png';
import GY from '../../assets/images/MobileInputText/Country_Flag/GY.png';
import HT from '../../assets/images/MobileInputText/Country_Flag/HT.png';
import HN from '../../assets/images/MobileInputText/Country_Flag/HN.png';
import HU from '../../assets/images/MobileInputText/Country_Flag/HU.png';
import IS from '../../assets/images/MobileInputText/Country_Flag/IS.png';
import IN from '../../assets/images/MobileInputText/Country_Flag/IN.png';
import ID from '../../assets/images/MobileInputText/Country_Flag/ID.png';
import IQ from '../../assets/images/MobileInputText/Country_Flag/IQ.png';
import IE from '../../assets/images/MobileInputText/Country_Flag/IE.png';
import IL from '../../assets/images/MobileInputText/Country_Flag/IL.png';
import IT from '../../assets/images/MobileInputText/Country_Flag/IT.png';
import JM from '../../assets/images/MobileInputText/Country_Flag/JM.png';
import JP from '../../assets/images/MobileInputText/Country_Flag/JP.png';
import JO from '../../assets/images/MobileInputText/Country_Flag/JO.png';
import KZ from '../../assets/images/MobileInputText/Country_Flag/KZ.png';
import KE from '../../assets/images/MobileInputText/Country_Flag/KE.png';
import KI from '../../assets/images/MobileInputText/Country_Flag/KI.png';
import KW from '../../assets/images/MobileInputText/Country_Flag/KW.png';
import KG from '../../assets/images/MobileInputText/Country_Flag/KG.png';
import LV from '../../assets/images/MobileInputText/Country_Flag/LV.png';
import LB from '../../assets/images/MobileInputText/Country_Flag/LB.png';
import LS from '../../assets/images/MobileInputText/Country_Flag/LS.png';
import LR from '../../assets/images/MobileInputText/Country_Flag/LR.png';
import LI from '../../assets/images/MobileInputText/Country_Flag/LI.png';
import LT from '../../assets/images/MobileInputText/Country_Flag/LT.png';
import LU from '../../assets/images/MobileInputText/Country_Flag/LU.png';
import MG from '../../assets/images/MobileInputText/Country_Flag/MG.png';
import MW from '../../assets/images/MobileInputText/Country_Flag/MW.png';
import MY from '../../assets/images/MobileInputText/Country_Flag/MY.png';
import MV from '../../assets/images/MobileInputText/Country_Flag/MV.png';
import ML from '../../assets/images/MobileInputText/Country_Flag/ML.png';
import MT from '../../assets/images/MobileInputText/Country_Flag/MT.png';
import MH from '../../assets/images/MobileInputText/Country_Flag/MH.png';
import MQ from '../../assets/images/MobileInputText/Country_Flag/MQ.png';
import MR from '../../assets/images/MobileInputText/Country_Flag/MR.png';
import MU from '../../assets/images/MobileInputText/Country_Flag/MU.png';
import YT from '../../assets/images/MobileInputText/Country_Flag/YT.png';
import MX from '../../assets/images/MobileInputText/Country_Flag/MX.png';
import MC from '../../assets/images/MobileInputText/Country_Flag/MC.png';
import MN from '../../assets/images/MobileInputText/Country_Flag/MN.png';
import ME from '../../assets/images/MobileInputText/Country_Flag/ME.png';
import MS from '../../assets/images/MobileInputText/Country_Flag/MS.png';
import MA from '../../assets/images/MobileInputText/Country_Flag/MA.png';
import MM from '../../assets/images/MobileInputText/Country_Flag/MM.png';
import NA from '../../assets/images/MobileInputText/Country_Flag/NA.png';
import NR from '../../assets/images/MobileInputText/Country_Flag/NR.png';
import NP from '../../assets/images/MobileInputText/Country_Flag/NP.png';
import NL from '../../assets/images/MobileInputText/Country_Flag/NL.png';
import AN from '../../assets/images/MobileInputText/Country_Flag/AN.png';
import NC from '../../assets/images/MobileInputText/Country_Flag/NC.png';
import NZ from '../../assets/images/MobileInputText/Country_Flag/NZ.png';
import NI from '../../assets/images/MobileInputText/Country_Flag/NI.png';
import NE from '../../assets/images/MobileInputText/Country_Flag/NE.png';
import NG from '../../assets/images/MobileInputText/Country_Flag/NG.png';
import NU from '../../assets/images/MobileInputText/Country_Flag/NU.png';
import NF from '../../assets/images/MobileInputText/Country_Flag/NF.png';
import MP from '../../assets/images/MobileInputText/Country_Flag/MP.png';
import NO from '../../assets/images/MobileInputText/Country_Flag/NO.png';
import OM from '../../assets/images/MobileInputText/Country_Flag/OM.png';
import PK from '../../assets/images/MobileInputText/Country_Flag/PK.png';
import PW from '../../assets/images/MobileInputText/Country_Flag/PW.png';
import PA from '../../assets/images/MobileInputText/Country_Flag/PA.png';
import PG from '../../assets/images/MobileInputText/Country_Flag/PG.png';
import PY from '../../assets/images/MobileInputText/Country_Flag/PY.png';
import PE from '../../assets/images/MobileInputText/Country_Flag/PE.png';
import PH from '../../assets/images/MobileInputText/Country_Flag/PH.png';
import PL from '../../assets/images/MobileInputText/Country_Flag/PL.png';
import PT from '../../assets/images/MobileInputText/Country_Flag/PT.png';
import PR from '../../assets/images/MobileInputText/Country_Flag/PR.png';
import QA from '../../assets/images/MobileInputText/Country_Flag/QA.png';
import RO from '../../assets/images/MobileInputText/Country_Flag/RO.png';
import RW from '../../assets/images/MobileInputText/Country_Flag/RW.png';
import WS from '../../assets/images/MobileInputText/Country_Flag/WS.png';
import SM from '../../assets/images/MobileInputText/Country_Flag/SM.png';
import SA from '../../assets/images/MobileInputText/Country_Flag/SA.png';
import SN from '../../assets/images/MobileInputText/Country_Flag/SN.png';
import RS from '../../assets/images/MobileInputText/Country_Flag/RS.png';
import SC from '../../assets/images/MobileInputText/Country_Flag/SC.png';
import SL from '../../assets/images/MobileInputText/Country_Flag/SL.png';
import SG from '../../assets/images/MobileInputText/Country_Flag/SG.png';
import SK from '../../assets/images/MobileInputText/Country_Flag/SK.png';
import SI from '../../assets/images/MobileInputText/Country_Flag/SI.png';
import SB from '../../assets/images/MobileInputText/Country_Flag/SB.png';
import ZA from '../../assets/images/MobileInputText/Country_Flag/ZA.png';
import GS from '../../assets/images/MobileInputText/Country_Flag/GS.png';
import ES from '../../assets/images/MobileInputText/Country_Flag/ES.png';
import LK from '../../assets/images/MobileInputText/Country_Flag/LK.png';
import SD from '../../assets/images/MobileInputText/Country_Flag/SD.png';
import SR from '../../assets/images/MobileInputText/Country_Flag/SR.png';
import SZ from '../../assets/images/MobileInputText/Country_Flag/SZ.png';
import SE from '../../assets/images/MobileInputText/Country_Flag/SE.png';
import CH from '../../assets/images/MobileInputText/Country_Flag/CH.png';
import TJ from '../../assets/images/MobileInputText/Country_Flag/TJ.png';
import TH from '../../assets/images/MobileInputText/Country_Flag/TH.png';
import TG from '../../assets/images/MobileInputText/Country_Flag/TG.png';
import TK from '../../assets/images/MobileInputText/Country_Flag/TK.png';
import TO from '../../assets/images/MobileInputText/Country_Flag/TO.png';
import TT from '../../assets/images/MobileInputText/Country_Flag/TT.png';
import TN from '../../assets/images/MobileInputText/Country_Flag/TN.png';
import TR from '../../assets/images/MobileInputText/Country_Flag/TR.png';
import TM from '../../assets/images/MobileInputText/Country_Flag/TM.png';
import TC from '../../assets/images/MobileInputText/Country_Flag/TC.png';
import TV from '../../assets/images/MobileInputText/Country_Flag/TV.png';
import UG from '../../assets/images/MobileInputText/Country_Flag/UG.png';
import UA from '../../assets/images/MobileInputText/Country_Flag/UA.png';
import AE from '../../assets/images/MobileInputText/Country_Flag/AE.png';
import GB from '../../assets/images/MobileInputText/Country_Flag/GB.png';
import US from '../../assets/images/MobileInputText/Country_Flag/US.png';
import UY from '../../assets/images/MobileInputText/Country_Flag/UY.png';
import UZ from '../../assets/images/MobileInputText/Country_Flag/UZ.png';
import VU from '../../assets/images/MobileInputText/Country_Flag/VU.png';
import WF from '../../assets/images/MobileInputText/Country_Flag/WF.png';
import YE from '../../assets/images/MobileInputText/Country_Flag/YE.png';
import ZM from '../../assets/images/MobileInputText/Country_Flag/ZM.png';
import ZW from '../../assets/images/MobileInputText/Country_Flag/ZW.png';
import BO from '../../assets/images/MobileInputText/Country_Flag/BO.png';
import BN from '../../assets/images/MobileInputText/Country_Flag/BN.png';
import CC from '../../assets/images/MobileInputText/Country_Flag/CC.png';
import CD from '../../assets/images/MobileInputText/Country_Flag/CD.png';
import CI from '../../assets/images/MobileInputText/Country_Flag/CI.png';
import FK from '../../assets/images/MobileInputText/Country_Flag/FK.png';
import GG from '../../assets/images/MobileInputText/Country_Flag/GG.png';
import VA from '../../assets/images/MobileInputText/Country_Flag/VA.png';
import HK from '../../assets/images/MobileInputText/Country_Flag/HK.png';
import IR from '../../assets/images/MobileInputText/Country_Flag/IR.png';
import IM from '../../assets/images/MobileInputText/Country_Flag/IM.png';
import JE from '../../assets/images/MobileInputText/Country_Flag/JE.png';
import KP from '../../assets/images/MobileInputText/Country_Flag/KP.png';
import KR from '../../assets/images/MobileInputText/Country_Flag/KR.png';
import LA from '../../assets/images/MobileInputText/Country_Flag/LA.png';
import LY from '../../assets/images/MobileInputText/Country_Flag/LY.png';
import MO from '../../assets/images/MobileInputText/Country_Flag/MO.png';
import MK from '../../assets/images/MobileInputText/Country_Flag/MK.png';
import FM from '../../assets/images/MobileInputText/Country_Flag/FM.png';
import MD from '../../assets/images/MobileInputText/Country_Flag/MD.png';
import MZ from '../../assets/images/MobileInputText/Country_Flag/MZ.png';
import PS from '../../assets/images/MobileInputText/Country_Flag/PS.png';
import PN from '../../assets/images/MobileInputText/Country_Flag/PN.png';
import RE from '../../assets/images/MobileInputText/Country_Flag/RE.png';
import RU from '../../assets/images/MobileInputText/Country_Flag/RU.png';
import BL from '../../assets/images/MobileInputText/Country_Flag/BL.png';
import SH from '../../assets/images/MobileInputText/Country_Flag/SH.png';
import KN from '../../assets/images/MobileInputText/Country_Flag/KN.png';
import LC from '../../assets/images/MobileInputText/Country_Flag/LC.png';
import MF from '../../assets/images/MobileInputText/Country_Flag/MF.png';
import PM from '../../assets/images/MobileInputText/Country_Flag/PM.png';
import VC from '../../assets/images/MobileInputText/Country_Flag/VC.png';
import ST from '../../assets/images/MobileInputText/Country_Flag/ST.png';
import SO from '../../assets/images/MobileInputText/Country_Flag/SO.png';
import SJ from '../../assets/images/MobileInputText/Country_Flag/SJ.png';
import SY from '../../assets/images/MobileInputText/Country_Flag/SY.png';
import TW from '../../assets/images/MobileInputText/Country_Flag/TW.png';
import TZ from '../../assets/images/MobileInputText/Country_Flag/TZ.png';
import TL from '../../assets/images/MobileInputText/Country_Flag/TL.png';
import VE from '../../assets/images/MobileInputText/Country_Flag/VE.png';
import VN from '../../assets/images/MobileInputText/Country_Flag/VN.png';
import VG from '../../assets/images/MobileInputText/Country_Flag/VG.png';
import VI from '../../assets/images/MobileInputText/Country_Flag/VI.png';

export const countriesAllData = [
  {
      "id": "1",
      "value": "Afghanistan",
      "label": "+93",
      "code": "AF",
      "icon": `${AF}`
  },
  {
      "id": "2",
      "value": "Albania",
      "label": "+355",
      "code": "AL",
      "icon": `${AL}`
  },
  {
      "id": "3",
      "value": "Algeria",
      "label": "+213",
      "code": "DZ",
      "icon": `${DZ}`
  },
  {
      "id": "4",
      "value": "AmericanSamoa",
      "label": "+1684",
      "code": "AS",
      "icon": `${AS}`
  },
  {
      "id": "5",
      "value": "Andorra",
      "label": "+376",
      "code": "AD",
      "icon": `${AD}`
  },
  {
      "id": "6",
      "value": "Angola",
      "label": "+244",
      "code": "AO",
      "icon": `${AO}`
  },
  {
      "id": "7",
      "value": "Anguilla",
      "label": "+1264",
      "code": "AI",
      "icon": `${AI}`
  },
  {
      "id": "8",
      "value": "Antigua and Barbuda",
      "label": "+1268",
      "code": "AG",
      "icon": `${AG}`
  },
  {
      "id": "9",
      "value": "Argentina",
      "label": "+54",
      "code": "AR",
      "icon": `${AR}`
  },
  {
      "id": "10",
      "value": "Armenia",
      "label": "+374",
      "code": "AM",
      "icon": `${AM}`
  },
  {
      "id": "11",
      "value": "Aruba",
      "label": "+297",
      "code": "AW",
      "icon": `${AW}`
  },
  {
      "id": "12",
      "value": "Australia",
      "label": "+61",
      "code": "AU",
      "icon": `${AU}`
  },
  {
      "id": "13",
      "value": "Austria",
      "label": "+43",
      "code": "AT",
      "icon": `${AT}`
  },
  {
      "id": "14",
      "value": "Azerbaijan",
      "label": "+994",
      "code": "AZ",
      "icon": `${AZ}`
  },
  {
      "id": "15",
      "value": "Bahamas",
      "label": "+1242",
      "code": "BS",
      "icon": `${BS}`
  },
  {
      "id": "16",
      "value": "Bahrain",
      "label": "+973",
      "code": "BH",
      "icon": `${BH}`
  },
  {
      "id": "17",
      "value": "Bangladesh",
      "label": "+880",
      "code": "BD",
      "icon": `${BD}`
  },
  {
      "id": "18",
      "value": "Barbados",
      "label": "+1246",
      "code": "BB",
      "icon": `${BB}`
  },
  {
      "id": "19",
      "value": "Belarus",
      "label": "+375",
      "code": "BY",
      "icon": `${BY}`
  },
  {
      "id": "20",
      "value": "Belgium",
      "label": "+32",
      "code": "BE",
      "icon": `${BE}`
  },
  {
      "id": "21",
      "value": "Belize",
      "label": "+501",
      "code": "BZ",
      "icon": `${BZ}`
  },
  {
      "id": "22",
      "value": "Benin",
      "label": "+229",
      "code": "BJ",
      "icon": `${BJ}`
  },
  {
      "id": "23",
      "value": "Bermuda",
      "label": "+1441",
      "code": "BM",
      "icon": `${BM}`
  },
  {
      "id": "24",
      "value": "Bhutan",
      "label": "+975",
      "code": "BT",
      "icon": `${BT}`
  },
  {
      "id": "197",
      "value": "Bolivia, Plurinational State of",
      "label": "+591",
      "code": "BO",
      "icon": `${BO}`
  },
  {
      "id": "25",
      "value": "Bosnia and Herzegovina",
      "label": "+387",
      "code": "BA",
      "icon": `${BA}`
  },
  {
      "id": "26",
      "value": "Botswana",
      "label": "+267",
      "code": "BW",
      "icon": `${BW}`
  },
  {
      "id": "27",
      "value": "Brazil",
      "label": "+55",
      "code": "BR",
      "icon": `${BR}`
  },
  {
      "id": "28",
      "value": "British Indian Ocean Territory",
      "label": "+246",
      "code": "IO",
      "icon": `${IO}`
  },
  {
      "id": "198",
      "value": "Brunei Darussalam",
      "label": "+673",
      "code": "BN",
      "icon": `${BN}`
  },
  {
      "id": "29",
      "value": "Bulgaria",
      "label": "+359",
      "code": "BG",
      "icon": `${BG}`
  },
  {
      "id": "30",
      "value": "Burkina Faso",
      "label": "+226",
      "code": "BF",
      "icon": `${BF}`
  },
  {
      "id": "31",
      "value": "Burundi",
      "label": "+257",
      "code": "BI",
      "icon": `${BI}`
  },
  {
      "id": "32",
      "value": "Cambodia",
      "label": "+855",
      "code": "KH",
      "icon": `${KH}`
  },
  {
      "id": "33",
      "value": "Cameroon",
      "label": "+237",
      "code": "CM",
      "icon": `${CM}`
  },
  {
      "id": "34",
      "value": "Canada",
      "label": "+1",
      "code": "CA",
      "icon": `${CA}`
  },
  {
      "id": "35",
      "value": "Cape Verde",
      "label": "+238",
      "code": "CV",
      "icon": `${CV}`
  },
  {
      "id": "36",
      "value": "Cayman Islands",
      "label": "+345",
      "code": "KY",
      "icon": `${KY}`
  },
  {
      "id": "37",
      "value": "Central African Republic",
      "label": "+236",
      "code": "CF",
      "icon": `${CF}`
  },
  {
      "id": "38",
      "value": "Chad",
      "label": "+235",
      "code": "TD",
      "icon": `${TD}`
  },
  {
      "id": "39",
      "value": "Chile",
      "label": "+56",
      "code": "CL",
      "icon": `${CL}`
  },
  {
      "id": "40",
      "value": "China",
      "label": "+86",
      "code": "CN",
      "icon": `${CN}`
  },
  {
      "id": "41",
      "value": "Christmas Island",
      "label": "+61",
      "code": "CX",
      "icon": `${CX}`
  },
  {
      "id": "199",
      "value": "Cocos (Keeling) Islands",
      "label": "+61",
      "code": "CC",
      "icon": `${CC}`
  },
  {
      "id": "42",
      "value": "Colombia",
      "label": "+57",
      "code": "CO",
      "icon": `${CO}`
  },
  {
      "id": "43",
      "value": "Comoros",
      "label": "+269",
      "code": "KM",
      "icon": `${KM}`
  },
  {
      "id": "44",
      "value": "Congo",
      "label": "+242",
      "code": "CG",
      "icon": `${CG}`
  },
  {
      "id": "200",
      "value": "Congo, The Democratic Republic of the",
      "label": "+243",
      "code": "CD",
      "icon": `${CD}`
  },
  {
      "id": "45",
      "value": "Cook Islands",
      "label": "+682",
      "code": "CK",
      "icon": `${CK}`
  },
  {
      "id": "46",
      "value": "Costa Rica",
      "label": "+506",
      "code": "CR",
      "icon": `${CR}`
  },
  {
      "id": "201",
      "value": "Cote d'Ivoire",
      "label": "+225",
      "code": "CI",
      "icon": `${CI}`
  },
  {
      "id": "47",
      "value": "Croatia",
      "label": "+385",
      "code": "HR",
      "icon": `${HR}`
  },
  {
      "id": "48",
      "value": "Cuba",
      "label": "+53",
      "code": "CU",
      "icon": `${CU}`
  },
  {
      "id": "49",
      "value": "Cyprus",
      "label": "+537",
      "code": "CY",
      "icon": `${CY}`
  },
  {
      "id": "50",
      "value": "Czech Republic",
      "label": "+420",
      "code": "CZ",
      "icon": `${CZ}`
  },
  {
      "id": "51",
      "value": "Denmark",
      "label": "+45",
      "code": "DK",
      "icon": `${DK}`
  },
  {
      "id": "52",
      "value": "Djibouti",
      "label": "+253",
      "code": "DJ",
      "icon": `${DJ}`
  },
  {
      "id": "53",
      "value": "Dominica",
      "label": "+1767",
      "code": "DM",
      "icon": `${DM}`
  },
  {
      "id": "54",
      "value": "Dominican Republic",
      "label": "+1849",
      "code": "DO",
      "icon": `${DO}`
  },
  {
      "id": "55",
      "value": "Ecuador",
      "label": "+593",
      "code": "EC",
      "icon": `${EC}`
  },
  {
      "id": "56",
      "value": "Egypt",
      "label": "+20",
      "code": "EG",
      "icon": `${EG}`
  },
  {
      "id": "57",
      "value": "El Salvador",
      "label": "+503",
      "code": "SV",
      "icon": `${SV}`
  },
  {
      "id": "58",
      "value": "Equatorial Guinea",
      "label": "+240",
      "code": "GQ",
      "icon": `${GQ}`
  },
  {
      "id": "59",
      "value": "Eritrea",
      "label": "+291",
      "code": "ER",
      "icon": `${ER}`
  },
  {
      "id": "60",
      "value": "Estonia",
      "label": "+372",
      "code": "EE",
      "icon": `${EE}`
  },
  {
      "id": "61",
      "value": "Ethiopia",
      "label": "+251",
      "code": "ET",
      "icon": `${ET}`
  },
  {
      "id": "202",
      "value": "Falkland Islands (Malvinas)",
      "label": "+500",
      "code": "FK",
      "icon": `${FK}`
  },
  {
      "id": "62",
      "value": "Faroe Islands",
      "label": "+298",
      "code": "FO",
      "icon": `${FO}`
  },
  {
      "id": "63",
      "value": "Fiji",
      "label": "+679",
      "code": "FJ",
      "icon": `${FJ}`
  },
  {
      "id": "64",
      "value": "Finland",
      "label": "+358",
      "code": "FI",
      "icon": `${FI}`
  },
  {
      "id": "65",
      "value": "France",
      "label": "+33",
      "code": "FR",
      "icon": `${FR}`
  },
  {
      "id": "66",
      "value": "French Guiana",
      "label": "+594",
      "code": "GF",
      "icon": `${GF}`
  },
  {
      "id": "67",
      "value": "French Polynesia",
      "label": "+689",
      "code": "PF",
      "icon": `${PF}`
  },
  {
      "id": "68",
      "value": "Gabon",
      "label": "+241",
      "code": "GA",
      "icon": `${GA}`
  },
  {
      "id": "69",
      "value": "Gambia",
      "label": "+220",
      "code": "GM",
      "icon": `${GM}`
  },
  {
      "id": "70",
      "value": "Georgia",
      "label": "+995",
      "code": "GE",
      "icon": `${GE}`
  },
  {
      "id": "71",
      "value": "Germany",
      "label": "+49",
      "code": "DE",
      "icon": `${DE}`
  },
  {
      "id": "72",
      "value": "Ghana",
      "label": "+233",
      "code": "GH",
      "icon": `${GH}`
  },
  {
      "id": "73",
      "value": "Gibraltar",
      "label": "+350",
      "code": "GI",
      "icon": `${GI}`
  },
  {
      "id": "74",
      "value": "Greece",
      "label": "+30",
      "code": "GR",
      "icon": `${GR}`
  },
  {
      "id": "75",
      "value": "Greenland",
      "label": "+299",
      "code": "GL",
      "icon": `${GL}`
  },
  {
      "id": "76",
      "value": "Grenada",
      "label": "+1473",
      "code": "GD",
      "icon": `${GD}`
  },
  {
      "id": "77",
      "value": "Guadeloupe",
      "label": "+590",
      "code": "GP",
      "icon": `${GP}`
  },
  {
      "id": "78",
      "value": "Guam",
      "label": "+1671",
      "code": "GU",
      "icon": `${GU}`
  },
  {
      "id": "79",
      "value": "Guatemala",
      "label": "+502",
      "code": "GT",
      "icon": `${GT}`
  },
  {
      "id": "203",
      "value": "Guernsey",
      "label": "+44",
      "code": "GG",
      "icon": `${GG}`
  },
  {
      "id": "80",
      "value": "Guinea",
      "label": "+224",
      "code": "GN",
      "icon": `${GN}`
  },
  {
      "id": "81",
      "value": "Guinea-Bissau",
      "label": "+245",
      "code": "GW",
      "icon": `${GW}`
  },
  {
      "id": "82",
      "value": "Guyana",
      "label": "+595",
      "code": "GY",
      "icon": `${GY}`
  },
  {
      "id": "83",
      "value": "Haiti",
      "label": "+509",
      "code": "HT",
      "icon": `${HT}`
  },
  {
      "id": "204",
      "value": "Holy See (Vatican City State)",
      "label": "+379",
      "code": "VA",
      "icon": `${VA}`
  },
  {
      "id": "84",
      "value": "Honduras",
      "label": "+504",
      "code": "HN",
      "icon": `${HN}`
  },
  {
      "id": "205",
      "value": "Hong Kong",
      "label": "+852",
      "code": "HK",
      "icon": `${HK}`
  },
  {
      "id": "85",
      "value": "Hungary",
      "label": "+36",
      "code": "HU",
      "icon": `${HU}`
  },
  {
      "id": "86",
      "value": "Iceland",
      "label": "+354",
      "code": "IS",
      "icon": `${IS}`
  },
  {
      "id": "87",
      "value": "India",
      "label": "+91",
      "code": "IN",
      "icon": `${IN}`
  },
  {
      "id": "88",
      "value": "Indonesia",
      "label": "+62",
      "code": "ID",
      "icon": `${ID}`
  },
  {
      "id": "206",
      "value": "Iran, Islamic Republic of",
      "label": "+98",
      "code": "IR",
      "icon": `${IR}`
  },
  {
      "id": "89",
      "value": "Iraq",
      "label": "+964",
      "code": "IQ",
      "icon": `${IQ}`
  },
  {
      "id": "90",
      "value": "Ireland",
      "label": "+353",
      "code": "IE",
      "icon": `${IE}`
  },
  {
      "id": "207",
      "value": "Isle of Man",
      "label": "+44",
      "code": "IM",
      "icon": `${IM}`
  },
  {
      "id": "91",
      "value": "Israel",
      "label": "+972",
      "code": "IL",
      "icon": `${IL}`
  },
  {
      "id": "92",
      "value": "Italy",
      "label": "+39",
      "code": "IT",
      "icon": `${IT}`
  },
  {
      "id": "93",
      "value": "Jamaica",
      "label": "+1876",
      "code": "JM",
      "icon": `${JM}`
  },
  {
      "id": "94",
      "value": "Japan",
      "label": "+81",
      "code": "JP",
      "icon": `${JP}`
  },
  {
      "id": "208",
      "value": "Jersey",
      "label": "+44",
      "code": "JE",
      "icon": `${JE}`
  },
  {
      "id": "95",
      "value": "Jordan",
      "label": "+962",
      "code": "JO",
      "icon": `${JO}`
  },
  {
      "id": "96",
      "value": "Kazakhstan",
      "label": "++7 7",
      "code": "KZ",
      "icon": `${KZ}`
  },
  {
      "id": "97",
      "value": "Kenya",
      "label": "+254",
      "code": "KE",
      "icon": `${KE}`
  },
  {
      "id": "98",
      "value": "Kiribati",
      "label": "+686",
      "code": "KI",
      "icon": `${KI}`
  },
  {
      "id": "209",
      "value": "Korea, Democratic People's Republic of",
      "label": "+850",
      "code": "KP",
      "icon": `${KP}`
  },
  {
      "id": "210",
      "value": "Korea, Republic of",
      "label": "+82",
      "code": "KR",
      "icon": `${KR}`
  },
  {
      "id": "99",
      "value": "Kuwait",
      "label": "+965",
      "code": "KW",
      "icon": `${KW}`
  },
  {
      "id": "100",
      "value": "Kyrgyzstan",
      "label": "+996",
      "code": "KG",
      "icon": `${KG}`
  },
  {
      "id": "211",
      "value": "Lao People's Democratic Republic",
      "label": "+856",
      "code": "LA",
      "icon": `${LA}`
  },
  {
      "id": "101",
      "value": "Latvia",
      "label": "+371",
      "code": "LV",
      "icon": `${LV}`
  },
  {
      "id": "102",
      "value": "Lebanon",
      "label": "+961",
      "code": "LB",
      "icon": `${LB}`
  },
  {
      "id": "103",
      "value": "Lesotho",
      "label": "+266",
      "code": "LS",
      "icon": `${LS}`
  },
  {
      "id": "104",
      "value": "Liberia",
      "label": "+231",
      "code": "LR",
      "icon": `${LR}`
  },
  {
      "id": "212",
      "value": "Libyan Arab Jamahiriya",
      "label": "+218",
      "code": "LY",
      "icon": `${LY}`
  },
  {
      "id": "105",
      "value": "Liechtenstein",
      "label": "+423",
      "code": "LI",
      "icon": `${LI}`
  },
  {
      "id": "106",
      "value": "Lithuania",
      "label": "+370",
      "code": "LT",
      "icon": `${LT}`
  },
  {
      "id": "107",
      "value": "Luxembourg",
      "label": "+352",
      "code": "LU",
      "icon": `${LU}`
  },
  {
      "id": "213",
      "value": "Macao",
      "label": "+853",
      "code": "MO",
      "icon": `${MO}`
  },
  {
      "id": "214",
      "value": "Macedonia, The Former Yugoslav Republic of",
      "label": "+389",
      "code": "MK",
      "icon": `${MK}`
  },
  {
      "id": "108",
      "value": "Madagascar",
      "label": "+261",
      "code": "MG",
      "icon": `${MG}`
  },
  {
      "id": "109",
      "value": "Malawi",
      "label": "+265",
      "code": "MW",
      "icon": `${MW}`
  },
  {
      "id": "110",
      "value": "Malaysia",
      "label": "+60",
      "code": "MY",
      "icon": `${MY}`
  },
  {
      "id": "111",
      "value": "Maldives",
      "label": "+960",
      "code": "MV",
      "icon": `${MV}`
  },
  {
      "id": "112",
      "value": "Mali",
      "label": "+223",
      "code": "ML",
      "icon": `${ML}`
  },
  {
      "id": "113",
      "value": "Malta",
      "label": "+356",
      "code": "MT",
      "icon": `${MT}`
  },
  {
      "id": "114",
      "value": "Marshall Islands",
      "label": "+692",
      "code": "MH",
      "icon": `${MH}`
  },
  {
      "id": "115",
      "value": "Martinique",
      "label": "+596",
      "code": "MQ",
      "icon": `${MQ}`
  },
  {
      "id": "116",
      "value": "Mauritania",
      "label": "+222",
      "code": "MR",
      "icon": `${MR}`
  },
  {
      "id": "117",
      "value": "Mauritius",
      "label": "+230",
      "code": "MU",
      "icon": `${MU}`
  },
  {
      "id": "118",
      "value": "Mayotte",
      "label": "+262",
      "code": "YT",
      "icon": `${YT}`
  },
  {
      "id": "119",
      "value": "Mexico",
      "label": "+52",
      "code": "MX",
      "icon": `${MX}`
  },
  {
      "id": "215",
      "value": "Micronesia, Federated States of",
      "label": "+691",
      "code": "FM",
      "icon": `${FM}`
  },
  {
      "id": "216",
      "value": "Moldova, Republic of",
      "label": "+373",
      "code": "MD",
      "icon": `${MD}`
  },
  {
      "id": "120",
      "value": "Monaco",
      "label": "+377",
      "code": "MC",
      "icon": `${MC}`
  },
  {
      "id": "121",
      "value": "Mongolia",
      "label": "+976",
      "code": "MN",
      "icon": `${MN}`
  },
  {
      "id": "122",
      "value": "Montenegro",
      "label": "+382",
      "code": "ME",
      "icon": `${ME}`
  },
  {
      "id": "123",
      "value": "Montserrat",
      "label": "+1664",
      "code": "MS",
      "icon": `${MS}`
  },
  {
      "id": "124",
      "value": "Morocco",
      "label": "+212",
      "code": "MA",
      "icon": `${MA}`
  },
  {
      "id": "217",
      "value": "Mozambique",
      "label": "+258",
      "code": "MZ",
      "icon": `${MZ}`
  },
  {
      "id": "125",
      "value": "Myanmar",
      "label": "+95",
      "code": "MM",
      "icon": `${MM}`
  },
  {
      "id": "126",
      "value": "Namibia",
      "label": "+264",
      "code": "NA",
      "icon": `${NA}`
  },
  {
      "id": "127",
      "value": "Nauru",
      "label": "+674",
      "code": "NR",
      "icon": `${NR}`
  },
  {
      "id": "128",
      "value": "Nepal",
      "label": "+977",
      "code": "NP",
      "icon": `${NP}`
  },
  {
      "id": "129",
      "value": "Netherlands",
      "label": "+31",
      "code": "NL",
      "icon": `${NL}`
  },
  {
      "id": "130",
      "value": "Netherlands Antilles",
      "label": "+599",
      "code": "AN",
      "icon": `${AN}`
  },
  {
      "id": "131",
      "value": "New Caledonia",
      "label": "+687",
      "code": "NC",
      "icon": `${NC}`
  },
  {
      "id": "132",
      "value": "New Zealand",
      "label": "+64",
      "code": "NZ",
      "icon": `${NZ}`
  },
  {
      "id": "133",
      "value": "Nicaragua",
      "label": "+505",
      "code": "NI",
      "icon": `${NI}`
  },
  {
      "id": "134",
      "value": "Niger",
      "label": "+227",
      "code": "NE",
      "icon": `${NE}`
  },
  {
      "id": "135",
      "value": "Nigeria",
      "label": "+234",
      "code": "NG",
      "icon": `${NG}`
  },
  {
      "id": "136",
      "value": "Niue",
      "label": "+683",
      "code": "NU",
      "icon": `${NU}`
  },
  {
      "id": "137",
      "value": "Norfolk Island",
      "label": "+672",
      "code": "NF",
      "icon": `${NF}`
  },
  {
      "id": "138",
      "value": "Northern Mariana Islands",
      "label": "+1670",
      "code": "MP",
      "icon": `${MP}`
  },
  {
      "id": "139",
      "value": "Norway",
      "label": "+47",
      "code": "NO",
      "icon": `${NO}`
  },
  {
      "id": "140",
      "value": "Oman",
      "label": "+968",
      "code": "OM",
      "icon": `${OM}`
  },
  {
      "id": "141",
      "value": "Pakistan",
      "label": "+92",
      "code": "PK",
      "icon": `${PK}`
  },
  {
      "id": "142",
      "value": "Palau",
      "label": "+680",
      "code": "PW",
      "icon": `${PW}`
  },
  {
      "id": "218",
      "value": "Palestinian Territory, Occupied",
      "label": "+970",
      "code": "PS",
      "icon": `${PS}`
  },
  {
      "id": "143",
      "value": "Panama",
      "label": "+507",
      "code": "PA",
      "icon": `${PA}`
  },
  {
      "id": "144",
      "value": "Papua New Guinea",
      "label": "+675",
      "code": "PG",
      "icon": `${PG}`
  },
  {
      "id": "145",
      "value": "Paraguay",
      "label": "+595",
      "code": "PY",
      "icon": `${PY}`
  },
  {
      "id": "146",
      "value": "Peru",
      "label": "+51",
      "code": "PE",
      "icon": `${PE}`
  },
  {
      "id": "147",
      "value": "Philippines",
      "label": "+63",
      "code": "PH",
      "icon": `${PH}`
  },
  {
      "id": "219",
      "value": "Pitcairn",
      "label": "+872",
      "code": "PN",
      "icon": `${PN}`
  },
  {
      "id": "148",
      "value": "Poland",
      "label": "+48",
      "code": "PL",
      "icon": `${PL}`
  },
  {
      "id": "149",
      "value": "Portugal",
      "label": "+351",
      "code": "PT",
      "icon": `${PT}`
  },
  {
      "id": "150",
      "value": "Puerto Rico",
      "label": "+1939",
      "code": "PR",
      "icon": `${PR}`
  },
  {
      "id": "151",
      "value": "Qatar",
      "label": "+974",
      "code": "QA",
      "icon": `${QA}`
  },
  {
      "id": "152",
      "value": "Romania",
      "label": "+40",
      "code": "RO",
      "icon": `${RO}`
  },
  {
      "id": "221",
      "value": "Russia",
      "label": "+7",
      "code": "RU",
      "icon": `${RU}`
  },
  {
      "id": "153",
      "value": "Rwanda",
      "label": "+250",
      "code": "RW",
      "icon": `${RW}`
  },
  {
      "id": "220",
      "value": "Réunion",
      "label": "+262",
      "code": "RE",
      "icon": `${RE}`
  },
  {
      "id": "222",
      "value": "Saint Barthélemy",
      "label": "+590",
      "code": "BL",
      "icon": `${BL}`
  },
  {
      "id": "223",
      "value": "Saint Helena, Ascension and Tristan Da Cunha",
      "label": "+290",
      "code": "SH",
      "icon": `${SH}`
  },
  {
      "id": "224",
      "value": "Saint Kitts and Nevis",
      "label": "+1869",
      "code": "KN",
      "icon": `${KN}`
  },
  {
      "id": "225",
      "value": "Saint Lucia",
      "label": "+1758",
      "code": "LC",
      "icon": `${LC}`
  },
  {
      "id": "226",
      "value": "Saint Martin",
      "label": "+590",
      "code": "MF",
      "icon": `${MF}`
  },
  {
      "id": "227",
      "value": "Saint Pierre and Miquelon",
      "label": "+508",
      "code": "PM",
      "icon": `${PM}`
  },
  {
      "id": "228",
      "value": "Saint Vincent and the Grenadines",
      "label": "+1784",
      "code": "VC",
      "icon": `${VC}`
  },
  {
      "id": "154",
      "value": "Samoa",
      "label": "+685",
      "code": "WS",
      "icon": `${WS}`
  },
  {
      "id": "155",
      "value": "San Marino",
      "label": "+378",
      "code": "SM",
      "icon": `${SM}`
  },
  {
      "id": "229",
      "value": "Sao Tome and Principe",
      "label": "+239",
      "code": "ST",
      "icon": `${ST}`
  },
  {
      "id": "156",
      "value": "Saudi Arabia",
      "label": "+966",
      "code": "SA",
      "icon": `${SA}`
  },
  {
      "id": "157",
      "value": "Senegal",
      "label": "+221",
      "code": "SN",
      "icon": `${SN}`
  },
  {
      "id": "158",
      "value": "Serbia",
      "label": "+381",
      "code": "RS",
      "icon": `${RS}`
  },
  {
      "id": "159",
      "value": "Seychelles",
      "label": "+248",
      "code": "SC",
      "icon": `${SC}`
  },
  {
      "id": "160",
      "value": "Sierra Leone",
      "label": "+232",
      "code": "SL",
      "icon": `${SL}`
  },
  {
      "id": "161",
      "value": "Singapore",
      "label": "+65",
      "code": "SG",
      "icon": `${SG}`
  },
  {
      "id": "162",
      "value": "Slovakia",
      "label": "+421",
      "code": "SK",
      "icon": `${SK}`
  },
  {
      "id": "163",
      "value": "Slovenia",
      "label": "+386",
      "code": "SI",
      "icon": `${SI}`
  },
  {
      "id": "164",
      "value": "Solomon Islands",
      "label": "+677",
      "code": "SB",
      "icon": `${SB}`
  },
  {
      "id": "230",
      "value": "Somalia",
      "label": "+252",
      "code": "SO",
      "icon": `${SO}`
  },
  {
      "id": "165",
      "value": "South Africa",
      "label": "+27",
      "code": "ZA",
      "icon": `${ZA}`
  },
  {
      "id": "166",
      "value": "South Georgia and the South Sandwich Islands",
      "label": "+500",
      "code": "GS",
      "icon": `${GS}`
  },
  {
      "id": "167",
      "value": "Spain",
      "label": "+34",
      "code": "ES",
      "icon": `${ES}`
  },
  {
      "id": "168",
      "value": "Sri Lanka",
      "label": "+94",
      "code": "LK",
      "icon": `${LK}`
  },
  {
      "id": "169",
      "value": "Sudan",
      "label": "+249",
      "code": "SD",
      "icon": `${SD}`
  },
  {
      "id": "170",
      "value": "Suriname",
      "label": "+597",
      "code": "SR",
      "icon": `${SR}`
  },
  {
      "id": "231",
      "value": "Svalbard and Jan Mayen",
      "label": "+47",
      "code": "SJ",
      "icon": `${SJ}`
  },
  {
      "id": "171",
      "value": "Swaziland",
      "label": "+268",
      "code": "SZ",
      "icon": `${SZ}`
  },
  {
      "id": "172",
      "value": "Sweden",
      "label": "+46",
      "code": "SE",
      "icon": `${SE}`
  },
  {
      "id": "173",
      "value": "Switzerland",
      "label": "+41",
      "code": "CH",
      "icon": `${CH}`
  },
  {
      "id": "232",
      "value": "Syrian Arab Republic",
      "label": "+963",
      "code": "SY",
      "icon": `${SY}`
  },
  {
      "id": "233",
      "value": "Taiwan, Province of China",
      "label": "+886",
      "code": "TW",
      "icon": `${TW}`
  },
  {
      "id": "174",
      "value": "Tajikistan",
      "label": "+992",
      "code": "TJ",
      "icon": `${TJ}`
  },
  {
      "id": "234",
      "value": "Tanzania, United Republic of",
      "label": "+255",
      "code": "TZ",
      "icon": `${TZ}`
  },
  {
      "id": "175",
      "value": "Thailand",
      "label": "+66",
      "code": "TH",
      "icon": `${TH}`
  },
  {
      "id": "235",
      "value": "Timor-Leste",
      "label": "+670",
      "code": "TL",
      "icon": `${TL}`
  },
  {
      "id": "176",
      "value": "Togo",
      "label": "+228",
      "code": "TG",
      "icon": `${TG}`
  },
  {
      "id": "177",
      "value": "Tokelau",
      "label": "+690",
      "code": "TK",
      "icon": `${TK}`
  },
  {
      "id": "178",
      "value": "Tonga",
      "label": "+676",
      "code": "TO",
      "icon": `${TO}`
  },
  {
      "id": "179",
      "value": "Trinidad and Tobago",
      "label": "+1868",
      "code": "TT",
      "icon": `${TT}`
  },
  {
      "id": "180",
      "value": "Tunisia",
      "label": "+216",
      "code": "TN",
      "icon": `${TN}`
  },
  {
      "id": "181",
      "value": "Turkey",
      "label": "+90",
      "code": "TR",
      "icon": `${TR}`
  },
  {
      "id": "182",
      "value": "Turkmenistan",
      "label": "+993",
      "code": "TM",
      "icon": `${TM}`
  },
  {
      "id": "183",
      "value": "Turks and Caicos Islands",
      "label": "+1649",
      "code": "TC",
      "icon": `${TC}`
  },
  {
      "id": "184",
      "value": "Tuvalu",
      "label": "+688",
      "code": "TV",
      "icon": `${TV}`
  },
  {
      "id": "185",
      "value": "Uganda",
      "label": "+256",
      "code": "UG",
      "icon": `${UG}`
  },
  {
      "id": "186",
      "value": "Ukraine",
      "label": "+380",
      "code": "UA",
      "icon": `${UA}`
  },
  {
      "id": "187",
      "value": "United Arab Emirates",
      "label": "+971",
      "code": "AE",
      "icon": `${AE}`
  },
  {
      "id": "188",
      "value": "United Kingdom",
      "label": "+44",
      "code": "GB",
      "icon": `${GB}`
  },
  {
      "id": "189",
      "value": "United States",
      "label": "+1",
      "code": "US",
      "icon": `${US}`
  },
  {
      "id": "190",
      "value": "Uruguay",
      "label": "+598",
      "code": "UY",
      "icon": `${UY}`
  },
  {
      "id": "191",
      "value": "Uzbekistan",
      "label": "+998",
      "code": "UZ",
      "icon": `${UZ}`
  },
  {
      "id": "192",
      "value": "Vanuatu",
      "label": "+678",
      "code": "VU",
      "icon": `${VU}`
  },
  {
      "id": "236",
      "value": "Venezuela, Bolivarian Republic of",
      "label": "+58",
      "code": "VE",
      "icon": `${VE}`
  },
  {
      "id": "237",
      "value": "VietNam",
      "label": "+84",
      "code": "VN",
      "icon": `${VN}`
  },
  {
      "id": "238",
      "value": "Virgin Islands, British",
      "label": "+1284",
      "code": "VG",
      "icon": `${VG}`
  },
  {
      "id": "239",
      "value": "Virgin Islands, U.S.",
      "label": "+1340",
      "code": "VI",
      "icon": `${VI}`
  },
  {
      "id": "193",
      "value": "Wallis and Futuna",
      "label": "+681",
      "code": "WF",
      "icon": `${WF}`
  },
  {
      "id": "194",
      "value": "Yemen",
      "label": "+967",
      "code": "YE",
      "icon": `${YE}`
  },
  {
      "id": "195",
      "value": "Zambia",
      "label": "+260",
      "code": "ZM",
      "icon": `${ZM}`
  },
  {
      "id": "196",
      "value": "Zimbabwe",
      "label": "+263",
      "code": "ZW",
      "icon": `${ZW}`
  }
]