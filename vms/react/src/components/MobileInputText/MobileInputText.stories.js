import React from "react";

import { MobileInputText } from "./MobileInputText";
import { countriesAllData } from "./CountryData";
import {Button} from '../Button/Button'
export default {
  title: "VMS_REACT/MobileInputText",
  component: MobileInputText,
  argTypes: {
   
  },
};

const onMobileNumberChange = (value) => {
  console.log('Mobile Number Changed, new value : ', value);
};

const onCountryChange = (value) => {
  console.log('Country name/ISD code Changed, new value : ', value);
};

export const Default = () => {
  const [isValid, setIsValid] = React.useState();
  const [value, setValue] = React.useState();
  return (
      <div>
        <h4>Default</h4>
          <MobileInputText
           listItems={"All"}
            id="Mobile Number 1"
            type="text"
            countryCodePlaceholder={""}
            customCountryList={countriesAllData}
            onValidationCallback={(valid) => {
              console.log('onValidationCallback : ', valid);
              setIsValid(valid);
            }}
            onMobileNumberChange={(value) => {
              console.log('Mobile Number Changed, new value : ', value);
              setValue(value);
            }}
            onCountryChange={(value) => {
              console.log('Country name/ISD code Changed, new value : ', value);
            }}
            mobileNumber={value}
            isdMaxLength={4}
            onMobileNumberFocus={(e) => console.log('onMobileNumberFocus')}
          />
          <p>
            <b>Valid state return by onValidationCallback : {isValid?.toString()}</b>
            <button  onClick={() => setValue("9082977824")}>click</button>
          </p>
      </div>
  );
};

export const Disabled = () => {
  const [isValid, setIsValid] = React.useState();
  return (
      <div>
        <h4>Default</h4>
          <MobileInputText
            id="Mobile Number 1"
            type="text"
            onValidationCallback={(valid) => {
              console.log('onValidationCallback : ', valid);
              setIsValid(valid);
            }}
            disabled={true}
          />
          <p>
            <b>Valid state return by onValidationCallback : {isValid?.toString()}</b>
          </p>
      </div>
  );
};

export const customeData = () => {
  const [isValid, setIsValid] = React.useState();
  const [isMobile, setIsMobile] = React.useState(false);
  const [additionalClass, setAdditionalClass] = React.useState([]);
  return (
      <div>
        <h4>Default</h4>
          <MobileInputText
            id="Mobile Number 1"
            additionalClassName={additionalClass}
            type="text"
            customCountryList={countriesAllData}
            // selectedIndex={1}
            mobileNumber={'9090909090'}
            placeHolderText="Enter Phone Number"
            messageMode="success"
            msgText="Enter your phone number here"
            isMobile={isMobile}
            onValidationCallback={(valid) => {
              console.log('onValidationCallback : ', valid);
              setIsValid(valid);
            }}
            selectedValueFilterBy="label"
            selectedValue={'+355'}
          />
          <p>
            <b>Valid state return by onValidationCallback : {isValid?.toString()}</b>
          </p>
      </div>
  );
};

export const customErrorMessage = () => (
    <div>
      <h4>Default</h4> 
        <MobileInputText
          id="Mobile Number 1"
          type="text"
          arrData={countriesAllData}
          msgText="Mobile no is incorrect"
          isdCodeErrorMsgText="Wrong ISD code"
        />
    </div>
);

export const OptionsWithIconCodeOnly = () => (
    <div>
      <h4>Default</h4>
        <MobileInputText
          id="Mobile Number 1"
          arrData={countriesAllData}
          type="text"
          listItems={['code']}
          detachableFlyout={false}
        />
    </div>
);

export const OptionsWithIconCountryOnly = () => (
    <div>
      <h4>Default</h4>
        <MobileInputText id="Mobile Number 1" arrData={countriesAllData} type="text" listItems={['country', 'icon']} />
    </div>
);

export const customStyle = () => (
    <div>
      <h4>Default</h4>
        <MobileInputText
          id="Mobile Number 1"
          type="text"
          arrData={countriesAllData}
          mobileNumber="9999999999"
          countryInputStyle={{
            backgroundColor: 'burlywood',
            color: '#896754',
            borderRadius: 0,
          }}
          selectedIndex={1}
          countryIconStyle={{ border: '2px solid #ffffff', top: '7px' }}
          mobileInputTextStyle={{ border: '2px solid darkgray', color: '#896754' }}
        />
    </div>
);

export const demonstratePropsUpdateWeb = () => {
  const [isValid, setIsValid] = React.useState();
  const [isdCode, setIsdCode] = React.useState(4);
  const [mobileNo, setMobileNo] = React.useState('');
  return (
      <div>
        <div style={{ display: 'flex' }}>
          <Button
            buttonType="primary"
            onClick={() => {
              setIsdCode(4);
              setMobileNo('523412361');
            }}
          >
            Change ISD: +966 & Mobile: 523412361
          </Button>
          <Button
            buttonType="primary"
            onClick={() => {
              setIsdCode(4);
            }}
          >
            Change ISD: +966
          </Button>
          <Button
            buttonType="primary"
            onClick={() => {
              setMobileNo('523412361');
            }}
          >
            Change Mobile: 523412361
          </Button>
        </div>
        <div style={{ display: 'flex' }}>
          <Button
            buttonType="primary"
            onClick={() => {
              setIsdCode(0);
              setMobileNo('9552726186');
            }}
          >
            Change ISD: +91 & Mobile: 9552726186
          </Button>
          <Button
            buttonType="primary"
            onClick={() => {
              setIsdCode(0);
            }}
          >
            Change ISD: +91
          </Button>
          <Button
            buttonType="primary"
            onClick={() => {
              setMobileNo('9552726186');
            }}
          >
            Change Mobile: 9552726186
          </Button>
        </div>
        <h4>Demonstrate props updation</h4>
          <MobileInputText
            id="Mobile Number 1"
            type="text"
            arrData={countriesAllData}
            selectedIndex={isdCode}
            mobileNumber={mobileNo}
            onValidationCallback={(valid) => {
              console.log('onValidationCallback : ', valid);
              setIsValid(valid);
            }}
            onCountryChange={(country) => {
              const index = countriesAllData.findIndex((data) => {
                return data.label === country;
              });
              if (index !== -1) {
                setIsdCode(index);
              }
            }}
          />
          <p>
            <b>Valid state return by onValidationCallback : {isValid?.toString()}</b>
          </p>
      </div>
  );
};

export const demonstrateChangeEventsWeb = () => (
    <div>
      <h4>Demonstrate Change Events</h4>
        <MobileInputText
          onMobileNumberChange={onMobileNumberChange}
          onCountryChange={onCountryChange}
          arrData={countriesAllData}
        />
    </div>
);

export const mobWebIsdCodePicker = () => {
  const [isValid, setIsValid] = React.useState();
  return (
      <div>
        <h4>Default</h4>
          <MobileInputText
            id="Mobile Number 1"
            type="text"
            arrData={countriesAllData}
            isMobile={true}
            onValidationCallback={(valid) => {
              console.log('onValidationCallback : ', valid);
              setIsValid(valid);
            }}
          />
          <p>
            <b>Valid state return by onValidationCallback : {isValid?.toString()}</b>
          </p>
      </div>
  );
};

mobWebIsdCodePicker.story = {
  name: 'Mobweb - Default Style',
  parameters: { viewport: { defaultViewport: 'iphonex' } },
};

export const moWebDisabled = () => {
  const [isValid, setIsValid] = React.useState();
  return (
      <div>
        <h4>Default</h4>
          <MobileInputText
            id="Mobile Number 1"
            type="text"
            isMobile={true}
            arrData={countriesAllData}
            onValidationCallback={(valid) => {
              console.log('onValidationCallback : ', valid);
              setIsValid(valid);
            }}
            disabled={true}
          />
          <p>
            <b>Valid state return by onValidationCallback : {isValid?.toString()}</b>
          </p>
      </div>
  );
};

moWebDisabled.story = {
  name: 'Mobweb - Disabled',
  parameters: { viewport: { defaultViewport: 'iphonex' } },
};

export const mobWebCustomeData = () => {
  const [isValid, setIsValid] = React.useState();
  return (
      <div>
        <h4>Default</h4>
          <MobileInputText
            id="Mobile Number 1"
            type="text"
            arrData={countriesAllData}
            selectedIndex={155}
            mobileNumber={'************'}
            placeHolderText="Enter Phone Number"
            isMobile={true}
            onValidationCallback={(valid) => {
              console.log('onValidationCallback : ', valid);
              setIsValid(valid);
            }}
          />
          <p>
            <b>Valid state return by onValidationCallback : {isValid?.toString()}</b>
          </p>
      </div>
  );
};
mobWebCustomeData.story = {
  name: 'Mobweb - Custome data',
  parameters: { viewport: { defaultViewport: 'iphonex' } },
};

export const mobWebCustomErrorMessage = () => (
    <div>
      <h4>Default</h4>
        <MobileInputText
          id="Mobile Number 1"
          type="text"
          arrData={countriesAllData}
          msgText="Mobile no is incorrect"
          isMobile={true}
        />
    </div>
);
mobWebCustomErrorMessage.story = {
  name: 'Mobweb - Custom Error message',
  parameters: { viewport: { defaultViewport: 'iphonex' } },
};

export const mobWebOptionsWithIconCodeOnly = () => (
    <div>
      <h4>Default</h4>
        <MobileInputText
          id="Mobile Number 1"
          type="text"
          listItems={['code', 'icon']}
          arrData={countriesAllData}
          isMobile={true}
        />
    </div>
);
mobWebOptionsWithIconCodeOnly.story = {
  name: 'Mobweb - Options with Icon and code',
  parameters: { viewport: { defaultViewport: 'iphonex' } },
};

export const mobWebOptionsWithIconCountryOnly = () => (
    <div>
      <h4>Default</h4>
        <MobileInputText
          id="Mobile Number 1"
          type="text"
          listItems={['country', 'icon']}
          arrData={countriesAllData}
          isMobile={true}
        />
    </div>
);
mobWebOptionsWithIconCountryOnly.story = {
  name: 'Mobweb - Options with Icon and country',
  parameters: { viewport: { defaultViewport: 'iphonex' } },
};

export const mobWebCustomStyleMobWeb = () => (
    <div>
      <h4>Default</h4>
        <MobileInputText
          id="Mobile Number 1"
          type="text"
          mobileNumber="9999999999"
          arrData={countriesAllData}
          countryInputStyle={{
            backgroundColor: 'burlywood',
            color: '#896754',
            borderBottom: '2px solid darkgray',
            borderRadius: '3px',
            height: '26px',
          }}
          countryIconStyle={{ borderRadius: '50%', left: '3px', top: '0px' }}
          popupCountryInputStyle={{
            backgroundColor: '#896754',
            color: '#fff',
          }}
          mobileInputTextStyle={{
            borderBottom: '2px solid darkgray',
            color: '#896754',
            height: '26px',
          }}
          isMobile={true}
        />
    </div>
);
mobWebCustomStyleMobWeb.story = {
  name: 'Mobweb - Custom Style',
  parameters: { viewport: { defaultViewport: 'iphonex' } },
};

export const demonstrateChangeEventsMoWeb = () => (
    <div>
      <h4>Demonstrate Change Events</h4>
        <MobileInputText
          isMobile={true}
          arrData={countriesAllData}
          onMobileNumberChange={onMobileNumberChange}
          onCountryChange={onCountryChange}
        />
    </div>
);
demonstrateChangeEventsMoWeb.story = {
  name: 'Demonstrate Change Events',
  parameters: { viewport: { defaultViewport: 'iphonex' } },
};

export const fliterByCounterName = () => {
  const [isValid, setIsValid] = React.useState();
  return (
      <div>
        <h4>Default</h4>
          <MobileInputText
            id="Mobile Number 1"
            type="text"
            arrData={countriesAllData}
            onValidationCallback={(valid) => {
              console.log('onValidationCallback : ', valid);
              setIsValid(valid);
            }}
            filterBy="countryName"
          />
          <p>
            <b>Valid state return by onValidationCallback : {isValid?.toString()}</b>
          </p>
      </div>
  );
};
