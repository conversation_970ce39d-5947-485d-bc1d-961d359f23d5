import React, { useState, memo, useCallback, useMemo } from "react";
import PropTypes from "prop-types";
import { useClassName } from "../../hooks/useClassName";
import mapToggle from "../../assets/images/ToggleView/mapToggle.svg";
import listToggle from "../../assets/images/ToggleView/listToggle.svg";
import tileToggle from "../../assets/images/ToggleView/tileToggle.svg";
import mapToggleSelected from "../../assets/images/ToggleView/mapToggleSelected.svg";
import listToggleSelected from "../../assets/images/ToggleView/listToggleSelected.svg";
import tileToggleSelected from "../../assets/images/ToggleView/tileToggleSelected.svg";
import { MainContainer, ItemContainer } from "./ToggleView.styled";
import { ThemeWrapper } from "../Theme/ThemeContext";

// Utility function (placed before main component)
const isNonNull = (prop) => prop !== null && prop !== undefined;

/**
 * Toggle View UI component for user interaction
 */
const ToggleView = memo((props) => {
  // 1. PROP DESTRUCTURING & VALIDATION
  const {
    viewType = "icon",
    values = [
      {
        icon: tileToggle,
        selectedIcon: tileToggleSelected,
      },
      {
        icon: listToggle,
        selectedIcon: listToggleSelected,
      },
      {
        icon: mapToggle,
        selectedIcon: mapToggleSelected,
      }
    ],
    initialIndex = 0,
    direction = "row",
    dividerWidth,
    dividerColor,
    customViewStyle,
    textStyle,
    selectedTextStyle,
    itemWidth,
    itemHeight,
    iconStyle,
    selectedIconStyle,
    selectedIconClassName,
    selectedBackgroundColor,
    onPress,
    isMobile,
    isMobileView,
    ...otherProps
  } = props;

  // 2. STATE MANAGEMENT
  const [selectedIndex, setSelectedIndex] = useState(initialIndex);

  // 3. PERFORMANCE OPTIMIZATIONS
  const computedSelectedIconClassName = useMemo(() => 
    useClassName(props, selectedIconClassName), 
    [props, selectedIconClassName]
  );

  const isTextView = useMemo(() => viewType === "text", [viewType]);

  // 4. EVENT HANDLING with useCallback
  const handleItemPress = useCallback((index) => {
    setSelectedIndex(index);
    if (isNonNull(onPress)) {
      onPress(index);
    }
  }, [onPress]);

  const handleKeyDown = useCallback((event, index) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      handleItemPress(index);
    }
  }, [handleItemPress]);

  // 5. MEMOIZED COMPONENTS
  const TextToggleItem = useMemo(() => memo(({ value, index, isSelected }) => (
    <ItemContainer
      key={index}
      className="vms_toggleview_textview"
      selectedBackgroundColor={selectedBackgroundColor}
      style={isSelected ? selectedTextStyle : textStyle}
      onClick={() => handleItemPress(index)}
      onKeyDown={(e) => handleKeyDown(e, index)}
      isSelected={isSelected}
      lastElement={index === values.length - 1}
      dividerWidth={dividerWidth}
      dividerColor={dividerColor}
      itemWidth={itemWidth}
      itemHeight={itemHeight}
      isMobile={isMobile}
      tabIndex={0}
      role="button"
      aria-pressed={isSelected}
      aria-label={`Toggle option ${value.title}`}
    >
      {value.title}
    </ItemContainer>
  )), [
    selectedBackgroundColor,
    selectedTextStyle,
    textStyle,
    handleItemPress,
    handleKeyDown,
    values.length,
    dividerWidth,
    dividerColor,
    itemWidth,
    itemHeight,
    isMobile
  ]);

  const IconToggleItem = useMemo(() => memo(({ value, index, isSelected }) => (
    <ItemContainer
      key={index}
      selectedBackgroundColor={selectedBackgroundColor}
      className={`vms_toggleview_iconview ${isSelected ? computedSelectedIconClassName : ''}`}
      onClick={() => handleItemPress(index)}
      onKeyDown={(e) => handleKeyDown(e, index)}
      isSelected={isSelected}
      lastElement={index === values.length - 1}
      dividerWidth={dividerWidth}
      dividerColor={dividerColor}
      itemWidth={itemWidth}
      itemHeight={itemHeight}
      isMobile={isMobile}
      tabIndex={0}
      role="button"
      aria-pressed={isSelected}
      aria-label={`Toggle option ${index + 1}`}
    >
      <img
        style={isSelected ? selectedIconStyle : iconStyle}
        src={isSelected ? value.selectedIcon : value.icon}
        alt={`Toggle option ${index + 1}`}
      />
    </ItemContainer>
  )), [
    selectedBackgroundColor,
    computedSelectedIconClassName,
    handleItemPress,
    handleKeyDown,
    values.length,
    dividerWidth,
    dividerColor,
    itemWidth,
    itemHeight,
    isMobile,
    selectedIconStyle,
    iconStyle
  ]);

  // 6. CONDITIONAL RENDERING (memoized)
  const toggleItems = useMemo(() => {
    return values.map((value, index) => {
      const isSelected = selectedIndex === index;
      
      if (isTextView) {
        return (
          <TextToggleItem
            key={index}
            value={value}
            index={index}
            isSelected={isSelected}
          />
        );
      } else {
        return (
          <IconToggleItem
            key={index}
            value={value}
            index={index}
            isSelected={isSelected}
          />
        );
      }
    });
  }, [values, selectedIndex, isTextView, TextToggleItem, IconToggleItem]);

  // 7. EFFECTS
  React.useEffect(() => {
    setSelectedIndex(initialIndex);
  }, [initialIndex]);

  // 8. ERROR HANDLING
  React.useEffect(() => {
    if (!values || values.length === 0) {
      console.warn('ToggleView: values array is required and should not be empty');
    }
    if (!isNonNull(onPress)) {
      console.warn('ToggleView: onPress callback is required');
    }
    if (initialIndex >= values.length) {
      console.warn(`ToggleView: initialIndex (${initialIndex}) should be less than values length (${values.length})`);
    }
  }, [values, onPress, initialIndex]);

  return (
    <ThemeWrapper isMobile={isMobile || isMobileView || false}>
      <MainContainer 
        className="vms_toggleview_container" 
        isMobile={isMobile} 
        style={customViewStyle} 
        direction={direction}
        role="tablist"
        aria-orientation={direction === "row" ? "horizontal" : "vertical"}
        {...otherProps}
      >
        {toggleItems}
      </MainContainer>
    </ThemeWrapper>
  );
});

ToggleView.displayName = 'ToggleView';

ToggleView.defaultProps = {
    viewType: "icon",
    values: [{
        "icon": tileToggle,
        "selectedIcon": tileToggleSelected,
    },
    {
        "icon": listToggle,
        "selectedIcon": listToggleSelected,
    },
    {
        "icon": mapToggle,
        "selectedIcon": mapToggleSelected,
    }],
    initialIndex: 0,
    direction: "row",
};
ToggleView.propTypes = {
    /**
     * Type of view : 'icon' for using images and 'text' from using text titles Default will be 'icon'
     */
    viewType: PropTypes.oneOf(["icon", "text"]),
    /**
     * Defines direction of the view either Horizontal or Vertical 'row' is for horizontal
     *  and 'column' for vertical. Default will be 'row'
     */
    direction: PropTypes.oneOf(["row", "column"]),
    /**
     * Width of divider in between each item
     */
    dividerWidth: PropTypes.number,
    /**
     * Color of divider in between each item
     */
    dividerColor: PropTypes.string,
    /**
     * View style of main view which includes backgroundColor and parameters for border
     */
    customViewStyle: PropTypes.object,
    /**
     * Default text style to render for viewType 'text'
     */
    textStyle: PropTypes.object,
    /**
     * Text style of selected item for viewType 'text'
     */
    selectedTextStyle: PropTypes.object,
    /**
     * Defines custom width of each item
     */
    itemWidth: PropTypes.number,
    /**
     * Defines custom height of each item
     */
    itemHeight: PropTypes.number,
    /**
     * Default image style to apply for viewType 'icon'
     */
    selectedIconStyle: PropTypes.object,
    /**
     * Selected image style to apply for selected item for view type 'icon'
     */
    selectedIconClassName: PropTypes.arrayOf(PropTypes.string),
    /**
     * Background color of selected item
     */
    selectedBackgroundColor: PropTypes.string,
    /**
     * Defines which index should be selected Default will be 0
     */
    initialIndex: PropTypes.number,
    /**
     * Array of data to render For view type 'icon' : [{icon: 'imagepath or url', selectedIcon:'imagepath or url'}]
     *  where selectedIcon is optional For view type 'text' : [{title: 'Name of item'}]
     */
    values: PropTypes.arrayOf(PropTypes.shape(PropTypes.oneOf([
        {
            "icon": PropTypes.string,
            "selectedIcon": PropTypes.string,
        },
        {
            "title": PropTypes.string
        }
    ]))),
    /**
     * Callback on item click which returns selected item index
     * (params: any) => void
     */
    onPress: PropTypes.func.isRequired,
    /**
     * Display mode of mobile or desktop.
     */
    isMobileView: PropTypes.bool,
}

export { ToggleView }