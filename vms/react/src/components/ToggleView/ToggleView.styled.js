import styled from "styled-components";


export const ItemContainer = styled.div`
    background-color: ${({ isSelected, selectedBackgroundColor, isMobile,theme }) => isMobile ? "#fff" : isSelected && selectedBackgroundColor ? selectedBackgroundColor : isSelected && !selectedBackgroundColor ? theme.palette.primary.main : "#ffffff"};
    padding:${({ isMobile }) => isMobile ? "0px" : "10px"};
    border-right:${({ lastElement }) => lastElement ? "none" : "solid"};
    border-right-width: ${({ dividerWidth, isMobile }) => dividerWidth ? `${dividerWidth}px` : isMobile ? '0px' : '1px'};
    border-right-color: ${({ dividerColor }) => dividerColor ? dividerColor : "#d1d3d4"};
    height:${({ itemHeight }) => itemHeight ? `${itemHeight}px` : "auto"};;
    width:${({ itemWidth }) => itemWidth ? `${itemWidth}px` : "100%"};
    display: flex;
    align-items: center;
    justify-content: center;
`

export const MainContainer = styled.div`
    display:flex;
    flex-direction:${({ direction }) => direction};
    border-radius: 5px;
    overflow: hidden;
    width: 122px;
    min-width: 40px;
    border: ${({ isMobile }) => isMobile ? "none" : "1px solid #d1d3d4"};
    cursor:pointer;
`
