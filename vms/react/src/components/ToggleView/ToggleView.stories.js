import React from "react";
import { ToggleView } from "./ToggleView";
import mapToggle from "../../assets/images/ToggleView/mapToggle.svg";
import listToggle from "../../assets/images/ToggleView/listToggle.svg";
import tileToggle from "../../assets/images/ToggleView/tileToggle.svg";
import mapToggleRed from "../../assets/images/ToggleView/mapToggleRed.svg";
import listToggleRed from "../../assets/images/ToggleView/listToggleRed.svg";
import tileToggleRed from "../../assets/images/ToggleView/tileToggleRed.svg";

export default {
  title: "VMS_REACT/ToggleView",
  component: ToggleView,
  argTypes: {
    additionalClassName: { control: { type: "" } },
  },
};

const Template = (args) => <ToggleView {...args}></ToggleView>;
export const Default = Template.bind({});
Default.args = {
  onPress:(id)=>{console.log("id",id)}
};

export const Mobile = Template.bind({});
Mobile.args = {
  isMobile: true,
  values: [{
    "icon": tileToggle,
    "selectedIcon": tileToggleRed,
  },
  {
    "icon": listToggle,
    "selectedIcon": listToggleRed,
  },
  {
    "icon": mapToggle,
    "selectedIcon": mapToggleRed,
  }],
  onPress:(id)=>{console.log("id",id)}
};
export const MobileColumnText = Template.bind({});
MobileColumnText.args = {
  viewType:"text",
  isMobile: true,
  direction: "column",
  values: [
    {
      "title": "Hotels"
    },
    {
      "title": "Flights"
    },
    {
      "title": "Flights"
    }],
  onPress:(id)=>{console.log("id",id)}
};

export const DesktopColumnText = Template.bind({});
DesktopColumnText.args = {
  viewType:"text",
  isMobile: true,
  direction: "column",
  values: [
    {
      "title": "Hotels"
    },
    {
      "title": "Flights"
    },
    {
      "title": "Flights"
    }],
    onPress:(id)=>{console.log("id",id)}
};



