import React, { useState, Fragment } from "react";
import { ModalOverlay } from "./ModalOverlay";
import { Button } from "../Button/Button";
import { Card } from "../Card/Card";

export default {
  title: "VMS_REACT/Modal Overlay",
  component: ModalOverlay,
  argTypes: {
    additionalStyle: { control: { type: "" } },
    additionalClassName: { control: { type: "" } },
  },
};

const BodyView = ({ size }) => {
  return (
    <div
      style={{
        display: "flex",
        flexDirection: "column",
        gap: "10px",
        marginLeft: "2%",
      }}
    >
      {Array.from({ length: size }).map((element, index) => {
        return (
          <div
            key={`body-${index}`}
            style={{ display: "flex", gap: "10px", flexDirection: "row" }}
          >
            {["Saver", "Flexi", "Flex-Plus", "Flexi-Saver"].map(
              (cardTitle, cardIndex) => {
                return <Card title={cardTitle} key={`card-${cardIndex}`} />;
              }
            )}
          </div>
        );
      })}
    </div>
  );
};

export const Default = (args) => {
  const [showModal, setShowModal] = useState(false);
  return (
    <Fragment>
      <Button
        onClick={(e) => {
          e.stopPropagation();
          setShowModal(true);
        }}
        isMobile={true}
      >
        Open Modal Overlay
      </Button>
      <ModalOverlay
        {...args}
        isOpen={showModal || args.isOpen}
        onModalClose={() => {
          setShowModal(false);
        }}
        title={"Select Fare"}
        footerButtons={{
          firstButton: {
            children: "Book",
            buttonType: "primary",
            isMobile: true,
            onClick: () => {
              setShowModal(false);
            },
          },
        }}
      >
        <BodyView size={2} />
      </ModalOverlay>
    </Fragment>
  );
};
Default.parameters = {
  viewport: {
    defaultViewport: "iphone5",
  },
};
export const WithTwoButtonsInFooter = (args) => {
  const [showModal, setShowModal] = useState(false);
  return (
    <Fragment>
      <Button
        onClick={(e) => {
          e.stopPropagation();
          setShowModal(true);
        }}
        isMobile={true}
      >
        Open Modal Overlay
      </Button>
      <ModalOverlay
        {...args}
        isOpen={showModal || args.isOpen}
        onModalClose={() => {
          setShowModal(false);
        }}
        title={"Select Fare"}
        footerButtons={{
          firstButton: {
            children: "Book",
            buttonType: "primary",
            isMobile: true,
          },
          secondButton: {
            children: "Later",
            buttonType: "secondary",
            isMobile: true,
          },
        }}
      >
        <BodyView size={2} />
      </ModalOverlay>
    </Fragment>
  );
};
WithTwoButtonsInFooter.parameters = {
  viewport: {
    defaultViewport: "iphone5",
  },
};
export const ScrollableBody = (args) => {
  const [showModal, setShowModal] = useState(false);
  return (
    <Fragment>
      <Button
        onClick={(e) => {
          e.stopPropagation();
          setShowModal(true);
        }}
        isMobile={true}
      >
        Open Modal Overlay
      </Button>
      <ModalOverlay
        {...args}
        isOpen={showModal || args.isOpen}
        onModalClose={() => {
          setShowModal(false);
        }}
        title={"Select Fare"}
        footerButtons={{
          firstButton: {
            children: "Book",
            buttonType: "primary",
            isMobile: true,
          },
          secondButton: {
            children: "Later",
            buttonType: "secondary",
            isMobile: true,
          },
        }}
      >
        <BodyView size={4} />
      </ModalOverlay>
    </Fragment>
  );
};
ScrollableBody.parameters = {
  viewport: {
    defaultViewport: "iphone5",
  },
};
