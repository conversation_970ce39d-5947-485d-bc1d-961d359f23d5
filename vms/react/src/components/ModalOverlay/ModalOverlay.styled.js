import styled from "styled-components";
export const Overlay = styled.div`
  display: block;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 100;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
`;
export const WrapperDiv = styled.div`
  display: flex;
  flex-direction: column;
  position: fixed;
  z-index: 101;
  background-color: #fff;
  bottom: 0%;
  left: 0%;
  right: 0%;
  max-height: 445px;
  resize:vertical;
`;
export const Thumb = styled.div`
  display: flex;
  justify-content: center;
  cursor: grab;
`;
export const Thumbline = styled.div`
  width: 50px;
  height: 4px;
  border-radius: 3px;
  background-color: #d1d3d4;
  margin: 3%;
`;
export const Header = styled.div`
  align-items: flex-start;
  margin-left: 3%;
  margin-bottom: 3%;
`;
export const Title = styled.span`
  font-family: ${({ theme }) => theme?.typography?.fontFamily};
  font-size: ${({ theme }) => theme?.typography?.text?.fontSize};
  font-weight: ${({ theme }) => theme?.typography?.text?.fontWeight};
`;
export const Body = styled.div`
  margin-top: 1%;
  width: 100%;
  overflow: scroll;
  height: 100%;
`;
export const Footer = styled.div`
  display: flex;
  margin-top: 1%;
  border-top: 1px solid #ebebec;
`;
export const FooterButtons = styled.div`
  display: flex;
  flex-direction: row;
  justify-content: center;
  gap: 10px;
  width: 100%;
  padding: 20px;
`;
export const FooterFirstButton = styled.div`
  width: 100%;
`;
export const FooterSecondButton = styled.div`
  width: 100%;
`;
