import React, { Fragment, useEffect, memo, useCallback, useMemo } from "react";
import PropTypes from "prop-types";
import { Button } from "../Button/Button";
import {
  Overlay,
  WrapperDiv,
  Thumbline,
  Thumb,
  Header,
  Body,
  Title,
  Footer,
  FooterButtons,
  FooterFirstButton,
  FooterSecondButton,
} from "./ModalOverlay.styled";
import { useClassName } from "../../hooks/useClassName";
import { ThemeWrapper } from "../Theme/ThemeContext";

// Utility function (placed before main component)
const isNonNull = (prop) => prop !== null && prop !== undefined;

export const ModalOverlay = memo((props) => {
  // 1. PROP DESTRUCTURING & VALIDATION
  const {
    children,
    footerButtons,
    title,
    titleProps,
    onModalClose,
    onModalOpen,
    additionalClassName,
    additionalStyle,
    isOpen,
    dragThreshold = 30,
    isMobile,
    isMobileView,
    ...otherProps
  } = props;

  // 2. PERFORMANCE OPTIMIZATIONS
  const computedClassName = useMemo(() => 
    useClassName(props, additionalClassName), 
    [props, additionalClassName]
  );

  const hasFooterButtons = useMemo(() => isNonNull(footerButtons), [footerButtons]);
  const hasTitle = useMemo(() => isNonNull(title), [title]);

  // 3. EVENT HANDLING with useCallback
  const handleModalClose = useCallback((event) => {
    if (isNonNull(onModalClose)) {
      onModalClose(event);
    }
  }, [onModalClose]);

  // 4. EFFECTS
  useEffect(() => {
    if (isOpen && isNonNull(onModalOpen)) {
      onModalOpen();
    }
    document.querySelector("body").style.overflow = isOpen ? "hidden" : "auto";
    
    // Cleanup function to reset body overflow
    return () => {
      document.querySelector("body").style.overflow = "auto";
    };
  }, [isOpen, onModalOpen]);

  // 5. CONDITIONAL RENDERING (memoized)
  const titleElement = useMemo(() => {
    if (!hasTitle) return null;
    
    return (
      <Header className="vms_modaloverlay_title" id="header">
        <Title>{title}</Title>
      </Header>
    );
  }, [hasTitle, title]);

  const footerElement = useMemo(() => {
    if (!hasFooterButtons) return null;
    
    return (
      <Footer id="footer" className="vms_modaloverlay_footer">
        <FooterButtons className="vms_modaloverlay_footerbtn">
          {isNonNull(footerButtons?.firstButton) && (
            <FooterFirstButton className="vms_modaloverlay_footerfirstbtn">
              <Button {...footerButtons.firstButton}>
                {footerButtons.firstButton.children}
              </Button>
            </FooterFirstButton>
          )}
          {isNonNull(footerButtons?.secondButton) && (
            <FooterSecondButton className="vms_modaloverlay_footersecondbtn">
              <Button {...footerButtons.secondButton}>
                {footerButtons.secondButton.children}
              </Button>
            </FooterSecondButton>
          )}
        </FooterButtons>
      </Footer>
    );
  }, [hasFooterButtons, footerButtons]);

  const modalContent = useMemo(() => {
    if (!isOpen) return null;

    return (
      <div>
        <Overlay 
          onClick={handleModalClose} 
          className="vms_modaloverlay_overlay"
          role="dialog"
          aria-modal="true"
          aria-labelledby={hasTitle ? "header" : undefined}
        />
        <WrapperDiv 
          isOpen={isOpen} 
          className={`${computedClassName} vms_modaloverlay_container`} 
          style={additionalStyle}
          role="dialog"
          aria-modal="true"
          {...otherProps}
        >
          <Thumb className="vms_modaloverlay_thumb">
            <Thumbline />
          </Thumb>
          {titleElement}
          <Body id="body" className="vms_modaloverlay_body">
            {children}
          </Body>
          {footerElement}
        </WrapperDiv>
      </div>
    );
  }, [isOpen, handleModalClose, hasTitle, computedClassName, additionalStyle, titleElement, children, footerElement, otherProps]);

  // 6. ERROR HANDLING
  React.useEffect(() => {
    if (!children) {
      console.warn('ModalOverlay: children prop is required for modal content');
    }
  }, [children]);

  return (
    <ThemeWrapper isMobile={isMobile || isMobileView || false}>
      <Fragment>
        {modalContent}
      </Fragment>
    </ThemeWrapper>
  );
});

// Component display name for debugging
ModalOverlay.displayName = 'ModalOverlay';

ModalOverlay.defaultProps = {
  dragThreshold: 30,
  additionalStyle: null,
  additionalClassName: null,
};
ModalOverlay.propTypes = {
  /**
   * Title text
   */
  title: PropTypes.string,
  /**
   * Title props
   */
  // titleProps: PropTypes.object,
  /**
   * It will render children in horizontal and vertical scroll if content width is bigger then available space
   *  `ReactNode`
   */
  children: PropTypes.node.isRequired,
  /**
   * firstButton?: ButtonProps; first button props. Please pass value of button text in firstButton.children.
   * secondButton?: ButtonProps; second button props. Please pass value of button text in secondButton.children.
   * { firstButton?: ButtonProps; secondButton?: ButtonProps; }
   */
  footerButtons: PropTypes.shape({
    firstButton: PropTypes.object,
    secondButton: PropTypes.object,
  }),
  /**
   * This will be called when modal close
   * () => void
   */
  onModalClose: PropTypes.func,
  /**
   * This will be called when modal open
   * () => void
   */
  onModalOpen: PropTypes.func,
  /**
   * Property to append additional css class
   */
  additionalClassName: PropTypes.arrayOf(PropTypes.string),
  /**
   * Property to set the `inline style` object
   */
  additionalStyle: PropTypes.object,
  /**
   * Boolean describing if the modal should be shown or not.
   * Defaults to false
   */
  isOpen: PropTypes.bool.isRequired,
  /**
   * Number of pixel allowed to drag after that component will close
   */
  dragThreshold: PropTypes.number,
};
