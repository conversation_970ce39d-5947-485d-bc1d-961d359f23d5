import React, { useState } from "react";

import { InputRadioButton } from "./InputRadioButton";

export default {
  title: "VMS_REACT/InputRadioButton",
  component: InputRadioButton,
  argTypes: {
    additionalStyle: { control: { type: "" } },
    additionalClassName: { control: { type: "" } },
  },
};

export const Default = (props) => {
  const [state, setState] = useState("male");
  return (
    <div style={{ display: "flex", gap: "5px" }}>
      <InputRadioButton
        id="radio-with-icon"
        checked={state == "male"}
        value={"male"}
        onChange={(value) => {
          console.log("Sdsdsdsd")
          setState(value);
        }}
        label="Male"
      />
      <InputRadioButton
        id="radio-with-icon-female"
        checked={state == "female"}
        value={"female"}
        onChange={(value) => {
          console.log("Sdsdsdsd")
          setState(value);
        }}
        label="Female"
      />
      <InputRadioButton
        id="radio-with-icon-other"
        checked={state == "others"}
        value={"others"}
        disabled
        onChange={(value) => {
          setState(value);
        }}
        label="Others"
      />
    </div>
  );
};

export const IconPassByApplication = (props) => {
  const [state, setState] = useState("male");
  return (
    <div style={{ display: "flex", gap: "20px" }}>
      <InputRadioButton
        id="radio-with-icon"
        checked={state=="male"}
        value={"male"}
        onChange={(value) => {
          setState(value);
        }}
        label="Male"
        icons={{
          checkedIcon:
            "https://img.icons8.com/material/20/000000/unchecked-radio-button--v2.png",
          uncheckedIcon:
            "https://img.icons8.com/color/20/000000/unchecked-radio-button.png",
        }}
      />
      <InputRadioButton
        id="radio-with-icon"
        checked={state=="female"}
        value={"female"}
        onChange={(value) => {
          setState(value);
        }}
        label="Female"
        icons={{
          checkedIcon:
            "https://img.icons8.com/material/20/000000/unchecked-radio-button--v2.png",
          uncheckedIcon:
            "https://img.icons8.com/color/20/000000/unchecked-radio-button.png",
        }}
      />
      <InputRadioButton
        id="radio-with-icon"
        checked={state=="other"}
        value={"other"}
        onChange={(value) => {
          setState(value);
        }}
        label="Others"
        disabled
        icons={{
          checkedIcon:
            "https://img.icons8.com/material/20/000000/unchecked-radio-button--v2.png",
          uncheckedIcon:
            "https://img.icons8.com/color/20/000000/unchecked-radio-button.png",
        }}
      />
    </div>
  );
};
