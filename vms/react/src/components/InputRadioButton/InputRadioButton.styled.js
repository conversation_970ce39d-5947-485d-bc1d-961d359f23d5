import styled from "styled-components";

export const StyledWrapper = styled.div`
  display: flex;
`;
export const Input = styled.input`
  &:checked {
    border: ${({ theme }) => `1px ${theme.palette.primary[theme.mode]} solid`};
  }
  &:checked:after {
    background-color: ${({ theme }) => theme.palette.primary[theme.mode]};
    width: 10px;
    height: 10px;
    border: ${({ theme }) => `1px ${theme.palette.primary[theme.mode]} solid`};
    content: "";
    border-radius: 50%;  
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
  width: 20px;
  height: 20px;
  display: inline-block;
  background-repeat: no-repeat !important;
  background-position: 50% !important;
  position: relative;
  right: 0;
  appearance: none;
  border-radius: 50%;
  border: 1px #d1d1d1 solid;
  opacity: ${({ disabled }) => disabled && "0.5"};
  cursor: pointer;
`;

export const Label = styled.span`
  display: inline-block;
  vertical-align: top;
  margin: 3px 0px 0px 10px;
  font-weight: ${({ checked }) => (checked ? `700` : `400`)};
  font-family: ${({ theme }) => theme.typography.fontFamily};
  font-size: 14px;
  line-height: 20px;
  letter-spacing: 0.3px;
  color: #4d4d4f;
  cursor: pointer;
  opacity: ${({ disabled }) => (disabled === true ? "0.5" : "1")};
  justify-content: center;
`;
export const IconsRadionBtn = styled.div`
  display: flex;
  align-self: center;
  opacity: ${({ disabled }) => (disabled === true ? "0.5" : "1")};
  cursor: ${({ disabled }) => (disabled === false ? "pointer" : "not-allowed")};
`;
