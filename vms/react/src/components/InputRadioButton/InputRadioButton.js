import React, { memo, useCallback, useMemo } from "react";
import PropTypes from "prop-types";
import {
  StyledWrapper,
  Input,
  Label,
  IconsRadionBtn,
} from "./InputRadioButton.styled";
import { useClassName } from "../../hooks/useClassName";
import { ThemeWrapper } from "../Theme/ThemeContext";

// Utility function (placed before main component)
const isNonNull = (prop) => prop !== null && prop !== undefined;

export const InputRadioButton = memo((props) => {
  // 1. PROP DESTRUCTURING & VALIDATION
  const {
    id,
    value,
    checked,
    onChange,
    label,
    disabled = false,
    additionalStyle,
    icons,
    additionalClassName,
    isMobile,
    isMobileView,
    ...otherProps
  } = props;

  // 2. PERFORMANCE OPTIMIZATIONS
  const computedClassName = useMemo(
    () => useClassName(props, additionalClassName),
    [props, additionalClassName]
  );

  const hasIcons = useMemo(() => isNonNull(icons), [icons]);
  const hasLabel = useMemo(() => isNonNull(label) && label !== "", [label]);

  // 3. EVENT HANDLING with useCallback
  const handleChange = useCallback(
    (event) => {
      if (disabled) return;

      event.stopPropagation();

      if (isNonNull(onChange)) {
        onChange(value, event);
      }
    },
    [disabled, onChange, value]
  );

  const handleLabelClick = useCallback(
    (event) => {
      if (disabled) return;

      event.stopPropagation();
      event.target.value = value;

      if (isNonNull(onChange)) {
        onChange(value, event);
      }
    },
    [disabled, onChange, value]
  );

  const handleKeyDown = useCallback(
    (event) => {
      if (event.key === "Enter" || event.key === " ") {
        event.preventDefault();
        handleChange(event);
      }
    },
    [handleChange]
  );

  // 4. CONDITIONAL RENDERING (memoized)
  const radioInput = useMemo(() => {
    if (hasIcons) {
      return (
        <IconsRadionBtn
          id={id}
          className="vms_inputradiobtn_iconsradiobtn"
          onClick={handleChange}
          disabled={disabled}
          role="radio"
          tabIndex={disabled ? -1 : 0}
          onKeyDown={handleKeyDown}
          aria-checked={checked}
          aria-disabled={disabled}
        >
          <img
            src={checked ? icons.checkedIcon : icons.uncheckedIcon}
            alt={checked ? "Selected" : "Not selected"}
          />
        </IconsRadionBtn>
      );
    }

    return (
      <Input
        id={id}
        type="radio"
        value={value}
        checked={checked}
        onChange={handleChange}
        disabled={disabled}
        className="vms_inputradiobutton_input"
        style={additionalStyle}
        aria-checked={checked}
        aria-disabled={disabled}
        {...otherProps}
      />
    );
  }, [
    hasIcons,
    id,
    handleChange,
    disabled,
    handleKeyDown,
    checked,
    icons,
    value,
    additionalStyle,
    otherProps,
  ]);

  const labelElement = useMemo(() => {
    if (!hasLabel) return null;

    return (
      <Label
        className="vms_inputradiobutton_label"
        checked={checked}
        onClick={handleLabelClick}
        disabled={disabled}
        htmlFor={id}
        role="label"
        tabIndex={disabled ? -1 : 0}
        onKeyDown={handleKeyDown}
      >
        {label}
      </Label>
    );
  }, [hasLabel, checked, handleLabelClick, disabled, id, handleKeyDown, label]);

  // 5. ERROR HANDLING
  React.useEffect(() => {
    if (!id) {
      console.warn("InputRadioButton: id prop is required for accessibility");
    }
    if (!value) {
      console.warn("InputRadioButton: value prop is required");
    }
    if (typeof checked !== "boolean") {
      console.warn("InputRadioButton: checked prop should be a boolean");
    }
  }, [id, value, checked]);

  return (
    <ThemeWrapper isMobile={isMobile || isMobileView || false}>
      <StyledWrapper
        className={`vms_inputradiobutton_container ${computedClassName}`}
        role="radiogroup"
      >
        {radioInput}
        {labelElement}
      </StyledWrapper>
    </ThemeWrapper>
  );
});

// Component display name for debugging
InputRadioButton.displayName = "InputRadioButton";

InputRadioButton.defaultProps = {
  disabled: false,
  label: null,
};

InputRadioButton.propTypes = {
  /**
   * Unique ID for the field. Required for web accessibility
   */
  id: PropTypes.string.isRequired,
  /**
   * Set a value for radio button.
   */
  value: PropTypes.string.isRequired,
  /**
   * Set the radio button selected on state change
   */
  checked: PropTypes.bool.isRequired,
  /**
   * Set onChange event for each radio button. it will return selected radio button value
   * (value) => void
   */
  onChange: PropTypes.func.isRequired,
  /**
   * Set label for each radio button
   */
  label: PropTypes.string,
  /**
   * Set disabled to radio button depends on disabled state
   */
  disabled: PropTypes.bool,
  /**
   * Classes to add additional styling in the parent container
   */
  additionalClassName: PropTypes.arrayOf(PropTypes.string),
  /**
   * Property to set the `inline style` object
   */
  additionalStyle: PropTypes.object,
  /**
   * Custom icons path or base64
   * { uncheckedIcon?: string; checkedIcon?: string; }
   */
  icons: PropTypes.shape({
    uncheckedIcon: PropTypes.string.isRequired,
    checkedIcon: PropTypes.string.isRequired,
  }),
};
