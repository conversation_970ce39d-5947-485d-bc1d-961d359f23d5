import React, { Fragment, useEffect, useState, useRef, memo, useCallback, useMemo } from "react";
import PropTypes from "prop-types";
import { Stepper } from "../Stepper/Stepper";
import { InputRadioButton } from "../InputRadioButton/InputRadioButton";
import {
  InputViewDiv,
  PassengerLeftDiv,
  PassengerRightDiv,
  TotalPassgerText,
  TravellerClassDiv,
  TravellerClassPassenger,
  TravellerClassText,
  TravellerPicker,
  TravellerPickerContainer,
  PrimaryText,
  SecondaryText,
  TravellerClassPickerContainer,
  TravellerClassContainer,
  Divider,
  OverlayContainer,
  TitlText,
  TitleDiv,
  InputLabel,
  DoneButtonDiv,
  PrimaryTextIcon,
} from "./TravelerClass.styled";
import { Button } from "../Button/Button";
import { useClassName } from "../../hooks/useClassName";
import { ThemeWrapper } from "../Theme/ThemeContext";
import { useClickAway } from "../../hooks/outsideClickHandler";

// Utility function (placed before main component)
const isNonNull = (prop) => prop !== null && prop !== undefined;

export const TravellerClass = memo((props) => {
  // 1. PROP DESTRUCTURING & VALIDATION
  const {
    passengerData = [],
    travellerClassData = [],
    selectedClassIndex = 0,
    onClassItemClick,
    onPassengerSelectionChange,
    onPickerVisibilityChange,
    customInputView,
    flyoutPosition = "right",
    isMobile = false,
    isMobileView = false,
    firstTitleText = null,
    secondTitleText = null,
    isOverlay = false,
    onDoneButtonPress,
    doneButtonText = "DONE",
    buttonProps,
    inputLabel,
    additionalClassName,
    additionalStyle,
    customViewLabel,
    onDoneButtonClick,
    submitBtnText,
    id,
    ...otherProps
  } = props;

  // 2. STATE MANAGEMENT
  const [travelClassState, setTravelClassState] = useState(selectedClassIndex);
  const [showTravelClassPickerState, setTravelClassPicker] = useState(false);
  const [totalPassengerCount, setTotalPassengerCount] = useState(0);
  const [showOverlay, setShowOverlay] = useState(false);

  // 3. REFS
  const travelClassRef = useRef();

  // 4. PERFORMANCE OPTIMIZATIONS
  const computedClassName = useMemo(() => 
    useClassName(props, additionalClassName), 
    [props, additionalClassName]
  );

  const getPassengerCount = useCallback(() => {
    return passengerData.reduce((prev, current) => {
      return prev + (current.initialValue || 0);
    }, 0);
  }, [passengerData]);

  const totalPassengerText = useMemo(() => {
    return `${totalPassengerCount} ${totalPassengerCount > 1 ? 'Travellers' : 'Traveller'}`;
  }, [totalPassengerCount]);

  const currentClassTitle = useMemo(() => {
    return travellerClassData[travelClassState]?.title || '';
  }, [travellerClassData, travelClassState]);

  // 5. EVENT HANDLERS with useCallback
  const travelClassChangeHandler = useCallback((event) => {
    const classIndex = Number(event);
    setTravelClassState(classIndex);
    
    if (onClassItemClick && travellerClassData[classIndex]) {
      try {
        onClassItemClick(classIndex, travellerClassData[classIndex]);
      } catch (error) {
        console.error("TravellerClass: Error in onClassItemClick callback", error);
      }
    }
  }, [onClassItemClick, travellerClassData]);

  const travelClassPickerHandler = useCallback(() => {
    const updatedShowTravelClassPickerState = !showTravelClassPickerState;
    setTravelClassPicker(updatedShowTravelClassPickerState);
    setShowOverlay(!showOverlay);
    
    if (onPickerVisibilityChange) {
      try {
        onPickerVisibilityChange(updatedShowTravelClassPickerState);
      } catch (error) {
        console.error("TravellerClass: Error in onPickerVisibilityChange callback", error);
      }
    }
  }, [showTravelClassPickerState, showOverlay, onPickerVisibilityChange]);

  const onDoneButtonHandler = useCallback((e) => {
    setTravelClassPicker(false);
    
    if (buttonProps?.onClick && e) {
      try {
        buttonProps.onClick(e);
      } catch (error) {
        console.error("TravellerClass: Error in buttonProps.onClick callback", error);
      }
    }
    
    if (onDoneButtonPress) {
      try {
        onDoneButtonPress(passengerData);
      } catch (error) {
        console.error("TravellerClass: Error in onDoneButtonPress callback", error);
      }
    }
  }, [buttonProps, onDoneButtonPress, passengerData]);

  const handleOverlayClick = useCallback((e) => {
    e.stopPropagation();
    travelClassPickerHandler();
  }, [travelClassPickerHandler]);

  const handleInputViewClick = useCallback((e) => {
    e.stopPropagation();
    travelClassPickerHandler();
  }, [travelClassPickerHandler]);

  const handleSubmitButtonClick = useCallback((e) => {
    setTravelClassPicker(false);
    if (onDoneButtonClick) {
      try {
        onDoneButtonClick(e);
      } catch (error) {
        console.error("TravellerClass: Error in onDoneButtonClick callback", error);
      }
    }
  }, [onDoneButtonClick]);

  const handleKeyDown = useCallback((event) => {
    if (event.key === 'Escape' && showTravelClassPickerState) {
      event.preventDefault();
      travelClassPickerHandler();
    }
  }, [showTravelClassPickerState, travelClassPickerHandler]);

  // 6. EFFECTS
  useEffect(() => {
    setTotalPassengerCount(getPassengerCount());
  }, [getPassengerCount]);

  useEffect(() => {
    setTravelClassState(selectedClassIndex);
  }, [selectedClassIndex]);

  // 7. useClickAway hook
  useClickAway(travelClassRef, () => {
    if (!isOverlay) {
      setTravelClassPicker(false);
      onDoneButtonHandler();
      if (onPickerVisibilityChange) {
        onPickerVisibilityChange(false);
      }
    }
  });

  // 8. ERROR HANDLING
  React.useEffect(() => {
    if (!onClassItemClick) {
      console.warn('TravellerClass: onClassItemClick prop is required');
    }
    if (!onPassengerSelectionChange) {
      console.warn('TravellerClass: onPassengerSelectionChange prop is required');
    }
    if (!Array.isArray(passengerData)) {
      console.warn('TravellerClass: passengerData should be an array');
    }
    if (!Array.isArray(travellerClassData)) {
      console.warn('TravellerClass: travellerClassData should be an array');
    }
  }, [onClassItemClick, onPassengerSelectionChange, passengerData, travellerClassData]);

  // 9. MEMOIZED COMPONENTS
  const passengerElements = useMemo(() => {
    return passengerData.map((ele) => (
      <TravellerClassPassenger key={ele.primaryText} className="vms_travelerclass_passenger">
        <PassengerLeftDiv className="vms_travelerclass_passengerleftdiv">
          {ele?.primaryTextIcon && (
            <PrimaryTextIcon 
              className="vms_travelerclass_primarytxticon" 
              src={ele.primaryTextIcon} 
              alt={ele.primaryTextIconAlt || `${ele.primaryText} icon`}
              loading="lazy"
            />
          )}
          <PrimaryText className="vms_travelerclass_primarytxt" isSelected={ele?.initialValue > 0}>
            {ele.primaryText}
          </PrimaryText>
          {ele?.secondaryText && (
            <SecondaryText className="vms_travelerclass_secondarytxt">
              {ele.secondaryText}
            </SecondaryText>
          )}
        </PassengerLeftDiv>
        <PassengerRightDiv className="vms_travelerclass_passengerrightview">
          <Stepper
            id={id ? `${id}-stepper-${ele.primaryText}` : undefined}
            images={ele?.images || null}
            minValue={ele.minValue}
            maxValue={ele.maxValue}
            value={ele.initialValue}
            onValueChange={(currentValue) => {
              const updatedPassengerData = passengerData.map(obj => 
                obj.primaryText === ele.primaryText 
                  ? { ...obj, initialValue: currentValue }
                  : obj
              );
              
              if (onPassengerSelectionChange) {
                try {
                  onPassengerSelectionChange(updatedPassengerData);
                } catch (error) {
                  console.error("TravellerClass: Error in onPassengerSelectionChange callback", error);
                }
              }
            }}
          />
        </PassengerRightDiv>
      </TravellerClassPassenger>
    ));
  }, [passengerData, id, onPassengerSelectionChange]);

  const travellerClassElements = useMemo(() => {
    return travellerClassData.map((travelclass, index) => (
      <TravellerClassContainer key={travelclass.title} className="vms_travelerclass_radiobutton">
        <InputRadioButton
          value={`${index}`}
          id={`${id || 'traveller'}-class-${index}`}
          label={travelclass.title}
          onChange={travelClassChangeHandler}
          checked={travelClassState === index}
        />
      </TravellerClassContainer>
    ));
  }, [travellerClassData, travelClassState, travelClassChangeHandler, id]);

  const overlayElement = useMemo(() => {
    if (!isOverlay || !showTravelClassPickerState || isMobile) return null;
    
    return (
      <OverlayContainer
        className="vms_travelerclass_overlay"
        onClick={handleOverlayClick}
        isOverlay={showOverlay}
        role="presentation"
      />
    );
  }, [isOverlay, showTravelClassPickerState, isMobile, handleOverlayClick, showOverlay]);

  const inputViewElement = useMemo(() => {
    if (isMobile) return null;
    
    return (
      <Fragment>
        {inputLabel && (
          <InputLabel 
            id={id ? `${id}-label` : undefined} 
            className="vms_travelerclass_inputlabel"
          >
            {inputLabel}
          </InputLabel>
        )}
        <InputViewDiv
          className="vms_travelerclass_inputview"
          flyoutPosition={flyoutPosition}
          isTravellerPickerOpen={showTravelClassPickerState}
          onClick={handleInputViewClick}
          onKeyDown={handleKeyDown}
          role="button"
          tabIndex={0}
          aria-expanded={showTravelClassPickerState}
          aria-haspopup="true"
          aria-label={`${totalPassengerText}, ${currentClassTitle}`}
        >
          {customInputView || (
            <>
              <TotalPassgerText className="vms_travelerclass_totalpassgertxt">
                {totalPassengerText}
              </TotalPassgerText>
              <TravellerClassText className="vms_travelerclass_travellerclasstxt">
                {currentClassTitle}
              </TravellerClassText>
            </>
          )}
        </InputViewDiv>
      </Fragment>
    );
  }, [
    isMobile, inputLabel, id, flyoutPosition, showTravelClassPickerState, 
    handleInputViewClick, handleKeyDown, totalPassengerText, currentClassTitle, customInputView
  ]);

  const submitButton = useMemo(() => {
    if (!submitBtnText) return null;
    
    return (
      <Button 
        id={id ? `${id}-btn` : undefined} 
        additionalClassName={['vms_traveller_class_doneBtn']}
        onClick={handleSubmitButtonClick}
      >
        {submitBtnText}
      </Button>
    );
  }, [submitBtnText, id, handleSubmitButtonClick]);

  return (
    <ThemeWrapper isMobile={isMobile || isMobileView || false}>
      <Fragment>
        {overlayElement}
        <TravellerClassDiv 
          isOverlay={isOverlay} 
          isOpen={showTravelClassPickerState} 
          className={`${computedClassName} vms_travelerclass_container`} 
          style={additionalStyle} 
          isMobile={isMobile} 
          ref={travelClassRef}
          onKeyDown={handleKeyDown}
          {...otherProps}
        >
          {inputViewElement}
          {(showTravelClassPickerState || isMobile) && (
            <TravellerPicker
              className="vms_travelerclass_travellerpicker"
              isTravellerPickerOpen={true}
              flyoutPosition={flyoutPosition}
              isMobile={isMobile}
              role="dialog"
              aria-modal={isOverlay}
              aria-labelledby={id ? `${id}-label` : undefined}
            >
              <TravellerPickerContainer className="vms_travelerclass_travellerpickercontainer">
                {firstTitleText && (
                  <TitleDiv className="vms_travelerclass_titletxt">
                    <TitlText>{firstTitleText}</TitlText>
                  </TitleDiv>
                )}
                {passengerElements}
              </TravellerPickerContainer>
              <Divider />
              <TravellerClassPickerContainer className="vms_travelerclass_travellerclasspicker">
                {secondTitleText && (
                  <TitleDiv className="vms_travelerclass_title">
                    <TitlText>{secondTitleText}</TitlText>
                  </TitleDiv>
                )}
                {customViewLabel || travellerClassElements}
                {submitButton}
              </TravellerClassPickerContainer>
            </TravellerPicker>
          )}
          {isMobile && (
            <DoneButtonDiv className="vms_travelerclass_donebtn">
              <Button
                id={id ? `${id}-done-btn` : undefined}
                {...buttonProps}
                onClick={onDoneButtonHandler}
                isMobile={true}
              >
                {doneButtonText}
              </Button>
            </DoneButtonDiv>
          )}
        </TravellerClassDiv>
      </Fragment>
    </ThemeWrapper>
  );
});

TravellerClass.displayName = 'TravellerClass';

TravellerClass.defaultProps = {
  passengerData: [],
  travellerClassData: [],
  selectedClassIndex: 0,
  isOverlay: false,
  flyoutPosition: "right",
  isMobile: false,
  firstTitleText: null,
  secondTitleText: null,
  doneButtonText: `DONE`,
  additionalClassName: null,
  additionalStyle: null,
};

TravellerClass.propTypes = {
  /**
   * Array of passenger data
   */
  passengerData: PropTypes.arrayOf(
    PropTypes.shape({
      primaryText: PropTypes.string.isRequired,
      primaryTextIcon: PropTypes.string,
      primaryTextIconAlt: PropTypes.string,
      secondaryText: PropTypes.string,
      initialValue: PropTypes.number.isRequired,
      minValue: PropTypes.number.isRequired,
      maxValue: PropTypes.number.isRequired,
      images: PropTypes.shape({
        plusImage: PropTypes.string,
        minusImage: PropTypes.string,
        plusDisabledImage: PropTypes.string,
        minusDisabledImage: PropTypes.string,
      })
    }),
  ),
  /**
   * Array of items for traveller
   */
  travellerClassData: PropTypes.arrayOf(
    PropTypes.shape({
      title: PropTypes.string.isRequired,
    })
  ),
  /**
   * Selected index of traveller class, Default is 0
   */

  id:PropTypes.string,

  selectedClassIndex: PropTypes.number,
  /**
   * On Press event of second title data
   * (index: number, item: {title:''}) => void
   */
  onClassItemClick: PropTypes.func.isRequired,
  /**
   * Callback for change in passenger selection
   * (data) => void. Here data is passengerData
   */
  onPassengerSelectionChange: PropTypes.func.isRequired,
  /**
   * It will give you callback when traveller class picker open/close.
   * It will be useful when you want to do some conditional coding based on picker visibility NOTE: Web Only
   * (isOpen: boolean) => void
   */
  onPickerVisibilityChange: PropTypes.func,
  /**
   * Custom view to display selected data, It will replace default box
   * However component will bind onClick event to this custom input so travel picker will open on click of it NOTE: Web Only
   * `ReactNode`
   */
  customInputView: PropTypes.node,
  /**
   * To wrap the component with overlay. Default value false **NOTE**: Web Only
   */
  isOverlay: PropTypes.bool,
  /**
   * Define if its for mobile or not
   */
  isMobile: PropTypes.bool,
  /**
   * Postion of Flyout. **NOTE:** when value assigned as center or right then the application needs to give width to the component **NOTE:** Web Only
   */
  flyoutPosition: PropTypes.oneOf(["left", "right", "centre"]),
  /**
   * First title text of component - **MoWeb Only**
   */
  firstTitleText: PropTypes.string,
  /**
   * Second title text of component - **MoWeb Only**
   */
  secondTitleText: PropTypes.string,
  /**
   * Done button press handler - **MoWeb Only**
   * (data:) => void. Here data is passengerData
   */
  onDoneButtonPress: PropTypes.func,
  /**
   * Button text for pax selection -  **MoWeb Only**
   */
  doneButtonText: PropTypes.string,
  /**
   * Props for button component - **MoWeb Only**
   */
  buttonProps: PropTypes.shape({ ...Button.propTypes }),
  /**
   * Custom label above input box **NOTE: Web Only**
   */
  inputLabel: PropTypes.string,
  /**
   * Classes to add additional styling in the parent container
   */
  additionalClassName: PropTypes.arrayOf(PropTypes.string),
  /**
   * Property to set the `style` object on button
   */
  additionalStyle: PropTypes.object,
  customViewLabel: PropTypes.node,
  onDoneButtonClick: PropTypes.func,
};
