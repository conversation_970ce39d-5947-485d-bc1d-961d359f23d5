import styled from "styled-components";

export const OverlayContainer = styled.div`
  ${({ isOverlay }) =>
    isOverlay
      ? `
      display: block;
      position: fixed;
      top: 0;
      left: 0;
      z-index: 100;
      width: 100%;
      height: 100vh;
      background-color: rgba(0,0,0,0.8);
`
      : `display: none;`};
`;

export const TravellerClassDiv = styled.div`
   z-index: ${({ isOverlay,isOpen }) => isOverlay && isOpen ? "101" : ""};
  background-color: transparent;
  position: relative;
  width:${({isMobile}) => isMobile===false&&"198px"};
`;

export const InputViewDiv = styled.div`
  width: ${({ flyoutPosition }) =>
    flyoutPosition === "centre" ? "280px" : "198px"};
  border: ${({ theme, isTravellerPickerOpen }) =>
    isTravellerPickerOpen
      ? `1px solid ${theme.palette.primary[theme.mode]}`
      : `1px solid #939598`};
  background-color: #fff;
  border-radius: 3px;
  background-color: #fff;
  display: block;
  padding-left: 16px;
  padding-right: 16px;
  padding-top: 13px;
  padding-bottom: 13px;
  margin-top: 8px;
  cursor: pointer;
  height: 60px;
  box-sizing: border-box;
  box-shadow: 0 2px 5px 0 rgb(221 221 221 / 50%);
`;
export const InputLabel = styled.span`
  color: #4d4d4f;
  font-size: 14px;
  line-height: 20px;
  letter-spacing: 0.3px;
  font-weight: 400;
  font-family: ${({ theme }) => theme.typography.fontFamily};
`;
export const TotalPassgerText = styled.span`
  color: #000;
  font-size: 14px;
  line-height: 20px;
  letter-spacing: 0.3px;
  font-weight: 700;
  font-family: ${({ theme }) => theme.typography.fontFamily};
`;
export const TravellerClassText = styled.p`
  font-size: 10px;
  line-height: 12px;
  letter-spacing: 0px;
  color: #4d4d4f;
  font-weight: 300;
  margin: 0px;
  cursor: pointer;
  font-family: ${({ theme }) => theme.typography.fontFamily};
`;

export const TravellerPicker = styled.div`
  width: ${({ isMobile }) => isMobile === false && `280px`};
  border: ${({ theme, isMobile }) =>
    isMobile === false && `1px solid ${theme.palette.primary[theme.mode]}`};
  border-radius: ${({ theme, isMobile }) =>
    isMobile === true ? "0px 0px" : theme?.shape?.borderRadius};
  background-color: #fff;
  display: block;
  margin-left: ${({ flyoutPosition, isMobile }) =>
    isMobile === true ? "0px" : flyoutPosition === "left" ? "-82px" : "0px"};
  position: ${({ isMobile }) => isMobile === false && `absolute`};
`;
export const TravellerPickerContainer = styled.div`
  margin-left: 16px;
  margin-right: 16px;
  margin-top: 32px;
  margin-bottom: 21px;
`;
export const TravellerClassPassenger = styled.div`
  margin-top: 18px;
  display: flex;
`;
export const PassengerLeftDiv = styled.div`
  display: flex;
  width: 100%;
  justify-content: flex-start;
  align-items: center;
`;
export const PassengerRightDiv = styled.div`
  justify-content: flex-end;
`;

export const PrimaryText = styled.span`
  font-weight: ${({ isSelected }) => (isSelected ? `700` : `400`)};
  font-family: ${({ theme }) => theme.typography.fontFamily};
  font-size: 14px;
  line-height: 20px;
  letter-spacing: 0.3px;
`;
export const SecondaryText = styled.span`
  margin-left: 4px;
  font-weight: 400;
  font-family: ${({ theme }) => theme.typography.fontFamily};
  font-size: 14px;
  line-height: 20px;
`;
export const TravellerClassPickerContainer = styled.div`
  margin: 16px 16px;
`;
export const TravellerClassContainer = styled.div`
  margin-bottom: 6px;
`;
export const Divider = styled.div`
  width: 100%;
  height: 1px;
  background-color: #ebebec;
  margin-top: 17.5px;
  margin-bottom: 1.5px;
`;
export const TitlText = styled.span`
  font-weight: ${({ theme }) => theme?.typography?.text?.fontWeight || "400"};
  font-size: ${({ theme }) => theme?.typography?.text?.fontSize || "14px"};
  line-height: 20px;
  letter-spacing: 0.3px;
  font-family: ${({ theme }) => theme?.typography?.fontFamily || ""};
  color: #4d4d4f;
`;
export const TitleDiv = styled.div`
  margin-bottom: 26px;
`;
export const DoneButtonDiv = styled.div`
  margin-top: 12%;
`;
export const PrimaryTextIcon  = styled.img`
 height: 24px;
 width:24px;
`;

