import React, { Fragment, useEffect, useState } from "react";

import { TravellerClass } from "./TravelerClass";
import singleusericon from "../../assets/images/travelerclass/singleusericon.svg";
import seatselecticon from "../../assets/images/travelerclass/seatselecticon.svg";
import { ModalPopup } from "../ModalPopup/ModalPopup";

const passengerInitialData = [
  {
    primaryText: "Adult",
    initialValue: 1,
    minValue: 1,
    maxValue: 9,
    primaryTextIcon: singleusericon,
    primaryTextIconAlt: "Adult passenger icon"
  },
  {
    primaryText: "Child",
    secondaryText: "(2-12 yrs)",
    initialValue: 0,
    minValue: 0,
    maxValue: 9,
  },
  {
    primaryText: "Infant",
    secondaryText: "(Below 2 yrs)",
    initialValue: 0,
    minValue: 0,
    maxValue: 1,
  },
];
const travelClassInitialData = [{ title: "Economy" }, { title: "Business" }];

export default {
  title: "VMS_REACT/Traveller Class",
  component: TravellerClass,
  argTypes: {
    isOverlay: { control: { type: "" } },
    customInputView: { control: { type: "" } },
    passengerData: { control: { type: "" } },
    travellerClassData: { control: { type: "" } },
    additionalStyle: { control: { type: "" } },
    additionalClassName: { control: { type: "" } },
    primaryTextIcon: { control: { type: "" } },
    userIconAlt: { 
      control: { type: "text" },
      description: "Alt text for user/traveler count icon"
    },
    seatIconAlt: { 
      control: { type: "text" },
      description: "Alt text for seat/travel class icon"
    }
  },
};

const CustomViewReactComponent = ({
  totalTravellerCount,
  selectedTravelClass,
  userIconAlt = "Traveler count icon",
  seatIconAlt = "Travel class icon"
}) => {
  return (
    <div
      style={{
        display: "flex",
        boxShadow: "none",
        cursor: "pointer",
        margin: "0px",
        padding: "0px",
        border: "0",
        height: "48px",
      }}
    >
      <div
        style={{
          display: "flex",
          borderRight: "1px solid #ebebec",
          padding: "6px 0",
          margin: "0px",
          marginRight: " 15px",
        }}
      >
        <img height={"24px"} width={"24px"} src={singleusericon} alt={userIconAlt} />
        <span
          style={{
            fontFamily: "Montserrat,sans-serif",
            margin: "0px",
            fontSize: "16px",
            fontWeight: "700",
            fontStretch: "normal",
            fontStyle: "normal",
            lineHeight: "1.38",
            letterSpacing: ".3px",
            textAlign: "left",
            color: "#000",
            verticalAlign: "middle",
            marginRight: "10px",
          }}
        >
          {totalTravellerCount}
        </span>
      </div>

      <div
        style={{
          width: "57.3%",
          display: "flex",
          fontSize: "16px",
          fontWeight: "700",
          fontStretch: "normal",
          fontStyle: "normal",
          lineHeight: "1.38",
          letterSpacing: ".3px",
          textAlign: "left",
          color: "#000",
          verticalAlign: "middle",
          padding: "6px 0",
        }}
      >
        <img height={"24px"} width={"24px"} src={seatselecticon} alt={seatIconAlt} />
        <span>{selectedTravelClass.title}</span>
      </div>
    </div>
  );
};

const DisplayCurrentState = (data, selectedClass) => {
  return (
    <div
      style={{
        marginTop: "40px",
        fontFamily: "Montserrat, sans-serif",
        WebkitFontSmoothing: "antialiased",
      }}
    >
      <span style={{fontSize:"12px"}}><b style={{color:"red"}}>Note:</b> Below Data is just for representation. It is not part of TravellerClass React Component</span>
      <h5>Passenger Callback Value</h5>
      <ul style={{ paddingTop: "1px" }}>
        {data !== null &&
          data?.length > 0 &&
          data.map((currentPassengerData) => {
            return (
              <li
                key={currentPassengerData.primaryText}
              >{`${currentPassengerData.primaryText} : ${currentPassengerData.initialValue}`}</li>
            );
          })}
      </ul>
      <h5>Class Callback Value</h5>
      <ul style={{ paddingLeft: "15px", paddingTop: "1px" }}>
        <li>{`Selected Class : ${selectedClass}`}</li>
      </ul>
    </div>
  );
};

const Template = (args) => {
  const [data, setData] = useState(passengerInitialData);
  const [totalTravellerCount, setTotalTravellerCount] = useState(1);
  useEffect(() => {
    const count = data.reduce((prev, current) => {
      return (prev += current.initialValue);
    }, 0);
    setTotalTravellerCount(count);
  }, [data]);
  const [selectedTravelClass, setSelectedTravelClass] = useState({
    title: "Economy",
  });
  return (
    <div style={{ marginLeft: "82px" }}>
      <TravellerClass
        {...args}
        inputLabel={"TRAVELLER(S) & CLASS"}
        onClassItemClick={(index, obj) => {
          setSelectedTravelClass(obj);
        }}
        onPassengerSelectionChange={(passengerData) => {
          setData(passengerData);
        }}
        passengerData={data}
        
      />
      <>{DisplayCurrentState(data, selectedTravelClass.title)}</>
    </div>
  );
};

export const Default = Template.bind({});
Default.args = {
  travellerClassData: travelClassInitialData,
  selectedClassIndex: 0,
  isMobile: false,
  flyoutPosition: "right",
  submitBtnText: "Done",
  id:"tc-id",
  onDoneButtonClick:(e) => console.log("done button clicked")
};

const CustomViewTemplate = (args) => {
  const [data, setData] = useState(passengerInitialData);
  const [totalTravellerCount, setTotalTravellerCount] = useState(1);
  
  // Get altText from args or use defaults
  const { userIconAlt = "Traveler count icon", seatIconAlt = "Travel class icon" } = args;
  
  useEffect(() => {
    const count = data.reduce((prev, current) => {
      return (prev += current.initialValue);
    }, 0);
    setTotalTravellerCount(count);
  }, [data]);
  const [selectedTravelClass, setSelectedTravelClass] = useState({
    title: "Economy",
  });
  return (
    <div style={{ marginLeft: "82px" }}>
      <TravellerClass
        {...args}
        onClassItemClick={(index, obj) => {
          setSelectedTravelClass(obj);
        }}
        onPassengerSelectionChange={(passengerData) => {
          setData(passengerData);
        }}
        passengerData={data}
        customInputView={
          <CustomViewReactComponent
            totalTravellerCount={totalTravellerCount}
            selectedTravelClass={selectedTravelClass}
            userIconAlt={userIconAlt}
            seatIconAlt={seatIconAlt}
          />
        }
      />
      <>{DisplayCurrentState(data, selectedTravelClass.title)}</>
    </div>
  );
};

export const WithCustomView = CustomViewTemplate.bind({});
WithCustomView.args = {
  travellerClassData: travelClassInitialData,
  selectedClassIndex: 0,
  isMobile: false,
  flyoutPosition: "right",
  id:"cust-tc-id",
  userIconAlt: "Custom traveler count icon",
  seatIconAlt: "Custom travel class icon"
};

const WithOverlayTemplate = (args) => {
  const [data, setData] = useState(passengerInitialData);
  const [totalTravellerCount, setTotalTravellerCount] = useState(1);
  
  // Get altText from args or use defaults
  const { userIconAlt = "Traveler count icon", seatIconAlt = "Travel class icon" } = args;
  
  useEffect(() => {
    const count = data.reduce((prev, current) => {
      return (prev += current.initialValue);
    }, 0);
    setTotalTravellerCount(count);
  }, [data]);
  const [selectedTravelClass, setSelectedTravelClass] = useState({
    title: "Economy",
  });
  const [showOverlay, setShowOverlay] = useState(false);
  return (
    <div style={{ marginLeft: "82px" }}>
      <TravellerClass
        {...args}
        onClassItemClick={(index, obj) => {
          setSelectedTravelClass(obj);
        }}
        onPassengerSelectionChange={(passengerData) => {
          setData(passengerData);
        }}
        isOverlay={showOverlay}
        passengerData={data}
        onPickerVisibilityChange={(isTravelPickerOpen) => {
          setShowOverlay(isTravelPickerOpen);
        }}
        customInputView={
          <CustomViewReactComponent
            totalTravellerCount={totalTravellerCount}
            selectedTravelClass={selectedTravelClass}
            userIconAlt={userIconAlt}
            seatIconAlt={seatIconAlt}
          />
        }
      />
      <>{DisplayCurrentState(data, selectedTravelClass.title)}</>
    </div>
  );
};
export const WithOverlay = WithOverlayTemplate.bind({});
WithOverlay.args = {
  travellerClassData: travelClassInitialData,
  selectedClassIndex: 0,
  isMobile: false,
  flyoutPosition: "left",
  id:"overlay-tc-id",
  userIconAlt: "Overlay traveler count icon",
  seatIconAlt: "Overlay travel class icon"
};

export const TravelClassMobile = (args) => {
  const [showModal, setShowModal] = useState(false);
  const [data, setData] = useState(passengerInitialData);
  const [selectedTravelClass, setSelectedTravelClass] = useState({
    title: "Economy",
  });
  
  // Get altText from args or use defaults
  const { userIconAlt = "Mobile traveler count icon", seatIconAlt = "Mobile travel class icon" } = args;
  
  const getTotalCount = (data) => {
    return data.reduce((prev, current) => {
      return (prev += current.initialValue);
    }, 0);
  };
  return (
    <Fragment>
      <div
        style={{
          display: "flex",
          boxShadow: "none",
          cursor: "pointer",
          margin: "0px",
          padding: "0px",
          height: "48px",
          width: "100%",
          border: "1px solid #939598",
          // boxShadow: "0px 2px 3px 0px grey",
        }}
        onClick={(e) => {
          console.log("Open the modal");
          e.stopPropagation();
          setShowModal(true);
        }}
      >
        <div
          style={{
            display: "flex",
            borderRight: "1px solid #ebebec",
            padding: "6px 0",
            marginTop: "10px",
            marginRight: " 15px",
          }}
        >
          <img height={"24px"} width={"24px"} src={singleusericon} alt={userIconAlt} />
          <span
            style={{
              fontFamily: "Montserrat,sans-serif",
              margin: "0px",
              fontSize: "16px",
              fontWeight: "700",
              fontStretch: "normal",
              fontStyle: "normal",
              lineHeight: "1.38",
              letterSpacing: ".3px",
              textAlign: "left",
              color: "#000",
              verticalAlign: "middle",
              marginRight: "10px",
            }}
          >
            {getTotalCount(data)}
          </span>
        </div>

        <div
          style={{
            width: "57.3%",
            display: "flex",
            fontSize: "16px",
            fontWeight: "700",
            fontStretch: "normal",
            fontStyle: "normal",
            lineHeight: "1.38",
            letterSpacing: ".3px",
            textAlign: "left",
            color: "#000",
            verticalAlign: "middle",
            padding: "6px 0",
            marginTop: "10px",
          }}
        >
          <img height={"24px"} width={"24px"} src={seatselecticon} alt={seatIconAlt} />
          <span>{selectedTravelClass.title}</span>
        </div>
      </div>
      <ModalPopup
        isMobileView={true}
        isOpen={showModal}
        onRequestClose={() => {
          setShowModal(false);
        }}
      >
        <TravellerClass
        id={"tc-id-mob"}
          {...args}
          isMobile={true}
          firstTitleText="PASSENGERS"
          secondTitleText="TRAVELLER CLASS"
          onClassItemClick={(index, obj) => {
            setSelectedTravelClass(obj);
          }}
          travellerClassData={travelClassInitialData}
          passengerData={data}
          onDoneButtonPress={(passengerData) => {
            console.log("done btton press", passengerData);
            setData(passengerData);
            setShowModal(false);
          }}
          buttonProps={{
            buttonType: "primary",
          }}
        />
      </ModalPopup>
      <div style={{ margin: "2%" }}>
        {DisplayCurrentState(data, selectedTravelClass.title)}
      </div>
    </Fragment>
  );
};
TravelClassMobile.args = {
  userIconAlt: "Mobile user count icon",
  seatIconAlt: "Mobile seat selection icon"
};
TravelClassMobile.parameters = {
  viewport: {
    defaultViewport: "iphone5",
  },
};
