import styled from "styled-components";

function _fontWeight(theme) {
  return theme?.typography?.text?.fontWeight || "";
}
function _fontFamily(theme) {
  return theme?.typography?.fontFamily || "";
}
function _fontSize(theme) {
  return theme?.typography?.text?.fontSize || "";
}
function _lineHeight(theme) {
  return theme?.typography?.lineHeight || "";
}
function _letterSpacing(theme) {
  return theme?.typography?.letterSpacing || "";
}

function _borderRadius(theme) {
  return theme.shape.borderRadius;
}

function messageColor(theme, messageMode) {
  switch (messageMode) {
    case "error":
      return theme.palette.error.main;
    case "alert":
      return theme.palette.warning.main;
    case "success":
      return theme.palette.success.main;
    case "info":
      return theme.palette.info.main;
    default:
      return "#000";
  }
}

export const Container = styled.div`
  max-width: 320px;
`;

export const InputContainer = styled.div`
  position: relative;
`;

export const StyledLabelText = styled.label`
  font-weight: ${({ theme }) => _fontWeight(theme)};
  font-family: ${({ theme }) => _fontFamily(theme)};
  font-size: ${({ theme }) => _fontSize(theme)};
  line-height: 20px;
  letter-spacing: 0.3px;
  color: ${({ disabled }) => (disabled ? "#d1d3d4" : "#000")};
  display: block;
  position: relative;
  text-transform: uppercase;
`;

export const StyledInputText = styled.input`
  display: block;
  box-sizing: border-box;
  font-weight: 700;
  font-family: ${({ theme }) => _fontFamily(theme)};
  font-size: ${({ theme }) => _fontSize(theme)};
  font-stretch: normal;
  font-style: normal;
  line-height: ${({ theme }) => _lineHeight(theme)};
  width: 100%;
  letter-spacing: ${({ theme }) => _letterSpacing(theme)};
  border-radius: ${({ theme }) => _borderRadius(theme)};
  color: ${({ valid }) => (valid ? "#000" : "#e11b22")};
  padding: ${({ isMobile }) => (isMobile ? "" : "14px 16px")};
  padding-bottom: ${({ isMobile }) => (isMobile ? "3px" : "")};

  margin-top: 12px;
  text-overflow: ellipsis;
  height: 48px;
  background-color: #fff;
  border: none;
  ${({ isMobile }) =>
    isMobile
      ? { "border-bottom": "2px solid #4d4d4f", "border-radius": "0px" }
      : { border: "1px solid #4d4d4f" }};

  &:focus {
    outline: none;
    ${({ isMobile,inputType,theme }) =>
      isMobile
        ? { "border-bottom": `1px solid ${theme?.palette?.[inputType]?.main}` }
        : { border: `1px solid ${theme?.palette?.[inputType]?.main}` }};
  }
  &::placeholder {
    font-weight: ${({ theme }) => _fontWeight(theme)};
    font-family: ${({ theme }) => _fontFamily(theme)};
    font-size: 12px;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.43;
    border-radius: 3px;
    letter-spacing: 0.3px;
    color: #4d4d4f;
  }
  &:disabled {
    color: #d1d3d4;
    border: ${({ isMobile }) => (isMobile ? "" : "solid 1px #d1d3d4")};
    border-bottom: ${({ isMobile }) => (isMobile ? "2px solid #d1d3d4" : "")};
    background-color: #fff;
  }
`;
export const StyledErrorLable = styled.span`
  font-weight: ${({ theme }) => _fontWeight(theme)};
  font-family: ${({ theme }) => _fontFamily(theme)};
  font-size: ${({ theme }) => _fontSize(theme)};
  font-stretch: normal;
  font-style: normal;
  line-height: 20px;
  letter-spacing: 0.3px;
  position: relative;
  color: ${({ theme, messageMode }) => messageColor(theme, messageMode)};
  top: 8px;
  left: 16px;
`;

export const StyledRightView = styled.div`
  position: absolute;
  top: 0;
  right: 0;
`;
export const StyledLeftView = styled.div`
  position: absolute;
  top: 0;
  left: 0;
`;
