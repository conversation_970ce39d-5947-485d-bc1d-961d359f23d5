import React from "react";

import { InputDateField } from "./InputDateField";

export default {
  title: "VMS_REACT/Input Date Field",
  component: InputDateField,
  parameters: {
    controls: {
      exclude: /.*/g,
    },
  },
};

const Template = (args) => (
  <InputDateField
    onCompletion={(date) => console.log("date from stories", date)}
    onDateError={(error, value) => console.log("error,value", error, value)}
    {...args}
  />
);

export const Default = Template.bind({});
Default.args = {
  type: "text",
  id:"ip-datefield-1"
};

export const PreFilledValue = Template.bind({});
PreFilledValue.args = {
  type: "text",
  dateValue: new Date(),
  id:"prefilled-id"
};
export const Disabled = Template.bind({});
Disabled.args = {
  type: "text",
  dateValue: new Date(),
  disabled: true,
  id:"dis-id"
};

export const MobileDefault = Template.bind({});
MobileDefault.args = {
  type: "text",
  isMobile: true,
  id:"ip-datefield-1"
};
MobileDefault.parameters = {
  viewport: {
    defaultViewport: "iphone5",
  },
};

export const MobilePreFilledValue = Template.bind({});
MobilePreFilledValue.args = {
  type: "text",
  isMobile: true,
  dateValue: new Date(),
  id:"prefilled-mob-id"

};
MobilePreFilledValue.parameters = {
  viewport: {
    defaultViewport: "iphone5",
  },
};
export const MobileDisabled = Template.bind({});
MobileDisabled.args = {
  type: "text",
  isMobile: true,
  dateValue: new Date(),
  disabled: true,
  id:"dis-mob-id"

};
MobileDisabled.parameters = {
  viewport: {
    defaultViewport: "iphone5",
  },
};
