import React, { useRef, useEffect, useState, memo, useCallback, useMemo } from "react";
import PropTypes from "prop-types";
import {
  Container,
  InputContainer,
  StyledErrorLable,
  StyledInputText,
  StyledLabelText,
  StyledLeftView,
  StyledRightView,
} from "./InputDateField.styled";
import { subYears } from "date-fns";
import { formatWithLocale, whetherBetween } from "../../hooks/calendarHooks";
import { useClassName } from "../../hooks/useClassName";
import { ThemeWrapper } from "../Theme/ThemeContext";

// Utility function (placed before main component)
const isNonNull = (prop) => prop !== null && prop !== undefined;

const InputDateField = memo(React.forwardRef((props, ref) => {
  // 1. PROP DESTRUCTURING & VALIDATION
  const {
    id,
    type = "text",
    label,
    minDate,
    maxDate,
    dateValue,
    disabled = false,
    isMobile,
    isMobileView,
    msgText,
    secondaryLabel,
    messageMode = "error",
    additionalLeftViewClassName,
    additionalRightViewClassName,
    additionalLeftViewStyle,
    additionalRightViewStyle,
    additionalMessageClassName,
    additionalMessageStyle,
    additionalClassName,
    additionalStyle,
    onCompletion,
    onEditing,
    onDateError,
    inputType,
    onKeyUp,
    placeholder = "DD/MM/YYYY",
    onBlur,
    leftView,
    rightView,
    ...otherProps
  } = props;

  // 2. STATE MANAGEMENT
  const [inputDateVal, setInputDateval] = useState(() =>
    dateValue ? formatWithLocale(dateValue, "dd/MM/yyyy") : ""
  );
  const [isDateValid, SetIsDateValid] = useState(true);

  // 3. PERFORMANCE OPTIMIZATIONS
  const leftViewClassName = useMemo(() =>
    useClassName(props, additionalLeftViewClassName),
    [props, additionalLeftViewClassName]
  );

  const rightViewClassName = useMemo(() =>
    useClassName(props, additionalRightViewClassName),
    [props, additionalRightViewClassName]
  );

  const messageClassName = useMemo(() =>
    useClassName(props, additionalMessageClassName),
    [props, additionalMessageClassName]
  );

  const mainClassName = useMemo(() =>
    useClassName(props, additionalClassName),
    [props, additionalClassName]
  );

  const isMobileDevice = useMemo(() =>
    isMobile || isMobileView || false,
    [isMobile, isMobileView]
  );

  const hasLeftView = useMemo(() => isNonNull(leftView), [leftView]);
  const hasRightView = useMemo(() => isNonNull(rightView), [rightView]);

  // 4. UTILITY FUNCTIONS (memoized)
  const handleBetween = useCallback((number, calc) => {
    let [a, b] = calc;
    let min = Math.min(a, b),
      max = Math.max(a, b);
    return number > min && number < max;
  }, []);

  const checkDate = useCallback((date) => {
    const dateFormat = Date.parse(date);
    return dateFormat > 0;
  }, []);

  // 5. EVENT HANDLING with useCallback
  const handleKeyPress = useCallback((e) => {
    let input = e.target;

    if (e.charCode < 47 || e.charCode > 57) {
      e.preventDefault();
    }
    var len = input.value.length;
    // If we're at a particular place, let the user type the slash
    if (len !== 1 || len !== 3) {
      if (e.charCode == 47) {
        e.preventDefault();
      }
    }
    // If they don't add the slash, do it for them...
    if (len === 2) {
      input.value += "/";
    }

    // If they don't add the slash, do it for them...
    if (len === 5) {
      input.value += "/";
    }
  }, []);

  const isValidDate = useCallback((str) => {
    if (!str || str.length !== 10) return false;

    var parms = str.split(/[\.\-\/]/);
    if (parms.length !== 3) return false;

    var yyyy = parseInt(parms[2], 10);
    var mm = parseInt(parms[1], 10);
    var dd = parseInt(parms[0], 10);

    if (isNaN(yyyy) || isNaN(mm) || isNaN(dd)) return false;

    var date = new Date(yyyy, mm - 1, dd, 0, 0, 0, 0);
    return (
      mm === date.getMonth() + 1 &&
      dd === date.getDate() &&
      yyyy === date.getFullYear()
    );
  }, []);

  const handleDateChange = useCallback((e) => {
    const value = e.target.value;
    setInputDateval(value);
    handleKeyPress(e);

    const isValid = isValidDate(value);
    const hasValue = value.length > 0;

    SetIsDateValid(isValid || !hasValue);

    if (value.length === 10) {
      if (isNonNull(onCompletion)) {
        onCompletion(value);
      }

      const isBetween = whetherBetween(
        new Date(value),
        minDate,
        maxDate
      );

      if (isNonNull(onDateError)) {
        if (isBetween && isValid) {
          onDateError(0, value);
        } else if (!isValid) {
          onDateError(1, value);
        } else {
          onDateError(2, value);
        }
      }
    } else {
      if (isNonNull(onEditing)) {
        onEditing(value);
      }
    }
  }, [handleKeyPress, isValidDate, onCompletion, onDateError, onEditing, minDate, maxDate]);

  const handleKeyUp = useCallback((e) => {
    if (isNonNull(onKeyUp)) {
      onKeyUp(e);
    }
  }, [onKeyUp]);

  const handleBlur = useCallback((e) => {
    if (isNonNull(onBlur)) {
      onBlur(e);
    }
  }, [onBlur]);

  // 6. EFFECTS
  useEffect(() => {
    if (dateValue && checkDate(dateValue)) {
      setInputDateval(formatWithLocale(dateValue, "dd/MM/yyyy"));
    }
  }, [dateValue, checkDate]);

  // 7. ERROR HANDLING
  useEffect(() => {
    if (minDate && maxDate && minDate > maxDate) {
      console.warn('InputDateField: minDate should be less than maxDate');
    }
  }, [minDate, maxDate]);

  // 8. CONDITIONAL RENDERING (memoized)
  const labelElement = useMemo(() => {
    if (!label && !secondaryLabel) return null;

    return (
      <StyledLabelText
        className="vms_inputdatefield_labeltext"
        disabled={disabled}
      >
        {label} {secondaryLabel && <span>{secondaryLabel}</span>}
      </StyledLabelText>
    );
  }, [label, secondaryLabel, disabled]);

  const leftViewElement = useMemo(() => {
    if (!hasLeftView) return null;

    return (
      <StyledLeftView
        style={additionalLeftViewStyle}
        className={`${leftViewClassName} vms_inputdatefield_leftview`}
      >
        {leftView}
      </StyledLeftView>
    );
  }, [hasLeftView, additionalLeftViewStyle, leftViewClassName, leftView]);

  const rightViewElement = useMemo(() => {
    if (!hasRightView) return null;

    return (
      <StyledRightView
        style={additionalRightViewStyle}
        className={`${rightViewClassName} vms_inputdatefield_rightview`}
      >
        {rightView}
      </StyledRightView>
    );
  }, [hasRightView, additionalRightViewStyle, rightViewClassName, rightView]);

  const errorElement = useMemo(() => {
    if (isDateValid || !msgText) return null;

    return (
      <StyledErrorLable
        className={`${messageClassName} vms_inputdatefield_errorlable`}
        style={additionalMessageStyle}
        messageMode={messageMode}
      >
        {msgText}
      </StyledErrorLable>
    );
  }, [isDateValid, msgText, messageClassName, additionalMessageStyle, messageMode]);

  return (
    <ThemeWrapper isMobile={isMobileDevice}>
      <Container
        id={id}
        style={additionalStyle}
        className={`${mainClassName} vms_inputdatefield_container`}
        role="group"
        aria-labelledby={label ? `${id}-label` : undefined}
        {...otherProps}
      >
        {labelElement}
        <InputContainer className="vms_inputdatefield_inputcontainer">
          {leftViewElement}

          <StyledInputText
            ref={ref}
            className="vms_inputdatefield_inputtext"
            type={type}
            maxLength="10"
            placeholder={placeholder}
            onKeyPress={handleKeyPress}
            onKeyUp={handleKeyUp}
            value={inputDateVal || ""}
            valid={isDateValid}
            min={minDate}
            max={maxDate}
            isMobile={isMobileDevice}
            onChange={handleDateChange}
            inputType={inputType}
            disabled={disabled}
            onBlur={handleBlur}
            id={id}
            aria-invalid={!isDateValid}
            aria-describedby={!isDateValid && msgText ? `${id}-error` : undefined}
            aria-label={label || "Date input"}
          />

          {errorElement}
          {rightViewElement}
        </InputContainer>
      </Container>
    </ThemeWrapper>
  );
}));

// Component display name for debugging
InputDateField.displayName = "InputDateField";

InputDateField.defaultProps = {
  type: "text",
  label: "DOB",
  minDate: subYears(new Date(), 20),
  maxDate: new Date(),
  disabled: false,
  isMobile: false,
  isMobileView: false,
  msgText: "Date is invalid",
  messageMode: "error",
  placeholder: "DD/MM/YYYY",
  inputType: "primary",
  onCompletion: () => { },
  onEditing: () => { },
  onDateError: () => { },
  onKeyUp: () => { },
  onBlur: () => { },
};

InputDateField.propTypes = {
  /**
   * Variations of Button Type
   */
  inputType: PropTypes.oneOf(["primary", "secondary"]),

  /**
   * Minimum allowed date
   */
  minDate: PropTypes.instanceOf(Date),

  /**
   * Maximum allowed date
   */
  maxDate: PropTypes.instanceOf(Date),

  /**
   * onCompletion calls when user completed entering a valid date date is entered date by user in date format
   */
  onCompletion: PropTypes.func,

  /**
   * This method will be called when user edit the value after either onCompletion or onError is called. This is where app can reset the error if any.
   */
  onEditing: PropTypes.func,
  onKeyUp: PropTypes.func,
  ref: PropTypes.oneOfType([PropTypes.func, PropTypes.shape({ current: PropTypes.instanceOf(InputDateField) })]),

  /**
   * Initial date value
   */
  dateValue: PropTypes.instanceOf(Date),

  /**
   * Unique ID for the field. Required for web accessibility
   */
  id: PropTypes.string.isRequired,

  /**
   * Type of the input field. Allowed types: text, number, tel, email, password
   */
  type: PropTypes.oneOf(["number", "text", "tel", "email", "password"]).isRequired,

  /**
   * Set this prop to true to render input text mobile
   */
  isMobile: PropTypes.bool,

  /**
   * Label for the field
   */
  label: PropTypes.string,

  /**
   * Label for the field
   */
  secondaryLabel: PropTypes.string,

  /**
   *Inline styles to add additional styling in the parent container
CSSProperties
   */
  additionalStyle: PropTypes.object,
  /**
   * Classes to add additional styling in the parent container
string[]
   */

  additionalClassName: PropTypes.arrayOf(PropTypes.string),

  /**
   *Set this props to true to display loading spinner
   */
  isLoading: PropTypes.bool,

  /**
   Inline style to add additional styling in the message container

   */
  additionalMessageStyle: PropTypes.object,
  /**
   * Classes to add additional styling in the message container
   */

  additionalMessageClassName: PropTypes.arrayOf(PropTypes.string),

  /**
   * use this prop to enable / disable the component.
   */

  disabled: PropTypes.bool,

  /**
   * use this props to display a placeholder string in text input.
   */

  placeholder: PropTypes.string,

  /**
   * use this props to display a placeholder string in text input.
   */

  messageMode: PropTypes.oneOf(["error", "information", "success", "warning"]),

  /**
   * Message text content in input text.
   */

  msgText: PropTypes.string,

  /**
   * left content for input text. Note: Please give padding to input as same of left view
   */

  leftView: PropTypes.oneOfType([PropTypes.node, PropTypes.string]),

  /**
   * left content for input text. Note: Please give padding to input as same of left view
   */

  rightView: PropTypes.oneOfType([PropTypes.node, PropTypes.string]),

  /**
   Inline style to add additional styling in the left view container
   */
  additionalLeftViewStyle: PropTypes.object,
  /**
   * Classes to add additional styling in the left view container
   */

  additionalLeftViewClassName: PropTypes.arrayOf(PropTypes.string),

  /**
   Inline style to add additional styling in the right view container
   */
  additionalRightViewStyle: PropTypes.object,
  /**
   * Classes to add additional styling in the right view container
   */

  additionalRightViewClassName: PropTypes.arrayOf(PropTypes.string),

  /**
   * Blur event handler
   */
  onBlur: PropTypes.func,

  /**
   * Whether component is in mobile view
   */
  isMobileView: PropTypes.bool,

  /**
   * Placeholder text for the input
   */
  placeholder: PropTypes.string,

  /**
   * Left view component
   */
  leftView: PropTypes.node,

  /**
   * Right view component
   */
  rightView: PropTypes.node,

  /**
   * Date error callback with error code and value
   */
  onDateError: PropTypes.func,
};

export { InputDateField };
