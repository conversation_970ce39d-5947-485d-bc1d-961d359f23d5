import React, { useRef, useEffect, useState } from "react";
import PropTypes from "prop-types";
import {
  Container,
  InputContainer,
  StyledErrorLable,
  StyledInputText,
  StyledLabelText,
  StyledLeftView,
  StyledRightView,
} from "./InputDateField.styled";
import { subYears } from "date-fns";
import { formatWithLocale, whetherBetween } from "../../hooks/calendarHooks";
import { useClassName } from "../../hooks/useClassName";
import { ThemeWrapper } from "../Theme/ThemeContext";

const InputDateField = (props) => {
  const {
    id,
    type,
    label,
    minDate,
    maxDate,
    dateValue,
    disabled,
    isMobile,
    isMobileView,
    msgText,
    secondaryLabel,
    messageMode,
    additionalLeftViewClassName,
    additionalRightViewClassName,
    additionalLeftViewStyle,
    additionalRightViewStyle,
    additionalMessageClassName,
    additionalMessageStyle,
    additionalClassName,
    additionalStyle,
    onCompletion,
    onEditing,
    onDateError,
    inputType,
    onKeyUp,
    ref,
    placeholder,
    onBlur,
  } = props;
  const [inputDateVal, setInputDateval] = useState();
  const [isDateValid, SetIsDateValid] = useState(true);
  const leftVieClassName = useClassName(props, additionalLeftViewClassName);
  const rightVieClassName = useClassName(props, additionalRightViewClassName);
  const messageClassName = useClassName(props, additionalMessageClassName);
  const mainClassName = useClassName(props, additionalClassName);

  const componentRef = useRef(null);

  function handleBetween(number, calc) {
    let [a, b] = calc;
    let min = Math.min(a, b),
      max = Math.max(a, b);
    return number > min && number < max;
  }



  const onKeyPress = (e) => {
    let input = e.target;

    if (e.charCode < 47 || e.charCode > 57) {
      e.preventDefault();
    }
    var len = input.value.length;
    // If we're at a particular place, let the user type the slash
    if (len !== 1 || len !== 3) {
      if (e.charCode == 47) {
        e.preventDefault();
      }
    }
    // If they don't add the slash, do it for them...
    if (len === 2) {
      input.value += "/";
    }

    // If they don't add the slash, do it for them...
    if (len === 5) {
      input.value += "/";
    }
  };
  const checkDate = date => {
    const dateFormat = Date.parse(date)
    if (dateFormat > 0) {
      return true
    } else {
      return false
    }
  }

  useEffect(() => {
    if (dateValue && checkDate(dateValue)) {
      setInputDateval(formatWithLocale(dateValue, "dd/MM/yyyy"));
    }
  }, []);

  function isValidDate(str) {
    var parms = str.split(/[\.\-\/]/);
    var yyyy = parseInt(parms[2], 10);
    var mm = parseInt(parms[1], 10);
    var dd = parseInt(parms[0], 10);
    var date = new Date(yyyy, mm - 1, dd, 0, 0, 0, 0);
    return (
      mm === date.getMonth() + 1 &&
      dd === date.getDate() &&
      yyyy === date.getFullYear()
    );
  }

  const onDateChange = (e) => {
    setInputDateval(e.target.value);
    onKeyPress(e);
    if (!isValidDate(e.target.value) && e.target.value.length > 0) {
      SetIsDateValid(false);
    } else {
      SetIsDateValid(true);
    }

    if (e.target.value.length === 10) {
      onCompletion(e.target.value);
      const isBetween = whetherBetween(
        new Date(e.target.value),
        minDate,
        maxDate
      );
      if (isBetween && isValidDate(e.target.value)) {
        onDateError(0, e.target.value);
      } else {
        if (!isValidDate(e.target.value)) {
          onDateError(1, e.target.value);
        } else {
          onDateError(2, e.target.value);
        }
      }
    } else {
      onEditing(e.target.value);
    }
  };

  return (
    <ThemeWrapper isMobile={isMobile || isMobileView || false}>
      <Container id={id} style={additionalStyle} className={`${mainClassName} vms_inputdatefield_container`}>
        <StyledLabelText className="vms_inputdatefield_labeltext" disabled={disabled}>
          {label} <span>{secondaryLabel}</span>
        </StyledLabelText>
        <InputContainer className="vms_inputdatefield_inputcontainer">
          <StyledLeftView
            style={additionalLeftViewStyle}
            className={`${leftVieClassName} vms_inputdatefield_leftview`}
          >
            {props.leftView}
          </StyledLeftView>
          <StyledInputText
            className="vms_inputdatefield_inputtext"
            type={type}
            maxLength="10"
            placeholder={placeholder ? placeholder : "DD/MM/YYYY"}
            onKeyPress={(e) => onKeyPress(e)}
            ref={ref}
            onKeyUp={(e) => onKeyUp(e)}
            value={inputDateVal}
            valid={isDateValid}
            min={minDate}
            isMobile={isMobile}
            onChange={(e) => {
              onDateChange(e);
            }}
            inputType={inputType}
            disabled={disabled}
            max={maxDate}
            onBlur={onBlur}
            id={id ? id : null}
          />
          {!isDateValid && (
            <StyledErrorLable
              className={`${messageClassName} vms_inputdatefield_errorlable`}
              style={additionalMessageStyle}
              messageMode={messageMode}
            >
              {msgText}
            </StyledErrorLable>
          )}

          <StyledRightView
            style={additionalRightViewStyle}
            className={`${rightVieClassName} vms_inputdatefield_rightview`}
          >
            {props.rightView}
          </StyledRightView>
        </InputContainer>
      </Container>
    </ThemeWrapper>
  );
};

InputDateField.defaultProps = {
  label: "DOB",
  minDate: subYears(new Date(), 20),
  maxDate: new Date(),
  disabled: false,
  isMobile: false,
  msgText: "Date is invalid",
  onCompletion: () => { },
  onEditing: () => { },
  onDateError: () => { },
  onKeyUp: () => { },
  inputType: "primary",
  messageMode: "error",
};

InputDateField.propTypes = {
  /**
   * Variations of Button Type
   */
  inputType: PropTypes.oneOf(["primary", "secondary"]),

  /**
   * Minimum allowed date
   */
  minDate: PropTypes.instanceOf(Date),

  /**
   * Maximum allowed date
   */
  maxDate: PropTypes.instanceOf(Date),

  /**
   * onCompletion calls when user completed entering a valid date date is entered date by user in date format
   */
  onCompletion: PropTypes.func,

  /**
   * This method will be called when user edit the value after either onCompletion or onError is called. This is where app can reset the error if any.
   */
  onEditing: PropTypes.func,
  onKeyUp: PropTypes.func,
  ref: PropTypes.oneOfType([PropTypes.func, PropTypes.shape({ current: PropTypes.instanceOf(InputDateField) })]),

  /**
   * Initial date value
   */
  dateValue: PropTypes.instanceOf(Date),

  /**
   * Unique ID for the field. Required for web accessibility
   */
  id: PropTypes.string.isRequired,

  /**
   * Type of the input field. Allowed types: text, number, tel, email, password
   */
  type: PropTypes.oneOf(["number", "text", "tel", "email", "password"]).isRequired,

  /**
   * Set this prop to true to render input text mobile
   */
  isMobile: PropTypes.bool,

  /**
   * Label for the field
   */
  label: PropTypes.string,

  /**
   * Label for the field
   */
  secondaryLabel: PropTypes.string,

  /**
   *Inline styles to add additional styling in the parent container
CSSProperties
   */
  additionalStyle: PropTypes.object,
  /**
   * Classes to add additional styling in the parent container
string[]
   */

  additionalClassName: PropTypes.arrayOf(PropTypes.string),

  /**
   *Set this props to true to display loading spinner
   */
  isLoading: PropTypes.bool,

  /**
   Inline style to add additional styling in the message container

   */
  additionalMessageStyle: PropTypes.object,
  /**
   * Classes to add additional styling in the message container
   */

  additionalMessageClassName: PropTypes.arrayOf(PropTypes.string),

  /**
   * use this prop to enable / disable the component.
   */

  disabled: PropTypes.bool,

  /**
   * use this props to display a placeholder string in text input.
   */

  placeholder: PropTypes.string,

  /**
   * use this props to display a placeholder string in text input.
   */

  messageMode: PropTypes.oneOf(["error", "information", "success", "warning"]),

  /**
   * Message text content in input text.
   */

  msgText: PropTypes.string,

  /**
   * left content for input text. Note: Please give padding to input as same of left view
   */

  leftView: PropTypes.oneOfType([PropTypes.node, PropTypes.string]),

  /**
   * left content for input text. Note: Please give padding to input as same of left view
   */

  rightView: PropTypes.oneOfType([PropTypes.node, PropTypes.string]),

  /**
   Inline style to add additional styling in the left view container
   */
  additionalLeftViewStyle: PropTypes.object,
  /**
   * Classes to add additional styling in the left view container
   */

  additionalLeftViewClassName: PropTypes.arrayOf(PropTypes.string),

  /**
   Inline style to add additional styling in the right view container
   */
  additionalRightViewStyle: PropTypes.object,
  /**
   * Classes to add additional styling in the right view container
   */

  additionalRightViewClassName: PropTypes.arrayOf(PropTypes.string),
  onBlur: PropTypes.func,
};
export { InputDateField };
