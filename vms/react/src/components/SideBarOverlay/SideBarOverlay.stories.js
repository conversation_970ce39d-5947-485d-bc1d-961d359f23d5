import React, { useState } from "react";
import { SideBarOverlay } from "./SideBarOverlay";

export default {
    title: "VMS_REACT/SideBarOverlay",
    component: SideBarOverlay,
    argTypes: {
        onItemClick: { control: { type: "" } },
        onClose: { control: { type: "" } },
    },
  };

export const Default = () => {
    const [isOpen, setIsOpen] = useState(false);
  
    return (
      <div>
        <button onClick={() => setIsOpen(!isOpen)}>Toggle Sidebar</button>
        <SideBarOverlay
         isOpen={isOpen}
         width={"600px"}
         height={"100%"}
         bgColor={"#f1f1f1"}
         textColor={"#000"}
         direction={"right"}
         transition={"0.3s"}        
         shouldCloseOnOverlayClick={true}
         onClose={() => setIsOpen(false)}
         sliderBarCustomHeader={<h1>Custom Header</h1>}
            sliderBarCustomFooter={<h1>Custom Footer</h1>}
        >
            <h1>SideBar Content</h1>
            <p>Lorem ipsum dolor sit amet consectetur adipisicing elit. Odit enim numquam deleniti praesentium! Aliquid ab sequi ut modi. Error odit labore sapiente explicabo. Soluta, sed qui ipsum natus nihil repellendus?</p>
            </SideBarOverlay>
       
      </div>
    );
  };


