import React, { memo, useCallback, useMemo } from "react";
import PropTypes from "prop-types";
import { Overlay, SidebarContainer, SidebarContent, CloseButton, SiderBarCustomHeader, SiderBarCustomHeaderContent, SiidebarCustomFooter, Svg } from "./SideBarOverlay.styled.js";
import { useClassName } from "../../hooks/useClassName.js";
import { ThemeWrapper } from "../Theme/ThemeContext";

// Utility function (placed before main component)
const isNonNull = (prop) => prop !== null && prop !== undefined;

const SideBarOverlay = memo((props) => {
  // 1. PROP DESTRUCTURING & VALIDATION
  const {
    isOpen,
    width,
    height,
    bgColor,
    textColor,
    direction = "left",
    items,
    onItemClick,
    onClose,
    children,
    sliderBarCustomHeader,
    sliderBarCustomFooter,
    transition,
    shouldCloseOnOverlayClick = true,
    customCloseButton,
    additionalClassName,
    isMobile,
    isMobileView,
    ...otherProps
  } = props;

  // 2. PERFORMANCE OPTIMIZATIONS
  const computedClassName = useMemo(() => 
    useClassName(props, additionalClassName), 
    [props, additionalClassName]
  );

  const overlayClassName = useMemo(() => 
    useClassName(props, additionalClassName), 
    [props, additionalClassName]
  );

  const hasCustomHeader = useMemo(() => isNonNull(sliderBarCustomHeader), [sliderBarCustomHeader]);
  const hasCustomFooter = useMemo(() => isNonNull(sliderBarCustomFooter), [sliderBarCustomFooter]);
  const hasCustomCloseButton = useMemo(() => isNonNull(customCloseButton), [customCloseButton]);

  // 3. EVENT HANDLING with useCallback
  const handleOverlayClick = useCallback(() => {
    if (shouldCloseOnOverlayClick && isNonNull(onClose)) {
      onClose();
    }
  }, [shouldCloseOnOverlayClick, onClose]);

  const handleCloseClick = useCallback(() => {
    if (isNonNull(onClose)) {
      onClose();
    }
  }, [onClose]);

  const handleItemClick = useCallback((item, index) => {
    if (isNonNull(onItemClick)) {
      onItemClick(item, index);
    }
  }, [onItemClick]);

  const handleKeyDown = useCallback((event) => {
    if (event.key === 'Escape' && isNonNull(onClose)) {
      onClose();
    }
  }, [onClose]);

  // 4. CONDITIONAL RENDERING (memoized)
  const closeButtonElement = useMemo(() => {
    if (hasCustomCloseButton) {
      return customCloseButton;
    }
    
    return (
      <CloseButton 
        direction={direction} 
        className="vms_sidebar_overlay_close_btn" 
        onClick={handleCloseClick}
        aria-label="Close sidebar"
        role="button"
        tabIndex={0}
      >
        <CrossIcon />
      </CloseButton>
    );
  }, [hasCustomCloseButton, customCloseButton, direction, handleCloseClick]);

  const headerElement = useMemo(() => {
    if (!hasCustomHeader) {
      return closeButtonElement;
    }
    
    return (
      <SiderBarCustomHeader>
        <SiderBarCustomHeaderContent className="vms_sidebar_overlay_header">
          {sliderBarCustomHeader}
        </SiderBarCustomHeaderContent>
        {closeButtonElement}
      </SiderBarCustomHeader>
    );
  }, [hasCustomHeader, sliderBarCustomHeader, closeButtonElement]);

  const footerElement = useMemo(() => {
    if (!hasCustomFooter) return null;
    
    return (
      <SiidebarCustomFooter className="vms_sidebar_overlay_footer">
        {sliderBarCustomFooter}
      </SiidebarCustomFooter>
    );
  }, [hasCustomFooter, sliderBarCustomFooter]);

  const sidebarContent = useMemo(() => {
    if (!isOpen) return null;

    return (
      <>
        <Overlay 
          isOpen={isOpen} 
          className={`${overlayClassName} vms_sidebar_overlay`} 
          onClick={handleOverlayClick}
          role="presentation"
        />
        <SidebarContainer
          className={`${computedClassName} vms_sidebar_overlay_container`}
          isOpen={isOpen}
          width={width}
          height={height}
          bgColor={bgColor}
          transition={transition}
          textColor={textColor}
          direction={direction}
          role="dialog"
          aria-modal="true"
          aria-labelledby="sidebar-header"
          onKeyDown={handleKeyDown}
          tabIndex={-1}
          {...otherProps}
        >
          {headerElement}
          <SidebarContent>
            {children}
          </SidebarContent>
          {footerElement}
        </SidebarContainer>
      </>
    );
  }, [
    isOpen,
    overlayClassName,
    handleOverlayClick,
    computedClassName,
    width,
    height,
    bgColor,
    transition,
    textColor,
    direction,
    handleKeyDown,
    headerElement,
    children,
    footerElement,
    otherProps
  ]);

  // 5. ERROR HANDLING
  React.useEffect(() => {
    if (!isNonNull(onClose)) {
      console.warn('SideBarOverlay: onClose callback is required for proper functionality');
    }
  }, [onClose]);

  return (
    <ThemeWrapper isMobile={isMobile || isMobileView || false}>
      {sidebarContent}
    </ThemeWrapper>
  );
});

// Memoized Cross Icon component
const CrossIcon = memo(() => (
  <Svg
    x="0px"
    y="0px"
    width="24px"
    height="24px"
    viewBox="364 364 24 24"
    role="img"
    aria-hidden="true"
  >
    <g>
      <path
        fill="currentColor"
        d="M386,374.4l-7.6-7.6c-0.5-0.5-1.3-0.5-1.8,0l-0.5,0.5c-0.5,0.5-0.5,1.3,0,1.8l7.6,7.6l-7.6,7.6
        c-0.5,0.5-0.5,1.3,0,1.8l0.5,0.5c0.5,0.5,1.3,0.5,1.8,0l7.6-7.6l7.6,7.6c0.5,0.5,1.3,0.5,1.8,0l0.5-0.5
        c0.5-0.5,0.5-1.3,0-1.8L386,374.4z"
      />
    </g>
  </Svg>
));

// Component display names for debugging
SideBarOverlay.displayName = 'SideBarOverlay';
CrossIcon.displayName = 'CrossIcon';

export { SideBarOverlay };