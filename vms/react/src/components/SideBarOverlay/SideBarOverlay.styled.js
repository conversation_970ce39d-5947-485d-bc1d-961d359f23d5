import styled from "styled-components";

export const Overlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  display: ${(props) => (props.isOpen ? "block" : "none")};
  z-index: 998;
`;

export const SidebarContainer = styled.div`
  width: ${(props) => (props.direction === "top" || props.direction === "bottom" ? "100vw" : props.width || "100vw")};
  height: ${(props) => (props.direction === "top" || props.direction === "bottom" ? props.height || "100vh" : "100vh")};
  background-color: ${(props) => props.bgColor || "#333"};
  color: ${(props) => props.textColor || "white"};
  display: flex;
  flex-direction: column;
  transition: all ${(props) => props.transition || "0.3s"} ease;
  position: fixed;
  z-index: 999;
  box-sizing: border-box;
  ${(props) => {
    switch (props.direction) {
      case "right":
        return `top: 0; right: ${props.isOpen ? "0" : `-${props.width || "100vw"} ;`}`;
      case "left":
        return `top: 0; left: ${props.isOpen ? "0" : `-${props.width || "100vw"}`}`;
      case "top":
        return `left: 0; top: ${props.isOpen ? "0" : `-${props.height || "100vh"}`}`;
      case "bottom":
        return `left: 0; bottom: ${props.isOpen ? "0" : `-${props.height || "100vh"}`}`;
      default:
        return "left: 0; top: 0;";
    }
  }};
`;

export const CloseButton = styled.button`
  position: absolute;
  top: 10px;
  ${(props) => (props.direction === "right" ? "left: 10px;" : "right: 10px;")}
  color: white;
  border: none;
  padding: 5px 10px;
  cursor: pointer;
`;

export const SidebarContent = styled.div`
  width: 100%;
  height: 100%;
  overflow-y: auto;
  padding: 20px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
`;
 export const SiderBarCustomHeader = styled.div`
 display: flex;
 flex-direction: row;
 align-items: center;
 justify-content: space-between;
  border-bottom: 1px solid #ccc;
  background: #f1f1f1;
  color: #000;
 `;

export const SiderBarCustomHeaderContent = styled.div`
  
`;
export const SiidebarCustomFooter = styled.div`
border-top: 1px solid #ccc;
padding: 10px;
`;

export const Svg = styled.svg`
  color: ${({ theme }) => theme?.palette?.primary[theme.mode]};
  width: 16px;
  height: 16px;
  cursor: pointer;
`;