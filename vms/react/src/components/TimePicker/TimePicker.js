import React, { useRef, useState, useEffect, memo, useCallback, useMemo } from "react";
import "./TimePicker.css";
import { InputText } from "../InputText/InputText";
import {
  Container,
  DropDownContainer,
  MainWrapperContainer,
  OptionContainer,
  OptionsWrapper,
  RightIconView,
  StyledRightView,
  Svg,
  TimePickerOption,
  TimePickerOptions,
  BottomBarContainer,
  ActionContainer,
} from "./TimePicker.styled";
import { Button } from "../Button/Button";
import PropTypes from "prop-types";
import { useClassName } from "../../hooks/useClassName";
import {
  TWELVE_LIST,
  MERDIAN_LIST,
  SIXTY_LIST,
  SIXTY_MAP,
  MERIDIAN_MAP,
  TWELVE_MAP,
  TWENTY_FOUR_LIST,
} from "./Constant";
import { ThemeWrapper } from "../Theme/ThemeContext";

const TimePicker = memo((props) => {
  // 1. PROP DESTRUCTURING & VALIDATION
  const {
    placeholder,
    showCloseIcon = true,
    showClockIcon = true,
    useTwelveHourFormat = false,
    timePickerType = "primary",
    defaultValue,
    onTimeChange,
    onInputChange,
    additionalClassName,
    additionalStyle,
    isMobile,
    isMobileView,
    id,
    ...otherProps
  } = props;

  // 2. STATE MANAGEMENT
  const [selectedText, setSelectedText] = useState("");
  const [displayText, setDisplayText] = useState("");
  const [showDropdown, setShowDropdown] = useState(false);
  const [hour, setHour] = useState(null);
  const [minute, setMinute] = useState(null);
  const [second, setSecond] = useState(null);
  const [meridian, setMeridian] = useState(null);

  // 3. REFS
  const hourWrapper = useRef(null);
  const minuteWrapper = useRef(null);
  const secondWrapper = useRef(null);
  const meridianWrapper = useRef(null);
  const inputRef = useRef(null);

  // 4. PERFORMANCE OPTIMIZATIONS
  const computedClassName = useMemo(() => 
    useClassName(props, additionalClassName), 
    [props, additionalClassName]
  );

  const computedPlaceholder = useMemo(() => 
    placeholder || (useTwelveHourFormat ? "00 : 00 : 00 am" : "00 : 00 : 00"),
    [placeholder, useTwelveHourFormat]
  );

  // 5. UTILITY FUNCTIONS
  const checkIfNumber = useCallback((value) => {
    const exp = /^\d+$/;
    return exp.test(value.trim());
  }, []);

  const getTimeValue = useCallback((val) => {
    let value;
    let currentText = val.split(":");
    let h, m, s, a, hour, minute, second, meridian;

    if (
      useTwelveHourFormat &&
      currentText.length === 3 &&
      (currentText[2]?.toLowerCase()?.includes("am") ||
        currentText[2]?.toLowerCase()?.includes("pm"))
    ) {
      let merd = "am";
      if (currentText[2]?.toLowerCase()?.includes("pm")) merd = "pm";
      let secondText = currentText[2].toLowerCase().replace(merd, "");

      // Process hour
      if (
        checkIfNumber(currentText[0]) &&
        0 < Number(currentText[0]) &&
        Number(currentText[0]) <= 12
      ) {
        h = currentText[0];
        hour = TWELVE_MAP[currentText[0].trim()]?.value
          ? TWELVE_MAP[currentText[0].trim()]
          : null;
      }

      // Process minute
      if (
        checkIfNumber(currentText[1]) &&
        0 <= Number(currentText[1]) &&
        Number(currentText[1]) <= 59
      ) {
        m = currentText[1];
        minute = SIXTY_MAP[currentText[1].trim()]?.value
          ? SIXTY_MAP[currentText[1].trim()]
          : null;
      }

      // Process second
      if (
        checkIfNumber(secondText) &&
        0 <= Number(secondText) &&
        Number(secondText) <= 59
      ) {
        s = secondText;
        second = SIXTY_MAP[secondText.trim()]?.value
          ? SIXTY_MAP[secondText.trim()]
          : null;
      }

      // Process meridian
      if (MERIDIAN_MAP[merd]) {
        a = merd;
        meridian = MERIDIAN_MAP[merd.trim()]?.value
          ? MERIDIAN_MAP[merd.trim()]
          : null;
      }

      if (hour && minute && second && meridian) {
        setHour(hour);
        setMinute(minute);
        setSecond(second);
        setMeridian(meridian);
        value = `${h} : ${m} : ${s} ${a}`;
      } else {
        value = selectedText;
        setDisplayText(value);
      }
    } else if (!useTwelveHourFormat && currentText.length === 3) {
      // 24-hour format processing
      if (
        checkIfNumber(currentText[0]) &&
        0 <= Number(currentText[0]) &&
        Number(currentText[0]) <= 23
      ) {
        h = currentText[0];
        hour = SIXTY_MAP[currentText[0].trim()]?.value
          ? SIXTY_MAP[currentText[0].trim()]
          : null;
      }
      if (
        checkIfNumber(currentText[1]) &&
        0 <= Number(currentText[1]) &&
        Number(currentText[1]) <= 59
      ) {
        m = currentText[1];
        minute = SIXTY_MAP[currentText[1].trim()]?.value
          ? SIXTY_MAP[currentText[1].trim()]
          : null;
      }
      if (
        checkIfNumber(currentText[2]) &&
        0 <= Number(currentText[2]) &&
        Number(currentText[2]) <= 59
      ) {
        s = currentText[2];
        second = SIXTY_MAP[currentText[2].trim()]?.value
          ? SIXTY_MAP[currentText[2].trim()]
          : null;
      }

      if (hour && minute && second) {
        setHour(hour);
        setMinute(minute);
        setSecond(second);
        value = `${h} : ${m} : ${s}`;
      } else {
        value = selectedText;
        setDisplayText(value);
      }
    } else {
      value = selectedText;
      setDisplayText(value);
    }
    return value;
  }, [useTwelveHourFormat, checkIfNumber, selectedText]);

  // 6. EVENT HANDLERS
  const changeHandler = useCallback((e) => {
    const value = e.target.value;
    setDisplayText(value);
  }, []);

  const applyChanges = useCallback(() => {
    let value = displayText;
    if (selectedText !== displayText) {
      value = getTimeValue(displayText);
    }
    try {
      if (value && onTimeChange) {
        onTimeChange(value);
      } else if (onTimeChange) {
        onTimeChange(null);
      }
    } catch (error) {
      console.error("TimePicker: Error in onTimeChange callback", error);
    }
  }, [displayText, selectedText, getTimeValue, onTimeChange]);

  const handleCloseIconClick = useCallback(() => {
    setShowDropdown(false);
    setHour(null);
    setMinute(null);
    setSecond(null);
    setMeridian(null);
    setSelectedText("");
    setDisplayText("");
    try {
      if (onTimeChange) {
        onTimeChange(null);
      }
    } catch (error) {
      console.error("TimePicker: Error in close callback", error);
    }
  }, [onTimeChange]);

  const handleClockIconClick = useCallback(() => {
    if (showDropdown) {
      applyChanges();
      setShowDropdown(false);
    } else {
      inputRef.current?.focus();
    }
  }, [showDropdown, applyChanges]);

  const handleButtonClick = useCallback(() => {
    applyChanges();
    setShowDropdown(false);
  }, [applyChanges]);

  const onFocusHandler = useCallback((e) => {
    setShowDropdown(true);
  }, []);

  // Keyboard navigation handlers
  const keyDownHandler = useCallback((e) => {
    if (!hourWrapper.current) return;
    
    const hoursWrapper = hourWrapper.current;
    const hours = hoursWrapper.children;
    const hrIndex = hour?.value ? hour.index : -1;
    
    if (e.keyCode === 40 || e.key === "ArrowDown") {
      e.preventDefault();
      const newIndex = hrIndex === -1 ? 0 : hrIndex;
      if (hours[newIndex]) {
        hours[newIndex].focus();
        setHour(
          useTwelveHourFormat ? TWELVE_LIST[newIndex] : TWENTY_FOUR_LIST[newIndex]
        );
      }
    } else if (e.keyCode === 38 || e.key === "ArrowUp") {
      e.preventDefault();
      const newIndex = hrIndex === -1 ? hours.length - 1 : hrIndex - 1;
      if (hours[newIndex]) {
        hours[newIndex].focus();
        setHour(
          useTwelveHourFormat ? TWELVE_LIST[newIndex] : TWENTY_FOUR_LIST[newIndex]
        );
      }
    } else if (e.keyCode === 13 || e.key === "Enter" || e.keyCode === 27 || e.key === "Escape") {
      applyChanges();
      setShowDropdown(false);
    }
  }, [hour, useTwelveHourFormat]);

  const setOptionValue = useCallback((e, key, option) => {
    if (!hourWrapper.current || !minuteWrapper.current || !secondWrapper.current) return;
    
    const hoursWrapper = hourWrapper.current;
    const minutesWrapper = minuteWrapper.current;
    const secondsWrapper = secondWrapper.current;
    const meridiansWrapper = meridianWrapper?.current;
    const hours = hoursWrapper.children;
    const minutes = minutesWrapper.children;
    const seconds = secondsWrapper.children;
    const meridians = meridiansWrapper?.children;
    const optionIndex = option.index;

    switch (key) {
      case "h": {
        if (hours[optionIndex]) {
          hours[optionIndex].focus();
          setHour(
            useTwelveHourFormat
              ? TWELVE_LIST[optionIndex]
              : TWENTY_FOUR_LIST[optionIndex]
          );
        }
        break;
      }
      case "m": {
        if (minutes[optionIndex]) {
          minutes[optionIndex].focus();
          setMinute(SIXTY_LIST[optionIndex]);
        }
        break;
      }
      case "s": {
        if (seconds[optionIndex]) {
          seconds[optionIndex].focus();
          setSecond(SIXTY_LIST[optionIndex]);
        }
        break;
      }
      case "a": {
        if (meridians && meridians[optionIndex]) {
          meridians[optionIndex].focus();
          setMeridian(MERDIAN_LIST[optionIndex]);
        }
        break;
      }
    }
  }, [useTwelveHourFormat]);

  // Create key down handlers for each time component
  const hourKeyDownHandler = useCallback((e, option) => {
    if (e.keyCode === 40 || e.key === "ArrowDown") {
      e.preventDefault();
      const newIndex = option.index === -1 ? 0 : option.index;
      if (hourWrapper.current.children[newIndex]) {
        hourWrapper.current.children[newIndex].focus();
        setHour(
          useTwelveHourFormat ? TWELVE_LIST[newIndex] : TWENTY_FOUR_LIST[newIndex]
        );
      }
    } else if (e.keyCode === 38 || e.key === "ArrowUp") {
      e.preventDefault();
      const newIndex = option.index === -1 ? hourWrapper.current.children.length - 1 : option.index - 1;
      if (hourWrapper.current.children[newIndex]) {
        hourWrapper.current.children[newIndex].focus();
        setHour(
          useTwelveHourFormat ? TWELVE_LIST[newIndex] : TWENTY_FOUR_LIST[newIndex]
        );
      }
    } else if (e.keyCode === 13 || e.key === "Enter" || e.keyCode === 27 || e.key === "Escape") {
      applyChanges();
      setShowDropdown(false);
    }
  }, [applyChanges, hour, useTwelveHourFormat]);

  const minKeyDownHandler = useCallback((e, option) => {
    if (e.keyCode === 40 || e.key === "ArrowDown") {
      e.preventDefault();
      const newIndex = option.index === -1 ? 0 : option.index;
      if (minuteWrapper.current.children[newIndex]) {
        minuteWrapper.current.children[newIndex].focus();
        setMinute(SIXTY_LIST[newIndex]);
      }
    } else if (e.keyCode === 38 || e.key === "ArrowUp") {
      e.preventDefault();
      const newIndex = option.index === -1 ? minuteWrapper.current.children.length - 1 : option.index - 1;
      if (minuteWrapper.current.children[newIndex]) {
        minuteWrapper.current.children[newIndex].focus();
        setMinute(SIXTY_LIST[newIndex]);
      }
    } else if (e.keyCode === 13 || e.key === "Enter" || e.keyCode === 27 || e.key === "Escape") {
      applyChanges();
      setShowDropdown(false);
    }
  }, [applyChanges]);

  const secKeyDownHandler = useCallback((e, option) => {
    if (e.keyCode === 40 || e.key === "ArrowDown") {
      e.preventDefault();
      const newIndex = option.index === -1 ? 0 : option.index;
      if (secondWrapper.current.children[newIndex]) {
        secondWrapper.current.children[newIndex].focus();
        setSecond(SIXTY_LIST[newIndex]);
      }
    } else if (e.keyCode === 38 || e.key === "ArrowUp") {
      e.preventDefault();
      const newIndex = option.index === -1 ? secondWrapper.current.children.length - 1 : option.index - 1;
      if (secondWrapper.current.children[newIndex]) {
        secondWrapper.current.children[newIndex].focus();
        setSecond(SIXTY_LIST[newIndex]);
      }
    } else if (e.keyCode === 13 || e.key === "Enter" || e.keyCode === 27 || e.key === "Escape") {
      applyChanges();
      setShowDropdown(false);
    }
  }, [applyChanges]);

  const merdKeyDownHandler = useCallback((e, option) => {
    if (e.keyCode === 40 || e.key === "ArrowDown") {
      e.preventDefault();
      const newIndex = option.index === -1 ? 0 : option.index;
      if (meridianWrapper.current.children[newIndex]) {
        meridianWrapper.current.children[newIndex].focus();
        setMeridian(MERDIAN_LIST[newIndex]);
      }
    } else if (e.keyCode === 38 || e.key === "ArrowUp") {
      e.preventDefault();
      const newIndex = option.index === -1 ? meridianWrapper.current.children.length - 1 : option.index - 1;
      if (meridianWrapper.current.children[newIndex]) {
        meridianWrapper.current.children[newIndex].focus();
        setMeridian(MERDIAN_LIST[newIndex]);
      }
    } else if (e.keyCode === 13 || e.key === "Enter" || e.keyCode === 27 || e.key === "Escape") {
      applyChanges();
      setShowDropdown(false);
    }
  }, [applyChanges]);

  // 7. EFFECTS
  useEffect(() => {
    if (defaultValue) {
      getTimeValue(defaultValue);
    }
  }, [defaultValue, getTimeValue]);

  useEffect(() => {
    const h = useTwelveHourFormat ? "01" : "00";
    const m = "00";
    const s = "00";
    const a = "am";
    
    const finalH = hour?.value ? hour.label : h;
    const finalM = minute?.value ? minute.label : m;
    const finalS = second?.value ? second.label : s;
    const finalA = meridian?.value ? meridian.label : a;
    
    if (hour || minute || second || meridian) {
      const value = `${finalH} : ${finalM} : ${finalS}${useTwelveHourFormat ? ` ${finalA}` : ""}`;
      setSelectedText(value);
      setDisplayText(value);
      try {
        if (onInputChange) {
          onInputChange(value);
        }
      } catch (error) {
        console.error("TimePicker: Error in onInputChange callback", error);
      }
    }
  }, [hour, minute, second, meridian, useTwelveHourFormat, onInputChange]);

  // 8. CONDITIONAL RENDERING (memoized)
  const rightViewContent = useMemo(() => (
    <StyledRightView>
      {showCloseIcon && (
        <RightIconView onClick={handleCloseIconClick} role="button" aria-label="Clear time">
          <CrossIcon />
        </RightIconView>
      )}
      {showClockIcon && (
        <RightIconView onClick={handleClockIconClick} role="button" aria-label="Open time picker">
          <ClockIcon />
        </RightIconView>
      )}
    </StyledRightView>
  ), [showCloseIcon, showClockIcon, handleCloseIconClick, handleClockIconClick]);

  const dropdownContent = useMemo(() => {
    if (!showDropdown) return null;

    return (
      <DropDownContainer
        type="primary"
        className="vms_TimePicker_DropDownContainer"
        showDropdown={showDropdown}
        role="dialog"
        aria-label="Time picker options"
      >
        <OptionContainer className="vms_TimePicker_OptionContainer">
          {/* Hour Options */}
          <OptionsWrapper className="vms_TimePicker_OptionsWrapper dropDownContainer">
            <TimePickerOptions
              ref={hourWrapper}
              className="vms_TimePicker_OptionsWrapper"
              role="listbox"
              aria-label="Hours"
            >
              {(useTwelveHourFormat ? TWELVE_LIST : TWENTY_FOUR_LIST).map((option) => (
                <TimePickerOption
                  selected={hour?.value && hour.index === option.index}
                  type={timePickerType}
                  className="vms_TimePicker_TimePickerOption"
                  key={`hour-option-${option.value}`}
                  tabIndex="0"
                  data-target-index={option.index}
                  onKeyDown={(e) => hourKeyDownHandler(e, option)}
                  onClick={(e) => setOptionValue(e, "h", option)}
                  role="option"
                  aria-selected={hour?.value && hour.index === option.index}
                >
                  {option.label}
                </TimePickerOption>
              ))}
            </TimePickerOptions>
          </OptionsWrapper>

          {/* Minute Options */}
          <OptionsWrapper className="vms_TimePicker_OptionsWrapper dropDownContainer">
            <TimePickerOptions
              ref={minuteWrapper}
              className="vms_TimePicker_OptionsWrapper"
              role="listbox"
              aria-label="Minutes"
            >
              {SIXTY_LIST.map((option) => (
                <TimePickerOption
                  selected={minute?.value && minute.index === option.index}
                  type={timePickerType}
                  className="vms_TimePicker_TimePickerOption"
                  key={`min-option-${option.value}`}
                  tabIndex="0"
                  data-target-index={option.index}
                  onKeyDown={(e) => minKeyDownHandler(e, option)}
                  onClick={(e) => setOptionValue(e, "m", option)}
                  role="option"
                  aria-selected={minute?.value && minute.index === option.index}
                >
                  {option.label}
                </TimePickerOption>
              ))}
            </TimePickerOptions>
          </OptionsWrapper>

          {/* Second Options */}
          <OptionsWrapper className="vms_TimePicker_OptionsWrapper dropDownContainer">
            <TimePickerOptions
              ref={secondWrapper}
              className="vms_TimePicker_OptionsWrapper"
              role="listbox"
              aria-label="Seconds"
            >
              {SIXTY_LIST.map((option) => (
                <TimePickerOption
                  selected={second?.value && second.index === option.index}
                  type={timePickerType}
                  className="vms_TimePicker_TimePickerOption"
                  key={`sec-option-${option.value}`}
                  tabIndex="0"
                  data-target-index={option.index}
                  onKeyDown={(e) => secKeyDownHandler(e, option)}
                  onClick={(e) => setOptionValue(e, "s", option)}
                  role="option"
                  aria-selected={second?.value && second.index === option.index}
                >
                  {option.label}
                </TimePickerOption>
              ))}
            </TimePickerOptions>
          </OptionsWrapper>

          {/* Meridian Options (12-hour format only) */}
          {useTwelveHourFormat && (
            <OptionsWrapper className="dropDownContainer">
              <TimePickerOptions 
                ref={meridianWrapper}
                role="listbox"
                aria-label="AM/PM"
              >
                {MERDIAN_LIST.map((option) => (
                  <TimePickerOption
                    selected={meridian?.value && meridian.index === option.index}
                    type={timePickerType}
                    className="vms_TimePicker_TimePickerOption"
                    key={`merd-option-${option.value}`}
                    tabIndex="0"
                    data-target-index={option.index}
                    onKeyDown={(e) => merdKeyDownHandler(e, option)}
                    onClick={(e) => setOptionValue(e, "a", option)}
                    role="option"
                    aria-selected={meridian?.value && meridian.index === option.index}
                  >
                    {option.label}
                  </TimePickerOption>
                ))}
              </TimePickerOptions>
            </OptionsWrapper>
          )}
        </OptionContainer>

        <BottomBarContainer className="vms_TimePicker_BottomBarContainer tp_bottomBarWrapper" />

        <ActionContainer>
          <Button
            additionalClassName={["tp_confirmBtn"]}
            onClick={handleButtonClick}
            aria-label="Confirm time selection"
          >
            OK
          </Button>
        </ActionContainer>
      </DropDownContainer>
    );
  }, [
    showDropdown,
    timePickerType,
    useTwelveHourFormat,
    hour,
    minute,
    second,
    meridian,
    hourKeyDownHandler,
    minKeyDownHandler,
    secKeyDownHandler,
    merdKeyDownHandler,
    setOptionValue,
    handleButtonClick
  ]);

  // 9. ERROR HANDLING
  React.useEffect(() => {
    if (!id) {
      console.warn('TimePicker: id prop is recommended for accessibility');
    }
  }, [id]);

  return (
    <ThemeWrapper isMobile={isMobile || isMobileView || false}>
      <Container
        className={`vms_TimePicker_container ${computedClassName}`}
        style={additionalStyle}
        {...otherProps}
      >
        <MainWrapperContainer className="vms_TimePicker_wrapper">
          <div>
            <InputText
              type="text"
              value={displayText}
              ref={inputRef}
              onChange={changeHandler}
              onKeyDown={keyDownHandler}
              placeholder={computedPlaceholder}
              onFocus={onFocusHandler}
              rightView={rightViewContent}
              id={id}
              aria-label="Time input"
              aria-expanded={showDropdown}
              aria-haspopup="dialog"
            />
          </div>
          {dropdownContent}
        </MainWrapperContainer>
      </Container>
    </ThemeWrapper>
  );
});

// Memoized icon components
const CrossIcon = memo(() => (
  <Svg role="img" aria-hidden="true">
    <path
      d="M15.8333 5.34166L14.6583 4.16666L9.99999 8.825L5.34166 4.16666L4.16666 5.34166L8.82499 10L4.16666 14.6583L5.34166 15.8333L9.99999 11.175L14.6583 15.8333L15.8333 14.6583L11.175 10L15.8333 5.34166Z"
      fill="#323232"
    />
  </Svg>
));

const ClockIcon = memo(() => (
  <Svg
    className="tp_icon"
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    role="img"
    aria-hidden="true"
  >
    <path
      d="M9.99166 1.66666C5.39166 1.66666 1.66666 5.4 1.66666 10C1.66666 14.6 5.39166 18.3333 9.99166 18.3333C14.6 18.3333 18.3333 14.6 18.3333 10C18.3333 5.4 14.6 1.66666 9.99166 1.66666ZM9.99999 16.6667C6.31666 16.6667 3.33332 13.6833 3.33332 10C3.33332 6.31666 6.31666 3.33333 9.99999 3.33333C13.6833 3.33333 16.6667 6.31666 16.6667 10C16.6667 13.6833 13.6833 16.6667 9.99999 16.6667ZM10.4167 5.83333H9.16666V10.4167L13.125 12.7L13.8333 11.6417L10.4167 9.70833V5.83333Z"
      fill="#323232"
    />
  </Svg>
));

// Component display names for debugging
TimePicker.displayName = 'TimePicker';
CrossIcon.displayName = 'CrossIcon';
ClockIcon.displayName = 'ClockIcon';

export { TimePicker };
