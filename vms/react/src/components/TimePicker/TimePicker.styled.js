import styled from "styled-components";

export const Container = styled.div`
  display: flex;
  height: max-content;
`;

export const MainWrapperContainer = styled.div`
  position: relative;
  display: flex;
  height: max-content;
  z-index: 2;
`;

export const DropDownContainer = styled.div`
  padding: ${({ showDropdown }) => (showDropdown ? "12px" : 0)};
  opacity: ${({ showDropdown }) => (showDropdown ? 1 : 0)};
  height: ${({ showDropdown }) => (showDropdown ? "max-content" : 0)};
  width: max-content;
  overflow: hidden;
  pointer-events: ${({ showDropdown }) => (showDropdown ? "all" : "none")};
  position: absolute;
  top: 20px;
  transform: translateY(10%);
  width: 100%;
  z-index: 2;
  border: ${({ theme, type, isMobile }) =>
    `1px solid  ${theme?.palette?.[type]?.main}` || ""};
  display: flex;
  flex-direction: column;
  align-items: stretch;
  background: #ffffff;
  transition: 0.3s ease;
  box-shadow: ${({ showDropdown }) =>
    showDropdown
      ? `4px 4px 16px rgba(0, 7, 9, 0.1)`
      : `4px 4px 16px rgba(0, 7, 9, 0)`};
`;

export const StyledRightView = styled.div`
  display: flex;
  align-items: stretch;
  width: max-content;
  height: max-content;
  margin-top: 15px;
`;

export const RightIconView = styled.div`
  padding-right: 8px;
  display: flex;
  align-items: center;
`;

export const Svg = styled.svg`
  width: 24px;
  height: 24px;
  cursor: pointer;
`;

export const OptionContainer = styled.div`
  display: flex;
  align-items: flex-start;
  padding: 0 8px;
  padding-bottom: 12px;
`;

export const OptionsWrapper = styled.div`
  flex: 1 1 auto;
  height: calc(56px + 9.8rem);
  overflow: auto;
`;

export const TimePickerOptions = styled.div`
  width: 100%;
`;

export const TimePickerOption = styled.div`
  padding: 4px 12px;
  font-size: 1rem;
  line-height: 1.4rem;
  transition: 0.3s cubic-bezier(0, 0, 0, 0.9);
  background-color: ${({ selected,theme,type }) => (selected ? `${theme?.palette?.[type]?.main}` : "#fff0")};
  color: ${({ selected }) => (selected ? `#fff` : "")};
  outline: none;
  border-radius: 30px;
  cursor: pointer;
`;

export const BottomBarContainer = styled.div`
  padding: 0 8px;
  border-top: 1px solid #d7d7d7;
`;

export const ActionContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding: 8px;
  padding-top: 12px;
`;
