import React, { useState } from "react";
import { TimePicker } from "./TimePicker";

export default {
  title: "VMS_REACT/TimePicker",
  component: TimePicker,
  argTypes: {
    additionalStyle: { control: { type: "" } },
    additionalClassName: { control: { type: "" } },
  },
};

// const Template = (args) => <TimePicker {...args}></TimePicker>;

const Template = (args) => {
  return (
    <div>
      {/* <TimePicker {id: "",...args}/> */}
      <TimePicker {...{ id: "timepicker-id", ...args }} />

    </div>
  );
};

const OnChangeView = (args) => {
  return (
    <div>
      <TimePicker
        defaultValue="10 : 00: 00 am"
        placeholder="hh : mm : ss a"
        useTwelveHourFormat
        showClockIcon
        showCloseIcon
        onInputChange={(value)=>{console.log("value is from stories is",value)}}
        onTimeChange={(value) => console.log("value is from stories is",value)}
        id={"timepicker-id-oc"}
      />
    </div>
  );
};

export const Default = Template.bind({});
Default.args = {
  defaultValue:"10 : 00: 00 am",
  placeholder:"hh : mm : ss a",
  useTwelveHourFormat:true,
  showClockIcon:true,
  showCloseIcon:true
};

export const OnChangeEvent = OnChangeView.bind({});
OnChangeEvent.args = {
  defaultValue:"10 : 00: 00 am",
  placeholder:"hh : mm : ss a",
  useTwelveHourFormat:true,
  showClockIcon:true,
  showCloseIcon:true
};
