import React, { useRef, useState, memo, useCallback, useMemo } from "react";
import PropTypes from "prop-types";
import {
  TooltipWrapper,
  TooltipTarget,
  TooltipContainer,
  TooltipBox,
} from "./ToolTip.styled";
import { useClassName } from "../../hooks/useClassName";
import { ThemeWrapper } from "../Theme/ThemeContext";

// Utility function (placed before main component)
const isNonNull = (prop) => prop !== null && prop !== undefined;

export const ToolTip = memo((props) => {
  // 1. PROP DESTRUCTURING & VALIDATION
  const {
    id,
    additionalClassName,
    children,
    title,
    position = "bottom",
    background = "#000",
    arrow = true,
    additionalStyle,
    isMobile,
    isMobileView,
    ...otherProps
  } = props;

  // 2. STATE MANAGEMENT
  const [isHovered, setIsHovered] = useState(false);
  const [isFocused, setIsFocused] = useState(false);
  const targetRef = useRef(null);

  // 3. PERFORMANCE OPTIMIZATIONS
  const computedClassName = useMemo(
    () => useClassName(props, additionalClassName),
    [props, additionalClassName]
  );

  const showTooltip = useMemo(
    () => isHovered || isFocused,
    [isHovered, isFocused]
  );

  const tooltipId = useMemo(
    () => id || `tooltip-${Math.random().toString(36).substr(2, 9)}`,
    [id]
  );

  // 4. EVENT HANDLING with useCallback
  const handleMouseEnter = useCallback(() => {
    setIsHovered(true);
  }, []);

  const handleMouseLeave = useCallback(() => {
    setIsHovered(false);
  }, []);

  const handleFocus = useCallback(() => {
    setIsFocused(true);
  }, []);

  const handleBlur = useCallback(() => {
    setIsFocused(false);
  }, []);

  const handleClick = useCallback(
    (e) => {
      e.stopPropagation();
      if (targetRef.current) {
        targetRef.current.blur();
      }
    },
    [targetRef]
  );

  const handleKeyDown = useCallback(
    (event) => {
      if (event.key === "Escape") {
        setIsFocused(false);
        setIsHovered(false);
        if (targetRef.current) {
          targetRef.current.blur();
        }
      }
    },
    [targetRef]
  );

  // 5. CONDITIONAL RENDERING (memoized)
  const tooltipContent = useMemo(() => {
    if (!showTooltip || !isNonNull(title)) return null;

    return (
      <TooltipContainer
        className="vms_tooltip_container"
        position={position}
        role="tooltip"
        aria-hidden={!showTooltip}
      >
        <TooltipBox
          className="vms_tooltip_box"
          background={background}
          position={position}
          arrow={arrow}
          id={tooltipId}
          role="tooltip"
          aria-live="polite"
        >
          {title}
        </TooltipBox>
      </TooltipContainer>
    );
  }, [showTooltip, title, position, background, arrow, tooltipId]);

  // 6. ERROR HANDLING
  React.useEffect(() => {
    if (!isNonNull(title)) {
      console.warn("ToolTip: title prop is required for tooltip content");
    }
    if (!children) {
      console.warn("ToolTip: children prop is required for tooltip target");
    }
  }, [title, children]);

  return (
    <ThemeWrapper isMobile={isMobile || isMobileView || false}>
      <TooltipWrapper
        id={id}
        className={`${computedClassName} vms_tooltip_wrapper`}
        style={additionalStyle}
        {...otherProps}
      >
        <TooltipTarget
          className="vms_tooltip_target"
          onMouseEnter={handleMouseEnter}
          onMouseLeave={handleMouseLeave}
          onFocus={handleFocus}
          onBlur={handleBlur}
          onClick={handleClick}
          onKeyDown={handleKeyDown}
          ref={targetRef}
          showOnFocus={isFocused}
          tabIndex={0}
          aria-describedby={showTooltip ? tooltipId : undefined}
          aria-expanded={showTooltip}
          role="button"
        >
          {children}
        </TooltipTarget>
        {tooltipContent}
      </TooltipWrapper>
    </ThemeWrapper>
  );
});

ToolTip.displayName = "ToolTip";

ToolTip.defaultProps = {
  arrow: true,
  title: null,
  position: "bottom",
  background: "#000",
  additionalClassName: null,
  additionalStyle: null,
};

ToolTip.propTypes = {
  /**
   * Tooltip title
   */
  title: PropTypes.node.isRequired,
  /**
   * Tooltip position
   */
  position: PropTypes.oneOf(["left", "right", "top", "bottom"]),
  /**
   * background color on tooltip
   * default color is "#000"
   */
  background: PropTypes.string,
  /**
   *  Tooltip reference element.
   */
  children: PropTypes.any,
  /**
   * If true, adds an arrow to the tooltip.
   */
  arrow: PropTypes.bool,
  /**
   * This prop is used to help implement the accessibility logic.
   * If you don't provide this prop. It falls back to a randomly generated id.
   */
  id: PropTypes.string,
  /**
   * Property to append additional css class
   */
  additionalClassName: PropTypes.arrayOf(PropTypes.string),
  /**
   * Property to set the `inline style` object
   */
  additionalStyle: PropTypes.object,
};
