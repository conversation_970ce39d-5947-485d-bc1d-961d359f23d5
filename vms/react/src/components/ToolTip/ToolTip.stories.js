import React from "react";

import { ToolTip } from "./ToolTip";

export default {
  title: "VMS_REACT/ToolTip",
  component: ToolTip,
  argTypes: {
    additionalStyle: { control: { type: "" } },
    additionalClassName: { control: { type: "" } },
    children: { control: { type: "" } },
  },
};

export const Default = (args) => {
  return (
    <div
      style={{
        margin: "40px",
        display: "flex",
        flexDirection: "row",
        gap: "50px",
        marginLeft: "100px",
      }}
    >
      <div>
        <ToolTip
          title={`${args.position}-tooltip`}
          position={args.position}
          background={args.background}
        >
          <button
          id="btn-id"
            style={{
              border: "2px solid #03868b",
              padding: "5px",
              color: "#03868b",
            }}
          >{`Any-React-Component`}</button>
        </ToolTip>
      </div>
    </div>
  );
};
Default.args = {
  position: "bottom",
};

export const CustomTitle = (props) => {
  return (
    <div style={{ marginTop: "30px" }}>
      <ToolTip
        position="right"
        title={
          <div
            style={{
              width: "300px",
              display: "flex",
              flexDirection: "column",
              gap: "5px",
            }}
          >
            <div style={{ border: "1px solid white", padding: "5px" }}>
              Tip-1
            </div>
            <div style={{ border: "1px solid white", padding: "5px" }}>
              Tip-2
            </div>
          </div>
        }
      >
        <div
          style={{
            border: "2px solid #03868b",
            padding: "10px",
            color: "#03868b",
          }}
        >{`Right-Side`}</div>
      </ToolTip>
    </div>
  );
};

export const WithoutArrow = (props) => {
  return (
    <div style={{ marginTop: "30px" }}>
      <ToolTip position="right" title="Right-Side tooltip" arrow={false}>
        <div
          style={{
            border: "2px solid #03868b",
            padding: "10px",
            color: "#03868b",
          }}
        >{`Right-Side`}</div>
      </ToolTip>
    </div>
  );
};

export const ToolTipWithIcon = (props) => {
  return (
    <div style={{ marginTop: "30px" }}>
      <ToolTip position="bottom" title="Delete">
        <svg
          width={"2em"}
          height={"2em"}
          focusable="false"
          aria-hidden="true"
          viewBox="0 0 24 24"
          color="rgba(0, 0, 0, 0.54)"
          fill="currentColor"
        >
          <path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"></path>
        </svg>
      </ToolTip>
    </div>
  );
};
