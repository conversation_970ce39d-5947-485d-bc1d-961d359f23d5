import styled, { css, keyframes } from "styled-components";

export const TooltipWrapper = styled.div`
  position: relative;
  display: inline-flex;
`;

export const TooltipTarget = styled.div`
  border: none;
  background: inherit;
  font-size: inherit;
  color: inherit;
  cursor: inherit;
  display: flex;
  outline : ${({showOnFocus}) => showOnFocus === false ? `none`:``};
`;

export const TooltipContainer = styled.div`
  position: absolute;
  margin-left: -100px;
  display: flex;
  justify-content: center;
  align-items: center;
  left : ${({position}) => (position==="bottom" || position==="top")?`50%`:position==="left"?`unset`:position==="right"&&`calc(100% + 5px);`};
  right: ${({position}) => position==="left"&&`calc(100% + 5px);`};
  bottom: ${({position}) => position==="bottom"?`unset`:`calc(100% + 5px)`};
  top: ${({position}) => position==="bottom"?`calc(100% + 5px)`: (position==="left"||position==="right") &&`50%;`};
  margin-left: ${({position}) => position==="right" && `0`};
  margin-right: ${({position}) => position==="left" && `0`};
  width: ${({position}) => (position==="right"||position==="left") ? `max-content`:`200px`};
  pointer-events: none;
`;

const fadeIn = keyframes`
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
`;

const getBorderColorTollTipBoxCss = (position, background) => {
  switch (position) {
    case "bottom":
      return `transparent transparent ${background} transparent`;
    case "top":
      return `${background} transparent transparent transparent`;
    case "left":
      return `transparent transparent transparent ${background}`;
    case "right":
      return `transparent ${background} transparent
      transparent`;
  }
};
export const TooltipBox = styled.span`
  position: relative;
  background-color: ${(props) => props.background};
  color: #fff;
  text-align: center;
  border-radius: 5px;
  padding: 5px 5px;
  font-size: 14px;
  box-shadow: 0 4px 14px rgba(0, 0, 0, 0.15), 0 4px 8px rgba(0, 0, 0, 0.2);
  animation: ${fadeIn} 0.5s linear;
  &:after {
    content: "";
    position: absolute;
    height: 1px;
    border-width: ${({ arrow }) => (arrow === true ? `5px` : `0px`)};
    border-style: solid;
    border-color: ${({position, background}) => getBorderColorTollTipBoxCss(position,background)};
    top: ${({position}) => position==="top"?`100%`:position==="bottom"?`unset`:`calc(50% - 5px)`};
    width: ${({position}) => (position==="bottom"|| position==="top")&& `1px`};
    bottom:${({position}) => position==="bottom" && `100%`};
    left:${({position}) => (position ==="top" || position ==="bottom")?`calc(50% - 5px)`:position==="left"?`100%`:`unset`};
    right:${({position}) => position==="right" && `100%`};
  }
`;
