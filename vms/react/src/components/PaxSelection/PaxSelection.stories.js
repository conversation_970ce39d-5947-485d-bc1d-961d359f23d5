import React, { Fragment } from "react";
import { PaxSelection } from "./PaxSelection";
import editIcon from "../../assets/images/PaxSelection/edit.svg";
import deleteIcon from "../../assets/images/PaxSelection/delete.svg";
import { ModalPopup } from "../ModalPopup/ModalPopup";
export default {
  title: "VMS_REACT/PaxSelection",
  component: PaxSelection,
  argTypes: {
    additionalStyle: { control: { type: "" } },
    additionalClassName: { control: { type: "" } },
  },
};

const Template = (args) => <PaxSelection {...args} />;
export const Default = Template.bind({});
import upArrow from "../../assets/images/FairBreakeup/up.svg";
import downArrow from "../../assets/images/FairBreakeup/down.svg";

Default.args = {
  accessibleId: "default",
  paxData: {
    rooms: [
      {
        room: 1,
        adult: 1,
        children: 0,
        childAge: [],
        adultIncrement: true,
        adultDecrement: true,
        childIncrement: true,
        childDecrement: true,
        disable: false,
      },
    ],
    adults: 1,
    childrens: 0,
  },
  childrenAgeRangeLabel: "(2 - 11 yrs)",
  adultAgeRangeLabel: "(12 yrs & above)",
  value: 1,
  step: 1,
  openPaxSelection: true,
  defaultAdult: 1,
  minAdultPerRoom: 1,
  maximumRoom: 6,
  addMoreBtnLabel: "+ Add More Room",
  showGuest: true,
  customChildUpArrow: upArrow,
  customChildDownArrow: downArrow
};

export const PaxSelectionWithDoneBtn = (args) => {
  return <PaxSelection {...args} />;
};
PaxSelectionWithDoneBtn.args = {
  accessibleId: "default",
  paxData: {
    rooms: [
      {
        room: 1,
        adult: 2,
        children: 0,
        childAge: [],
        adultIncrement: true,
        adultDecrement: true,
        childIncrement: true,
        childDecrement: true,
        disable: false,
      },
    ],
    adults: 2,
    childrens: 0,
  },
  childrenAgeRangeLabel: "(2 - 11 yrs)",
  adultAgeRangeLabel: "(12 yrs & above)",
  openPaxSelection: true,
  defaultAdult: 2,
  doneButton: {
    children: "Done",
    buttonType: "tertiary",
    onClick: (e) => {
      console.log("done btn clicked");
    },
  },
  paxChangeCallback: (roomData, id, isRemoved) => {
    console.log("user callback function data ***", roomData, id, isRemoved);
  },
  removeBtnIcon: deleteIcon,
  editBtnIcon: editIcon,
};
export const PaxSelectionWithCabinClass = (args) => {
  return <PaxSelection {...args} />;
};
PaxSelectionWithCabinClass.args = {
  accessibleId: "default",
  customViewLabel: [
    { title: "Economy" },
    { title: "Premium Economy" },
    { title: "Business" },
    { title: "First Class" },
  ].map((item) => <button key={item?.title}>{item.title}</button>),
  travellerClassShown: true, // Add this line
  travellerClassData: [
    { title: "Economy" },
    { title: "Premium Economy" },
    { title: "Business" },
    { title: "First Class" },
  ], // Add this line
  passengerData: [
    { primaryText: "Adult", initialValue: 2, minValue: 1, maxValue: 9 },
    { primaryText: "Child", initialValue: 0, minValue: 0, maxValue: 9 },
  ], // Add this line
  paxData: {
    rooms: [
      {
        room: 1,
        adult: 2,
        children: 0,
        childAge: [],
        adultIncrement: true,
        adultDecrement: true,
        childIncrement: true,
        childDecrement: true,
        disable: false,
      },
    ],
    adults: 2,
    childrens: 0,
  },
  childrenAgeRangeLabel: "(2 - 11 yrs)",
  adultAgeRangeLabel: "(12 yrs & above)",
  openPaxSelection: true,
  defaultAdult: 2,
  doneButton: {
    children: "Done",
    buttonType: "tertiary",
    onClick: (e) => {
      console.log("done btn clicked");
    },
  },
  paxChangeCallback: (roomData, id, isRemoved) => {
    console.log("user callback function data ***", roomData, id, isRemoved);
  },
  removeBtnIcon: deleteIcon,
  editBtnIcon: editIcon,
  onClassItemClick: (index, item) => {
    console.log("Class item clicked:", index, item);
  }, // Add this line
  onPickerVisibilityChange: (isVisible) => {
    console.log("Picker visibility changed:", isVisible);
  }, // Add this line
};
PaxSelectionWithDoneBtn.storyName = "Pax-Selection With Done Button and icons";

export const PaxSelectionWithValidation = (args) => {
  return <PaxSelection {...args} />;
};
PaxSelectionWithValidation.args = {
  accessibleId: "default",
  paxData: {
    rooms: [
      {
        room: 1,
        adult: 2,
        children: 0,
        childAge: [],
        adultIncrement: true,
        adultDecrement: true,
        childIncrement: true,
        childDecrement: true,
        disable: false,
      },
    ],
    adults: 2,
    childrens: 0,
  },
  childrenAgeRangeLabel: "(2 - 11 yrs)",
  adultAgeRangeLabel: "(12 yrs & above)",
  openPaxSelection: true,
  defaultAdult: 2,
  doneButton: {
    children: "Done",
    buttonType: "tertiary",
    onClick: (e) => {
      console.log("done btn clicked");
    },
  },
  paxChangeCallback: (roomData, id, isRemoved) => {
    console.log("user callback function data ***", roomData, id, isRemoved);
  },
  removeBtnIcon: deleteIcon,
  editBtnIcon: editIcon,
  maximumRoomPax: 5,
  maxChildPerRoom: 3,
  minChildPerRoom: 1

};
PaxSelectionWithValidation.storyName = "Pax-Selection With Validation";


export const Mobile = Template.bind({});
Mobile.args = {
  accessibleId: "default",
  paxData: {
    rooms: [
      {
        room: 1,
        adult: 2,
        children: 0,
        childAge: [],
        adultIncrement: true,
        adultDecrement: true,
        childIncrement: true,
        childDecrement: true,
        disable: false,
      },
    ],
    adults: 2,
    childrens: 0,
  },
  defaultAdult: 2,
  childrenAgeRangeLabel: "(2 - 11 yrs)",
  adultAgeRangeLabel: "(12 yrs & above)",
  isMobile: true,
  doneButton: {
    children: "Done",
    buttonType: "tertiary",
    onClick: (e) => {
      console.log("done btn clicked");
    },
    isMobile: true,
  },
  customChildUpArrow: upArrow,
  customChildDownArrow: downArrow
};

export const MobileWithTravellerClass = () => {
  const [isOpenPopup, setIsOpenPopup] = React.useState(false);
  const [roomData, setRoomData] = React.useState({
    rooms: [
      {
        room: 1,
        adult: 2,
        children: 0,
        childAge: [],
        adultIncrement: true,
        adultDecrement: true,
        childIncrement: true,
        childDecrement: true,
        disable: false,
      },
    ],
    adults: 2,
    childrens: 0,
  }); // Initialize roomData with default values
  const [selectedTravellerClass, setSelectedTravellerClass] = React.useState("Economy"); // Default traveller class
  const [displayData, setDisplayData] = React.useState(null); // State to store data to display below the popup

  return (
    <Fragment>
      <button onClick={() => setIsOpenPopup(true)}>Open Popup</button>
      <ModalPopup
        isMobileView={true}
        isOpen={isOpenPopup}
        onRequestClose={() => setIsOpenPopup(false)}
      >
        <PaxSelection
          accessibleId={"default"}
          customViewLabel={[
            { title: "Economy" },
            { title: "Premium Economy" },
            { title: "Business" },
            { title: "First Class" },
          ].map((item, index) => (
            <button
              key={item?.title}
              onClick={() => setSelectedTravellerClass(item.title)} // Update traveller class on selection
            >
              {item.title}
            </button>
          ))}
          travellerClassShown={true}
          travellerClassData={[
            { title: "Economy" },
            { title: "Premium Economy" },
            { title: "Business" },
            { title: "First Class" },
          ]}
          paxData={roomData} // Pass the roomData state
          isMobile={true}
          addMoreBtnLabel={`+ ${"Add Another Room"}`}
          openPaxSelection={true}
          doneButton={{
            children: "Done",
            buttonType: "tertiary",
            onClick: () => {
              // Combine room data with traveller class
              const combinedData = {
                ...roomData,
                travellerClass: selectedTravellerClass,
              };
              setDisplayData(combinedData); // Set the combined data to display below the popup
              setIsOpenPopup(false); // Close the popup
            },
            isMobile: true,
          }}
          paxChangeCallback={(updatedRoomData) => {
            console.log(updatedRoomData, "paxChangeCallback"); // Debugging log
            setRoomData(updatedRoomData); // Update the room data state
          }}
        />
      </ModalPopup>
      {/* Display the updated room and traveller data below the popup */}
      {displayData && (
        <div style={{ marginTop: "20px" }}>
          <h3>Updated Traveller Data:</h3>
          <pre>{JSON.stringify(displayData, null, 2)}</pre>
        </div>
      )}
    </Fragment>
  );
};


Mobile.parameters = {
  viewport: {
    defaultViewport: "iphone5",
  },
};

MobileWithTravellerClass.parameters = {
  viewport: {
    defaultViewport: "iphone5",
  },
};
