import styled from "styled-components";

export const DoneButtonDiv = styled.div`
  display: flex;
  justify-content: center;
`;

export const ChildAgeContainerMain = styled.div`
  display: grid;
  grid-template-columns: ${({ isMobile }) =>
    isMobile ? "repeat(2, 1fr)" : "repeat(4, 1fr)"};
  grid-gap: 9px;
`;

export const ChildAgeContainer = styled.div`
  position: relative;
`;

export const ChildAgeInput = styled.input`
  border: none;
  margin-top: 0 !important;
  padding-left: 0;
  cursor: pointer;
  display: block;
  box-sizing: border-box;
  font-weight: 700;
  font-family: Montserrat;
  font-size: 14px;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.43;
  width: 100%;
  border-radius: 3px;
  letter-spacing: 0.3px;
  color: #4d4d4f;
  padding: 14px 16px;
  margin-top: 12px;
  text-overflow: ellipsis;
  height: 48px;
  border: 1px solid #4d4d4f;
  background-color: #fff;
`;

export const ChildAgeInputMobile = styled.input`
  border: none;
  border-bottom: 2px solid #4d4d4f;
  width: 100%;
  color: #000;
  font-size: 19px;
  font-weight: 700;
  font-family: "Montserrat";
  outline: none;
  cursor: pointer;
`;

export const ChildAgeDropDown = styled.ul`
  overflow-y: scroll;
  margin: 0;
  list-style: none;
  width: 100%;
  max-height: ${"150px"};
  position: absolute;
  background-color: #ffffff;
  padding: 0;
  z-index: 1;
  cursor: pointer;
`;

export const ListItem = styled.li`
  padding: 5px;
  text-align: center;
  font-family: ${({ theme }) => theme?.typography?.fontFamily};
  font-size: ${({ theme }) => theme?.typography?.text?.fontSize};
  font-weight: ${({ theme }) => theme?.typography?.text?.fontWeight};
  font-style: normal;
  font-stretch: normal;
  line-height: ${({ theme }) => theme?.typography?.lineHeight};
  letter-spacing: ${({ theme }) => theme?.typography?.letterSpacing};
  &:hover {
    background: #dfdfdf;
  }
`;

export const Paxmain = styled.div`
  position: relative;
  width: 300px;
`;

export const PaxChildmain = styled.div`
  position: relative;
  width: 80px;
`;

export const MainInputIcon = styled.img`
  bottom: 0;
  left: ${({ isMobile }) => {
    isMobile ? "0" : "22px";
  }};
  background-position: 100%;
  content: "";
  width: 39px;
  height: 24px;
  position: absolute;
  vertical-align: middle;
  right: 15px;
  bottom: 13px;
  cursor: pointer;
  z-index: 1;
`;

export const TotalPassgerText = styled.span`
  color: #000;
  font-size: 14px;
  line-height: 20px;
  letter-spacing: 0.3px;
  font-weight: 700;
  font-family: ${({ theme }) => theme.typography.fontFamily};
`;

export const TravellerClassText = styled.p`
  font-size: 10px;
  line-height: 12px;
  letter-spacing: 0px;
  color: #4d4d4f;
  font-weight: 300;
  margin: 0px;
  cursor: pointer;
  font-family: ${({ theme }) => theme.typography.fontFamily};
`;

export const TravellerClassContainer = styled.div`
  margin-bottom: 6px;
`;

export const TitleDiv = styled.div`
  margin-bottom: 26px;
`;

export const TitlText = styled.span`
  font-weight: ${({ theme }) => theme?.typography?.text?.fontWeight || "400"};
  font-size: ${({ theme }) => theme?.typography?.text?.fontSize || "14px"};
  line-height: 20px;
  letter-spacing: 0.3px;
  font-family: ${({ theme }) => theme?.typography?.fontFamily || ""};
  color: #4d4d4f;
`;

export const ChildAgeInputIcon = styled.img`
  top: 14px;
  right: 9px;
  position: absolute;
  max-width: 320px;
`;

export const MainPaxInput = styled.input`
  font-family: ${({ theme }) => theme?.typography?.fontFamily || ""};
  font-size: ${({ theme }) => theme?.typography?.title?.fontSize || ""};
  font-weight: ${({ theme }) => theme?.typography?.title?.fontWeight || ""};
  line-height: ${({ theme }) => theme?.typography?.lineHeight || ""};
  letter-spacing: ${({ theme }) => theme?.typography?.letterSpacing || ""};
  border: none;
  margin-top: 0 !important;
  cursor: pointer;
  position: relative;
  display: block;
  box-sizing: border-box;
  font-stretch: normal;
  font-style: normal;
  width: 100%;
  border-radius: 3px;
  color: #4d4d4f;
  padding: 14px 16px;
  text-overflow: ellipsis;
  height: 48px;
  border: 1px solid #4d4d4f;
  background-color: #fff;
`;
export const MobileListItem = styled.div`
  font-family: ${({ theme }) => theme?.typography?.fontFamily || ""};
  font-size: ${({ theme }) => theme?.typography?.title?.fontSize || ""};
  font-weight: ${({ theme }) => theme?.typography?.title?.fontWeight || ""};
  padding: 15px !important;
  text-align: center;
  border-bottom: 1px solid #435b73 !important;
  background-color: transparent;
  color: #fff;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  max-height: 60px;
  margin-top: 0;
  background-color: black;
  cursor: pointer;
`;

export const MobileListHeader = styled.div`
  padding: 20px 0;
  font-family: ${({ theme }) => theme?.typography?.fontFamily || ""};
  font-size: ${({ theme }) => theme?.typography?.title?.fontSize || ""};
  font-weight: ${({ theme }) => theme?.typography?.title?.fontWeight || ""};
  color: #ebebec;
  text-align: center;
  border-bottom: 2px solid #4d4d4f;
  background-color: black;
`;

export const ChildAgeInputLabel = styled.label`
  font-family: ${({ theme }) => theme?.typography?.fontFamily || ""};
  font-size: ${({ theme }) => theme?.typography?.text?.fontSize || ""};
  line-height: ${({ theme }) => theme?.typography?.lineHeight || ""};
  letter-spacing: ${({ theme }) => theme?.typography?.letterSpacing || ""};
  font-weight: ${({ theme }) => theme?.typography?.text.fontWeight || ""};
  color: #3e3e52;
  margin-bottom: 10px;
  text-transform: uppercase;
`;

export const ChildAgeContainerMobile = styled.div`
  position: fixed;
  bottom: 0;
  right: 0;
  width: 100%;
  max-height: 60%;
  text-align: center;
  z-index: 2;
  overflow-y: scroll;
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none;
  &::-webkit-scrollbar {
    display: none;
  }
`;

export const OverlayContainer = styled.div`
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  position: fixed;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1;
  display: block;
`;

export const RoomTitle = styled.div`
  font-family: ${({ theme }) => theme?.typography?.fontFamily || ""};
  font-size: ${({ theme }) => theme?.typography?.text?.fontSize || ""};
  font-weight: ${({ theme }) => theme?.typography?.title?.fontWeight || ""};
  color: #4d4d4f;
  text-transform: uppercase;
  margin: ${({ roomNo }) =>
    roomNo == 1 ? "19px 0 19px 20px" : "0 0 19px 20px"};
  display: flex;
  align-items: center;
`;

export const RoomTextSeperator = styled.div`
  height: 1px;
  background: #d1d3d4;
  flex: 1 1 auto;
  margin: 0 20px;
`;

export const RoomInnerContainer = styled.div`
  padding: ${({ isMobile }) => (isMobile ? "0px" : "0 20px 20px")};
  padding-bottom: 20px;
  box-sizing: border-box;
`;

export const PaxContainer = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

export const PaxTitleContainer = styled.p`
  margin: 0px;
`;

export const PaxTitle = styled.span`
  font-weight: bold;
`;

export const DisableRoomInner = styled.div`
  display: flex;
  justify-content: space-between;
`;

export const PaxHeaderTitle = styled.div`
  font-family: ${({ theme }) => theme?.typography?.fontFamily || ""};
  font-size: ${({ theme }) => theme?.typography?.text?.fontSize || ""};
  font-weight: ${({ theme }) => theme?.typography?.title?.fontWeight || ""};
  font-stretch: normal;
  font-style: normal;
  line-height: ${({ theme }) => theme?.typography?.lineHeight || ""};
  letter-spacing: ${({ theme }) => theme?.typography?.letterSpacing || ""};
  color: #3e3e52;
`;

export const RoomButton = styled.button`
  font-family: ${({ theme }) => theme?.typography?.fontFamily || ""};
  font-size: ${({ theme }) => theme?.typography?.text?.fontSize || ""};
  font-weight: ${({ theme }) => theme?.typography?.title?.fontWeight || ""};
  line-height: ${({ theme }) => theme?.typography?.lineHeight || ""};
  letter-spacing: ${({ theme }) => theme?.typography?.letterSpacing || ""};
  color: ${({ isEdit }) => (isEdit ? "#3e3e52" : "#03868b")};
  cursor: pointer;
  border: none;
  text-align: right;
  margin: 0 0 0 15px;
  padding: 0;
  background: none;
  outline: none;
`;

export const RoomButtonContainer = styled.div`
  text-align: right;
`;

export const AddMoreButtonContainer = styled.div`
  border-top: 1px solid #4d4d4f;
  padding: 15px 20px;
`;

export const AddMoreButton = styled.button`
  font-family: ${({ theme }) => theme?.typography?.fontFamily || ""};
  font-size: ${({ theme }) => theme?.typography?.text?.fontSize || ""};
  font-weight: ${({ theme }) => theme?.typography?.title?.fontWeight || ""};
  line-height: ${({ theme }) => theme?.typography?.lineHeight || ""};
  letter-spacing: ${({ theme }) => theme?.typography?.letterSpacing || ""};
  color: #03868b;
  cursor: pointer;
  border: none;
  text-align: right;
  background: none;
  padding: 0;
  outline: none;
`;

export const RoomContainer = styled.div`
  display: block;
`;

export const MainRoomContainer = styled.div`
  width: 360px;
  height: auto;
  border-radius: 3px 3px;
  border: 1px solid #4d4d4f;
  background-color: #fff;
  padding: 10px 0px;
  display: flex;
  gap: 10px;
  flex-direction: column;
`;

export const RoomText = styled.div`
  display: block;
`;

export const RoomTextSpan = styled.span`
  display: inline;
`;

export const EditBtnIcon = styled.img`
  width: 24px;
  height: 24px;
`;

export const DeleteBtnIcon = styled.img`
  width: 24px;
  height: 24px;
`;
export const PaxMainWrapper = styled.div`
  display: flex;
`;

export const PaxIcon  = styled.img`
 height: 24px;
 width:24px;
`;