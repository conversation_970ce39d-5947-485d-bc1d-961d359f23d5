import React, {
  Fragment,
  useEffect,
  useState,
  useReducer,
  useRef,
  memo,
  useCallback,
  useMemo,
} from "react";
import PropTypes from "prop-types";
import { Stepper } from "../Stepper/Stepper";
import downArrowSvg from "../../assets/images/PaxSelection/dropdown_down_icon.svg";
import upArrowSvg from "../../assets/images/PaxSelection/dropdown_up_icon.svg";
import { InputRadioButton } from "../InputRadioButton/InputRadioButton";
import {
  DoneButtonDiv,
  ChildAgeContainer,
  ChildAgeInput,
  ChildAgeDropDown,
  ListItem,
  ChildAgeContainerMain,
  MainInputIcon,
  MainPaxInput,
  Paxmain,
  MobileListHeader,
  MobileListItem,
  ChildAgeContainerMobile,
  OverlayContainer,
  RoomTitle,
  RoomTextSeperator,
  RoomInnerContainer,
  PaxTitle,
  PaxTitleContainer,
  ChildAgeInputLabel,
  RoomButtonContainer,
  RoomButton,
  MainRoomContainer,
  RoomContainer,
  PaxContainer,
  AddMoreButtonContainer,
  AddMoreButton,
  RoomTextSpan,
  DisableRoomInner,
  PaxHeaderTitle,
  ChildAgeInputMobile,
  ChildAgeInputIcon,
  PaxChildmain,
  EditBtnIcon,
  DeleteBtnIcon,
  PaxMainWrapper,
  PaxIcon,
  TotalPassgerText,
  TravellerClassText,
  TravellerClassContainer,
  TitleDiv,
  TitlText,
} from "./PaxSelection.styled";
import { Button } from "../Button/Button";
import { useClassName } from "../../hooks/useClassName";
import { useClickAway, useOnClickOutside } from "../../hooks/outsideClickHandler";
import { ThemeWrapper } from "../Theme/ThemeContext";

// Utility function (placed before main component)
const isNonNull = (prop) => prop !== null && prop !== undefined;

// Constants
const roomActions = {
  CHANGE_ADULT_COUNT: "CHANGE_ADULT_COUNT",
  CHANGE_CHILD_COUNT: "CHANGE_CHILD_COUNT",
  CHANGE_CHILD_AGE: "CHANGE_CHILD_AGE",
  ADD_ROOM: "ADD_ROOM",
  REMOVE_ROOM: "REMOVE_ROOM",
  EDIT_ROOM: "EDIT_ROOM",
  CHANGE_ALL: "CHANGE_ALL"
};

// Memoized room reducer
const roomReducer = (prevState = {}, action) => {
  let childCount, adultCount;
  const roomData = JSON.parse(JSON.stringify(prevState));
  
  switch (action.type) {
    case roomActions.CHANGE_ALL:
      return { ...action.payload };

    case roomActions.CHANGE_ADULT_COUNT:
      adultCount = 0;
      childCount = 0;
      
      roomData.rooms.forEach((room, id) => {
        if (id !== action.payload.room_index) {
          adultCount += room.adult;
        }
        childCount += room.children;
      });

      adultCount += action.payload.value;
      roomData.adults = adultCount;
      roomData.childrens = childCount;
      roomData.rooms[action.payload.room_index].adult = action.payload.value;
      return { ...roomData };

    case roomActions.CHANGE_CHILD_COUNT:
      adultCount = 0;
      childCount = 0;
      
      roomData.rooms.forEach((room, id) => {
        if (id !== action.payload.room_index) {
          childCount += room.children;
        }
        adultCount += room.adult;
      });

      childCount += action.payload.value;
      roomData.adults = adultCount;
      roomData.childrens = childCount;
      roomData.rooms[action.payload.room_index].children = action.payload.value;
      
      if (roomData.rooms[action.payload.room_index].childAge.length > action.payload.value) {
        roomData.rooms[action.payload.room_index].childAge.pop();
      } else {
        roomData.rooms[action.payload.room_index].childAge.push({
          age: action.payload.min_value,
        });
      }
      return { ...roomData };

    case roomActions.CHANGE_CHILD_AGE:
      roomData.rooms[action.payload.room_index].childAge[action.payload.child_id].age = action.payload.value;
      return { ...roomData };

    case roomActions.ADD_ROOM:
      let totalPaxCount = roomData.adults + roomData.childrens + action.payload.value.adult;
      if (totalPaxCount > action.payload.maxPax) {
        action.payload.value.adult = action.payload.minAdult;
      }

      roomData.adults += action.payload.value.adult;
      action.payload.value.room = roomData.rooms.length + 1;
      roomData.rooms.push(action.payload.value);
      roomData.activeRoomIndex = roomData.rooms.length - 1;
      return { ...roomData };

    case roomActions.REMOVE_ROOM:
      adultCount = 0;
      childCount = 0;
      roomData.rooms.splice(action.payload.room_index, 1);
      
      roomData.rooms.forEach((roomInfo) => {
        adultCount += roomInfo.adult;
        childCount += roomInfo.children;
      });
      
      roomData.rooms.forEach((room, id) => {
        room.room = id + 1;
      });
      
      roomData.adults = adultCount;
      roomData.childrens = childCount;
      roomData.activeRoomIndex = roomData.rooms.length - 1;
      return { ...roomData };
      
    case roomActions.EDIT_ROOM:
      roomData.activeRoomIndex = action.payload.room_index;
      return { ...roomData };

    default:
      return { ...roomData };
  }
};

export const PaxSelection = memo((props) => {
  // 1. PROP DESTRUCTURING & VALIDATION
  const {
    defaultAdult = 1,
    paxData,
    minimumChildAge = 0,
    maximumChildAge = 12,
    maximumTotalPax = 9,
    maximumRoom = 6,
    maximumRoomPax,
    minAdultPerRoom = 1,
    maxAdultPerRoom = 5,
    minChildPerRoom = 0,
    maxChildPerRoom = 5,
    openPaxSelection = false,
    customInputString,
    adultAgeLabel,
    adultAgeRangeLabel,
    childrenAgeRangeLabel,
    childAgeLabel,
    childAgeDescription,
    containerClassName,
    paxChangeCallback,
    onPaxVisibilityChange,
    onPaxInputClick,
    onChildDropDownVisibiltyChange,
    customChildDropdownLabel,
    childAgePrefix,
    childAgeSuffix,
    adultCustomIcon,
    childCustomIcon,
    editBtnIcon,
    removeBtnIcon,
    addMoreBtnLabel = "+ Add More",
    customUpArrow,
    customDownArrow,
    customChildUpArrow,
    customChildDownArrow,
    travellerClassShown = false,
    travellerClassData = [],
    travellerClassTitle = "Class",
    customViewLabel,
    onClassItemClick,
    passengerData = [],
    AdultStepperProps,
    ChildStepperProps,
    doneButton,
    cancelButton,
    accessibleId,
    isMobile,
    isMobileView,
    ...otherProps
  } = props;

  // 2. STATE MANAGEMENT
  const defaultRoomJson = useMemo(() => ({
    adults: defaultAdult,
    childrens: 0,
    rooms: [
      {
        room: 1,
        adult: defaultAdult,
        children: 0,
        childAge: [],
      },
    ],
  }), [defaultAdult]);

  const [paxInputValue, setPaxInputValue] = useState("");
  const [roomData, dispatchRoom] = useReducer(roomReducer, paxData || defaultRoomJson);
  const [activeChildAgeIndex, setActiveChildAgeIndex] = useState();
  const [childAgeArr, setChildAgeArr] = useState([]);
  const [maximumPaxLimitExceed, setMaximumPaxLimitExceed] = useState(false);
  const [isDropDownOpen, setIsDropDownOpen] = useState(openPaxSelection);
  const [isAgeDropDownOpen, setIsAgeDropDownOpen] = useState(false);
  const [travelClassState, setTravelClassState] = useState(0);
  const [showTravelClassPickerState, setTravelClassPicker] = useState(false);
  const [totalPassengerCount, setTotalPassengerCount] = useState(0);
  const [showOverlay, setShowOverlay] = useState(false);

  // 3. REFS
  const paxDropDownRef = useRef();
  const ageDropdownRef = useRef();

  // 4. PERFORMANCE OPTIMIZATIONS
  const computedContainerClassName = useMemo(() => 
    useClassName(props, containerClassName), 
    [props, containerClassName]
  );

  const isMobileDevice = useMemo(() => 
    isMobile || isMobileView || false, 
    [isMobile, isMobileView]
  );

  const totalTravellerCount = useMemo(() => 
    roomData.adults + roomData.childrens, 
    [roomData.adults, roomData.childrens]
  );

  const combinedPaxInputValue = useMemo(() => {
    if (travellerClassShown) {
      return `${totalTravellerCount} Traveller${totalTravellerCount > 1 ? 's' : ''}, ${roomData.rooms.length} room${roomData.rooms.length > 1 ? 's' : ''}, ${travellerClassData[travelClassState]?.title || ''}`;
    }
    return paxInputValue;
  }, [travellerClassShown, totalTravellerCount, roomData.rooms.length, travellerClassData, travelClassState, paxInputValue]);

  // 5. EVENT HANDLERS with useCallback
  const handleTravelClassChange = useCallback((event) => {
    setTravelClassState(event);
    if (isNonNull(onClassItemClick)) {
      onClassItemClick(Number(event), travellerClassData[event]);
    }
  }, [onClassItemClick, travellerClassData]);

  const handleTravelClassPickerToggle = useCallback(() => {
    const updatedShowTravelClassPickerState = !showTravelClassPickerState;
    setTravelClassPicker(updatedShowTravelClassPickerState);
    
    if (isNonNull(props.onPickerVisibilityChange)) {
      props.onPickerVisibilityChange(updatedShowTravelClassPickerState);
    }
    setShowOverlay(!showOverlay);
  }, [showTravelClassPickerState, showOverlay, props.onPickerVisibilityChange]);

  const handlePaxInputClick = useCallback(() => {
    if (isNonNull(onPaxInputClick)) {
      onPaxInputClick();
      if (isDropDownOpen) {
        setIsDropDownOpen(!isDropDownOpen);
      }
    } else {
      setIsDropDownOpen(!isDropDownOpen);
    }
    
    if (isNonNull(onPaxVisibilityChange)) {
      onPaxVisibilityChange(!isDropDownOpen);
    }
  }, [onPaxInputClick, isDropDownOpen, onPaxVisibilityChange]);

  const handleChildAgeClick = useCallback((childId, roomIndex) => {
    setActiveChildAgeIndex(childId);
    setIsAgeDropDownOpen(true);
    if (isNonNull(onChildDropDownVisibiltyChange)) {
      onChildDropDownVisibiltyChange(true, childId, roomIndex);
    }
  }, [onChildDropDownVisibiltyChange]);

  const handleChildAgeSelect = useCallback((childAge, roomIndex, childId) => {
    dispatchRoom({
      type: roomActions.CHANGE_CHILD_AGE,
      payload: {
        room_index: roomIndex,
        child_id: childId,
        value: childAge,
      },
    });
    setActiveChildAgeIndex();
    setIsAgeDropDownOpen(false);
    if (isNonNull(onChildDropDownVisibiltyChange)) {
      onChildDropDownVisibiltyChange(false, childId, roomIndex);
    }
  }, [onChildDropDownVisibiltyChange]);

  const handleAdultCountChange = useCallback((currentValue, roomIndex) => {
    dispatchRoom({
      type: roomActions.CHANGE_ADULT_COUNT,
      payload: {
        value: currentValue,
        room_index: roomIndex,
      },
    });
  }, []);

  const handleChildCountChange = useCallback((currentValue, roomIndex) => {
    dispatchRoom({
      type: roomActions.CHANGE_CHILD_COUNT,
      payload: {
        value: currentValue,
        room_index: roomIndex,
        min_value: 2,
      },
    });
  }, []);

  const handleRoomEdit = useCallback((roomIndex) => {
    dispatchRoom({
      type: roomActions.EDIT_ROOM,
      payload: { room_index: roomIndex },
    });
  }, []);

  const handleRoomRemove = useCallback((roomIndex) => {
    dispatchRoom({
      type: roomActions.REMOVE_ROOM,
      payload: { room_index: roomIndex },
    });
  }, []);

  const handleAddRoom = useCallback(() => {
    if (maximumRoom <= roomData.rooms.length || !maximumPaxLimitExceed) {
      dispatchRoom({
        type: roomActions.ADD_ROOM,
        payload: {
          value: {
            adult: defaultAdult,
            childAge: [],
            children: 0,
          },
          maxPax: maximumTotalPax,
          minAdult: minAdultPerRoom,
        },
      });
    }
  }, [maximumRoom, roomData.rooms.length, maximumPaxLimitExceed, defaultAdult, maximumTotalPax, minAdultPerRoom]);

  // 6. EFFECTS
  useEffect(() => {
    let ageArr = [];
    for (let i = minimumChildAge; i <= maximumChildAge; i++) {
      ageArr.push(i);
    }
    setChildAgeArr(ageArr);
  }, [minimumChildAge, maximumChildAge]);

  useEffect(() => {
    const adultCount = roomData.adults;
    const childCount = roomData.childrens;
    const roomCount = roomData.rooms.length;
    const totalPaxCount = adultCount + childCount;

    if (totalPaxCount >= maximumTotalPax) {
      setMaximumPaxLimitExceed(true);
    } else {
      setMaximumPaxLimitExceed(false);
    }

    const adultPostfix = adultCount > 1 ? "Adults" : "Adult";
    const displayChild = childCount > 0 ? 
      (childCount > 1 ? `+ ${childCount} children` : `+ ${childCount} child`) : "";
    const roomPostfix = roomCount > 1 ? "Rooms" : "Room";
    const string = `${adultCount} ${adultPostfix} ${displayChild}, ${roomCount} ${roomPostfix}`;
    
    setPaxInputValue(customInputString || string);

    // Trigger callback if data changed and dropdown is open
    if (isDropDownOpen && isNonNull(paxChangeCallback)) {
      const roomCallbackData = { ...roomData };
      paxChangeCallback(roomCallbackData, roomData?.activeRoomIndex || 0, false);
    }
  }, [roomData, maximumTotalPax, customInputString, isDropDownOpen, paxChangeCallback]);

  useEffect(() => {
    if (paxData) {
      dispatchRoom({
        type: roomActions.CHANGE_ALL,
        payload: paxData,
      });
    }
  }, [paxData]);

  useEffect(() => {
    setIsDropDownOpen(openPaxSelection);
  }, [openPaxSelection]);

  // Outside click handlers
  useOnClickOutside(ageDropdownRef, useCallback(() => {
    setIsAgeDropDownOpen(false);
    setActiveChildAgeIndex();
  }, []));

  useClickAway(paxDropDownRef, useCallback(() => {
    setIsDropDownOpen(false);
    if (isNonNull(paxChangeCallback)) {
      const roomCallbackData = { ...roomData };
      paxChangeCallback(roomCallbackData, roomData?.activeRoomIndex || 0, false);
    }
    if (isNonNull(onPaxVisibilityChange)) {
      onPaxVisibilityChange(false);
    }
  }, [paxChangeCallback, roomData, onPaxVisibilityChange]));

  // 7. MEMOIZED COMPONENTS
  const renderChildAgeMobile = useCallback((room, roomIndex) => (
    <ChildAgeContainerMain
      className="vms_paxselection_childage_mobilecontainer"
      isMobile={isMobileDevice}
    >
      {room.childAge.map((child, childId) => (
        <div className="vms_paxselection_div" key={`child-${roomIndex}-${childId}`}>
          <Paxmain
            key={`child-index-${childId}`}
            id={`vms_paxselection_paxmain_room_${roomIndex}_child_${childId}`}
            className="vms_paxselection_paxmain"
            onClick={() => handleChildAgeClick(childId, roomIndex)}
            role="button"
            tabIndex={0}
            aria-label={`Select age for child ${childId + 1}`}
          >
            <ChildAgeInputLabel className="vms_paxselection_child_inputlabel">
              {customChildDropdownLabel || `Child ${childId + 1} Age`}
            </ChildAgeInputLabel>
            <ChildAgeInputMobile
              className="vms_paxselection_child_inputmobile"
              type="text"
              value={`${childAgePrefix || ""} ${child.age} ${childAgeSuffix || ""}`}
              readOnly
            />
            <ChildAgeInputIcon
              className="vms_paxselection_childage_inputicon"
              src={
                (roomData?.activeRoomIndex || 0) === roomIndex && activeChildAgeIndex === childId
                  ? (customChildUpArrow || upArrowSvg)
                  : (customChildDownArrow || downArrowSvg)
              }
              isMobile={isMobileDevice}
              alt="Age selector arrow"
            />
          </Paxmain>

          {(roomData?.activeRoomIndex || 0) === roomIndex && activeChildAgeIndex === childId && (
            <Fragment>
              <ChildAgeContainerMobile
                className="vms_paxselection_childage_mobilecontainer"
                onClick={() => setActiveChildAgeIndex()}
              >
                <MobileListHeader className="vms_paxselection_mobile_listheader">
                  {customChildDropdownLabel || `Child ${childId + 1} Age`}
                </MobileListHeader>
                {childAgeArr.map((childAge, index) => (
                  <MobileListItem
                    key={`${childAge}-${index}`}
                    className={`${
                      roomData.rooms[roomData?.activeRoomIndex || 0].childAge[activeChildAgeIndex].age === childAge
                        ? "vmsActiveAge"
                        : ""
                    } vms_paxselection_mobilelistitem`}
                    tabIndex={0}
                    onClick={() => handleChildAgeSelect(childAge, roomIndex, childId)}
                    role="option"
                    aria-selected={roomData.rooms[roomData?.activeRoomIndex || 0].childAge[activeChildAgeIndex].age === childAge}
                  >
                    {childAgePrefix || ""} {childAge} {childAgeSuffix || ""}
                  </MobileListItem>
                ))}
              </ChildAgeContainerMobile>
              <OverlayContainer
                className="vms_paxselection_overlaycontainer"
                onClick={() => setActiveChildAgeIndex()}
              />
            </Fragment>
          )}
        </div>
      ))}
    </ChildAgeContainerMain>
  ), [
    isMobileDevice,
    handleChildAgeClick,
    customChildDropdownLabel,
    childAgePrefix,
    childAgeSuffix,
    roomData,
    activeChildAgeIndex,
    customChildUpArrow,
    customChildDownArrow,
    childAgeArr,
    handleChildAgeSelect
  ]);

  const renderChildAgeDesktop = useCallback((room, roomIndex) => (
    <ChildAgeContainerMain className="vms_paxselection_chidage_desktopcontainer">
      {room.childAge.map((child, childId) => (
        <ChildAgeContainer
          key={`child-age-${roomIndex}-${childId}`}
          className="vms_paxselection_childage_containerdiv"
          id={`vms_paxselection_paxmain_room_${roomIndex}_child_${childId}`}
        >
          <ChildAgeInputLabel className="vms_paxselection_childage_inputlabel">
            {customChildDropdownLabel || `Child ${childId + 1} Age`}
          </ChildAgeInputLabel>
          <PaxChildmain
            className="vms_paxselection_paxmain"
            onClick={() => handleChildAgeClick(childId, roomIndex)}
            role="button"
            tabIndex={0}
            aria-label={`Select age for child ${childId + 1}`}
          >
            <ChildAgeInput
              className="vms_paxselection_childage_input"
              type="text"
              onClick={() => setIsAgeDropDownOpen(true)}
              value={`${childAgePrefix || ""} ${child.age} ${childAgeSuffix || ""}`}
              readOnly
            />
            <ChildAgeInputIcon
              className="vms_paxselection_childage_inputicon"
              src={
                (roomData?.activeRoomIndex || 0) === roomIndex && activeChildAgeIndex === childId
                  ? (customChildUpArrow || upArrowSvg)
                  : (customChildDownArrow || downArrowSvg)
              }
              isMobile={isMobileDevice}
              alt="Age selector arrow"
            />
          </PaxChildmain>
          {(roomData?.activeRoomIndex || 0) === roomIndex && activeChildAgeIndex === childId && isAgeDropDownOpen ? (
            <ChildAgeDropDown ref={ageDropdownRef} className={`vms_paxselection_childagedropdown`} id={`vms_paxselection_childage_room_${roomIndex}_child_${childId}`}>
              {childAgeArr.map((childAge, index) => (
                <Fragment key={`${childAge}-${index}`}>
                  <ListItem
                    className="vms_paxselection_listitem"
                    onClick={() => handleChildAgeSelect(childAge, roomIndex, childId)}
                    role="option"
                    aria-selected={roomData.rooms[roomData?.activeRoomIndex || 0].childAge[activeChildAgeIndex].age === childAge}
                  >
                    {childAgePrefix || ""} {childAge} {childAgeSuffix || ""}
                  </ListItem>
                </Fragment>
              ))}
            </ChildAgeDropDown>
          ) : null}
        </ChildAgeContainer>
      ))}
    </ChildAgeContainerMain>
  ), [
    isMobileDevice,
    handleChildAgeClick,
    customChildDropdownLabel,
    childAgePrefix,
    childAgeSuffix,
    roomData,
    activeChildAgeIndex,
    customChildUpArrow,
    customChildDownArrow,
    childAgeArr,
    handleChildAgeSelect,
    isAgeDropDownOpen
  ]);

  const roomContainer = useCallback(() => {
    return (
      <Fragment>
        {roomData.rooms.map((room, id) => {
          let adultCount = room.adult || 1;
          let childCount = room.children || 0;
          let roomPaxCount = adultCount + childCount;
          let adultPostfix = adultCount > 1 ? "Adults" : "Adult";
          let displayChild =
            childCount > 0
              ? childCount > 1
                ? `, ${childCount} children`
                : `, ${childCount} child`
              : "";
          let displayString =
            adultCount + " " + adultPostfix + " " + displayChild;

          let minimumAdultCount = minAdultPerRoom || 0;
          let maximumAdultCount = maxAdultPerRoom || 5;
          let maximumChildCount = maxChildPerRoom || 5;
          let minimumChildCount = minChildPerRoom || 0;

          if (
            maximumPaxLimitExceed ||
            roomPaxCount >= maximumRoomPax) {
            maximumAdultCount = adultCount;
            maximumChildCount = childCount;
          }
          return (
            <RoomContainer
              key={id}
              className="vms_paxselection_roomcontainer"
              isMobile={isMobileDevice}
            >
              <RoomTitle className="vms_paxselection_roomtitle" roomNo={id + 1}>
                <div>
                  Room <RoomTextSpan>{room.room}</RoomTextSpan>
                </div>
                <RoomTextSeperator />
              </RoomTitle>
              {(roomData?.activeRoomIndex || 0) === id ? (
                <RoomInnerContainer
                  className="vms_paxselection_roominnercontainer"
                  isMobile={isMobileDevice}
                >
                  <PaxContainer className="vms_paxselection_pax_container">

                    {adultCustomIcon != null && (
                      <PaxIcon className="vms_paxselection_paxicon" src={adultCustomIcon} />
                    )}

                    <PaxTitleContainer className="vms_paxselection_pax_title_container">
                      <PaxTitle>{isNonNull(adultAgeLabel) ? adultAgeLabel : "Adult"}</PaxTitle> {adultAgeRangeLabel}
                    </PaxTitleContainer>
                    <Stepper
                      minValue={minimumAdultCount || 1}
                      maxValue={maximumAdultCount}

                      initialValue={room.adult ? room.adult : minimumAdultCount}
                      value={room.adult ? room.adult : minimumAdultCount}
                      {...AdultStepperProps}
                      onValueChange={(currentValue) => handleAdultCountChange(currentValue, roomData?.activeRoomIndex || 0)}
                    />
                  </PaxContainer>

                  <PaxContainer className="vms_paxselection_paxcontainer">

                    {childCustomIcon != null && (
                      <PaxIcon className="vms_paxselection_paxicon" src={childCustomIcon} />
                    )}

                    <PaxTitleContainer className="vms_paxselection_paxtitle">
                      <PaxTitle>{isNonNull(childAgeLabel) ? childAgeLabel : "Child"}</PaxTitle> {childrenAgeRangeLabel}
                    </PaxTitleContainer>
                    <Stepper
                      {...ChildStepperProps}
                      minValue={minimumChildCount}
                      maxValue={maximumChildCount}
                      initialValue={room.children ? room.children : minimumChildCount}
                      value={room.children ? room.children : minimumChildCount}
                      onValueChange={(currentValue) => handleChildCountChange(currentValue, id)}
                    />
                  </PaxContainer>
                  {isNonNull(childAgeDescription) && room.children ?
                    <>{childAgeDescription}</>
                    : null}
                  {isMobileDevice
                    ? renderChildAgeMobile(room, id)
                    : <>{renderChildAgeDesktop(room, id)}</>}
                </RoomInnerContainer>
              ) : (
                <RoomInnerContainer
                  className={
                    "disabled-room vms_paxselection_roominner_container"
                  }
                  isMobile={isMobileDevice}
                >
                  <DisableRoomInner className="vms_paxselection_disableroom">
                    <PaxHeaderTitle className="vms_paxselection_pax_header_title">
                      {displayString}
                    </PaxHeaderTitle>
                    <RoomButtonContainer className="vms_paxselection_roombtn_container">
                      {isNonNull(editBtnIcon) ? (
                        <EditBtnIcon
                          className="vms_paxselection_roombtn_editicon"
                          src={editBtnIcon}
                          onClick={() => handleRoomEdit(id)}
                        />
                      ) : (
                        <RoomButton
                          className="vms_paxselection_roombtn_edit"
                          onClick={() => handleRoomEdit(id)}
                        >
                          Edit
                        </RoomButton>
                      )}
                      {isNonNull(removeBtnIcon) ? (
                        <DeleteBtnIcon
                          className="vms_paxselection_roombtn_removeicon"
                          src={removeBtnIcon}
                          onClick={() => handleRoomRemove(id)}
                        />
                      ) : (
                        <RoomButton
                          className="vms_paxselection_roombtn_remove"
                          isEdit={true}
                          onClick={() => handleRoomRemove(id)}
                        >
                          Remove
                        </RoomButton>
                      )}
                    </RoomButtonContainer>
                  </DisableRoomInner>
                </RoomInnerContainer>
              )}
            </RoomContainer>
          );
        })}

        {roomData.rooms.length >= maximumRoom || maximumPaxLimitExceed ?
          null :
          (
            <AddMoreButtonContainer className="vms_paxselection_addmorebtn_container">
              <AddMoreButton
                className="vms_paxselection_addmorebtn"
                onClick={handleAddRoom}
              >
                {isNonNull(addMoreBtnLabel) ? addMoreBtnLabel : "+ Add More"}
              </AddMoreButton>
            </AddMoreButtonContainer>
          )}
      </Fragment>
    );
  }, [roomData, maximumRoom, maximumPaxLimitExceed, handleAdultCountChange, handleChildCountChange, handleRoomEdit, handleRoomRemove, handleAddRoom, renderChildAgeMobile, renderChildAgeDesktop]);

  return (
    <ThemeWrapper isMobile={isMobileDevice}>
      <PaxMainWrapper
        className="vms_paxselection_paxmainwrapper"
        id={accessibleId}
      >
        {!isMobileDevice ? (
          <div className={computedContainerClassName} ref={paxDropDownRef}>
            <Paxmain onClick={handlePaxInputClick}>
              <MainPaxInput
                className="vms_paxselection_mainpaxinput"
                type="text"
                value={combinedPaxInputValue}
                readOnly
              />
              <MainInputIcon
                className="vms_paxselection_maininputicon"
                src={isDropDownOpen ? (customUpArrow ? customUpArrow : upArrowSvg) : (customDownArrow ? customDownArrow : downArrowSvg)}
                alt="plus"
              />
            </Paxmain>
            {isDropDownOpen ? (
              <MainRoomContainer
                id="room_container"
                className="vms_paxselection_mainroomcontainer"
              >
                {roomContainer()}
                {travellerClassShown && (
                  <Fragment>
                    <TitleDiv className="vms_travelerclass_title">
                      <TitlText>{travellerClassTitle}</TitlText>
                    </TitleDiv>
                    {customViewLabel ? (
                      <ul className="vms_travelerclass_customviewlabel_ul">
                        {customViewLabel.map((item, index) => (
                          <li className="vms_travelerclass_customviewlabel_item_li"
                            key={index}
                            onClick={() => handleTravelClassChange(index)}
                          >
                            {item}
                          </li>
                        ))}
                      </ul>
                    ) : (
                      <>
                        {travellerClassData.map((travelclass, index) => (
                          <TravellerClassContainer key={travelclass.title} className="vms_travelerclass_radiobutton">
                            <InputRadioButton
                              value={`${index}`}
                              id={travelclass.title}
                              label={travelclass.title}
                              onChange={handleTravelClassChange}
                              checked={travelClassState == index}
                            />
                          </TravellerClassContainer>
                        ))}
                      </>
                    )}
                  </Fragment>
                )}
                {isNonNull(doneButton) && (
                  <DoneButtonDiv>
                    <Button {...doneButton} />
                  </DoneButtonDiv>
                )}

              </MainRoomContainer>
            ) : null}
          </div>
        ) : (
          <div className={computedContainerClassName}>
            <div className="vms_paxselection_room_container_mobile">
              {roomContainer()}
              {travellerClassShown && (
                  <Fragment>
                    <TitleDiv className="vms_travelerclass_title">
                      <TitlText>{travellerClassTitle}</TitlText>
                    </TitleDiv>
                    {customViewLabel ? (
                      <ul className="vms_travelerclass_customviewlabel_ul">
                        {customViewLabel.map((item, index) => (
                          <li className="vms_travelerclass_customviewlabel_item_li"
                            key={index}
                            onClick={() => handleTravelClassChange(index)}
                          >
                            {item}
                          </li>
                        ))}
                      </ul>
                    ) : (
                      <>
                        {travellerClassData.map((travelclass, index) => (
                          <TravellerClassContainer key={travelclass.title} className="vms_travelerclass_radiobutton">
                            <InputRadioButton
                              value={`${index}`}
                              id={travelclass.title}
                              label={travelclass.title}
                              onChange={handleTravelClassChange}
                              checked={travelClassState == index}
                            />
                          </TravellerClassContainer>
                        ))}
                      </>
                    )}
                  </Fragment>
                )}
            </div>
            {(isNonNull(doneButton) || isNonNull(cancelButton)) && (
              <div style={{ justifyContent: "center", display: "flex" }}>
                {cancelButton ? <Button {...cancelButton} /> : null}
                {doneButton ? <Button {...doneButton} /> : null}
              </div>
            )}
          </div>
        )}
      </PaxMainWrapper>
    </ThemeWrapper>
  );
});

PaxSelection.defaultProps = {
  addMoreBtnLabel: "+ Add More",
  travellerClassData: [],
  travellerClassShown: false,
  passengerData: [],
  customViewLabel: null,
  travellerClassTitle: "Class",
}

PaxSelection.propTypes = {

  travellerClassShown: PropTypes.bool,
  travellerClassTitle: PropTypes.string,
  /**
   * Prop to change pax view for desktop/mobile
   */
  isMobile: PropTypes.bool,

  /**
   * Prop to set custom adult age label 
   */
  adultAgeLabel: PropTypes.string,

  /**
   * Prop to set adult age range after Adult
   */
  adultAgeRangeLabel: PropTypes.string.isRequired,

  /**
   * Prop to set child age range after Child
   */
  childrenAgeRangeLabel: PropTypes.string.isRequired,

  /**
   * Prop to set custom child age label
   */
  childAgeLabel: PropTypes.string,

  /**
   * Prop to set custom child age description html or string
   */
  childAgeDescription: PropTypes.oneOfType([PropTypes.node, PropTypes.string]),

  /**
   * Prop to display rooms
   * Sample
   * const rooms: RoomData[]=[{room:1, adult:1, children:2, childAge:[{age:2},{age:8}]}]
   */
  paxData: PropTypes.shape({
    adults: PropTypes.number,
    childrens: PropTypes.number,
    rooms: PropTypes.arrayOf(
      PropTypes.shape({
        room: PropTypes.number,
        adult: PropTypes.number,
        childAge: PropTypes.arrayOf(
          PropTypes.shape({
            age: PropTypes.number,
          })
        ),
        children: PropTypes.number,
      })
    ),
  }).isRequired,

  /**
   * Prop to set minimum child  age
   */
  minimumChildAge: PropTypes.number,

  /**
   * Prop to set maximum child  age
   */
  maximumChildAge: PropTypes.number,

  /**
   * Prop to set default adult while add a room
   */
  defaultAdult: PropTypes.number,

  /**
   * Prop to set minimum pax for a room
   */
  minimumPax: PropTypes.number,

  /**
   * Prop to set maximum pax per one room validation
   */
  maximumRoomPax: PropTypes.number,

  /**
   * Prop to set maximum room validation
   */
  maximumRoom: PropTypes.number,

  /**
   * Prop to set maximum total pax
   */
  maximumTotalPax: PropTypes.number,

  /**
   * Prop to set accessibleId
   */
  accessibleId: PropTypes.string.isRequired,

  /**
   * Prop to handle additional className for container
   */
  containerClassName: PropTypes.arrayOf(PropTypes.string),

  /**
   * Prop to handle call back function while the value change
   * (paxData:Paxdata, index:number, isRemoved:boolean) => void
   */
  paxChangeCallback: PropTypes.func,

  /**
   * min adult per room
   */
  minAdultPerRoom: PropTypes.number,

  /**
   * max adult per room
   */
  maxAdultPerRoom: PropTypes.number,

  /**
   * min child per room
   */
  minChildPerRoom: PropTypes.number,

  /**
   * max child per room
   */
  maxChildPerRoom: PropTypes.number,


  /**
   * it will open pax selection modal. default value false
   */
  openPaxSelection: PropTypes.bool,

  /**
   * Array of items for traveller
   */
  travellerClassData: PropTypes.arrayOf(
    PropTypes.shape({
      title: PropTypes.string.isRequired,
    })
  ),

  onClassItemClick: PropTypes.func.isRequired,
  /**
   * Props for stepper component of Adult
   */
  AdultStepperProps: PropTypes.object,

  /**
   * Props for stepper component of child
   */
  ChildStepperProps: PropTypes.object,
  /**
   * Prefix for ChildAge
   */
  ChildAgePrefix: PropTypes.string,
  /**
   * Suffix for ChildAge
   */
  ChildAgeSuffix: PropTypes.string,
  /**
   * To view the Done button below room containers.all the properties of done button component is applicable on this prop
   */
  doneButton: PropTypes.shape({
    onClick: PropTypes.func,
    buttonType: PropTypes.oneOf(["primary", "secondary", "link", "tertiary"]),
    disabled: PropTypes.bool,
    isLoading: PropTypes.bool,
    progress: PropTypes.number,
    additionalClassName: PropTypes.arrayOf(PropTypes.string),
    additionalStyle: PropTypes.object,
    isMobile: PropTypes.bool,
    children: PropTypes.node,
  }),

  /**
   * Edit Button Icon
   */
  editBtnIcon: PropTypes.string,

  /**
   * Delete Button Icon
   */
  removeBtnIcon: PropTypes.string,


  onPaxVisibilityChange: PropTypes.func,
  /**
   * Add More Button Label
   */
  addMoreBtnLabel: PropTypes.string,

  /**
   * custom up arrow icon when dropdown is open 
   */
  customUpArrow: PropTypes.string,


  /**
   * custom down  arrow icon when dropdown is close  
   */
  customDownArrow: PropTypes.string,



  /**
 * custom up arrow icon when dropdown is open 
 */
  customChildUpArrow: PropTypes.string,


  /**
   * custom down  arrow icon when dropdown is close  
   */
  customChildDownArrow: PropTypes.string,

  /**
   * Prop to handle callback function while the child dropdown change
   * (isVisible:Paxdata, child_index:number, room_index:number) => void
   */
  onChildDropDownVisibiltyChange: PropTypes.func,

  /**
   * custom child dropdown label  
   */
  customChildDropdownLabel: PropTypes.string,

  /**
   * custom cabin class label 
   */
  customViewLabel: PropTypes.node,

};
