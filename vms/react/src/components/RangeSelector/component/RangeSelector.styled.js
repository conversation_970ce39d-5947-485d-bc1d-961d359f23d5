import styled from "styled-components";

export const Container = styled.div`
  width: 200px;
`;

export const TextViewContainer = styled.div`
  display: flex;
  flex-direction: row;
  justify-content: space-between;
`;

export const LeftLabel = styled.div`
  line-height: 20px;
  letter-spacing: 0.3px;
  font-size: ${({ theme }) => theme?.typography?.text?.fontSize || ""};
  color: #4d4d4f;
  font-weight: 700;
  font-family: ${({ theme }) => theme?.typography?.fontFamily || ""};
  text-align: left;
`;

export const RightLabel = styled.div`
  line-height: 20px;
  letter-spacing: 0.3px;
  font-size: ${({ theme }) => theme?.typography?.text?.fontSize || ""};
  color: #4d4d4f;
  font-weight: 700;
  font-family: ${({ theme }) => theme?.typography?.fontFamily || ""};
  text-align: right;
`;
export const LabelDiv = styled.div``;
