import React, { Component } from "react";
import { RangeSlider } from "reactrangeslider"; // eslint-disable-line import/no-unresolved
import {
  Container,
  LabelDiv,
  LeftLabel,
  RightLabel,
  TextViewContainer,
} from "./RangeSelector.styled";
import { withTheme } from "styled-components";
import { useClassName } from "../../../hooks/useClassName";

class CustomRangeSlider extends Component {
  onSliderChange = (value) => {
    this.props.ValChange(value);
  };

  onComplete = (value) => {
    console.log("value onComplete",value)
    this.props.afterValChange(value);
  };

  render() {
    const {
      min,
      max,
      step,
      labelPosition,
      labelSuffix,
      labelPrefix,
      showLabel,
      readOnly,
      wrapperClassName,
      wrapperStyle,
      trackClassName,
      trackStyle,
      highlightedTrackStyle,
      highlightedTrackClassName,
      disabled,
      labelTextStyle,
      defaultValue,
      tabIndex,
      handleClassName,
      handleStyle,
      disabledHighlightedTrackStyle,
      disabledHandleClassName,
      disabledHighlightedTrackClassName,
      disabledHandleStyle,
      hoveredHandleStyle,
      activeHandleStyle,
      focusedHandleStyle,
      disabledTrackStyle,
      value,
      theme,
      type,
      additionalClassName,
    } = this.props;
    const styles = {
      sliderWrapper: {
        width: "75%",
      },
      slider: {
        height: 34,
      },
      trackStyle: {
        height: 3,
        // border: "3px",
        backgroundColor: "#d1d3d4",
        border: "none",
      },
      highlightedTrackStyle: {
        height: 3,
        backgroundColor: theme?.palette?.[type]?.[theme.mode],
        border: "none",
      },
      handleStyle: {
        height: 24,
        width: 24,
        border: `1px solid ${theme?.palette?.[type]?.[theme.mode]}`,
        backgroundColor: "#fff",
      },
      hoveredHandleStyle: {
        backgroundColor: "#fff",
        border: `1px solid  ${theme?.palette?.[type]?.[theme.mode]}`,
        boxShadow: "0px 0px 5px 0px #0A53C3",
      },
      activeHandleStyle: {
        backgroundColor: "#fff",
        border: `1px solid ${theme?.palette?.[type]?.[theme.mode]} !important`,
        boxShadow: "inset 0px 0px 5px 0px #0A53C3",
      },
      focusedHandleStyle: {
        border: `1px solid ${theme?.palette?.[type]?.[theme.mode]} !important`,
      },
      disabledTrackStyle: {
        height: 3,
        // border: "3px",
        backgroundColor: "#d1d3d4 !important",
        border: "none",
        opacity: "1 !important",
      },
      disabledHighlightedTrackStyle: {
        backgroundColor: "#ef4044",
        height: "3px",
        borderRadius: "3px",
        borderWidth: "initiaL",
        borderStyle: "none",
        borderColor: "initial",
        borderImage: "initial",
        opacity: "1 !important",
      },
    };
    const className = useClassName(this.props, additionalClassName);
    return (
      <Container className={`${className} vms_RangeSelector_Container`}>
        <RangeSlider
          step={step}
          value={value}
          min={min}
          max={max}
          disabled={disabled}
          readOnly={readOnly}
          tabIndex={tabIndex}
          defaultValue={defaultValue}
          onChange={(val)=>this.onSliderChange(val)}
          afterChange={(val)=>this.onComplete(val)}
          handleStyle={handleStyle ? handleStyle : styles.handleStyle}
          handleClassName={handleClassName}
          wrapperStyle={wrapperStyle ? wrapperStyle : styles.slider}
          wrapperClassName={wrapperClassName}
          trackStyle={trackStyle ? trackStyle : styles.trackStyle}
          trackClassName={trackClassName}
          highlightedTrackStyle={
            highlightedTrackStyle
              ? highlightedTrackStyle
              : styles.highlightedTrackStyle
          }
          highlightedTrackClassName={highlightedTrackClassName}
          disabledTrackStyle={
            disabledTrackStyle ? disabledTrackStyle : styles.disabledTrackStyle
          }
          disabledHighlightedTrackStyle={
            disabledHighlightedTrackStyle
              ? disabledHighlightedTrackStyle
              : styles.disabledHighlightedTrackStyle
          }
          disabledHighlightedTrackClassName={disabledHighlightedTrackClassName}
          hoveredHandleStyle={
            hoveredHandleStyle ? hoveredHandleStyle : styles.hoveredHandleStyle
          }
          focusedHandleStyle={
            focusedHandleStyle ? focusedHandleStyle : styles.focusedHandleStyle
          }
          activeHandleStyle={
            activeHandleStyle ? activeHandleStyle : styles.activeHandleStyle
          }
          disabledHandleStyle={
            disabledHandleStyle ? disabledHandleStyle : styles.handleStyle
          }
          disabledHandleClassName={disabledHandleClassName}
        />
        {showLabel && (
          <TextViewContainer className={"vms_RangeSelector_Text_Container"}>
            <LeftLabel className={"vms_RangeSelector_LeftText"} style={labelTextStyle}>
              {labelPosition === "suffix"
                ? value?.start + " " + labelSuffix
                : labelPrefix + " " + value?.start}
            </LeftLabel>
            <LabelDiv>
              <RightLabel className={"vms_RangeSelector_RightText"} style={labelTextStyle}>
                {labelPosition === "suffix"
                  ? value?.end + " " + labelSuffix
                  : labelPrefix + " " + value?.end}
              </RightLabel>
            </LabelDiv>
          </TextViewContainer>
        )}
      </Container>
    );
  }
}

export default withTheme(CustomRangeSlider);
