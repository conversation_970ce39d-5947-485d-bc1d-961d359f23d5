import React, { memo, useCallback, useMemo } from "react";
import { RangeSlider } from "reactrangeslider"; // eslint-disable-line import/no-unresolved
import {
  Container,
  LabelDiv,
  LeftLabel,
  RightLabel,
  TextViewContainer,
} from "./RangeSelector.styled";
import { useClassName } from "../../../hooks/useClassName";

// Utility function (placed before main component)
const isNonNull = (prop) => prop !== null && prop !== undefined;

const CustomRangeSlider = memo((props) => {
  // 1. PROP DESTRUCTURING & VALIDATION
  const {
    min = 0,
    max = 100,
    step = 1,
    labelPosition = "bottom",
    labelSuffix = "",
    labelPrefix = "",
    showLabel = true,
    readOnly = false,
    wrapperClassName,
    wrapperStyle,
    trackClassName,
    trackStyle,
    highlightedTrackStyle,
    highlightedTrackClassName,
    disabled = false,
    labelTextStyle,
    defaultValue,
    tabIndex = 0,
    handleClassName,
    handleStyle,
    disabledHighlightedTrackStyle,
    disabledHandleClassName,
    disabledHighlightedTrackClassName,
    disabledHandleStyle,
    hoveredHandleStyle,
    activeHandleStyle,
    ValChange,
    afterValChange,
    value,
    theme,
    additionalClassName,
    additionalStyle,
    ...otherProps
  } = props;

  // 2. PERFORMANCE OPTIMIZATIONS
  const computedClassName = useMemo(
    () => useClassName(props, additionalClassName),
    [props, additionalClassName]
  );

  // 3. EVENT HANDLING with useCallback
  const handleSliderChange = useCallback((value) => {
    if (isNonNull(ValChange)) {
      ValChange(value);
    }
  }, [ValChange]);

  const handleComplete = useCallback((value) => {
    if (isNonNull(afterValChange)) {
      afterValChange(value);
    }
  }, [afterValChange]);

  // 4. ERROR HANDLING
  React.useEffect(() => {
    if (min >= max) {
      console.warn('CustomRangeSlider: min value should be less than max value');
    }
    if (step <= 0) {
      console.warn('CustomRangeSlider: step should be greater than 0');
    }
  }, [min, max, step]);

  // 5. MEMOIZED STYLES
  const styles = useMemo(() => ({
    sliderWrapper: {
      width: "75%",
    },
    slider: {
      height: 34,
    },
    trackStyle: {
      height: 3,
      backgroundColor: "#d1d3d4",
      border: "none",
      ...trackStyle,
    },
    highlightedTrackStyle: {
      height: 3,
      backgroundColor: theme?.palette?.[type]?.[theme.mode],
      border: "none",
    },
    handleStyle: {
      height: 24,
      width: 24,
      border: `1px solid ${theme?.palette?.[type]?.[theme.mode]}`,
      backgroundColor: "#fff",
    },
    hoveredHandleStyle: {
      backgroundColor: "#fff",
      border: `1px solid  ${theme?.palette?.[type]?.[theme.mode]}`,
      boxShadow: "0px 0px 5px 0px #0A53C3",
    },
    activeHandleStyle: {
      backgroundColor: "#fff",
      border: `1px solid ${theme?.palette?.[type]?.[theme.mode]} !important`,
      boxShadow: "inset 0px 0px 5px 0px #0A53C3",
    },
    focusedHandleStyle: {
      border: `1px solid ${theme?.palette?.[type]?.[theme.mode]} !important`,
    },
    disabledTrackStyle: {
      height: 3,
      // border: "3px",
      backgroundColor: "#d1d3d4 !important",
      border: "none",
      opacity: "1 !important",
    },
    disabledHighlightedTrackStyle: {
      backgroundColor: "#ef4044",
      height: "3px",
      borderRadius: "3px",
      borderWidth: "initial",
      borderStyle: "none",
      borderColor: "initial",
      borderImage: "initial",
      opacity: "1 !important",
      ...handleStyle,
    },
    disabledTrackStyle: {
      backgroundColor: "#ccc",
      ...disabledTrackStyle,
    },
    disabledHighlightedTrackStyle: {
      backgroundColor: "#999",
      ...disabledHighlightedTrackStyle,
    },
    hoveredHandleStyle: {
      backgroundColor: theme?.palette?.[type]?.[theme.mode] || "#007bff",
      ...hoveredHandleStyle,
    },
    focusedHandleStyle: {
      boxShadow: `0 0 0 2px ${theme?.palette?.[type]?.[theme.mode] || "#007bff"}`,
      ...focusedHandleStyle,
    },
    activeHandleStyle: {
      backgroundColor: theme?.palette?.[type]?.[theme.mode] || "#007bff",
      ...activeHandleStyle,
    },
  }), [theme, type, trackStyle, highlightedTrackStyle, handleStyle, disabledTrackStyle, disabledHighlightedTrackStyle, hoveredHandleStyle, focusedHandleStyle, activeHandleStyle]);

  // 6. CONDITIONAL RENDERING (memoized)
  const labelElement = useMemo(() => {
    if (!showLabel) return null;

    return (
      <LabelDiv>
        <LeftLabel style={labelTextStyle}>
          {labelPrefix}{value?.start || min}{labelSuffix}
        </LeftLabel>
        <RightLabel style={labelTextStyle}>
          {labelPrefix}{value?.end || max}{labelSuffix}
        </RightLabel>
      </LabelDiv>
    );
  }, [showLabel, labelTextStyle, labelPrefix, labelSuffix, value, min, max]);

  return (
    <Container
      className={`${computedClassName} vms_RangeSelector_Container`}
      style={additionalStyle}
      role="group"
      aria-label="Range selector"
      {...otherProps}
    >
      <RangeSlider
        step={step}
        value={value}
        min={min}
        max={max}
        disabled={disabled}
        readOnly={readOnly}
        tabIndex={tabIndex}
        defaultValue={defaultValue}
        onChange={handleSliderChange}
        afterChange={handleComplete}
        handleStyle={styles.handleStyle}
        handleClassName={handleClassName}
        wrapperStyle={wrapperStyle || styles.slider}
        wrapperClassName={wrapperClassName}
        trackStyle={styles.trackStyle}
        trackClassName={trackClassName}
        highlightedTrackStyle={styles.highlightedTrackStyle}
        highlightedTrackClassName={highlightedTrackClassName}
        disabledTrackStyle={styles.disabledTrackStyle}
        disabledHighlightedTrackStyle={styles.disabledHighlightedTrackStyle}
        disabledHighlightedTrackClassName={disabledHighlightedTrackClassName}
        hoveredHandleStyle={styles.hoveredHandleStyle}
        focusedHandleStyle={styles.focusedHandleStyle}
        activeHandleStyle={styles.activeHandleStyle}
        disabledHandleStyle={disabledHandleStyle || styles.handleStyle}
        disabledHandleClassName={disabledHandleClassName}
        aria-label="Range slider"
        aria-valuemin={min}
        aria-valuemax={max}
        aria-valuenow={value?.start || min}
      />

      {showLabel && (
        <TextViewContainer className="vms_RangeSelector_Text_Container">
          <LeftLabel className="vms_RangeSelector_LeftText" style={labelTextStyle}>
            {labelPosition === "suffix"
              ? `${value?.start || min} ${labelSuffix}`
              : `${labelPrefix} ${value?.start || min}`}
          </LeftLabel>
          <LabelDiv>
            <RightLabel className="vms_RangeSelector_RightText" style={labelTextStyle}>
              {labelPosition === "suffix"
                ? `${value?.end || max} ${labelSuffix}`
                : `${labelPrefix} ${value?.end || max}`}
            </RightLabel>
          </LabelDiv>
        </TextViewContainer>
      )}
    </Container>
  );
});

// Component display name for debugging
CustomRangeSlider.displayName = "CustomRangeSlider";

export default CustomRangeSlider;
