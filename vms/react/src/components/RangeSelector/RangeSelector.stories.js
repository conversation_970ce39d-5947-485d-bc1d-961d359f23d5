import React,{useState} from "react";

import { RangeSelector } from "./RangeSelector";
import styles from "./RangeSelector.css";
export default {
  title: "VMS_REACT/RangeSelector",
  component: RangeSelector,
  parameters: { controls: { sort: "requiredFirst" } },
  argTypes: {
    id: { control: { type: "" } },
    min: { control: { type: "" } },
    max: { control: { type: "" } },
    step: { control: { type: "" } },
    tabIndex: { control: { type: "" } },
    defaultValue: { control: { type: "" } },
    showLabel: { control: { type: "" } },
    additionalClassName: { control: { type: "" } },
    activeHandleStyle: { control: { type: "" } },
    hoveredHandleStyle: { control: { type: "" } },
    focusedHandleStyle: { control: { type: "" } },
    handleStyle: { control: { type: "" } },
    disabledHighlightedTrackStyle: { control: { type: "" } },
    highlightedTrackStyle: { control: { type: "" } },
    disabledTrackStyle: { control: { type: "" } },
    // highlightedTrackStyle: { control: { type: "" } },
    trackStyle: { control: { type: "" } },
    wrapperStyle: { control: { type: "" } },
    labelTextStyle: { control: { type: "" } },
    labelPrefix: { control: { type: "" } },
    labelSuffix: { control: { type: "" } },
    disabledHandleStyle: { control: { type: "" } },
    wrapperClassName: { control: { type: "" } },
    handleClassName: { control: { type: "" } },
    disabledHandleClassName: { control: { type: "" } },
    trackClassName: { control: { type: "" } },
    highlightedTrackClassName: { control: { type: "" } },
    disabledHighlightedTrackClassName: { control: { type: "" } },
  },
};

const Template = (args) => {
  const [value, setValue] = useState(
    {start: 720,end: 66000}
  );
  return (
    <RangeSelector
      value={value}
      min={720}
      step={1000}
      max={66000}
      onChange={(val) => {setValue(val) ,console.log("value on change", val)}}
      afterChange={(val) => console.log("value After changes", val)}
      // {...args}
    />
  );
};

export const Default = Template.bind({});
Default.args = {
  showLabel: true,
  disabled: false,
  readOnly: false,
};

export const WithSuffix = Template.bind({});
WithSuffix.args = {
  labelPosition: "suffix",
  labelSuffix: "InterMiles",
};

export const WithoutLabels = Template.bind({});
WithoutLabels.args = {
  showLabel: false,
};

export const WithoutLabelsPrefix = Template.bind({});
WithoutLabelsPrefix.args = {
  labelPrefix: "",
};

export const CustomLabelStyle = Template.bind({});
CustomLabelStyle.args = {
  labelTextStyle: {
    fontSize: 20,
    color: "#987564",
    fontWeight: "normal",
    fontFamily: "monospace",
  },
};

export const CustomImage = Template.bind({});
CustomImage.args = {
  handleClassName: "rangeCustomImageHandleClass",
  handleStyle: { width: 30, height: 30 },
};

export const CustomView = Template.bind({});
CustomView.args = {
  handleClassName: "rangeHandleClass",
  trackClassName: "rangeTrackClass disableTrack",
  highlightedTrackClassName: "rangeTrackClass highlightedTrack",
};
export const CustomHandleSize = Template.bind({});
CustomHandleSize.args = {
  handleClassName: "rangeHandleClass",
  trackClassName: "rangeTrackSmallClass disableTrack",
  highlightedTrackClassName: "rangeTrackSmallClass highlightedTrack",
  handleStyle: { width: 15, height: 15, top: 7 },
};
