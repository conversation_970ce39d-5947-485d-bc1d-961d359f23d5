import React, { useState, useEffect, memo, useCallback, useMemo } from "react";
import CustomRangeSlider from "./component/RangeSelector";
import PropTypes from "prop-types";
import { ThemeWrapper } from "../Theme/ThemeContext";

const RangeSelector = memo((props) => {
  // 1. PROP DESTRUCTURING & VALIDATION
  const {
    value: propValue,
    onChange,
    afterChange,
    min = 0,
    max = 30000,
    isMobile,
    isMobileView,
    ...otherProps
  } = props;

  // 2. STATE MANAGEMENT
  const defaultSliderValue = useMemo(() => ({
    start: min,
    end: max,
  }), [min, max]);

  const [value, setValue] = useState(propValue || defaultSliderValue);

  // 3. PERFORMANCE OPTIMIZATIONS
  const onSliderValueChange = useCallback((val) => {
    setValue(val);
    if (onChange) {
      onChange(val);
    }
  }, [onChange]);

  const onSliderFinish = useCallback((val) => {
    setValue(val);
    if (afterChange) {
      afterChange(val);
    }
  }, [afterChange]);

  // 4. EFFECTS
  useEffect(() => {
    if (propValue && propValue !== value) {
      setValue(propValue);
    }
  }, [propValue, value]);

  // 5. ERROR HANDLING
  React.useEffect(() => {
    if (min >= max) {
      console.warn('RangeSelector: min value should be less than max value');
    }
  }, [min, max]);

  return (
    <ThemeWrapper isMobile={isMobile || isMobileView || false}>
      <CustomRangeSlider
        value={value}
        ValChange={onSliderValueChange}
        afterValChange={onSliderFinish}
        min={min}
        max={max}
        {...otherProps}
      />
    </ThemeWrapper>
  );
});

// Component display name for debugging
RangeSelector.displayName = 'RangeSelector';

RangeSelector.defaultProps = {
  min: 0,
  max: 30000,
  step: 300,
  mode: "icon",
  type: "primary",
  labelPosition: "prefix",
  labelPrefix: "₹",
  showLabel: true,
  readOnly: false,
  wrapperStyle: null,
  wrapperClassName: null,
  labelTextStyle: null,
  disabled: false,
  onChange: () => { },
  afterChange: () => { },
};

RangeSelector.propTypes = {
  /**
   * Classes to add additional styling in the parent container
   */
  additionalClassName: PropTypes.arrayOf(PropTypes.string),
  /**
   * id of the root div element
   */
  id: PropTypes.string,

  /**
   * Minimum value in the range Default is 0
   */
  min: PropTypes.number,
  /**
   * maximum value in the range Default is 30000
   */
  max: PropTypes.number,

  /**
   * Amount by which the position of slider will change in one movement Default is 300
   */
  step: PropTypes.number,

  /**
   * This function will executed after the user has stopped moving the slider
   * (params: any) => void
   */
  afterChange: PropTypes.func,

  /**
   * This is used to set the tabIndex of handles which are moved to change value of slider
   */
  tabIndex: PropTypes.number,

  /**
   * It is used to initialize uncontrolled components
   */
  defaultValue: PropTypes.number,

  /**
   * Decides wheather to show bottom label or not Default is true
   */
  showLabel: PropTypes.bool,

  /**
   * Label position, if prefix then labelPrefix else labelSuffix will be displayed Default is prefix
   */
  labelPosition: PropTypes.oneOf(["prefix", "suffix"]),

  /**
   * Label prefix to added before amount. It will displayed only if labelPosition value is prefix Default is ₹
   */
  labelPrefix: PropTypes.string,

  /**
   * Label suffix to added after amount. It will displayed only if labelPosition value is suffix
   */
  labelSuffix: PropTypes.string,

  /**
   * Property used to make component readOnly, it can still be focused
   */
  readOnly: PropTypes.bool,

  /**
   * Property used to disable component, disable component can not even receive focus
   */
  disabled: PropTypes.bool,

  /**
   * Label text style
   */
  labelTextStyle: PropTypes.object,

  /**
   * Style applied to wrapper div element
   */
  wrapperStyle: PropTypes.objectOf(PropTypes.string),

  /**
   * Style applied to track
   */
  trackStyle: PropTypes.objectOf(PropTypes.string),
  /**
   * Style applied to track of disabled component
   */
  disabledTrackStyle: PropTypes.objectOf(PropTypes.string),

  /**
   * Style applied to highlighted track
   */
  highlightedTrackStyle: PropTypes.objectOf(PropTypes.string),

  /**
   * Style applied to highlighted track of disabled component
   */
  disabledHighlightedTrackStyle: PropTypes.objectOf(PropTypes.string),

  /**
   * Style applied to handle
   */
  handleStyle: PropTypes.objectOf(PropTypes.string),

  /**
   * Style applied to focused handle
   */
  focusedHandleStyle: PropTypes.objectOf(PropTypes.string),

  /**
   * Style applied to hovered handle
   */
  hoveredHandleStyle: PropTypes.objectOf(PropTypes.string),

  /**
   * Style applied to active handle
   */
  activeHandleStyle: PropTypes.objectOf(PropTypes.string),

  /**
   * Style applied to disabled handle
   */
  disabledHandleStyle: PropTypes.objectOf(PropTypes.string),

  /**
   * Class applied to wrapper div element
   */
  wrapperClassName: PropTypes.string,

  /**
   * Either of these classes is applied to the handle depending on whether its enabled or disabled
   */
  handleClassName: PropTypes.string,

  /**
   * Either of these classes is applied to the handle depending on whether its enabled or disabled
   */

  disabledHandleClassName: PropTypes.string,

  /**
   * Either of these classes is applied to the track depending on whether its enabled or disabled
   */

  trackClassName: PropTypes.string,

  /**
   * Either of these classes is applied to the track depending on whether its enabled or disabled
   */
  highlightedTrackClassName: PropTypes.string,

  /**
   * Either of these classes is applied to the highlighted handle depending on whether its enabled or disabled
   */

  disabledHighlightedTrackClassName: PropTypes.string,

  /**
    * 	
      Variations of Range selector
    */
  type: PropTypes.oneOf(["primary", "secondary"]),
};

export { RangeSelector };
