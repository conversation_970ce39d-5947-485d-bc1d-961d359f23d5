import styled from "styled-components";

export const FilterBarWrapper = styled.div`
  display: flex;
  flex: 1 1;
`;
export const FilterItems = styled.ul`
  list-style: none;
  margin: 0px;
  padding: 0px;
  align-self: flex-start;
  float: left;
  overflow-x: auto;
  display: inline-flex;
  &::-webkit-scrollbar {
    width: 0;
    background: transparent;
  }
  &::-webkit-scrollbar-thumb {
    background: transparent;
  }
`;
export const FilterItem = styled.li`
  display: flex;
  min-width: fit-content;
  padding: 15px 20px;
  background-color: ${({ theme }) => theme.palette.primary[theme.mode]};
  margin-right: 0px;
  border-right-width: 1px;
  border-radius: 0;
  border-right-style: dotted;
  border-right-color: ${({ theme }) => theme.palette.primary.contrastText};
  cursor: pointer;
`;
export const FilterText = styled.span`
  color: ${({ theme, isSelected,selectedFilterColor }) =>
    isSelected === true ? selectedFilterColor ? selectedFilterColor : "#f26b6a" : theme.palette.primary.contrastText};
  font-weight: bold;
  font-family: ${({ theme }) => theme.typography.fontFamily};
  font-size: ${({ theme }) => theme.typography.text.fontSize};
`;
export const FixFilterBar = styled.div`
  display: flex;
  position: relative;
  background-color: black;
  color: white;
  height: 46px;
  min-width: 79px;
  text-align: center;
  align-items: center;
  cursor: pointer;
`;
export const FixFilterBarText = styled.span`
  color: ${({ theme }) => theme.palette.primary.contrastText};
  font-family: ${({ theme }) => theme.typography.fontFamily};
  font-size: ${({ theme }) => theme.typography.text.fontSize};
  font-weight: bold;
  text-align: left;
  padding: 8px 16px;
  margin-right: 0px;
`;
export const Svg = styled.svg`
  cursor: pointer;
  color: ${({ theme }) => theme.palette.primary[theme.mode]};
  width: 14px;
  height: 22px;
  position: absolute;
  top: 25px;
  right: 3px;
`;
