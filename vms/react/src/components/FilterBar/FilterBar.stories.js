import React, { Fragment, useState } from "react";
import { FilterBar } from "./FilterBar";
import { ModalPopup } from "../ModalPopup/ModalPopup";
import { Button } from "../Button/Button";
export default {
  title: "VMS_REACT/FilterBar",
  component: FilterBar,
  argTypes: {
    additionalClassName: { control: { type: "" } },
  },
};
const FilterData = [
  "Non-Stop",
  "Refundable",
  "Early Departure",
  "Discount",
  "Free Cancellation",
  "Early Check-in",
];
const Template = (args) => <FilterBar {...args} />;

export const Default = () => {
  return <FilterBar filterArray={FilterData} />;
};
Default.parameters = {
  viewport: { defaultViewport: "iphone5" },
};
// PreSelectedFilter story
export const PreSelectedFilter = () => {
  const [selectFilterState, setSelectedFilterState] = useState([0, 2, 4]);
  const OnFilterHandler = (selectedIndex) => {
    console.log("OnFilterHandler", selectedIndex);
    setSelectedFilterState(selectedIndex);
  };

  return (
    <FilterBar
      filterArray={FilterData}
      selectedFilters={selectFilterState}
      onFilterSelection={OnFilterHandler}
    />
  );
};
PreSelectedFilter.parameters = {
  viewport: { defaultViewport: "iphone5" },
};
// PreSelectedFixFilter
export const PreSelectedFixFilter = () => {
  const [selectFilterState, setSelectedFilterState] = useState([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const OnFilterHandler = (selectedIndex) => {
    console.log("OnFilterHandler", selectedIndex);
    setSelectedFilterState(selectedIndex);
  };

  const OnFilterButtonSelection = (selectedIndexVal) => {
    const updateSelectedFilter = [...selectFilterState];
    if (!updateSelectedFilter.includes(selectedIndexVal)) {
      updateSelectedFilter.push(selectedIndexVal);
      setSelectedFilterState(updateSelectedFilter);
    }
    setIsModalOpen(false);
  };

  return (
    <Fragment>
      <FilterBar
        filterArray={FilterData}
        selectedFilters={selectFilterState}
        onFilterSelection={OnFilterHandler}
        isFixFilterSelected={true}
        onFixFilterSelection={() => {
          setIsModalOpen(true);
        }}
      />
      <ModalPopup
        isMobileView={true}
        isOpen={isModalOpen}
        onRequestClose={() => {
          setIsModalOpen(false);
        }}
      >
        <div style={{ display: "flex", gap: "10px", flexDirection: "column" }}>
          {FilterData.map((data, index) => {
            return (
              <Button
                onClick={(e) => {
                  OnFilterButtonSelection(index);
                }}
                key={data}
                buttonType={"secondary"}
                isMobile={true}
              >
                {data}
              </Button>
            );
          })}
        </div>
      </ModalPopup>
    </Fragment>
  );
};
PreSelectedFixFilter.parameters = {
  viewport: { defaultViewport: "iphone5" },
};
// CustomStyle story
export const WithoutFixFilter = () => {
  return <FilterBar filterArray={FilterData} fixFilter={""} />;
};
WithoutFixFilter.parameters = {
  viewport: { defaultViewport: "iphone5" },
};
