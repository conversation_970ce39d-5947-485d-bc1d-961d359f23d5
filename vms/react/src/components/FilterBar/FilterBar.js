import PropTypes from "prop-types";
import React, { useEffect, useState, memo, useCallback, useMemo } from "react";
import { useClassName } from "../../hooks/useClassName";
import {
  FilterBarWrapper,
  FilterItems,
  FilterItem,
  FilterText,
  FixFilterBar,
  FixFilterBarText,
  Svg,
} from "./FilterBar.styled";
import { ThemeWrapper } from "../Theme/ThemeContext";

// Utility function (placed before main component)
const isNonNull = (prop) => prop !== null && prop !== undefined;

// Memoized IconSvg component
const IconSvg = memo(() => (
  <Svg
    xmlns="http://www.w3.org/2000/svg"
    width="10"
    height="10"
    viewBox="0 0 10 10"
    role="img"
    aria-hidden="true"
  >
    <g fill="none">
      <path
        fill="currentColor"
        d="M5 0C2.24 0 0 2.24 0 5s2.24 5 5 5 5-2.24 5-5-2.24-5-5-5z"
      />
      <path
        fill="#FFF"
        d="M4.143 7L2 5.081 2.604 4.539 4.143 5.914 7.396 3 8 3.545z"
      />
    </g>
  </Svg>
));

IconSvg.displayName = 'IconSvg';

export const FilterBar = memo((props) => {
  // 1. PROP DESTRUCTURING & VALIDATION
  const {
    filterArray = [],
    additionalStyle,
    fixFilter = "Sort & Filter",
    onFilterSelection,
    onFixFilterSelection,
    selectedFilters = [],
    isFixFilterSelected = false,
    additionalClassName,
    selectedFilterColor,
    fixFilterSelectedIcon,
    isMobile,
    isMobileView,
    ...otherProps
  } = props;

  // 2. STATE MANAGEMENT
  const [selectFilterState, setSelectedFilterState] = useState(selectedFilters);

  // 3. PERFORMANCE OPTIMIZATIONS
  const computedClassName = useMemo(() => 
    useClassName(props, additionalClassName), 
    [props, additionalClassName]
  );

  const isMobileDevice = useMemo(() => 
    isMobile || isMobileView || false, 
    [isMobile, isMobileView]
  );

  const hasFixFilter = useMemo(() => 
    isNonNull(fixFilter) && fixFilter !== "", 
    [fixFilter]
  );

  const hasFixFilterSelectedIcon = useMemo(() => 
    isFixFilterSelected && isNonNull(fixFilterSelectedIcon), 
    [isFixFilterSelected, fixFilterSelectedIcon]
  );

  // 4. EVENT HANDLING with useCallback
  const handleFilterSelection = useCallback((selectedIndex) => {
    setSelectFilterState(prevState => {
      const updatedSelectedFilter = [...prevState];
      let newFilterState;

      if (updatedSelectedFilter.includes(selectedIndex)) {
        newFilterState = updatedSelectedFilter.filter(
          (element) => element !== selectedIndex
        );
      } else {
        updatedSelectedFilter.push(selectedIndex);
        newFilterState = updatedSelectedFilter;
      }

      if (isNonNull(onFilterSelection)) {
        onFilterSelection(newFilterState);
      }

      return newFilterState;
    });
  }, [onFilterSelection]);

  const handleFixFilterSelection = useCallback((event) => {
    event?.stopPropagation();
    
    if (isNonNull(onFixFilterSelection)) {
      onFixFilterSelection();
    }
  }, [onFixFilterSelection]);

  const handleFilterItemClick = useCallback((index) => (event) => {
    event?.stopPropagation();
    handleFilterSelection(index);
  }, [handleFilterSelection]);

  const handleKeyDown = useCallback((index) => (event) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      handleFilterSelection(index);
    }
  }, [handleFilterSelection]);

  const handleFixFilterKeyDown = useCallback((event) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      handleFixFilterSelection(event);
    }
  }, [handleFixFilterSelection]);

  // 5. EFFECTS
  useEffect(() => {
    setSelectedFilterState(selectedFilters);
  }, [selectedFilters]);

  // 6. CONDITIONAL RENDERING (memoized)
  const filterItems = useMemo(() => {
    if (!filterArray?.length) return null;

    return filterArray.map((filter, index) => (
      <FilterItem
        className="vms_filterbar_item"
        key={`filter-${index}-${filter}`}
        onClick={handleFilterItemClick(index)}
        onKeyDown={handleKeyDown(index)}
        role="button"
        tabIndex={0}
        aria-pressed={selectFilterState?.includes(index)}
        aria-label={`Filter by ${filter}`}
      >
        <FilterText 
          selectedFilterColor={selectedFilterColor} 
          className="vms_filterbar_filtertext" 
          isSelected={selectFilterState?.includes(index)}
        >
          {filter}
        </FilterText>
      </FilterItem>
    ));
  }, [
    filterArray, 
    selectFilterState, 
    selectedFilterColor, 
    handleFilterItemClick, 
    handleKeyDown
  ]);

  const fixFilterElement = useMemo(() => {
    if (!hasFixFilter) return null;

    return (
      <FixFilterBar
        className="vms_filterbar_fixfilter"
        onClick={handleFixFilterSelection}
        onKeyDown={handleFixFilterKeyDown}
        role="button"
        tabIndex={0}
        aria-pressed={isFixFilterSelected}
        aria-label={fixFilter}
      >
        <FixFilterBarText className="vms_filterbar_fixfilterbartext">
          {fixFilter}
        </FixFilterBarText>
        {hasFixFilterSelectedIcon ? (
          <img 
            src={fixFilterSelectedIcon} 
            width="10" 
            height="10" 
            alt="Selected filter icon"
          />
        ) : (
          <IconSvg />
        )}
      </FixFilterBar>
    );
  }, [
    hasFixFilter,
    handleFixFilterSelection,
    handleFixFilterKeyDown,
    isFixFilterSelected,
    fixFilter,
    hasFixFilterSelectedIcon,
    fixFilterSelectedIcon
  ]);

  // 7. ERROR HANDLING
  useEffect(() => {
    if (!Array.isArray(filterArray)) {
      console.warn('FilterBar: filterArray prop should be an array');
    }
    if (!Array.isArray(selectedFilters)) {
      console.warn('FilterBar: selectedFilters prop should be an array');
    }
  }, [filterArray, selectedFilters]);

  return (
    <ThemeWrapper isMobile={isMobileDevice}>
      <FilterBarWrapper
        className={`${computedClassName} vms_filterbar_container`}
        style={additionalStyle}
        role="toolbar"
        aria-label="Filter options"
      >
        <FilterItems 
          className="vms_filterbar_items"
          role="group"
          aria-label="Filter items"
        >
          {filterItems}
        </FilterItems>
        {fixFilterElement}
      </FilterBarWrapper>
    </ThemeWrapper>
  );
});

FilterBar.displayName = 'FilterBar';

FilterBar.defaultProps = {
  fixFilter: "Sort & Filter",
  selectedFilters: [],
  fixFilterSelectedIcon: null,
};

FilterBar.propTypes = {
  /**
   * Array of filters
   */
  filterArray: PropTypes.arrayOf(PropTypes.string).isRequired,
  /**
   * Property to set the `inline style` object
   */
  additionalStyle: PropTypes.object,
  /**
   * Property to append additional css class
   */
  additionalClassName: PropTypes.arrayOf(PropTypes.string),
  /**
   * Fix sticky filter value at the end. if pass blank then fixFilter will not display
   */
  fixFilter: PropTypes.string,
  /**
   * Return an array of selected filters indexes
   * (selectedIndexes: number[]) => void
   */
  onFilterSelection: PropTypes.func,
  /**
   * Callback function which will call on click of fix filter
   * () => void
   */
  onFixFilterSelection: PropTypes.func,
  /**
   * Array of selected filter index
   * number[]
   */
  selectedFilters: PropTypes.arrayOf(PropTypes.number),
  /**
   * if true and property fixFilter is not blank then selected icon will be display
   */
  isFixFilterSelected: PropTypes.bool,



  /**
  Custom Selected Chip style
   */
  selectedFilterColor: PropTypes.string,
  /**
   * Icon to be shown on fix filter when selected
   */
  fixFilterSelectedIcon: PropTypes.string,
};
