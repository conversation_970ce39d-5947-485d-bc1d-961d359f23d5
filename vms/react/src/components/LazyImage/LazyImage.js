import PropTypes from "prop-types";
import React, { useEffect, memo, useMemo } from "react";
import LazyLoad from "vanilla-lazyload";
import { LazyImageImg } from "./LazyImage.styled";

// Utility function (placed before main component)
const isNonNull = (prop) => prop !== null && prop !== undefined;

export const LazyImage = memo((props) => {
  // 1. PROP DESTRUCTURING & VALIDATION
  const {
    aspectRatio,
    draggable = false,
    src,
    alt = "Lazy loaded image",
    onError,
    additionalStyle,
    height,
    placeholderImage,
    ...otherProps
  } = props;

  // 2. PERFORMANCE OPTIMIZATIONS
  const lazyLoadConfig = useMemo(() => ({
    elements_selector: ".lazy",
    callback_error: (el) => {
      if (isNonNull(placeholderImage)) {
        el.style.display = "none";
        const elementToAdd = document.createElement("div");
        elementToAdd.style.backgroundImage = `url('${placeholderImage}')`;
        elementToAdd.style.backgroundSize = "cover";
        elementToAdd.style.backgroundPosition = "center";
        elementToAdd.style.width = "100%";
        elementToAdd.style.height = height || "200px";
        el.parentNode.insertBefore(elementToAdd, el.nextSibling);
      } else if (isNonNull(alt)) {
        el.setAttribute("alt", alt);
      }
      if (isNonNull(onError)) {
        onError(el);
      }
    },
  }), [placeholderImage, height, alt, onError]);

  // 3. EFFECTS
  useEffect(() => {
    if (!document.lazyLoadInstance) {
      try {
        document.lazyLoadInstance = new LazyLoad(lazyLoadConfig);
      } catch (error) {
        console.error("LazyImage: Failed to initialize LazyLoad instance", error);
      }
    }
  }, [lazyLoadConfig]);

  useEffect(() => {
    try {
      document.lazyLoadInstance?.update();
    } catch (error) {
      console.warn("LazyImage: Failed to update LazyLoad instance", error);
    }
  }, [src]);

  // 4. ERROR HANDLING
  React.useEffect(() => {
    if (!src) {
      console.warn('LazyImage: src prop is required for image loading');
    }
  }, [src]);

  return (
    <LazyImageImg
      aspectRatio={aspectRatio}
      src={null}
      draggable={draggable}
      className="lazy vms_lazyimage"
      data-src={src}
      data-srcset={src}
      width="100%"
      height={height}
      style={additionalStyle}
      alt={alt}
      loading="lazy"
      {...otherProps}
    />
  );
});

// Component display name for debugging
LazyImage.displayName = 'LazyImage';

LazyImage.defaultProps = {
  draggable: false,
  onError: null,
  additionalClassName: null,
  additionlStyle: null,
  aspectRatio: null,
};
LazyImage.propTypes = {
  /**
   * Aspect ratio
   */
  aspectRatio: PropTypes.arrayOf(PropTypes.number),
  /**
   * This prop will allow slide to be draggable. Default: `false`
   */
  draggable: PropTypes.bool,
  /**
   * Soure-image to display
   */
  src: PropTypes.string.isRequired,
  /**
   * Alternate image to display
   */
  alt: PropTypes.string,
  /**
   * Placeholder-image
   */
  placeholderImage: PropTypes.string,
  /**
   * Height of an image
   */
  height: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  /**
   * on error of the image loading this callback is triggered
   */
  onError: PropTypes.func,
  /**
   * Property to set the `inline style` object
   */
  additionalStyle: PropTypes.object,
};
