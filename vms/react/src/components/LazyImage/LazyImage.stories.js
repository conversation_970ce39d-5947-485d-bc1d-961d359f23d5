import { LazyImage } from "./LazyImage";
import LinkTo from "@storybook/addon-links/react";

export default {
  title: "VMS_REACT/LazyImage",
  component: LazyImage,
  argTypes: {
    additionalStyle: { control: { type: "" } },
    additionalClassName: { control: { type: "" } },
  },
};
const images = [
  "https://images.unsplash.com/photo-1549989476-69a92fa57c36?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=crop&w=800&q=60",
  "https://images.unsplash.com/photo-1549396535-c11d5c55b9df?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=60",
  "https://images.unsplash.com/photo-1550133730-695473e544be?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=crop&w=800&q=60",
  "https://images.unsplash.com/photo-1550167164-1b67c2be3973?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=crop&w=800&q=60",
  "https://images.unsplash.com/photo-1550338861-b7cfeaf8ffd8?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=crop&w=800&q=60",
  "https://images.unsplash.com/photo-1550223640-23097fc71cb2?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=crop&w=800&q=60",
  "https://images.unsplash.com/photo-1550353175-a3611868086b?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=crop&w=800&q=60",
  "https://images.unsplash.com/photo-1550330039-a54e15ed9d33?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=crop&w=800&q=60",
  "https://images.unsplash.com/photo-1549737328-8b9f3252b927?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=crop&w=800&q=60",
  "https://images.unsplash.com/photo-1549833284-6a7df91c1f65?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=crop&w=800&q=60",
  "https://images.unsplash.com/photo-1549985908-597a09ef0a7c?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=crop&w=800&q=60",
  "https://images.unsplash.com/photo-1550064824-8f993041ffd3?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=crop&w=800&q=60",
];

export const Default = (args) => {
  return (
    <div style={{ margin: "5%", maxHeight: "400px" }}>
      <div
        style={{
          padding: "10px",
          display: "flex",
          flexDirection: "column",
          gap: "20px",
        }}
      >
        <div>
          <span
            style={{ fontWeight: "bold", color: "red", paddingRight: "10px" }}
          >
            Example#1 Lazy Loading Carousel
          </span>
          <LinkTo kind="vms-react-carousel--lazy-loading-carousel">
            Lazy Loading Carousel
          </LinkTo>
        </div>
        <div>
          <span style={{ fontWeight: "bold", color: "red" }}>
            Example#2 Please open the network-tab to see that images are loaded
            as we scroll the gallery
          </span>
        </div>
      </div>
      <div
        style={{ maxHeight: "400px", overflowY: "auto", position: "relative" }}
      >
        {images.map((crrImg, index) => {
          return (
            <LazyImage
              key={index}
              src={crrImg}
              alt={"."}
              onError={(e) => {
                console.log(`Image-${index} onError function,`, e);
              }}
              additionalStyle={{ height: "400px" }}
            />
          );
        })}
      </div>
    </div>
  );
};
