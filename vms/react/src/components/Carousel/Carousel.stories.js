import { nominalTypeHack } from "prop-types";
import React, { Fragment, useEffect, useState } from "react";
import { LazyImage } from "../LazyImage/LazyImage";
import { Carousel } from "./Carousel";
import { AnchoredHorizontalTabs } from "../AnchoredHorizontalTabs";
import { Skeleton } from "../Skeleton";
import { ModalPopup } from "../ModalPopup";
import styles from "./Carousel.stories.module.css";

export default {
  title: "VMS_REACT/Carousel",
  component: Carousel,
  argTypes: {
    additionalStyle: { control: { type: "" } },
    additionalClassName: { control: { type: "" } },
  },
};
const nestedCarouselDataSix = [
  "#E6E5A3",
  "#A9AF7E",
  "#7D8F69",
  "#557153",
  "#FAD6A5",
  "#FA7070",
  "#81CACF",
];
const nestedCarouselDataFour = ["#E6E5A3", "#A9AF7E", "#7D8F69", "#557153"];
const images = [
  "https://images.unsplash.com/photo-1549989476-69a92fa57c36?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=crop&w=800&q=60",
  "https://images.unsplash.com/photo-1549396535-c11d5c55b9df?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=60",
  "https://images.unsplash.com/photo-1550133730-695473e544be?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=crop&w=800&q=60",
  "https://images.unsplash.com/photo-1550167164-1b67c2be3973?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=crop&w=800&q=60",
  "https://images.unsplash.com/photo-1550338861-b7cfeaf8ffd8?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=crop&w=800&q=60",
  "https://images.unsplash.com/photo-1550223640-23097fc71cb2?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=crop&w=800&q=60",
  "https://images.unsplash.com/photo-1550353175-a3611868086b?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=crop&w=800&q=60",
  "https://images.unsplash.com/photo-1550330039-a54e15ed9d33?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=crop&w=800&q=60",
  "https://images.unsplash.com/photo-1549737328-8b9f3252b927?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=crop&w=800&q=60",
  "https://images.unsplash.com/photo-1549833284-6a7df91c1f65?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=crop&w=800&q=60",
  "https://images.unsplash.com/photo-1549985908-597a09ef0a7c?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=crop&w=800&q=60",
  "https://images.unsplash.com/photo-1550064824-8f993041ffd3?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=crop&w=800&q=60",
];

export const Default = (args) => {
  const carouselItems = images;

  return (
    <div style={{ margin: "5%" }}>
      <Carousel {...args}>
        {carouselItems.map((element, index) => {
          return (
            <div key={index} style={{ height: "100%", width: "100%" }}>
              <img src={images[index]} width={"100%"} height="400px" />
            </div>
          );
        })}
      </Carousel>
    </div>
  );
};
Default.args = {
  responsive: {
    desktop: {
      items: 1,
    },
  },
  deviceType: "desktop",
  showDots: false,
};

export const NestedCarousel = (args) => {
  const itemss = nestedCarouselDataSix;
  return (
    <div style={{ margin: "2%" }}>
      <Carousel {...args} id="outer">
        {itemss.map((element, index) => {
          return (
            <div
              key={`carousel-child-${index + 1}`}
              id={`carousel-child-${index + 1}`}
              style={{
                // height:"350px",
                background: "#fff",

                textAlign: "center",
                color: "white",
                fontWeight: "bold",
                display: "block",
                borderRadius: "5px",
                boxShadow: "rgb(149 157 165 / 20%) 0px 8px 24px",
                marginBottom: "20px",
                // display: "flex",
                flexDirection: "column",
              }}
            >
              <div id="outer-custom-wrap">
                <Carousel
                  responsive={{ desktop: { items: 1 } }}
                  deviceType="desktop"
                  showDots={true}
                  arrows={false}
                  id="inner"
                  customLeftArrow={<p>{`<`}</p>}
                  customRightArrow={<p>{`>`}</p>}
                  autoPlay={true}
                >
                  {Array.from({ length: nestedCarouselDataFour.length }).map(
                    (element, index) => {
                      return (
                        <div
                          key={`nested-carousel-child-${index + 1}`}
                          id={`nested-carousel-child-${index + 1}`}
                          style={{
                            background: nestedCarouselDataFour[index],
                            minHeight: "175px",
                            width: "100%",
                            textAlign: "center",
                            color: "white",
                            fontWeight: "bold",
                            display: "block",
                          }}
                        >
                          <p style={{ margin: "0px", paddingTop: "40px" }}>
                            {index + 1}
                          </p>
                          <p>{`Color-Code: ${nestedCarouselDataFour[index]}`}</p>
                        </div>
                      );
                    }
                  )}
                </Carousel>
              </div>
              <div id={`word-index-${index}`} style={{ minHeight: "175px" }}>
                <div
                  style={{
                    display: "flex",
                    flexDirection: "column",
                    color: "black",
                    padding: "20px",
                  }}
                >
                  <b>Carousel Item</b>
                  <span>{index + 1}</span>
                </div>
              </div>
            </div>
          );
        })}
      </Carousel>
    </div>
  );
};
NestedCarousel.args = {
  responsive: {
    desktop: {
      items: 3,
      partialVisibilityGutter: 40,
    },
  },
  deviceType: "desktop",
  showDots: false,
};

export const TwoSlideCarousel = (args) => {
  const carouselItems = nestedCarouselDataSix;
  return (
    <div style={{ margin: "2%" }}>
      <Carousel {...args}>
        {carouselItems.map((element, index) => {
          return (
            <div
              key={`carousel-child-${index + 1}`}
              id={`carousel-child-${index + 1}`}
              style={{
                height: "300px",
                background: element,

                textAlign: "center",
                color: "white",
                fontWeight: "bold",
                display: "block",
              }}
            >
              <div style={{ paddingTop: "15%" }}>
                <span>{index + 1}</span>
              </div>
            </div>
          );
        })}
      </Carousel>
    </div>
  );
};
TwoSlideCarousel.storyName = "Two Slides Carousel";
TwoSlideCarousel.args = {
  responsive: {
    desktop: {
      items: 2,
    },
  },
  deviceType: "desktop",
  showDots: true,
};

export const ThirdSlidePartialCarousel = (args) => {
  const carouselItems = nestedCarouselDataSix;
  return (
    <div style={{ margin: "2%" }}>
      <Carousel {...args}>
        {carouselItems.map((element, index) => {
          return (
            <div
              key={`carousel-child-${index + 1}`}
              id={`carousel-child-${index + 1}`}
              style={{
                height: "300px",
                background: element,
                width: "100%",
                textAlign: "center",
                color: "white",
                fontWeight: "bold",
                display: "flex",
              }}
            >
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  height: "100%",
                  width: "100%",
                }}
              >
                <span>{index + 1}</span>
              </div>
            </div>
          );
        })}
      </Carousel>
    </div>
  );
};
ThirdSlidePartialCarousel.storyName = "Third Slide Partial Visible";
ThirdSlidePartialCarousel.args = {
  responsive: {
    desktop: {
      items: 3.5,
      partialVisibilityGutter: 10,
    },
  },
  deviceType: "desktop",
  showDots: true,
};

export const AutoPlayCarousel = (args) => {
  const carouselItems = nestedCarouselDataSix;
  return (
    <div style={{ margin: "2%" }}>
      <Carousel {...args}>
        {carouselItems.map((element, index) => {
          return (
            <div
              key={`carousel-child-${index + 1}`}
              id={`carousel-child-${index + 1}`}
              style={{
                height: "300px",
                background: element,

                textAlign: "center",
                color: "white",
                fontWeight: "bold",
                display: "block",
              }}
            >
              <div style={{ paddingTop: "15%" }}>
                <span>{index + 1}</span>
              </div>
            </div>
          );
        })}
      </Carousel>
    </div>
  );
};
AutoPlayCarousel.storyName = "Auto-Play Carousel";
AutoPlayCarousel.args = {
  responsive: {
    desktop: {
      items: 1,
      partialVisibilityGutter: 0,
    },
  },
  deviceType: "desktop",
  showDots: true,
  autoPlay: true,
};

export const MobileCarousel = (args) => {
  const carouselItems = nestedCarouselDataSix;
  return (
    <div>
      <Carousel {...args}>
        {carouselItems.map((element, index) => {
          return (
            <div
              key={`carousel-child-${index + 1}`}
              id={`carousel-child-${index + 1}`}
              style={{
                padding: "10px",
              }}
            >
              <div
                id={`Card-${index}`}
                style={{
                  boxShadow:
                    "rgba(0, 0, 0, 0.02) 0px 1px 3px 0px, rgba(27, 31, 35, 0.15) 0px 0px 0px 1px",
                  display: "flex",
                  flexDirection: "column",
                  borderRadius: "10px",
                }}
              >
                <div id="outer-custom-wrap" style={{ padding: "10px" }}>
                  <Carousel
                    responsive={{ desktop: { items: 1 } }}
                    deviceType="desktop"
                    showDots={true}
                    arrows={false}
                    id="inner"
                    customLeftArrow={<p>{`<`}</p>}
                    customRightArrow={<p>{`>`}</p>}
                    autoPlay={false}
                    swipeable={true}
                    draggable={true}
                    additionalStyle={{ borderRadius: "5px" }}
                  >
                    {Array.from({length:100}).map((element, index) => {
                      return (
                        <div
                          key={`nested-carousel-child-${index + 1}`}
                          id={`nested-carousel-child-${index + 1}`}
                          style={{
                            background: element,
                            minHeight: "175px",

                            textAlign: "center",
                            color: "white",
                            fontWeight: "bold",
                            display: "block",
                          }}
                        >
                          <p style={{ margin: "0px", paddingTop: "40px",color:"black" }}>
                            {index + 1}
                          </p>
                          <p style={{color:"black"}}>{`Color-Code: ${index+1}`}</p>
                        </div>
                      );
                    })}
                  </Carousel>
                </div>
                <div
                  style={{
                    minHeight: "250px",
                    textAlign: "center",
                    display: "block",
                  }}
                >
                  <div
                    style={{
                      display: "flex",
                      flexDirection: "column",
                      color: "black",
                      paddingTop: "20px",
                    }}
                  >
                    <b>Carousel Item</b>
                    <b>{index + 1}</b>
                  </div>
                </div>
              </div>
            </div>
          );
        })}
      </Carousel>
    </div>
  );
};
MobileCarousel.storyName = "Mobile Carousel";
MobileCarousel.parameters = {
  viewport: { defaultViewport: "iphone5" },
};
MobileCarousel.args = {
  responsive: {
    mobile: {
      items: 1,
      partialVisibilityGutter: 0,
    },
  },
  deviceType: "mobile",
  showDots: true,
  arrows: false,
  swipeable: true,
  draggable: true,
};

export const CustomSlideCarouselTransition = (args) => {
  const carouselItems = nestedCarouselDataSix;
  return (
    <div style={{ display: "flex", flexDirection: "column" }}>
      <div>{`Custom Carousel Item transition is ${`"all 1s ease-in-out"`}`}</div>
      <div style={{ margin: "2%" }}>
        <Carousel {...args}>
          {carouselItems.map((element, index) => {
            return (
              <div
                key={`carousel-child-${index + 1}`}
                id={`carousel-child-${index + 1}`}
                style={{
                  height: "300px",
                  background: element,

                  textAlign: "center",
                  color: "white",
                  fontWeight: "bold",
                  display: "block",
                }}
              >
                <div style={{ paddingTop: "15%" }}>
                  <span>{index + 1}</span>
                </div>
              </div>
            );
          })}
        </Carousel>
      </div>
    </div>
  );
};
CustomSlideCarouselTransition.storyName = "Custom Transition Carousel";
CustomSlideCarouselTransition.args = {
  responsive: {
    desktop: {
      items: 1,
      partialVisibilityGutter: 0,
    },
  },
  deviceType: "desktop",
  showDots: true,
  customTransition: "all 1s ease-in-out",
};

export const LazyLoadingCarousel = (args) => {
  const carouselItems = images;
  return (
    <div style={{ margin: "5%" }}>
      <Carousel {...args}>
        {carouselItems.map((element, index) => {
          return (
            <LazyImage
              key={index}
              src={index == 0 ? "https://images" : element}
              alt={"."}
              aspectRatio={[2, 1]}
              height="400px"
              placeholderImage={
                "https://img.icons8.com/cute-clipart/50/000000/nothing-found.png"
              }
            />
          );
        })}
      </Carousel>
    </div>
  );
};
LazyLoadingCarousel.storyName = "Lazy Loading Carousel";
LazyLoadingCarousel.args = {
  responsive: {
    desktop: {
      items: 1,
    },
  },
  deviceType: "desktop",
  showDots: false,
};

export const CustomControl = (args) => {
  const carouselItems = images;
  const carouselRef = React.useRef();
  const [currentIndex, setCurrentIndex] = useState(0);
  const [maxIndex, setMaxIndex] = useState(0);

  // Add methods to control the carousel
  const nextSlide = () => {
    setCurrentIndex((prevIndex) => prevIndex + 1);
  };

  const prevSlide = () => {
    setCurrentIndex((prevIndex) => prevIndex - 1);
  };
  // console.log("carosel**", carouselRef)
  // console.log("currentIndex", currentIndex <= 0, currentIndex , currentIndex >= carouselItems.length - 1)

  return (
    <div style={{ margin: "5%" }}>
      <Carousel
        {...args}
        //ref={carouselRef}
        activeSelectedIndex={currentIndex}
        onCarouselSlideChange={(activeIndex) => {
          console.log("onCarouselSlideChange", activeIndex);
          setCurrentIndex(activeIndex);
        }}
        showDots
        draggable
        onCarouselLoad={(maxCarouselIndex) => {
          setMaxIndex(maxCarouselIndex);
        }}
      >
        {carouselItems.map((element, index) => {
          return (
            <div key={index} style={{ height: "100%", width: "100%" }}>
              <img src={images[index]} width={"100%"} height="400px" />
            </div>
          );
        })}
      </Carousel>
      <button
        style={{
          border: "none",
          borderRadius: "50px",
          height: "40px",
          width: "60px",
          marginRight: "10px",
          fontWeight: "bold",
          backgroundColor: currentIndex <= 0 ? "#7fb3b5" : "#03868b",
          color: "#fff",
        }}
        onClick={prevSlide}
        disabled={currentIndex <= 0}
      >
        Prev
      </button>
      <button
        style={{
          border: "none",
          borderRadius: "50px",
          height: "40px",
          width: "60px",
          fontWeight: "bold",
          backgroundColor:
            currentIndex >= carouselItems.length - 1 ? "#7fb3b5" : "#03868b",
          color: "#fff",
        }}
        onClick={nextSlide}
        disabled={currentIndex == maxIndex}
      >
        Next
      </button>
    </div>
  );
};
CustomControl.args = {
  responsive: {
    desktop: {
      items: 1,
    },
  },
  deviceType: "desktop",
  showDots: false,
};

export const SliderWithTabs = (args) => {
  const [sliderData, setSliderData] = useState([]);
  const [tabIndex, setSelectedTabIndex] = useState(1);
  const [activeSliderData, setActiveSliderData] = useState([]);
  const [showPopup, setShowPopup] = useState(false);
  const tabData = [
    {
      id: 1,
      title: "Image-Set-1",
    },
    {
      id: 2,
      title: "Image-Set-2",
    },
    {
      id: 3,
      title: "Image-Set-3",
    },
  ];
  const fetchData = async () => {
    const data = [];
    for (let count = 1; count <= 3; count++) {
      let limit = 7;
      if (count == 1) {
        limit = 7;
      } else if (count == 2) {
        limit = 3;
      } else {
        limit = 1;
      }
      const response = await fetch(
        `https://picsum.photos/v2/list?page=${count + 10}&limit=${limit}`
      );
      const jsonImgs = await response.json();
      const images = [];
      jsonImgs.forEach((e, index) => {
        images.push(e);
      });
      data.push({
        id: count - 1,
        images: images,
      });
      if (count == 3) {
        setSliderData(data);
        setActiveSliderData(data[1]);
      }
      console.log("json images", jsonImgs);
    }
  };
  useEffect(() => {
    console.log("useEffect fetchData");
    fetchData();
  }, []);
  console.log("sliderData", sliderData, activeSliderData);

  const onTabChangeHandler = (selectedTabIndex) => {
    const data = sliderData.find((data) => data.id == selectedTabIndex);
    console.log("onTabChangeHandler", data);
    setActiveSliderData(data);
    setSelectedTabIndex(selectedTabIndex);
  };

  return (
    <Fragment>
      <div
        style={{
          display: "flex",
          margin: "5%",
          flexDirection: "column",
          gap: "20px",
        }}
      >
        {/* <CarouselItems activeSliderData={activeSliderData}/>       */}
        {activeSliderData?.images?.length > 0 ? (
          <div>
            <Carousel
              responsive={{
                desktop: {
                  items: activeSliderData?.images?.length > 1 ? 1.3 : 1,
                  partialVisibilityGutter: 0,
                },
              }}
              deviceType="desktop"
              showDots={true}
              autoPlay={false}
            >
              {activeSliderData.images.map((image, index) => {
                console.log("running map of images", image);
                return (
                  <div
                    key={`tab-${index}-${image.download_url}`}
                    style={{ height: "100%", width: "100%" }}
                  >
                    <img
                      key={image.download_url}
                      height={"300px"}
                      width={"100%"}
                      src={image.download_url}
                    />
                  </div>
                );
              })}
            </Carousel>
            <div
              style={{
                position: "absolute",
                top: "60%",
                height: "auto",
                left: "80%",
              }}
            >
              <button
                style={{
                  color: "blue",
                  background: "white",
                  height: "34px",
                  borderRadius: "15px",
                }}
                onClick={(e) => {
                  e.stopPropagation();
                  setShowPopup(true);
                }}
              >{`Show +${activeSliderData.images.length}`}</button>
            </div>
          </div>
        ) : (
          <div>
            <Skeleton height={"300px"} width={"100%"} />
          </div>
        )}
        <div>
          <AnchoredHorizontalTabs
            tabHeader={tabData}
            onChange={(selectedTabObject, selectedTabIndex) => {
              onTabChangeHandler(selectedTabIndex);
            }}
            selectedTabIndex={tabIndex}
          />
        </div>
        <div>
          <span>
            Lorem ipsum dolor sit amet, consectetur adipiscing elit. Curabitur
            id tellus in magna condimentum rutrum. Nulla magna purus,
            scelerisque vitae fringilla fringilla, rhoncus a nisl. Aliquam erat
            volutpat. Donec interdum, nunc ac porta molestie, purus ante
            tristique tortor, id molestie mauris arcu at est. Nulla facilisi.
            Cras tincidunt quam eget nisl accumsan, nec lacinia mauris iaculis.
            Aenean commodo, nulla pretium posuere blandit, elit orci finibus
            nisi, at accumsan tellus risus sed velit. Etiam nec fringilla ante,
            id condimentum nibh. Aliquam sed bibendum dolor. Nulla sagittis
            ullamcorper mauris et dictum. Nulla id dolor lectus. In ullamcorper
            non elit sed imperdiet. Vivamus orci est, efficitur ornare convallis
            nec, tristique id dui. Donec lobortis metus vitae commodo mattis. In
            hac habitasse platea dictumst. In tincidunt, nibh porta suscipit
            cursus, orci nunc luctus tortor, sed pellentesque diam orci a diam.
            Fusce nibh arcu, vulputate sit amet lacus bibendum, accumsan
            venenatis massa. Suspendisse potenti. Cras maximus posuere velit sed
            hendrerit. Mauris varius orci quam. Quisque imperdiet magna a nibh
            tempus, ut dignissim velit eleifend. Ut rutrum augue a neque feugiat
            rutrum. Quisque lobortis, mauris a congue tristique, tortor augue
            feugiat turpis, sed consectetur erat neque id felis. In hac
            habitasse platea dictumst.
          </span>
        </div>
      </div>
      {showPopup && (
        <ModalPopup
          closeButtonAlignment="right"
          isOpen={showPopup}
          onRequestClose={() => {
            setShowPopup(false);
          }}
          additionalStyle={{
            // left:"5px",
            // right:"5px",
            // top:"5px",
            // bottom:"5px",
            inset: "1%",
            // backgroundColor:"transparent"
          }}
          // overlayClassName={[styles.overlayBckgColor]}
        >
          <div
            style={{ display: "flex", flexDirection: "column", gap: "20px" }}
          >
            <div>
              <AnchoredHorizontalTabs
                tabHeader={tabData}
                onChange={(selectedTabObject, selectedTabIndex) => {
                  onTabChangeHandler(selectedTabIndex);
                }}
                selectedTabIndex={tabIndex}
                additionalStyle={{
                  position: "absolute",
                  top: "10px",
                  backgroundColor: "transparent",
                  width: "90%",
                }}
              />
              <div>
                <Carousel
                  responsive={{
                    desktop: {
                      items: activeSliderData?.images?.length > 1 ? 1.2 : 1,
                      partialVisibilityGutter: 10,
                    },
                  }}
                  deviceType="desktop"
                  showDots={false}
                  autoPlay={false}
                  additionalClassName={[styles.carousel_items]}
                >
                  {activeSliderData?.images?.map((image, index) => {
                    console.log("running map of images", image);
                    return (
                      <div
                        key={`tab-${index}-${image.download_url}`}
                        style={{ height: "100%", width: "100%" }}
                      >
                        <img
                          key={image.download_url}
                          height={400}
                          width={"100%"}
                          src={image.download_url}
                        />
                      </div>
                    );
                  })}
                </Carousel>
              </div>
            </div>
          </div>
        </ModalPopup>
      )}
    </Fragment>
  );
};

const CarouselItems = ({ activeSliderData }) => {
  const [items, setItems] = useState([]);
  useEffect(() => {
    console.log("re-render items", activeSliderData);
    setItems(activeSliderData);
  }, [activeSliderData]);
  return (
    <>
      {items?.images?.length > 0 && (
        <div>
          <Carousel
            responsive={{
              desktop: {
                items: 1,
                partialVisibilityGutter: 0,
              },
            }}
            deviceType="desktop"
            showDots={true}
            autoPlay={false}
          >
            {items.images.map((image, index) => {
              console.log("running map of images", image);
              return (
                <div
                  key={`tab-${index}-${image.download_url}`}
                  style={{ height: "100%", width: "100%" }}
                >
                  <img
                    key={image.download_url}
                    height={"300px"}
                    width={"100%"}
                    src={image.download_url}
                  />
                </div>
              );
            })}
          </Carousel>
        </div>
      )}
    </>
  );
};
