import styled from "styled-components";

export const CarouselWrapperDiv = styled.div`
  display: flex;
  position: relative;
  overflow: hidden;
  padding-bottom: ${({ renderDotsOutside }) =>
    renderDotsOutside === true ? "20px" : "0px"};
`;

export const CarouselChildrenWrapper = styled.div`
  display: flex;
  overflow: unset;
  overflow: unset;
  transform: ${({ slideDirectionWidth }) =>
    `translate3d(${slideDirectionWidth}px, 0px, 0px)`};
  flex-direction: row;
  scroll-behavior: smooth;
  /* transition: all 0.5s ease; */
  transition-property: all;
  transition-timing-function: ease;
  transition-duration: ${({ transitionDuration }) =>
    transitionDuration !== null ? `${transitionDuration}s` : `0.5s`};
  ${({ customTransition }) =>
    customTransition !== null && `transition:${customTransition}`};
  background-color: #fff;
`;

export const ChildItems = styled.ol`
  display: flex;
  list-style: none;
  padding: 0px;
  margin: 0px;
  overflow: hidden;
  position: relative;
  background-color: #fff;
`;
export const ChildItem = styled.li`
  /* flex-grow: 1 1 0; */
  justify-content: center;
  align-items: center;
  background-color: #fff;
  padding-left: ${({ paddingLeftRight }) => `${paddingLeftRight}px`};
  padding-right: ${({ paddingLeftRight }) => `${paddingLeftRight}px`};
  width: ${({ width }) => `${width}px`};
`;

export const PrevDiv = styled.div`
  visibility: ${({ showButton }) =>
    showButton === false ? "hidden" : "visible"};
  opacity: ${({ showButton }) => (showButton === false ? "0" : "1")};
  position: absolute;
  left: 0px;
  top: 50%;
  transform: translateY(-50%);
  user-select: none;
  width: auto;
  cursor: ${({showButton}) => showButton===false? "normal":"pointer"};
  min-width: 43px;
  min-height: 43px;
  max-width: 47px;
  max-height: 47px;
  z-index: 100;
  transition: all 0.3s ease-in-out;
`;
export const PrevButton = styled.button`
  position: absolute;
  padding: 0px;
  margin: 0px;
  background: transparent;
  border: none;
  cursor: pointer;
  background-color: #ffffff;
  border-radius: 100%;
  min-width: 43px;
  min-height: 43px;
  max-width: 47px;
  max-height: 47px;
  box-shadow: 0 2px 4px 0 rgb(0 0 0 / 17%);
  text-align: center;
  align-items: center;
  top: 50%;
  transform: translateY(-50%);
`;
export const PrevButtonSvg = styled.svg`
  min-width: 14px;
  min-height: 14px;
  color: ${({ theme }) => theme.palette.primary[theme.mode]};
`;
export const NextDiv = styled.div`
  visibility: ${({ showButton }) =>
    showButton === false ? "hidden" : "visible"};
  opacity: ${({ showButton }) => (showButton === false ? "0" : "1")};
  position: absolute;
  right: 0px;
  top: 50%;
  transform: translateY(-50%);
  user-select: none;
  width: auto;
  cursor: ${({showButton}) => showButton===false? "normal":"pointer"};
  min-width: 43px;
  min-height: 43px;
  max-width: 47px;
  max-height: 47px;
  z-index: 100;
  transition: all 0.3s ease-in-out;
`;
export const NextButton = styled.button`
  position: absolute;
  padding: 0px;
  margin: 0px;
  background: transparent;
  border: none;
  cursor: pointer;
  background-color: #ffffff;
  border-radius: 100%;
  min-width: 43px;
  min-height: 43px;
  max-width: 47px;
  max-height: 47px;
  box-shadow: 0 2px 4px 0 rgb(0 0 0 / 17%);
  text-align: center;
  align-items: center;
  transition: all 0.5s ease-in-out;
  top: 50%;
  transform: translateY(-50%);
`;
export const NextButtonSvg = styled.svg`
  color: ${({ theme }) => theme.palette.primary[theme.mode]};
`;

export const DotsDiv = styled.div`
  position: absolute;
  bottom: 0px;
  padding-bottom: ${({ renderDotsOutside }) =>
    renderDotsOutside === true ? "0px" : "20px"};
  left: 50%;
  transform: translateX(-50%);
  background: transparent;
  display: flex;
  width: 90%;
  overflow-x:auto;
  justify-content: center;
`;
export const SingleDotDiv = styled.div``;
export const SingleDotButton = styled.button`
  padding: 0px;
  cursor: pointer;
  height: 9px;
  width: 9px;
  margin: 0 2px;
  background-color: ${({ isActive }) => (isActive === true ? "#000" : "#bbb")};
  border-radius: 50%;
  display: inline-block;
  transition: background-color 0.6s ease;
  border: none;
  :hover {
    background-color: #000;
  }
`;
