import React, { useEffect, useState, useRef, forwardRef, useMemo, useCallback } from "react";
import PropTypes from "prop-types";
import { useClassName } from "../../hooks/useClassName";
import {
  CarouselChildrenWrapper,
  CarouselWrapperDiv,
  ChildItem,
  ChildItems,
  DotsDiv,
  NextButton,
  NextButtonSvg,
  NextDiv,
  PrevButton,
  PrevButtonSvg,
  PrevDiv,
  SingleDotButton,
  SingleDotDiv,
} from "./Carousel.styled";
import { ThemeWrapper } from "../Theme/ThemeContext";

const TOUCH_START = "TOUCH-START";
const TOUCH_END = "TOUCH-END";
const DRAG_START = "DRAG-START";
const DRAG_END = "DRAG-END";
export const Carousel = React.memo(
  forwardRef((props, ref) => {
    const {
      id,
      children,
      responsive,
      deviceType,
      showDots,
      arrows,
      customLeftArrow,
      customRightArrow,
      customDot,
      autoPlay,
      autoPlaySpeed,
      draggable,
      swipeable,
      renderDotsOutside,
      itemClass,
      dotClass,
      partialVisible,
      customTransition,
      transitionDuration,
      additionalClassName,
      additionalStyle,
      activeSelectedIndex,
      onCarouselSlideChange,
      onCarouselLoad,
      isMobile,
      isMobileView
    } = props;
    const [counter, setCounter] = useState(0);
    const [dotsCount, setDotsCount] = useState(0);
    const [activeCarouselIndex, setActiveCarouselIndex] = useState(0);
    const [childItemWidth, setChildItemWidth] = useState([]);
    const [slideDirectionWidth, setSlideDirectionWidth] = useState(0);
    const [childItemDragStartPosition, setChildItemDragStartPosition] =
      useState(null);
    const carouselRef = useRef(null);
    const className = useClassName(props, additionalClassName);

    // Memoize expensive calculations
    const memoizedResponsiveConfig = useMemo(() => responsive[deviceType], [responsive, deviceType]);
    
    const itemsToDisplay = useMemo(() => {
      let items = memoizedResponsiveConfig.items;
      if (items === 1 && partialVisible === true) {
        items = 1.1;
      }
      return items;
    }, [memoizedResponsiveConfig.items, partialVisible]);

    const gap = useMemo(() => {
      return memoizedResponsiveConfig?.partialVisibilityGutter || 0;
    }, [memoizedResponsiveConfig]);

    useEffect(() => {
      //console.log("useEffect children updated !!!");
      setChildItemWidth([]);
      calculateWidthOfChildItem();
      setActiveCarouselIndex(0);
    }, [children?.length, carouselRef?.current?.offsetWidth]);

    useEffect(() => {
      //console.log("useEffect of activeSelectedIndex", activeSelectedIndex);
      if (
        isCarouselIndexValid(activeSelectedIndex) &&
        childItemWidth?.length > 0
      ) {
        //console.log("activeSelectedIndex isValid");
        setActiveCarouselIndex(activeSelectedIndex);
      }
    }, [activeSelectedIndex]);

    useEffect(() => {
      if (childItemWidth?.length > 0) {
        //console.log("useEffect of activeCarouselIndex", activeCarouselIndex);
        slideChangeHandler();
      }
    }, [activeCarouselIndex]);

    const calculateWidthOfChildItem = useCallback(() => {
      const carouselWrapperWidth = carouselRef.current?.offsetWidth;
      if (!carouselWrapperWidth) return;
      
      const childItemWidth = [];
      const dotsCount = isDecimal(itemsToDisplay)
        ? Math.round(children.length - itemsToDisplay)
        : children.length - itemsToDisplay;
      
      const childItemWidthToSet = carouselWrapperWidth / itemsToDisplay;

      for (let count = 0; count < children.length; count++) {
        if (count === children.length - 1 && isDecimal(Number(itemsToDisplay))) {
          childItemWidth.push(carouselWrapperWidth);
        } else {
          childItemWidth.push(childItemWidthToSet - gap);
        }
      }
      
      setDotsCount(dotsCount);
      setChildItemWidth(childItemWidth);
      
      if (onCarouselLoad) {
        onCarouselLoad(dotsCount);
      }
    }, [itemsToDisplay, children.length, gap, onCarouselLoad]);

    const isCarouselIndexValid = useCallback((index) => {
      return index != null && dotsCount != null && index >= 0 && index <= dotsCount;
    }, [dotsCount]);

    const getWidthToScroll = useCallback((index) => {
      let widthToScroll = 0;
      for (let count = 0; count < index; count++) {
        widthToScroll += (childItemWidth[count] + gap);
      }
      return widthToScroll;
    }, [childItemWidth, gap]);

    const slideChangeHandler = useCallback(() => {
      const widthToScroll = getWidthToScroll(activeCarouselIndex);
      setSlideDirectionWidth(0 - widthToScroll);
      
      if (onCarouselSlideChange) {
        onCarouselSlideChange(activeCarouselIndex);
      }
    }, [activeCarouselIndex, getWidthToScroll, onCarouselSlideChange]);

    const onDotClickHandler = useCallback((selectedIndex) => {
      setActiveCarouselIndex((prevActiveCarouselIndex) => {
        if (isCarouselIndexValid(selectedIndex)) {
          return selectedIndex;
        } else {
          return prevActiveCarouselIndex;
        }
      });
    }, [isCarouselIndexValid]);

    const childItemTouchHandler = useCallback((action, e) => {
      let clientXValue = null;
      let slideChildItem = false;
      
      switch (action) {
        case TOUCH_START:
          if (e.targetTouches.length === 1) {
            clientXValue = e.targetTouches[0].clientX;
          }
          break;
        case TOUCH_END:
          if (e.changedTouches.length === 1) {
            clientXValue = e.changedTouches[0].clientX;
            slideChildItem = true;
          }
          break;
        case DRAG_START:
          clientXValue = e.clientX;
          break;
        case DRAG_END:
          slideChildItem = true;
          clientXValue = e.clientX;
          break;
      }
      
      if (slideChildItem && clientXValue != null) {
        if (clientXValue < childItemDragStartPosition) {
          onDotClickHandler(activeCarouselIndex + 1);
        } else {
          onDotClickHandler(activeCarouselIndex - 1);
        }
        setChildItemDragStartPosition(null);
      } else {
        setChildItemDragStartPosition(clientXValue);
      }
    }, [childItemDragStartPosition, activeCarouselIndex, onDotClickHandler]);

    const handlePrevClick = useCallback((e) => {
      e.stopPropagation();
      setActiveCarouselIndex((prevState) => {
        const valueToUpdate = prevState - 1;
        if (isCarouselIndexValid(valueToUpdate)) {
          return valueToUpdate;
        } else {
          return prevState;
        }
      });
    }, [isCarouselIndexValid]);

    const handleNextClick = useCallback((e) => {
      e.stopPropagation();
      setActiveCarouselIndex((prevState) => {
        const valueToUpdate = prevState + 1;
        if (isCarouselIndexValid(valueToUpdate)) {
          return valueToUpdate;
        } else {
          return prevState;
        }
      });
    }, [isCarouselIndexValid]);

    // Memoize rendered children to prevent unnecessary re-renders
    const renderedChildren = useMemo(() => {
      if (!children || childItemWidth?.length === 0) return null;
      
      return children.map((child, index) => (
        <ChildItem
          className={`${itemClass} vms_carousel_childitem ${
            index === activeCarouselIndex ? "vms_carousel_childitem_active" : ""
          }`}
          key={`${id || ""}-carousel-child-item-${index}`}
          ref={ref}
          isActive={index === activeCarouselIndex}
          paddingLeftRight={gap / 2}
          width={childItemWidth.at(index)}
          draggable={draggable}
          onDragStart={(e) => {
            if (draggable) {
              e.stopPropagation();
              childItemTouchHandler(DRAG_START, e);
            }
          }}
          onDragEnd={(e) => {
            if (draggable) {
              e.stopPropagation();
              childItemTouchHandler(DRAG_END, e);
            }
          }}
          onTouchStart={(e) => {
            if (swipeable === true) {
              e.stopPropagation();
              childItemTouchHandler(TOUCH_START, e);
            }
          }}
          onTouchEnd={(e) => {
            if (swipeable === true) {
              e.stopPropagation();
              childItemTouchHandler(TOUCH_END, e);
            }
          }}
        >
          {child}
        </ChildItem>
      ));
    }, [
      children,
      childItemWidth,
      activeCarouselIndex,
      itemClass,
      id,
      ref,
      gap,
      draggable,
      swipeable,
      childItemTouchHandler
    ]);

    // Memoize dots rendering
    const renderedDots = useMemo(() => {
      if (!showDots) return null;
      
      return Array.from({ length: dotsCount + 1 }).map((element, index) => (
        <SingleDotDiv
          className={`${dotClass} ${
            index === activeCarouselIndex ? "vmsActiveDot" : ""
          } vms_carousel_singledotdiv`}
          key={`${id || ""}-carousel-dots-count-${index}`}
          onClick={(e) => {
            e.stopPropagation();
            onDotClickHandler(index);
          }}
        >
          {customDot || (
            <SingleDotButton
              isActive={index === activeCarouselIndex}
              className="vms_carousel_singledotbutton"
            />
          )}
        </SingleDotDiv>
      ));
    }, [
      showDots,
      dotsCount,
      activeCarouselIndex,
      dotClass,
      id,
      customDot,
      onDotClickHandler
    ]);

    useEffect(() => {
      //console.log("useEffect autoplay",activeCarouselIndex,dotsCount);
      if (autoPlay === true && children && children.length > 1) {
        const interval = setInterval(() => {
          setActiveCarouselIndex((prevActiveCarouselIndex) => {
            if (prevActiveCarouselIndex == dotsCount) {
              return 0;
            } else {
              return prevActiveCarouselIndex + 1;
            }
          });
          setCounter((prevCounter) => prevCounter + 1);
        }, autoPlaySpeed);
        return () => {
          //console.log("clear-interval");
          clearInterval(interval);
        };
      }
    }, [autoPlay, counter]);

    // ...existing return statement...
    return (
      <ThemeWrapper isMobile={isMobile || isMobileView || false}>
        <CarouselWrapperDiv
          id={id}
          ref={carouselRef}
          renderDotsOutside={renderDotsOutside}
          className={`${className} vms_carousel_container`}
          style={additionalStyle}
        >
          {arrows === true && (
            <PrevDiv
              className="vms_carousel_previousdiv"
              showButton={activeCarouselIndex > 0}
              onClick={handlePrevClick}
            >
              {customLeftArrow || (
                <PrevButton
                  disabled={!(activeCarouselIndex > 0)}
                  className="vms_carousel_previousbutton"
                >
                  <PrevSvg />
                </PrevButton>
              )}
            </PrevDiv>
          )}
          
          {renderedChildren && (
            <CarouselChildrenWrapper
              customTransition={customTransition}
              transitionDuration={transitionDuration}
              slideDirectionWidth={slideDirectionWidth}
              className="vms_carousel_childcontainer"
            >
              <ChildItems className="vms_carousel_childitems">
                {renderedChildren}
              </ChildItems>
            </CarouselChildrenWrapper>
          )}
          
          {arrows === true && (
            <NextDiv
              className="vms_carousel_nextdiv"
              showButton={activeCarouselIndex < dotsCount}
              onClick={handleNextClick}
            >
              {customRightArrow || (
                <NextButton
                  disabled={!(activeCarouselIndex <= dotsCount)}
                  className="vms_carousel_nextbutton"
                >
                  <NextSvg />
                </NextButton>
              )}
            </NextDiv>
          )}
          
          {showDots === true && (
            <DotsDiv
              renderDotsOutside={renderDotsOutside}
              className="vms_carousel_dotsdiv"
            >
              {renderedDots}
            </DotsDiv>
          )}
        </CarouselWrapperDiv>
      </ThemeWrapper>
    );
  })
);
Carousel.defaultProps = {
  showDots: false,
  deviceType: "desktop",
  draggable: false,
  arrows: true,
  swipeable: true,
  removeArrowOnDeviceType: null,
  children: null,
  customLeftArrow: null,
  customRightArrow: null,
  customDot: null,
  autoPlay: false,
  autoPlaySpeed: 3000,
  // showDots: false,
  id: null,
  renderDotsOutside: false,
  itemClass: null,
  dotClass: null,
  partialVisible: false,
  customTransition: null,
  transitionDuration: null,
  slideDirection: "",
  activeCarouselIndex: 0,
  onCarouselLoad: null,
  onCarouselSlideChange: null,
};
Carousel.propTypes = {
  /**
   * id of carousel item
   */
  id: PropTypes.string,
  /**
   * This prop will accept a object for the responsive type.
   * eg. desktop: { breakpoint: { max: 3000; min: 1024 }; items: 2; partialVisibilityGutter: 40; };
   */
  responsive: PropTypes.shape({
    ["desktop|mobile"]: PropTypes.shape({
      breakpoint: PropTypes.shape({
        min: PropTypes.number.isRequired,
        max: PropTypes.number.isRequired,
      }),
      items: PropTypes.number.isRequired,
      partialVisibilityGutter: PropTypes.number,
    }),
  }).isRequired,
  /**
   * This prop can types of device which is currently consumed by, default will be desktop
   */
  deviceType: PropTypes.oneOf(["desktop", "mobile"]),
  /**
   * This prop will allow slide to be draggable. Default: `false`
   */
  draggable: PropTypes.bool,
  /**
   * To show or hide arrow in component. Default `true`
   */
  arrows: PropTypes.bool,
  /**
   * this prop will allow slide to be swipe able. Default: `true`
   */
  swipeable: PropTypes.bool,
  /**
   * this prop will display all the content in carousel component. `ReactNode`
   */
  children: PropTypes.node,
  /**
   * this prop will allow you configure the custom left arrow
   */
  customLeftArrow: PropTypes.node,
  /**
   * this prop will allow you to configure the custom right arrow
   */
  customRightArrow: PropTypes.node,
  /**
   * this prop will allow you to configure custom dots
   */
  customDot: PropTypes.node,
  // /**
  //  * After any slide has changed in carousel it will fire this event.
  //  * (previousSlide: number) => void
  //  */
  // afterChange: PropTypes.func,
  // /**
  //  * This event / callback will be called before a slide changed.
  //  * (nextSlide: number) => void
  //  */
  // beforeChange: PropTypes.func,
  /**
   * Allows you to customize the custom item class for items in component.
   */
  itemClass: PropTypes.string,
  /**
   * this prop allows to customize the dot
   */
  dotClass: PropTypes.string,
  /**
   * Auto play for the items in component. Default `false`
   */
  autoPlay: PropTypes.bool,
  /**
   * Configure auto play speed, default `3000ms`
   */
  autoPlaySpeed: PropTypes.number,
  /**
   * show or hide dots. Default `false`
   */
  showDots: PropTypes.bool,
  /**
   * show dots outside of the container for custom styling. Default `false`
   */
  renderDotsOutside: PropTypes.bool,
  /**
   * make next slide paritalVisible. Default `false`
   */
  partialVisible: PropTypes.bool,
  /**
   * allows you to add any custom transition for the slide
   * `Example: all 1s linear`
   */
  customTransition: PropTypes.string,
  /**
   * configure the transition duration
   */
  transitionDuration: PropTypes.number,
  /**
   * Property to append additional css class
   */
  additionalClassName: PropTypes.arrayOf(PropTypes.string),
  /**
   * Property to set the `inline style` object
   */
  additionalStyle: PropTypes.object,
  /**
   * Ref element
   */
  ref: PropTypes.oneOfType([
    // Either a function
    PropTypes.func,
    // Or the instance of a DOM native element (see the note about SSR)
    PropTypes.shape({ current: PropTypes.instanceOf(Carousel) }),
  ]),
  /**
   * To set active index of a carousel
   * Note: please use this property alongwith
   * onCarouselSlideChange and onCarouselLoad props to control the entire carousel functionality
   */
  activeSelectedIndex: PropTypes.number,
  /**
   * Callback executed after carousel slide changed
   * (activeCarouselIndex) => void
   */
  onCarouselSlideChange: PropTypes.func,
  /**
   * Callback executed after carousel is loaded on screen
   * (maxCarouselIndex) => void;
   */
  onCarouselLoad: PropTypes.func,
};

const PrevSvg = (props) => {
  return (
    <PrevButtonSvg
      xmlns="http://www.w3.org/2000/svg"
      width="9"
      height="14"
      viewBox="0 0 9 14"
    >
      <g fill="none" fillRule="evenodd">
        <path d="M0 0H24V24H0z" transform="translate(-8 -5)" />
        <g stroke="currentColor" strokeLinecap="round">
          <path
            d="M0 7L7 0M0 7L2.994 9.994 7 14"
            transform="translate(-8 -5) matrix(1 0 0 -1 9 19)"
          />
        </g>
      </g>
    </PrevButtonSvg>
  );
};

const NextSvg = (props) => {
  return (
    <NextButtonSvg
      xmlns="http://www.w3.org/2000/svg"
      width="9"
      height="14"
      viewBox="0 0 9 14"
    >
      <g fill="none" fillRule="evenodd">
        <path d="M0 0H24V24H0z" transform="translate(-8 -5)" />
        <g stroke="currentColor" strokeLinecap="round">
          <path
            d="M0 7L7 0M0 7L2.994 9.994 7 14"
            transform="translate(-8 -5) matrix(-1 0 0 1 16 5)"
          />
        </g>
      </g>
    </NextButtonSvg>
  );
};
const isNonNull = (prop) => {
  return prop !== null && prop !== undefined;
};
const isDecimal = (num) => {
  return num % 1;
};
