import React, { useRef, useState, useEffect, memo, useCallback, useMemo } from "react";
import PropTypes from "prop-types";
import {
  Container,
  ErrorMessage,
  Icon,
  Item,
  LeftIconContainer,
  RightIconContainer,
  WithoutScrollDiv,
  WithScrollDiv,
} from "./Chip.styled";
import BackArrowSvg from "../../assets/images/DateRangeCalendar/backArrow.svg";
import ForwardArrowSvg from "../../assets/images/DateRangeCalendar/forwardArrow.svg";
import { useClassName } from "../../hooks/useClassName";
import { ThemeWrapper } from "../Theme/ThemeContext";

// Utility function (placed before main component)
const isNonNull = (prop) => prop !== null && prop !== undefined;

const Chip = memo((props) => {
  // 1. PROP DESTRUCTURING & VALIDATION
  const {
    id,
    chipData = [],
    selectedChipIndexs = [],
    scollable = false,
    onChipSelectionChange,
    multipleSelect = false,
    showErrorMessage = false,
    errorMsgText = "Please select a chip",
    additionalStyle,
    additionalClassName,
    arrowIcons,
    isMobile,
    isMobileView,
    selectedChipStyle,
    ...otherProps
  } = props;

  // 2. REFS
  const allTabsRef = useRef();
  const scrollRefs = useRef([]);

  // 3. STATE MANAGEMENT
  const [chipDataArray, setChipDataArray] = useState(chipData);
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [isChipSelected, setIsChipSelected] = useState(false);
  const [scrollX, setScrollX] = useState(0);
  const [scrollEnd, setScrollEnd] = useState(false);

  // 4. PERFORMANCE OPTIMIZATIONS
  const computedClassName = useMemo(() => 
    useClassName(props, additionalClassName), 
    [props, additionalClassName]
  );

  const hasErrorMessage = useMemo(() => 
    showErrorMessage && !isChipSelected, 
    [showErrorMessage, isChipSelected]
  );

  const leftArrowIcon = useMemo(() => 
    arrowIcons?.leftArrowIcon || BackArrowSvg, 
    [arrowIcons?.leftArrowIcon]
  );

  const rightArrowIcon = useMemo(() => 
    arrowIcons?.rightArrowIcon || ForwardArrowSvg, 
    [arrowIcons?.rightArrowIcon]
  );

  // 5. EVENT HANDLING with useCallback
  const slide = useCallback((direction, shift) => {
    const paddingOfTabComponent = 10;
    if (allTabsRef.current) {
      allTabsRef.current.scrollLeft += shift;
      setScrollX(scrollX + shift);
      
      const isScrollEnd = Math.floor(
        allTabsRef.current.scrollWidth -
        paddingOfTabComponent -
        allTabsRef.current.scrollLeft
      ) <= allTabsRef.current.offsetWidth;
      
      setScrollEnd(isScrollEnd);
    }
  }, [scrollX]);

  const scrollCheck = useCallback((e) => {
    const paddingOfTabComponent = 10;
    if (allTabsRef.current) {
      setScrollX(allTabsRef.current.scrollLeft);
      
      const isScrollEnd = Math.floor(
        allTabsRef.current.scrollWidth -
        paddingOfTabComponent -
        allTabsRef.current.scrollLeft
      ) <= allTabsRef.current.offsetWidth;
      
      setScrollEnd(isScrollEnd);
    }
  }, []);

  const onSelect = useCallback((index) => {
    let newArr = [...chipDataArray];
    
    if (multipleSelect) {
      newArr[index].isActive = !newArr[index].isActive;
    } else {
      newArr = newArr.map((item, i) => ({
        ...item,
        isActive: i === index
      }));
    }
    
    setChipDataArray(newArr);
    setIsChipSelected(!isChipSelected);
    setSelectedIndex(index);
  }, [chipDataArray, multipleSelect, isChipSelected]);

  const scrollSmoothHandler = useCallback((index) => {
    if (scrollRefs.current[index]) {
      scrollRefs.current[index].scrollIntoView({
        behavior: "smooth",
        block: "center",
        inline: "center",
      });
    }
  }, []);

  // 6. EFFECTS
  const loadData = useCallback(() => {
    const safeSelectedChipIndexs = selectedChipIndexs || [];
    const newArr = chipData.map((item, index) => ({
      ...item,
      isActive: safeSelectedChipIndexs.length > 0 && safeSelectedChipIndexs.includes(index)
    }));
    setChipDataArray(newArr);
  }, [chipData, selectedChipIndexs]);

  useEffect(() => {
    loadData();
  }, [loadData]);

  useEffect(() => {
    if (scollable) {
      scrollSmoothHandler(selectedIndex);
    }
  }, [selectedIndex, scollable, scrollSmoothHandler]);

  useEffect(() => {
    const selectedArray = [];
    if (isChipSelected) {
      chipDataArray.forEach((e, i) => {
        if (e.isActive) {
          selectedArray.push(i);
        }
      });
      
      if (isNonNull(onChipSelectionChange)) {
        onChipSelectionChange(selectedArray);
      }
    }
  }, [isChipSelected, chipDataArray, onChipSelectionChange]);

  // 7. CONDITIONAL RENDERING (memoized)
  const RenderItem = useMemo(() => {
    return chipDataArray.map((e, i) => (
      <Item
        ref={(el) => (scrollRefs.current[i] = el)}
        isSelected={e.isActive}
        onClick={() => onSelect(i)}
        tabIndex="0"
        type="button"
        id={`chip-container-${i}-chip-${e.title}-${i}`}
        disabled={e.isDisabled || false}
        className={`vms_Chip_item ${e.isActive ? 'vms_Chip_item_Active' : ''}`}
        key={i}
        selectedChipStyle={selectedChipStyle}
        role="button"
        aria-pressed={e.isActive}
        aria-disabled={e.isDisabled}
        onKeyDown={(event) => {
          if (event.key === 'Enter' || event.key === ' ') {
            onSelect(i);
          }
        }}
      >
        {e.title}
      </Item>
    ));
  }, [chipDataArray, onSelect, selectedChipStyle]);

  const WithScrollView = useMemo(() => (
    <>
      {!isMobile && (
        <LeftIconContainer
          onClick={(e) => {
            e.stopPropagation();
            slide("left", -130);
          }}
          className="vms_Chip_LeftIconContainer"
          role="button"
          tabIndex={0}
          aria-label="Scroll left"
        >
          <Icon
            style={{ display: scrollX === 0 ? "none" : "" }}
            imagePath={leftArrowIcon}
            className="vms_Chip_LeftIcon"
          />
        </LeftIconContainer>
      )}

      <WithScrollDiv 
        className="vms_Chip_ScrollingDiv" 
        id={id} 
        ref={allTabsRef} 
        onScroll={scrollCheck}
        role="listbox"
        aria-label="Chip selection"
      >
        {RenderItem}
      </WithScrollDiv>
      
      {!isMobile && (
        <RightIconContainer
          onClick={(e) => {
            e.stopPropagation();
            slide("right", +130);
          }}
          className="vms_Chip_RightIconContainer"
          role="button"
          tabIndex={0}
          aria-label="Scroll right"
        >
          <Icon
            imagePath={rightArrowIcon}
            className="vms_Chip_RightIcon"
            style={{ display: scrollEnd ? "none" : "block" }}
          />
        </RightIconContainer>
      )}
    </>
  ), [isMobile, scrollX, scrollEnd, id, RenderItem, leftArrowIcon, rightArrowIcon, slide, scrollCheck]);

  const WithoutScrollView = useMemo(() => (
    <WithoutScrollDiv 
      className="vms_Chip_WithoutScrollingDiv"
      role="listbox"
      aria-label="Chip selection"
    >
      {RenderItem}
    </WithoutScrollDiv>
  ), [RenderItem]);

  const errorMessageElement = useMemo(() => {
    if (!hasErrorMessage) return null;
    
    return (
      <ErrorMessage 
        className="vms_Chip_ErrorMessage"
        role="alert"
        aria-live="polite"
      >
        {errorMsgText}
      </ErrorMessage>
    );
  }, [hasErrorMessage, errorMsgText]);

  // 8. ERROR HANDLING
  React.useEffect(() => {
    if (!chipData || chipData.length === 0) {
      console.warn('Chip: chipData prop is required and should contain at least one item');
    }
    if (!isNonNull(onChipSelectionChange)) {
      console.warn('Chip: onChipSelectionChange callback is recommended for handling selection');
    }
  }, [chipData, onChipSelectionChange]);

  return (
    <ThemeWrapper isMobile={isMobile || isMobileView || false}>
      <Container 
        className={`${computedClassName} vms_Chip_Container`}
        style={additionalStyle}
        {...otherProps}
      >
        {scollable ? WithScrollView : WithoutScrollView}
        {errorMessageElement}
      </Container>
    </ThemeWrapper>
  );
});

// Component display name for debugging
Chip.displayName = 'Chip';

Chip.defaultProps = {
  scollable: false,
  selectedChipIndexs: [],
  multipleSelect: false,
  showErrorMessage: false,
  errorMsgText: "Please select a chip",
  additionalStyle: null,
  additionalClassName: null,
  isMobile: false,
  selectedChipStyle: {
    "backgroundColor": '#fcdad9',
    "borderColor": "#ef4044",
    "color": "#ef4044"
  }
};
Chip.propTypes = {
  /**
   *	
array of chipdata. { id: string, title: string, // chip text isDisabled?: boolean // To Disable chip (optional) }
   */
  chipData: PropTypes.array.isRequired,

  /**
   *	
If true then all chips will be shown in horizontal scroll view If false then chips will shown in rows as per screen and chips size

   */
  scollable: PropTypes.bool,
  /**
   *	
Array of indexs that will be selected by default default value 
   */
  selectedChipIndexs: PropTypes.array,

  /**
   *	
It will be called when chip got selected/unselected It will return current selected chips index


   */

  onChipSelectionChange: PropTypes.func.isRequired,

  /**
   *	
This id will be appiled to scrollable div and used to scroll the div
   */
  id: PropTypes.string.isRequired,

  /**
   *	
If true then it will allow to select multiple chips If false then only one chip will be selected at a time default value false
   */
  multipleSelect: PropTypes.bool,

  /**
   *	
To show error message below the chips. default value false
   */
  showErrorMessage: PropTypes.bool,

  /**
   *	
Error message text. default value Please select a chip
   */
  errorMsgText: PropTypes.string,

  /**
   * Classes to add additional styling in the parent container
   */
  additionalClassName: PropTypes.arrayOf(PropTypes.string),
  /**
   * Inline styles to add additional styling in the parent container
   */
  additionalStyle: PropTypes.object,
  /**
   *Custom icons path or base64
{ leftArrowIcon?: string; rightArrowIcon?: string; }
   */
  arrowIcons: PropTypes.shape({
    leftArrowIcon: PropTypes.string,
    rightArrowIcon: PropTypes.string,
  }),

  /**
  For moWeb view
   */
  isMobile: PropTypes.bool,
  /**
  Custom Selected Chip style
   */
  selectedChipStyle: PropTypes.shape({
    backgroundColor: PropTypes.string,
    borderColor: PropTypes.string,
    color: PropTypes.string
  }),
};

export { Chip };
