import styled from "styled-components";

export const Container = styled.div`
  display: flex;
`;

export const LeftIconContainer = styled.div`
  width: 24px;
  height: 24px;
  display: inline-flex;
  text-align: center;
  margin: auto 5px;
`;

export const RightIconContainer = styled.div`
  width: 24px;
  height: 24px;
  display: inline-flex;
  text-align: center;
  margin: auto 5px;
`;

export const Icon = styled.div`
  background: ${({ imagePath }) =>
    `transparent url(${imagePath}) center no-repeat`};
  width: 24px;
  height: 24px;
`;

export const WithScrollDiv = styled.div`
  width: 100%;
  overflow-x: auto;
  display: flex;
  -ms-overflow-style: none;
  scrollbar-width: none;
  &::-webkit-scrollbar {
    background: transparent;
    -webkit-appearance: none;
    width: 0;
    height: 0;
    display: none;
  }
`;

export const WithoutScrollDiv = styled.div`
  width: 100%;
  display: inline-block;
`;

export const Item = styled.button`
  padding: 8px 20px;
  border-radius: 19.5px;
  margin: 8px 12px 8px 0px;
  background-color: ${({ isSelected,selectedChipStyle }) => (isSelected ?  selectedChipStyle?.backgroundColor : "#ffffff")};
  outline: none;
  color: ${({ isSelected,selectedChipStyle}) => (isSelected ? selectedChipStyle?.color : "#4d4d4f")};
  font-size: 12px;
  font-weight: ${({ isSelected, theme }) =>
    isSelected ? "bold" : theme?.typography?.text?.fontSize};
  font-family: ${({ theme }) => theme.typography.fontFamily};
  border: ${({ isSelected,selectedChipStyle }) =>
    isSelected ? `0.5px solid ${selectedChipStyle?.borderColor}` : "0.5px solid #939598"};
  display: inline-flex;
  :disabled {
    color: rgb(209, 211, 212);
    background-color: rgb(255, 255, 255);
    border-color: rgb(209, 211, 212);
  }
`;

export const ErrorMessage = styled.div`
  margin-left: ${({ scollable, isMobile }) =>
    isMobile ? "0px" : scollable ? "33px" : "0px"};
  color: #ef4044;
  font-weight: ${({ theme }) => theme?.typography?.text?.fontSize};
  font-family: ${({ theme }) => theme.typography.fontFamily};
  font-size: 14px;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: 0.35px;
`;
