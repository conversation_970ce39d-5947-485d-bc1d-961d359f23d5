import React, { useState } from "react";
import { Chip } from "./Chip";

export default {
  title: "VMS_REACT/Chip",
  component: Chip,
  // parameters: {
  //   component: Chip,
  // },
};

const chipData = [
  {
    id: "1",
    title: "Mr.",
  },
  {
    id: "2",
    title: "Ms.",
  },
  {
    id: "3",
    title: "Mrs.",
  },
  {
    id: "4",
    title: "BCA",
    isDisabled: true,
  },
  {
    id: "5",
    title: "MCA",
  },
  {
    id: "6",
    title: "PHD",
  },
  {
    id: "7",
    title: "MP<PERSON>",
  },
  {
    id: "8",
    title: "Mr.",
  },
  {
    id: "9",
    title: "Ms.",
  },
  {
    id: "10",
    title: "Mrs.",
  },
  {
    id: "11",
    title: "BCA",
    isDisabled: true,
  },
  {
    id: "12",
    title: "MCA",
  },
  {
    id: "13",
    title: "PHD",
  },
  {
    id: "14",
    title: "MPHD",
  },
];

const onChipSelectionChange = (selectedIndexs) => {
  console.log("onChipSelectionChange", selectedIndexs);
};

export const SingleSelection = () => {
  return (
    <div style={{ margin: "20px" }}>
      <h4>With Scroll</h4>
      <Chip
        id="chip-container-1"
        chipData={chipData}
        scollable={true}
        onChipSelectionChange={onChipSelectionChange}
      />

      <h4>Without Scroll</h4>
      <Chip
        id="chip-container-2"
        chipData={chipData}
        onChipSelectionChange={onChipSelectionChange}
      />

      <h4>Value pass by application</h4>
      <Chip
        chipData={chipData}
        id="chip-container-3"
        scollable={true}
        selectedChipIndexs={[1, 8]}
        onChipSelectionChange={onChipSelectionChange}
      />
    </div>
  );
};

SingleSelection.story = {
  name: "Single Selection",
};


export const CustomChip = () => {
  const CustomStyle = {
    backgroundColor:"green",
    color:"#fff",
    borderColor:"red"
  }
  return (
    <div style={{ margin: "20px" }}>
      <h4>With Scroll</h4>
      <Chip
        id="chip-container-1"
        chipData={chipData}
        scollable={true}
        selectedChipStyle={CustomStyle}
        onChipSelectionChange={onChipSelectionChange}
      />

      <h4>Without Scroll</h4>
      <Chip
        id="chip-container-2"
        chipData={chipData}
        selectedChipStyle={CustomStyle}
        onChipSelectionChange={onChipSelectionChange}
      />

      <h4>Value pass by application</h4>
      <Chip
        chipData={chipData}
        id="chip-container-3"
        scollable={true}
        selectedChipStyle={CustomStyle}
        selectedChipIndexs={[1, 8]}
        onChipSelectionChange={onChipSelectionChange}
      />
    </div>
  );
};

CustomChip.story = {
  name: "Custom Chip",
};

export const MultipleSelection = () => (
  <div style={{ margin: "20px" }}>
    <h4>With Scroll</h4>
    <Chip
      id="chip-container-4"
      chipData={chipData}
      scollable={true}
      multipleSelect={true}
      onChipSelectionChange={onChipSelectionChange}
    />

    <h4>Without Scroll</h4>
    <Chip
      id="chip-container-5"
      chipData={chipData}
      multipleSelect={true}
      onChipSelectionChange={onChipSelectionChange}
    />

    <h4>Value pass by application</h4>
    <Chip
      chipData={chipData}
      id="chip-container-6"
      scollable={true}
      multipleSelect={true}
      selectedChipIndexs={[1, 8]}
      onChipSelectionChange={onChipSelectionChange}
    />
  </div>
);

MultipleSelection.story = {
  name: "Multiple Selection",
};

export const WithErrorMessage = () => {
  const [selectedChips, setSelectedChips] = React.useState([]);
  return (
    <>
      <div style={{ margin: "20px" }}>
        <h4>With Scroll</h4>
        <Chip
          id="chip-container-16"
          chipData={chipData}
          scollable={true}
          multipleSelect={true}
          showErrorMessage={true}
          onChipSelectionChange={onChipSelectionChange}
        />

        <h4> Error state handled by the application</h4>
        <Chip
          id="chip-container-19"
          chipData={chipData}
          scollable={true}
          multipleSelect={true}
          showErrorMessage={
            selectedChips && selectedChips.length > 0 ? false : true
          }
          isMobile={true}
          onChipSelectionChange={(index) => {
            setSelectedChips(index);
          }}
        />
        <h4>Without Scroll</h4>

        <Chip
          id="chip-container-17"
          chipData={chipData}
          multipleSelect={true}
          showErrorMessage={true}
          onChipSelectionChange={onChipSelectionChange}
        />
        <h4>Custom Error Message</h4>

        <Chip
          id="chip-container-18"
          chipData={chipData}
          multipleSelect={true}
          showErrorMessage={true}
          errorMsgText="Please select a title"
          onChipSelectionChange={onChipSelectionChange}
        />
      </div>
    </>
  );
};

WithErrorMessage.story = {
  name: "With Error Message",
};

export const CustomStyling = () => (
  <div>
    <div style={{ margin: "20px" }}>
      <h4>With scroll container width 400px</h4>
      <Chip
        id="chip-container-7"
        additionalStyle={{ width: "400px" }}
        scollable={true}
        chipData={chipData}
        onChipSelectionChange={onChipSelectionChange}
      />
    </div>
    <div style={{ margin: "20px" }}>
      <h4>Without scroll container width 400px</h4>
      <Chip
        id="chip-container-8"
        additionalStyle={{ width: "400px" }}
        chipData={chipData}
        onChipSelectionChange={onChipSelectionChange}
      />
    </div>
    <div style={{ margin: "20px" }}>
      <h4>Without scroll container width 400px</h4>
      <Chip
        id="chip-container-8"
        additionalStyle={{ width: "400px" }}
        chipData={chipData}
        onChipSelectionChange={onChipSelectionChange}
      />
    </div>
    <div style={{ margin: "20px" }}>
      <h4>With scroll arrow icons passed by application</h4>
      <Chip
        id="chip-container-9"
        scollable={true}
        chipData={chipData}
        arrowIcons={{
          leftArrowIcon:
            "https://img.icons8.com/flat_round/24/000000/arrow-left.png",
          rightArrowIcon:
            "https://img.icons8.com/flat_round/24/000000/arrow-right.png",
        }}
        onChipSelectionChange={onChipSelectionChange}
      />
    </div>
  </div>
);

CustomStyling.story = {
  name: "Custom styling",
};

export const MoWebSingleSelection = () => (
  <div style={{ padding: 0 }}>
    <h4 style={{ marginLeft: 0, marginRight: 0 }}>With Scroll</h4>
    <Chip
      id="chip-container-10"
      chipData={chipData}
      scollable={true}
      isMobile={true}
      onChipSelectionChange={onChipSelectionChange}
    />
    <h4 style={{ marginLeft: 0, marginRight: 0 }}>Without Scroll</h4>

    <Chip
      id="chip-container-11"
      chipData={chipData}
      isMobile={true}
      onChipSelectionChange={onChipSelectionChange}
    />
    <h4 style={{ marginLeft: 0, marginRight: 0 }}>Value pass by application</h4>
    <Chip
      id="chip-container-12"
      selectedChipIndexs={[2]}
      chipData={chipData}
      scollable={true}
      isMobile={true}
      onChipSelectionChange={onChipSelectionChange}
    />
  </div>
);

MoWebSingleSelection.story = {
  name: "MoWeb Single Selection",
  parameters: { viewport: { defaultViewport: "iphonex" } },
};

export const MoWebMultipleSelection = () => (
  <div style={{ padding: 0 }}>
    <h4 style={{ marginLeft: 0, marginRight: 0 }}>With Scroll</h4>
    <Chip
      id="chip-container-13"
      chipData={chipData}
      scollable={true}
      multipleSelect={true}
      isMobile={true}
      onChipSelectionChange={onChipSelectionChange}
    />
    <h4 style={{ marginLeft: 0, marginRight: 0 }}>Without Scroll</h4>

    <Chip
      id="chip-container-14"
      chipData={chipData}
      multipleSelect={true}
      isMobile={true}
      onChipSelectionChange={onChipSelectionChange}
    />
    <h4 style={{ marginLeft: 0, marginRight: 0 }}>Value pass by application</h4>

    <Chip
      id="chip-container-15"
      selectedChipIndexs={[2, 5]}
      multipleSelect={true}
      chipData={chipData}
      isMobile={true}
      onChipSelectionChange={onChipSelectionChange}
    />
  </div>
);

MoWebMultipleSelection.story = {
  name: "MoWeb Multiple Selection",
  parameters: { viewport: { defaultViewport: "iphonex" } },
};

export const MoWebWithErrorMessage = () => {
  const [selectedChips, setSelectedChips] = React.useState([]);
  return (
    <div style={{ padding: 0 }}>
      <h4>With Scroll</h4>
      <Chip
        id="chip-container-19"
        chipData={chipData}
        scollable={true}
        multipleSelect={true}
        showErrorMessage={true}
        isMobile={true}
        onChipSelectionChange={onChipSelectionChange}
      />
      <h4>Error state handled by the application</h4>
      <Chip
        id="chip-container-19"
        chipData={chipData}
        scollable={true}
        multipleSelect={true}
        showErrorMessage={
          selectedChips && selectedChips.length > 0 ? false : true
        }
        isMobile={true}
        onChipSelectionChange={(index) => {
          setSelectedChips(index);
        }}
      />
      <h4>Without Scroll</h4>

      <Chip
        id="chip-container-20"
        chipData={chipData}
        multipleSelect={true}
        showErrorMessage={true}
        isMobile={true}
        onChipSelectionChange={onChipSelectionChange}
      />
      <h4>Custom Error Message</h4>

      <Chip
        id="chip-container-21"
        chipData={chipData}
        multipleSelect={true}
        showErrorMessage={true}
        errorMsgText="Please select a title"
        isMobile={true}
        onChipSelectionChange={onChipSelectionChange}
      />
    </div>
  );
};

MoWebWithErrorMessage.story = {
  name: "MoWeb With Error Message",
  parameters: { viewport: { defaultViewport: "iphonex" } },
};
