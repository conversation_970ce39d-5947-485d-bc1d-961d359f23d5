import React, { useState, useEffect, useRef, memo, useMemo, useCallback } from "react";
import PropTypes from "prop-types";
import {
  <PERSON>er<PERSON>ontaine<PERSON>,
  PickerBodyContainer,
  CalendarContainer,
  OverlayDiv,
  OverlayContainer,
  Divider,
  CalendarCloseButton,
  StickyWeekDay,
  StickyWeekDayLabel,
} from "./Components/CalendarContainer";
import { DateInput } from "./Components/DateInput";
import { Header } from "./Components/Header";
import { DateCell } from "./Components/DateCell";
import {
  addMonth,
  addYear,
  formatWithLocale,
  getNextYear,
  subMonth,
  subYear,
  whetherSameMonth,
} from "../../hooks/calendarHooks";
import { Button } from "../Button/Button";
import { useClassName } from "../../hooks/useClassName";
import { ThemeWrapper } from "../Theme/ThemeContext";

const formatMonthYear = "LLL yyyy";
let result = {};

const Calendar = memo((props) => {
  // 1. PROP DESTRUCTURING & VALIDATION
  const {
    selectedDate: propSelectedDate,
    openCalendar = false,
    isMobile = false,
    isMobileView = false,
    minDate,
    maxDate,
    onDateSelected = () => {},
    inputBoxLabel = "DATE",
    withLabel = true,
    view = "day",
    inputPlaceholder = "Select Date",
    arrowImages,
    dateDisplayFormat = "dd MMM yyyy",
    isOverlay = false,
    calendarType = "primary",
    onBlur = () => {},
    onInputClick = () => {},
    customInputView,
    closeButtonType = "primary",
    closeButtonText = "Done",
    additionalClassName,
    additionalStyle,
    ...otherProps
  } = props;

  // 2. PERFORMANCE OPTIMIZATIONS
  const className = useMemo(() => 
    useClassName(props, additionalClassName), 
    [props, additionalClassName]
  );

  const listInnerRef = useRef();
  const ref = useRef();

  // 3. STATE MANAGEMENT
  const [editting, setEditting] = useState(openCalendar);
  const [selectedDate, setSelectedDate] = useState(
    propSelectedDate || new Date()
  );
  const [mobileDateArray, setMobileArray] = useState([]);
  const [showPlaceholder, setShowPlaceholder] = useState(!propSelectedDate);
  const [showYearView, setYearView] = useState(false);
  const [MobileView, setMobileView] = useState(isMobile);
  const [showSubView, setSubView] = useState(false);
  const [isDateChange, setIsDateChange] = useState(false);
  const [newSelectedDate, setNewSelectedDate] = useState();

  // 4. MEMOIZED CALCULATIONS
  const checkMinDate = useMemo(() => 
    minDate || new Date(), 
    [minDate]
  );
  
  const checkMaxDate = useMemo(() => 
    maxDate || getNextYear(1), 
    [maxDate]
  );

  // 5. EVENT HANDLERS with useCallback
  const nextMonth = useCallback(() => {
    setSelectedDate(prev => addMonth(prev, 1));
  }, []);

  const prevMonth = useCallback(() => {
    setSelectedDate(prev => subMonth(prev, 1));
  }, []);

  const nextYear = useCallback(() => {
    setSelectedDate(prev => addYear(prev, 12));
  }, []);

  const prevYear = useCallback(() => {
    setSelectedDate(prev => subYear(prev, 12));
  }, []);

  const clearDate = useCallback(() => {
    setSelectedDate(new Date());
    setEditting(false);
    setShowPlaceholder(true);
  }, []);

  const onSave = useCallback((showView) => {
    if (showView) {
      setEditting(prev => !prev);
    }
  }, []);

  const onDateClick = useCallback((day, showSubView, view) => {
    setSelectedDate(day);
    setNewSelectedDate(day);
    if (showPlaceholder) {
      setShowPlaceholder(false);
    }
    onSave(showSubView);
    result = {
      Date: day,
      format1: formatWithLocale(day, "yyyy-MM-dd'T'HH:mm:ss'Z'"),
      format2: formatWithLocale(day, "dd-MM-yyyy"),
    };
    if (view !== "mobile") {
      onDateSelected(result);
    }
    setIsDateChange(true);
  }, [showPlaceholder, onSave, onDateSelected]);

  const onDoneClick = useCallback(() => {
    onDateSelected(result);
    setEditting(prev => !prev);
    setMobileView(prev => !prev);
  }, [onDateSelected]);

  const onYearViewChange = useCallback(() => {
    setYearView(prev => !prev);
  }, []);

  const onShowSubView = useCallback(() => {
    setSubView(prev => !prev);
  }, []);

  const handleInputClick = useCallback((e) => {
    setEditting(prev => !prev);
    onInputClick(!editting);
  }, [editting, onInputClick]);

  const onScroll = useCallback((e) => {
    if (listInnerRef.current) {
      const bottom =
        e.target.scrollTop + e.target.clientHeight >= e.target.scrollHeight - 1;
      let firstMonth = mobileDateArray?.length > 0 ? mobileDateArray[0] : [];
      let lastMonth =
        mobileDateArray?.length > 0
          ? mobileDateArray[Math.floor(mobileDateArray.length - 1)]
          : [];
      const checkMaxDateInSameMonth = whetherSameMonth(lastMonth, checkMaxDate);
      const checkMinDateInSameMonth = whetherSameMonth(firstMonth, checkMinDate);
      
      if (bottom && !checkMaxDateInSameMonth) {
        let tempMonth = [];
        let addNewMonth = addMonth(lastMonth ? lastMonth : selectedDate, 1);
        tempMonth.push(addNewMonth);
        setMobileArray(prev => [...prev, ...tempMonth]);
        setSelectedDate(addNewMonth);
      }
      if (e.target.scrollTop <= 10 && !checkMinDateInSameMonth) {
        let tempMonth = [];
        let subNewMonth = subMonth(firstMonth ? firstMonth : selectedDate, 1);
        tempMonth.push(subNewMonth);
        setMobileArray(prev => [...tempMonth, ...prev]);
        setSelectedDate(subNewMonth);
      }
    }
  }, [mobileDateArray, selectedDate, checkMaxDate, checkMinDate]);

  // 6. MEMOIZED COMPONENTS
  const CustomInputComponent = useMemo(() => {
    if (customInputView) {
      return <>{customInputView}</>;
    }
    return (
      <DateInput
        selectedDate={selectedDate}
        formatDateInput={dateDisplayFormat}
        withLabel={withLabel}
        labelMessage={inputBoxLabel}
        inputPlaceholder={inputPlaceholder}
        showPlaceholder={showPlaceholder}
        onButtonClick={handleInputClick}
        onBlur={onBlur}
        onClearIconClick={clearDate}
        isMobile={isMobile}
        editting={editting}
        calendarType={calendarType}
        isDateChange={isDateChange}
      />
    );
  }, [
    customInputView, selectedDate, dateDisplayFormat, withLabel, inputBoxLabel,
    inputPlaceholder, showPlaceholder, handleInputClick, onBlur, clearDate,
    isMobile, editting, calendarType, isDateChange
  ]);

  const StickyWeekDayComponent = useMemo(() => {
    if (!isMobile || !editting) return null;
    
    return (
      <StickyWeekDay className={`vms_Calendar_WeekdayContainer`}>
        <StickyWeekDayLabel className={`vms_Calendar_WeekdayLabel`}>S</StickyWeekDayLabel>
        <StickyWeekDayLabel className={`vms_Calendar_WeekdayLabel`}>M</StickyWeekDayLabel>
        <StickyWeekDayLabel className={`vms_Calendar_WeekdayLabel`}>T</StickyWeekDayLabel>
        <StickyWeekDayLabel className={`vms_Calendar_WeekdayLabel`}>W</StickyWeekDayLabel>
        <StickyWeekDayLabel className={`vms_Calendar_WeekdayLabel`}>T</StickyWeekDayLabel>
        <StickyWeekDayLabel className={`vms_Calendar_WeekdayLabel`}>F</StickyWeekDayLabel>
        <StickyWeekDayLabel className={`vms_Calendar_WeekdayLabel`}>S</StickyWeekDayLabel>
      </StickyWeekDay>
    );
  }, [isMobile, editting]);

  const MobileDateArrayComponent = useMemo(() => {
    if (!mobileDateArray || mobileDateArray.length === 0) return null;

    return mobileDateArray.map((date, i) => (
      <React.Fragment key={i}>
        <Header
          selectedDate={date}
          view={"mobile"}
          formatMonthYear={"MMMM yyyy"}
          prev={prevMonth}
          next={nextMonth}
          arrowImages={arrowImages}
          showSubView={onShowSubView}
        />
        <DateCell
          minDate={minDate}
          maxDate={maxDate}
          selectedDate={date}
          onItemClick={(value, showSubView) =>
            onDateClick(value, false, "mobile")
          }
          newSelectedDate={newSelectedDate}
          isDateChange={isDateChange}
          isMobile={isMobile}
          view={"mobile"}
        />
        {mobileDateArray.length - 1 !== i && <Divider />}
      </React.Fragment>
    ));
  }, [mobileDateArray, prevMonth, nextMonth, arrowImages, onShowSubView, minDate, maxDate, onDateClick, newSelectedDate, isDateChange, isMobile]);

  const CloseButtonComponent = useMemo(() => {
    if (!isMobile || !editting) return null;
    
    return (
      <CalendarCloseButton className={`vms_Calendar_CloseButtonContainer`}>
        <Button
          buttonType={closeButtonType}
          additionalStyle={{
            maxWidth: "100%",
            margin: "10px 0px 10px 0",
          }}
          onClick={onDoneClick}
        >
          {closeButtonText}
        </Button>
      </CalendarCloseButton>
    );
  }, [isMobile, editting, closeButtonType, onDoneClick, closeButtonText]);

  // 7. EFFECTS
  useEffect(() => {
    if (editting && isMobile) {
      setMobileView(false);
      let tempMonth = [];
      let addNewMonth = new Date();
      tempMonth.push(addNewMonth);
      setMobileArray(tempMonth);
    }
  }, [editting, isMobile]);

  // 8. ERROR HANDLING
  useEffect(() => {
    if (minDate && maxDate && minDate > maxDate) {
      console.warn('Calendar: minDate should be less than maxDate');
    }
  }, [minDate, maxDate]);

  return (
    <ThemeWrapper isMobile={isMobile || isMobileView || false}>
      <>
        <OverlayContainer className={`vms_Calendar_OverlayContainer`}>
          <PickerContainer className={`vms_Calendar_PickerContainer`} ref={ref} isMobile={isMobile}>
            {CustomInputComponent}
            {StickyWeekDayComponent}

            {editting && (
              <CalendarContainer
                className={`${className} vms_Calendar_CalendarContainer`}
                style={additionalStyle}
                type={calendarType}
                isMobile={isMobile}
              >
                {isMobile ? (
                  <PickerBodyContainer
                    ref={listInnerRef}
                    onScroll={onScroll}
                    editting={editting}
                    isMobile={isMobile}
                    className={`vms_Calendar_PickerBodyContainer`}
                  >
                    {MobileDateArrayComponent}
                  </PickerBodyContainer>
                ) : (
                  <PickerBodyContainer className={`vms_Calendar_PickerBodyContainer`} editting={editting} isMobile={isMobile}>
                    <Header
                      selectedDate={selectedDate}
                      view={view}
                      formatMonthYear={formatMonthYear}
                      prev={prevMonth}
                      next={nextMonth}
                      arrowImages={arrowImages}
                      showSubView={onShowSubView}
                    />
                    <DateCell
                      minDate={minDate}
                      maxDate={maxDate}
                      selectedDate={selectedDate}
                      onItemClick={(value, showSubView) =>
                        onDateClick(value, showSubView, "day")
                      }
                      newSelectedDate={newSelectedDate}
                      isDateChange={isDateChange}
                      onScroll={onScroll}
                      isMobile={isMobile}
                      view={"day"}
                    />
                  </PickerBodyContainer>
                )}
                {showSubView && (
                  <PickerBodyContainer
                    editting={editting}
                    subView={showSubView}
                    className={`vms_Calendar_PickerBodyContainer`}
                  >
                    <Header
                      selectedDate={selectedDate}
                      view={"year"}
                      prevYear={prevYear}
                      nextYear={nextYear}
                      formatMonthYear={formatMonthYear}
                      prev={prevMonth}
                      next={nextMonth}
                      changeView={onYearViewChange}
                      arrowImages={arrowImages}
                    />
                    <DateCell
                      minDate={minDate}
                      maxDate={maxDate}
                      selectedDate={selectedDate}
                      onItemClick={(value, showSubView) =>
                        onDateClick(value, showSubView)
                      }
                      view={showYearView ? "year" : "month"}
                    />
                  </PickerBodyContainer>
                )}
              </CalendarContainer>
            )}
          </PickerContainer>
          {CloseButtonComponent}
        </OverlayContainer>
        {isOverlay && !isMobile && <OverlayDiv className="vms_Calendar_OverlayDiv" editting={editting} />}
      </>
    </ThemeWrapper>
  );
});

// Component display name for debugging
Calendar.displayName = 'Calendar';

Calendar.defaultProps = {
  withLabel: true,
  minDate: undefined,
  maxDate: undefined,
  onDateSelected: () => { },
  view: "day",
  openCalendar: false,
  className: "",
  isMobile: false,
  closeButtonType: "primary",
  closeButtonText: "Done",
  dateDisplayFormat: "dd MMM yyyy",
  additionalClassName: null,
  additionalStyle: null,
  inputBoxLabel: "DATE",
  inputPlaceholder: "Select Date",
  isOverlay: false,
  calendarType: "primary",
};

Calendar.propTypes = {
  /**
    
  * ID for the input
   */
  id: PropTypes.string,

  /**
   * Variations of Calendar Type
   */
  calendarType: PropTypes.oneOf(["primary", "secondary"]),

  /**
   * Selected Date that application want to be selected default
Date
   */
  selectedDate: PropTypes.instanceOf(Date),

  /**
   * Whether show up the label of the Date input
   */
  withLabel: PropTypes.bool,

  /**
   * To display label above input box. Default value DATE
string
   */

  inputBoxLabel: PropTypes.string,

  /**
   * 	
Defines minimum date. Disabled earlier dates.
When isMobile is false, it will take current Date - 100 years as default value.
When isMobile is true, it will take current Date as default value.

Date
   */
  minDate: PropTypes.instanceOf(Date),

  /**
   * Defines maximum date. Disabled later dates.
When isMobile is false, it will take current Date + 100 years as default value.
When isMobile is true, it will take current Date + 1 year as default value.

Date
   */
  maxDate: PropTypes.oneOfType([PropTypes.instanceOf(Date)]),

  /**
   * Only applicable when @property isMobile is false.
It will replace up and down arrow with given values.
you can pass direct web URL or local image path

{ upArrow: string; downArrow: string; disabledUpArrow: string; disabledDownArrow: string; }
   */
  arrowImages: PropTypes.shape({
    upArrow: PropTypes.string,
    downArrow: PropTypes.string,
    disabledUpArrow: PropTypes.string,
    disabledDownArrow: PropTypes.string,
  }),
  /**
   * To enable mobile view. Default value false
boolean
   */
  isMobile: PropTypes.bool,

  /**
   * If @property isMobile is true then this function will be called when user click on done button
If @property isMobile is false then this function will be called when user click on any date
it will return {
   date: Date | undefined,
   format1 (ISO format): string,
   format2 (DD-MM-YYYY): string,
   };
}

(result: CalendarOutput) => void
   */
  onDateSelected: PropTypes.func,
  /**
   * Only applicable when @property isMobile value true.
Close button text. it will take Done as default value.

string
   */
  closeButtonText: PropTypes.string,
  /**
   * Only applicable when @property isMobile value true.
Close button type. it will take primary as default value.

"primary" | "link" | "secondary" | "tertiary" | "ghost"
   */
  closeButtonType: PropTypes.oneOf([
    "primary",
    "secondary",
    "link",
    "tertiary",
  ]),
  /**
   * Displayed Date format . Default value dd MMM yyyy
Please refer https://date-fns.org/v2.12.0/docs/format for various allowed format

string
   */
  dateDisplayFormat: PropTypes.string,

  /**
   * Only applicable when @property isMobile value false.
If you want to open calendar pass true

boolean
   */
  openCalendar: PropTypes.bool,
  /**
   *Inline styles to add additional styling in the parent container
CSSProperties
   */
  additionalStyle: PropTypes.object,
  /**
   * Classes to add additional styling in the parent container
string[]
   */

  additionalClassName: PropTypes.arrayOf(PropTypes.string),
  /**
   * Place holder text for input box. Default value Select Date
string
   */
  inputPlaceholder: PropTypes.string,
  /**
   *Only applicable when @property isMobile value false.
To wrap the component with overlay. Default value false

boolean
   */
  isOverlay: PropTypes.bool,
};
export { Calendar };
