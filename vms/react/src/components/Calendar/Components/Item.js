import React from "react";
import styled from "styled-components";
import classNames from "classnames";

const ItemContainer = styled.td`
  position: relative;
  overflow: hidden;
  cursor: pointer;
  background: ${({ theme }) => "#fff"};
  transition: 0.25s ease-out;
  border: 0px solid #333;
  padding: 0px;
  height: 30px !important;
  line-height: 30px;
  width: 41px;
  flex: 1 1var (--widthB);
`;

const Number = styled.div`
  text-align: center;
  font-size: 0.85em;
  line-height: 0.85em;
  font-weight: 400;
  height: 2em;
  line-height: 2em;
  font-family: ${({ theme }) => theme?.typography?.fontFamily || ""};
  font-size: ${({ theme }) => `${theme?.typography?.text?.fontSize}` || ""};

  ${ItemContainer}.selected.notrangeview & {
    opacity: 0;
    transition: 0.5s ease-out;
  }

  ${ItemContainer}.selected & {
    color: #4d4d4f;
    background: ${(props) => (props.view === "month" ? "#F1F6DE" : "#F1F6DE")};
    border: ${(props) =>
      props.view === "day" || props.view === "mobile"
        ? "2px solid #03868b"
        : ""};
    text-align: "center";
    font-weight: ${(props) =>
      props.view === "month" || props.view === "day" || props.view === "mobile"
        ? "600"
        : "400"};
    font-family: ${({ theme }) => theme?.typography?.fontFamily || ""};
    font-size: ${({ theme }) => `${theme?.typography?.text?.fontSize}` || ""};
  }
  ${ItemContainer}:hover & {
    background: #f1f6de;
    text-align: center;
  }
`;

const Item = (props) => {
  const {
    value,
    onDateClick,
    showConfirmButton,
    formattedDate,
    selectedCell,
    disabledCell,
    startCell,
    betweenCell,
    endCell,
    selectedSat,
    selectedSun,
    rangeView,
    view,
  } = props;
  const onDateClickHandler = () => {
    onDateClick(value, showConfirmButton);
  };

  return (
    <ItemContainer
      key={value}
      className={`${classNames({
        disabled: disabledCell,
        selected: selectedCell,
        start: startCell,
        between: betweenCell,
        end: endCell,
        saturday: selectedSat,
        sunday: selectedSun,
        notRangeView: !rangeView,
      })} vms_Calendar_itemMainContainer`}
      onClick={!disabledCell ? onDateClickHandler : () => {}}
    >
      <Number view={view} className="vms_Calendar_dateItem">{formattedDate}</Number>
    </ItemContainer>
  );
};

export default Item;
