import React from "react";
import styled from "styled-components";
import PropTypes from "prop-types";
import { Row, Col } from "./CalendarContainer";
import { addMonth, formatWithLocale } from "../../../hooks/calendarHooks";
import ArrowUpSvg from "../../../assets/images/calendar/arrow-up.svg";
import ArrowDownSvg from "../../../assets/images/calendar/arrow-down.svg";

const HeaderContainer = styled(Row)`
  position: relative;
  margin: ${(props) => (props.isMobile ? `20px 0px 0px 0px` : "28px 10px 0px 24px")};
  display: flex;
  align-items: center;
`;

const IconContainer = styled.div`
  display: inline-block;
  margin-right: 8px;
  height: 24px;
  width: 24px;
`;

const TitleButton = styled.button`
  border: none;
  background: transparent;
  display: inline-block;
  font-weight: 700;
  font-family: ${({ theme }) => theme?.typography?.fontFamily || ""};
  font-size: ${({ theme }) => `${theme?.typography?.text?.fontSize}` || ""};
  line-height: 20px;
  letter-spacing: 0.3px;
  color: #4d4d4f !important;
  outline: none;
  padding: 0px;
  margin-left: ${(props) => (props.view === 'year' ? "8px" : "")};
`;

const NavButton = styled.div`
  display: inline-block;
  position: absolute;
  right: 10px;
`;

const NavUpButton = styled.div`
  display: inline-block;
  margin-right: 8px;
  height: 24px;
  width: 24px;
`;

const NavDownButton = styled.div`
  display: inline-block;
  height: 24px;
  width: 24px;
`;

const Header = (props) => {
  const {
    selectedDate,
    next,
    prev,
    formatMonthYear,
    view,
    arrowImages,
    nextYear,
    prevYear,
    changeView,
    showSubView,
  } = props;

  if (view === "year") {
    return (
      <HeaderContainer className={"vms_Calendar_HeaderContainer"}>
        <TitleButton className={"vms_Calendar_HeaderTitle"} view={view} onClick={changeView}>
          {`${formatWithLocale(selectedDate, "yyyy")}`}
        </TitleButton>
        <NavButton>
          <NavUpButton className={"vms_Calendar_Header_PrevButton"} onClick={prevYear}>
            <img src={arrowImages?.upArrow || ArrowUpSvg} alt="plus" />
          </NavUpButton>
          <NavDownButton className={"vms_Calendar_Header_NextButton"} onClick={nextYear}>
            <img src={arrowImages?.downArrow || ArrowDownSvg} alt="plus" />
          </NavDownButton>
        </NavButton>
      </HeaderContainer>
    );
  }

  if (view === "month") {
    return (
      <HeaderContainer className={"vms_Calendar_HeaderContainer"}>
        <TitleButton className={"vms_Calendar_HeaderTitle"} onClick={changeView}>
          {`${formatWithLocale(selectedDate, "yyyy mmm")}`}
        </TitleButton>
        <NavButton>
          <NavUpButton onClick={prev} className={"vms_Calendar_Header_PrevButton"}>
            <img src={arrowImages?.upArrow || ArrowUpSvg} alt="plus" />
          </NavUpButton>
          <NavDownButton onClick={next} className={"vms_Calendar_Header_NextButton"}>
            <img src={arrowImages?.downArrow || ArrowDownSvg} alt="plus" />
          </NavDownButton>
        </NavButton>
      </HeaderContainer>
    );
  }

  if (view === "mobile") {
    return (
      <HeaderContainer isMobile className={"vms_Calendar_HeaderContainer"}>
        <TitleButton className={"vms_Calendar_HeaderTitle"} onClick={changeView}>
          {`${formatWithLocale(selectedDate, formatMonthYear)}`}
        </TitleButton>
      </HeaderContainer>
    );
  }

  return (
    <HeaderContainer className={"vms_Calendar_HeaderContainer"}>
      <TitleButton className={"vms_Calendar_HeaderTitle"} onClick={showSubView}>
        {formatWithLocale(selectedDate, formatMonthYear)}
      </TitleButton>
      <NavButton>
        <NavUpButton onClick={prev} className={"vms_Calendar_Header_PrevButton"}>
          <img src={arrowImages?.upArrow || ArrowUpSvg} alt="plus" />
        </NavUpButton>
        <NavDownButton onClick={next} className={"vms_Calendar_Header_NextButton"}>
          <img src={arrowImages?.downArrow || ArrowDownSvg} alt="plus" />
        </NavDownButton>
      </NavButton>
    </HeaderContainer>
  );
};

Header.defaultProps = {
  selectedDate: new Date(),
  prev: () => {},
  next: () => {},
  prevYear: () => {},
  nextYear: () => {},
  changeView: () => {},
  showSubView: () => {},
  fromMonth: new Date(),
  toMonth: addMonth(new Date(), 1),
};

Header.propTypes = {
  selectedDate: PropTypes.instanceOf(Date),
  view: PropTypes.string.isRequired,
  prev: PropTypes.func,
  next: PropTypes.func,
  changeView: PropTypes.func,
  formatMonthYear: PropTypes.string,
  fromMonth: PropTypes.instanceOf(Date),
  toMonth: PropTypes.instanceOf(Date),
};

export { Header };
