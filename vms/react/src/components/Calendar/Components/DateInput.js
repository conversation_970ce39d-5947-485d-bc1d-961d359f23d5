import React from "react";
import styled from "styled-components";
import { formatWithLocale } from "../../../hooks/calendarHooks";
import CrossIconSvg from "../../../assets/images/calendar/cross.svg";

function border(isMobile, editting,theme,type) {
  if (isMobile) {
    return "none";
  } else {
    return editting ? `solid 1px ${theme?.palette?.[type]?.main}`: "solid 1px #4d4d4f";
  }
}

function borderBottom(isMobile,theme,type) {
  if (isMobile) {
    return "1px solid #03868b";
  } else {
    return "1px solid #4d4d4f";
  }
}

const DateInputContainer = styled.div`
  position: relative;
  min-width: ${(props) => (props.isMobile ? "100%" : "290px")};
  width: ${(props) => (props.isMobile ? "100%" : "290px")};
  --widthA: calc(100% - 28px);
  --widthB: calc(var(--widthA) / 7);
`;

const Label = styled.label`
  font-weight: 400;
  font-family: ${({ theme }) => theme?.typography?.fontFamily || ""};
  font-size: ${({ theme }) => `${theme?.typography?.text?.fontSize}` || ""};
  font-stretch: normal;
  font-style: normal;
  line-height: 20px;
  letter-spacing: 0.3px;
  color: #4d4d4f;
  display: block;
  position: relative;
  text-transform: uppercase;
`;

const HightLight = styled.span`
  position: absolute;
  height: 60%;
  width: 100%;
  top: 25%;
  left: 0;
  pointer-events: none;
  opacity: 0.5;
`;

const Bar = styled.span`
  position: relative;
  display: block;
  width: 100%;
  &:before,
  &:after {
    content: "";
    height: 48px;
    width: ${(props) => (props.editting ? "50%" : "0")};
    bottom: 1px;
    position: absolute;
    background-color: rgb(255, 255, 255);
  }
  &:before {
    left: 50%;
  }
  &:after {
    right: 50%;
  }
`;

const InputContainer = styled.input`
  display: block;
  box-sizing: border-box;
  font-weight: 700;
  font-family: ${({ theme }) => theme?.typography?.fontFamily || ""};
  font-size: ${({ theme }) => `${theme?.typography?.text?.fontSize}` || ""};
  font-stretch: normal;
  font-style: normal;
  line-height: 1.43;
  width: 100%;
  border-radius: 3px;
  letter-spacing: 0.3px;
  color: #4d4d4f;
  padding: 14px 16px;
  margin-top: 12px;
  text-overflow: ellipsis;
  height: 48px;
  position: relative;
  border: ${({theme,isMobile,calendarType,editting}) => border(isMobile, editting ? true : false,theme,calendarType)};
  background-color: #fff;

  &::placeholder {
    font-weight: 400;
    font-family: ${({ theme }) => theme?.typography?.fontFamily || ""};
    font-size: 16px;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.43;
    border-radius: 3px;
    letter-spacing: 0.3px;
    color: #D2D3D4;
  }

  &:focus {
    outline: none;
    height: 48px;
    border-radius: 3px;
    border: ${({ theme,isMobile,calendarType }) => border(isMobile, true,theme,calendarType)};
    border-bottom: ${(props) => borderBottom(true)};
    background-color: #fff;
  }
`;

const IconContainer = styled.div`
  display: inline-block;
  background-size: 20px 18px !important;
  background-repeat: no-repeat !important;
  background-position: center !important;
  width: 24px;
  height: 24px;
  text-decoration: none;
  outline: none;
  position: absolute;
  top: 45px;
  right: 12px;
`;

const DateInput = (props) => {
  const {
    selectedDate,
    onButtonClick,
    editting,
    formatDateInput,
    withLabel,
    labelMessage,
    inputPlaceholder,
    showPlaceholder,
    onClearIconClick,
    isDateChange,
    calendarType,
    onBlur
  } = props;
  const formattedDate = selectedDate
    ? formatWithLocale(selectedDate, formatDateInput)
    : "";

  return (
    <DateInputContainer className={`vms_Calendar_InputContainer`} isMobile={props.isMobile}>
      {withLabel ? <Label className={`vms_Calendar_Input_lable`}>{labelMessage}</Label> : ""}
      <InputContainer
        type="date-text"
        editting={editting}
        value={
          !showPlaceholder && formattedDate ? formattedDate : ""
        }
        placeholder={inputPlaceholder ? inputPlaceholder : "Select Date"}
        onClick={(e) => onButtonClick(e)}
        readOnly
        isMobile={props.isMobile}
        className={`vms_DateRangeCalendar_Input`}
        calendarType={calendarType}
        onBlur={(e)=>onBlur(e)}
      />
      {!showPlaceholder && formattedDate && (
        <IconContainer className={`vms_DateRangeCalendar_IconCotainer`} onClick={() => onClearIconClick()}>
          <img src={CrossIconSvg} alt="plus" />
        </IconContainer>
      )}
    </DateInputContainer>
  );
};

export { DateInput };
