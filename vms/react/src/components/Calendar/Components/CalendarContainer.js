import styled from "styled-components";

const Row = styled.div`
  margin: 0;
  padding: 0;
  flex-grow: 1;
  flex-basis: auto;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  align-items: flex-start;
  justify-content: ${(props) =>
    props.justifyContent ? props.justifyContent : ""};
`;

const Col = styled.div`
  flex-grow: 1;
  flex-basis: auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: ${(props) =>
    props.justifyContent ? props.justifyContent : "center"};
  text-align: ${(props) => props.textAlign};
`;

const CalendarContainer = styled.div`
  display: flex;
  width: ${(props) => (props.isMobile ? "100%" : "-webkit-max-content")};
  width: ${(props) => (props.isMobile ? "100%" : "-moz-max-content")};
  width: ${(props) => (props.isMobile ? "100%" : "max-content")};
  border-radius: 3px;
  box-shadow: 0 2px 4px 0rgba (0, 0, 0, 0.12);
  border: ${({ theme, type, isMobile }) =>
    (isMobile ? "none" : `solid 1px ${theme?.palette?.[type]?.main}`) || ""};
  background-color: ${({ theme, type }) =>
    theme?.palette?.[type]?.contrastText || ""};
`;

const PickerBodyContainer = styled.div`
  display: ${(props) => (props.editting ? "block" : "none")};
  box-shadow: none;
  border: none;
  background-color: transparent;
  width: ${(props) =>
    props.isMobile ? "100%" : props.subView ? "260px" : "288px"};
  border-left: ${(props) => (props.subView ? "1px solid #ebebec" : "none")};
  overflow: auto;
  height: ${(props) => (props.isMobile ? "200px" : "")};
  &::-webkit-scrollbar {
    width: 0;
    background: transparent;
  }
  &::-webkit-scrollbar-thumb {
    background: transparent;
  }
`;

const PickerContainer = styled.div`
  background: transparent;
  width: ${(props) => (props.isMobile ? "100%" : "14em")};
`;
const OverlayDiv = styled.div`
  top: 0%;
  left: 0%;
  width: 100%;
  height: 100%;
  position: fixed;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 99;
  display: ${(props) => (props.editting ? "block" : "none")};
`;

const OverlayContainer = styled.div`
  position: relative;
  z-index: 100;
`;

const Divider = styled.div`
  overflow: hidden;
  border-top: 1px solid #33435b73;
  width: 100%;
  margin: 0 0 24px;
  padding: 0;
  height: auto !important;
`;

const CalendarCloseButton = styled.div`
margin: 0 auto;
text-align: center;
position: fixed;
bottom: 0;
left: 0;
right: 0;
background-color: #fff;
`;
const StickyWeekDay = styled.div`
  padding: 0;
  margin: 0 auto;
  width: 100%;
  border-bottom: 1px solid #ebebec;
  z-index: 1000;
  position: relative;
  background-color: white;
  display: flex;
`;

const StickyWeekDayLabel = styled.span`
  font-size: 12px;
  line-height: 1.4;
  letter-spacing: normal;
  text-align: center;
  color: #4d4d4f;
  font-weight: 400;
  font-family: ${({ theme }) => theme?.typography?.fontFamily || ""};
  padding: 0.7em 0.3em;
  flex-basis: calc(100% / 7);
  box-sizing: inherit;
  text-align: center;
`;

export {
  Col,
  Row,
  PickerContainer,
  PickerBodyContainer,
  CalendarContainer,
  OverlayDiv,
  OverlayContainer,
  Divider,
  CalendarCloseButton,
  StickyWeekDay,
  StickyWeekDayLabel,
};
