import React from "react";
import styled from "styled-components";
import Item from "./Item";
import HeaderWeek from "./HeaderWeek";
import PropTypes from "prop-types";
import {
  formatWithLocale,
  whetherDisabled,
  whetherSameMonth,
  getStartOfMonth,
  getEndOfMonth,
  getStartOfWeek,
  getEndOfWeek,
  getStartOfYear,
  getEndOfYear,
  addDay,
  addMonth,
  getYears,
  getYearsPeriod,
  setYears,
  whetherisToday,
} from "../../../hooks/calendarHooks";

const DateTr = styled.tr`
  .selected {
    border-left: 5px solid transparent;
  }
  .disabled {
    color: #d1d3d4 !important;
    pointer-events: none;
  }
`;

const DateTable = styled.table`
  width: 100%;
  margin-top: 18px;
  margin-bottom: 16px;
  padding-left: 10px !important;
  padding-right: 10px !important;
  box-sizing: border-box;
`;

const YearTableContainer = styled.div`
  display: flex;
  flex-wrap: wrap;
  margin: 24px 24px 8px 24px;
`;

const YearText = styled.button`
  border: none;
  outline: none;
  background: ${(props) => (props.selected ? "#F1F6DE" : "transparent")};
  font-weight: ${(props) => (props.selected ? "600" : "400")};
  font-size: ${({ theme }) => `${theme?.typography?.text?.fontSize}` || ""};
  font-family: ${({ theme }) => theme?.typography?.fontFamily || ""};
  border-radius: 3px;
  text-align: center;
  height: auto;
  padding: 8px;
  color: #4d4d4f;
  line-height: 20px;
  letter-spacing: 0.3px;
  text-align: center;
  flex-basis: 25%;
`;

const DateTb = styled.tbody``;

const DateCell = ({
  selectedDate,
  view,
  onItemClick,
  showConfirmButton,
  minDate,
  maxDate,
  isMobile,
  isDateChange,
  newSelectedDate,
}) => {
  let className;
  let cloneDate;
  let formattedDate;
  let itemPerRow;
  let itemPerCol;

  const onDateClickHandler = (e) => {
    const setYear = setYears(selectedDate, e);
    onItemClick(setYear, false);
  };

  switch (view) {
    case "day": {
      const monthStart = getStartOfMonth(selectedDate);
      const monthEnd = getEndOfMonth(monthStart);
      const startDate = getStartOfWeek(monthStart);
      const endDate = getEndOfWeek(monthEnd);
      const rows = [];
      let row = [];
      let i;

      cloneDate = startDate;
      itemPerRow = 7;

      while (cloneDate <= endDate) {
        for (i = 0; i < itemPerRow; i += 1) {
          formattedDate = formatWithLocale(cloneDate, "d");
          const disabled = whetherDisabled(
            cloneDate,
            monthStart,
            minDate,
            maxDate
          );
          const checkTodayDate = isDateChange
            ? formatWithLocale(cloneDate, "dd.MM.yyyy") ==
              formatWithLocale(newSelectedDate, "dd.MM.yyyy")
              ? true
              : false
            : whetherisToday(cloneDate);

          const selected = !disabled && checkTodayDate;
          row.push(
            <Item
              key={cloneDate}
              value={cloneDate}
              formattedDate={formattedDate}
              onDateClick={(value) => onItemClick(value, true)}
              showConfirmButton={showConfirmButton}
              disabledCell={disabled}
              selectedCell={selected}
              view={view}
            />
          );

          cloneDate = addDay(cloneDate, 1);
        }
        rows.push(<DateTr className="vms_Calendar_dateTableContainer_row" key={cloneDate}>{row}</DateTr>);
        row = [];
      }

      return (
        <>
            <DateTable className="vms_Calendar_dateTableContainer" isMobile={isMobile}>
              <HeaderWeek
                currentMonth={selectedDate}
                formatWeek="eeeee"
                view="day"
              />
              <DateTb className="vms_Calendar_dateTableContainer_body">{rows}</DateTb>
            </DateTable>
        </>
      );
    }

    case "mobile": {
      const monthStart = getStartOfMonth(selectedDate);
      const monthEnd = getEndOfMonth(monthStart);
      const startDate = getStartOfWeek(monthStart);
      const endDate = getEndOfWeek(monthEnd);
      const userMinDate = minDate ? minDate : new Date();
      const userMaxDate = maxDate
        ? maxDate
        : new Date().setFullYear(new Date().getFullYear() + 1);
      const rows = [];
      let row = [];
      let i;

      cloneDate = startDate;
      itemPerRow = 7;

      while (cloneDate <= endDate) {
        for (i = 0; i < itemPerRow; i += 1) {
          formattedDate = formatWithLocale(cloneDate, "d");
          const disabled = whetherDisabled(
            cloneDate,
            monthStart,
            userMinDate,
            userMaxDate
          );
          const checkTodayDate = isDateChange
            ? formatWithLocale(cloneDate, "dd.MM.yyyy") ==
              formatWithLocale(newSelectedDate, "dd.MM.yyyy")
              ? true
              : false
            : whetherisToday(cloneDate);
          const selected = !disabled && checkTodayDate;
          row.push(
            <Item
              key={cloneDate}
              value={cloneDate}
              formattedDate={formattedDate}
              onDateClick={(value) => onItemClick(value, true)}
              showConfirmButton={showConfirmButton}
              disabledCell={disabled}
              selectedCell={selected}
              view={view}
            />
          );

          cloneDate = addDay(cloneDate, 1);
        }
        rows.push(<DateTr className="vms_Calendar_dateTableContainer_row" key={cloneDate}>{row}</DateTr>);
        row = [];
      }

      return (
        <>
          {isMobile ? (
            <DateTable className="vms_Calendar_dateTableContainer" isMobile={isMobile}>
              <DateTb className="vms_Calendar_dateTableContainer_body">{rows}</DateTb>
            </DateTable>
          ) : (
            <DateTable className="vms_Calendar_dateTableContainer" isMobile={isMobile}>
              <HeaderWeek
                currentMonth={selectedDate}
                formatWeek="eeeee"
                view="day"
              />
              <DateTb className="vms_Calendar_dateTableContainer_body">{rows}</DateTb>
            </DateTable>
          )}
        </>
      );
    }

    case "year": {
      let yearsList = [];
      let selected = getYears(selectedDate);
      const { startPeriod, endPeriod } = getYearsPeriod(selectedDate, 16);
      for (let y = startPeriod; y <= endPeriod; y++) {
        yearsList.push(y);
      }
      return (
        <>
          <YearTableContainer className="vms_Calendar_YearContainer">
            {yearsList.map((item, i) => {
              return (
                <YearText
                  key={i}
                  className="vms_Calendar_YearText"
                  selected={selected === item ? true : false}
                  onClick={(e) => onDateClickHandler(item)}
                >
                  {item}
                </YearText>
              );
            })}
          </YearTableContainer>
        </>
      );
    }
    case "month": {
      const startOfYear = getStartOfYear(selectedDate);
      const endOfYear = getEndOfYear(selectedDate);
      const cols = [];

      let col = [];

      cloneDate = startOfYear;
      itemPerCol = 4;
      while (cloneDate < endOfYear) {
        for (let i = 0; i < itemPerCol && cloneDate < endOfYear; i += 1) {
          formattedDate = formatWithLocale(cloneDate, "LLL");
          const disabled = whetherDisabled(
            cloneDate,
            undefined,
            minDate,
            maxDate
          );
          const selected = whetherSameMonth(cloneDate, selectedDate);
          col.push(
            <Item
              className={className}
              key={cloneDate}
              value={cloneDate}
              formattedDate={formattedDate}
              showConfirmButton={showConfirmButton}
              onDateClick={(value) => onItemClick(value, false)}
              disabledCell={disabled}
              selectedCell={selected}
              view={view}
            />
          );
          cloneDate = addMonth(cloneDate, 1);
        }
        cols.push(<DateTr className="vms_Calendar_dateTableContainer_row" key={cloneDate}>{col}</DateTr>);
        col = [];
      }
      return (
        <DateTable className="vms_Calendar_dateTableContainer">
          <DateTb className="vms_Calendar_dateTableContainer_body">{cols}</DateTb>
        </DateTable>
      );
    }
    default: {
      return undefined;
    }
  }
};

DateCell.defaultProps = {
  onScroll: () => {},
};

DateCell.propTypes = {
  onScroll: PropTypes.func,
  isDateChange: PropTypes.bool,
};

export { DateCell };
