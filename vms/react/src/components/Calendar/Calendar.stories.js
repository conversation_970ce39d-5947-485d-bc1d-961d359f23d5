import React, { useState } from "react";
import { Calendar } from "./Calendar";
import { Button } from "../Button/Button";
import { addMonth } from "../../hooks/calendarHooks";

export default {
  title: "VMS_REACT/Calendar",
  component: Calendar,
  parameters: { controls: { sort: "requiredFirst" } },
  argTypes: {
    id: { control: { type: "" } },
    additionalStyle: { control: { type: "" } },
    additionalClassName: { control: { type: "" } },
    dateDisplayFormat: { control: { type: "" } },
    selectedDate: { control: { type: "" } },
    value: { control: { type: "" } },
    arrowImages: { control: { type: "" } },
    minDate: { control: { type: "" } },
    maxDate: { control: { type: "" } },
  },
};

const Template = (args) => {
  const [openCalendar, setOpenCalendar] = useState(false);
  const [result, setResult] = useState();
  return (
    <div>
      <Calendar
        {...args}
        onDateSelected={(result) => {
          setResult(result);
          setOpenCalendar(false);
        }}
      />
      <>{result && !openCalendar ? DisplayCurrentState(result) : ""} </>
    </div>
  );
};


const DisplayCurrentState = (data) => {
  return (
    <div style={{ marginTop: "10px" }}>
      <h5>Callback Value</h5>
      <ul style={{ paddingLeft: "15px", paddingTop: "1px" }}>
        <li>Date : {data?.Date.toString()}</li>
      </ul>
      <h5>Dates In Format</h5>
      <ul style={{ paddingLeft: "15px", paddingTop: "1px" }}>
        <li>{`Format 1 : ${data?.format1}`}</li>
      </ul>
      <ul style={{ paddingLeft: "15px", paddingTop: "1px" }}>
        <li>{`Format 2 : ${data?.format2}`}</li>
      </ul>
    </div>
  );
};

const MobileTemplate = (args) => {
  const [openCalendar, setOpenCalendar] = useState(false);
  const [result, setResult] = useState();
  return (
    <div>
      {!openCalendar && (
        <Button
          isMobile
          buttonType="primary"
          onClick={() => {
            setOpenCalendar(!openCalendar);
          }}
        >
          Open
        </Button>
      )}

      {openCalendar && (
        <Calendar
          {...args}
          openCalendar={openCalendar}
          onDateSelected={(result) => {
            setResult(result);
            setOpenCalendar(false);
          }}
        />
      )}
      <>{result && !openCalendar ? DisplayCurrentState(result) : ""} </>
    </div>
  );
};

export const Default = Template.bind({});
Default.args = {
  value: "",
  isOverlay: false,
  isMobile: false,
  withLabel: true,
  openCalendar: false,
  dateDisplayFormat: "dd MMM yyyy",
  inputBoxLabel: "Date",
  inputPlaceholder: "Select Date",
  calendarType:"primary"
};

export const openCalendarByApplication = Template.bind({});
openCalendarByApplication.args = {
  withLabel: true,
  openCalendar: true,
  inputBoxLabel: "Date",
  inputPlaceholder: "Select Date",
  calendarType:"primary"
};

export const selectedDatePassByApplication = Template.bind({});
selectedDatePassByApplication.args = {
  withLabel: true,
  selectedDate: addMonth(new Date,1),
  inputBoxLabel: "Date",
  inputPlaceholder: "Select Date",
  calendarType:"primary"
};

export const customArrowImages = Template.bind({});
customArrowImages.args = {
  withLabel: true,
  arrowImages: {
    upArrow: "https://img.icons8.com/pastel-glyph/24/000000/minus.png",
    downArrow: "https://img.icons8.com/pastel-glyph/24/000000/plus.png",
    disabledUpArrow: "https://img.icons8.com/pastel-glyph/24/d1d3d4/minus.png",
    disabledDownArrow: "https://img.icons8.com/pastel-glyph/24/d1d3d4/plus.png",
  },
  inputBoxLabel: "Date",
  inputPlaceholder: "Select Date",
  calendarType:"primary"
};

export const MoDefault = MobileTemplate.bind({});
MoDefault.storyName = "MoWeb Default";
MoDefault.parameters = {
  viewport: {
    defaultViewport: "iphone6",
  },
};
MoDefault.args = {
  value: "",
  isOverlay: false,
  isMobile: true,
  withLabel: true,
  openCalendar: false,
  dateDisplayFormat: "dd MMM yyyy",
  inputBoxLabel: "Date",
  inputPlaceholder: "Select Date",
  calendarType:"primary",
  closeButtonType:"primary",
  closeButtonText:"Done"
};

export const WithOverlay = Template.bind({});
WithOverlay.args = {
  value: "",
  isOverlay: true,
  isMobile: false,
  withLabel: true,
  openCalendar: false,
  dateDisplayFormat: "dd MMM yyyy",
  inputBoxLabel: "Date",
  inputPlaceholder: "Select Date",
  calendarType:"primary"
};
