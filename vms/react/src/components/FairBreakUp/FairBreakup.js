import React, { useState, memo, useCallback, useMemo, useEffect } from "react";
import PropTypes from "prop-types";
import {
  Container,
  CurrencySymbol,
  ExpandIcon,
  FairBreakUpBody,
  FairBreakUpLeftView,
  FairBreakUpRightView,
  FairBreakupRow,
  FairBreakUpView,
  TitleText,
} from "./FairBreakup.styled";
import { useClassName } from "../../hooks/useClassName";
import ExpandDonwSvg from "../../assets/images/FairBreakeup/down.svg";
import ExpandUpSvg from "../../assets/images/FairBreakeup/up.svg";
import { ThemeWrapper } from "../Theme/ThemeContext";

// Utility function (placed before main component)
const isNonNull = (prop) => prop !== null && prop !== undefined;

// Memoized ExpandIcon component
const ExpandIconComponent = memo(({ isExpanded, expandedIcon, collapsedIcon, additionalIconClassName, onIconClick }) => {
  const iconSrc = useMemo(() => {
    if (isExpanded) {
      return expandedIcon || ExpandUpSvg;
    }
    return collapsedIcon || ExpandDonwSvg;
  }, [isExpanded, expandedIcon, collapsedIcon]);

  return (
    <ExpandIcon
      className={`${additionalIconClassName} vms_fairbreakup_expandicon`}
      onClick={onIconClick}
      role="button"
      tabIndex={0}
      aria-label={isExpanded ? "Collapse" : "Expand"}
    >
      <img
        className="vms_fairbreakup_expandicon_img"
        height={20}
        width={20}
        style={{ objectFit: "scale-down" }}
        src={iconSrc}
        alt={isExpanded ? "Collapse icon" : "Expand icon"}
      />
    </ExpandIcon>
  );
});

ExpandIconComponent.displayName = 'ExpandIconComponent';

const FairBreakup = memo((props) => {
  // 1. PROP DESTRUCTURING & VALIDATION
  const {
    id,
    children,
    title = "",
    subtitle = "",
    subtitleSymbol = "₹",
    titleComponent,
    subtitleComponent,
    expanded = false,
    expandedIcon,
    data,
    titleClassName,
    subtitleClassName,
    iconClassName,
    subtitleSymbolClassName,
    collapsedIcon,
    onExpansionToggled = () => {},
    showIcon = true,
    isMobile,
    isMobileView,
    ...otherProps
  } = props;

  // 2. STATE MANAGEMENT
  const [expandedState, setExpandedState] = useState(expanded);

  // 3. PERFORMANCE OPTIMIZATIONS
  const additionalTitleClassName = useMemo(() => 
    useClassName(props, titleClassName), 
    [props, titleClassName]
  );

  const additionalSubtitleClassName = useMemo(() => 
    useClassName(props, subtitleClassName), 
    [props, subtitleClassName]
  );

  const additionalIconClassName = useMemo(() => 
    useClassName(props, iconClassName), 
    [props, iconClassName]
  );

  const additionalSubtitleSymbolClassName = useMemo(() => 
    useClassName(props, subtitleSymbolClassName), 
    [props, subtitleSymbolClassName]
  );

  const isControlled = useMemo(() => isNonNull(expanded), [expanded]);
  const hasData = useMemo(() => isNonNull(data) && data.length > 0, [data]);
  const hasTitleComponent = useMemo(() => title === "" && isNonNull(titleComponent), [title, titleComponent]);
  const hasSubtitleComponent = useMemo(() => subtitle === "" && isNonNull(subtitleComponent), [subtitle, subtitleComponent]);

  // 4. EVENT HANDLING with useCallback
  const handleExpansionToggle = useCallback((event) => {
    event?.stopPropagation();
    
    const newExpandedState = !expandedState;
    
    if (!isControlled) {
      setExpandedState(newExpandedState);
    }
    
    if (isNonNull(onExpansionToggled)) {
      onExpansionToggled(event, newExpandedState);
    }
  }, [expandedState, isControlled, onExpansionToggled]);

  const handleKeyDown = useCallback((event) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      handleExpansionToggle(event);
    }
  }, [handleExpansionToggle]);

  const handleItemIconClick = useCallback((item) => (event) => {
    event?.stopPropagation();
    if (item.title) {
      alert(item.title);
    }
  }, []);

  // 5. EFFECTS
  useEffect(() => {
    if (isControlled) {
      setExpandedState(expanded);
    }
  }, [expanded, isControlled]);

  // 6. CONDITIONAL RENDERING (memoized)
  const titleElement = useMemo(() => {
    if (hasTitleComponent) {
      return <>{titleComponent}</>;
    }

    return (
      <TitleText
        className={`${additionalTitleClassName} vms_fairbreakup_leftview_titletext`}
      >
        {title}
      </TitleText>
    );
  }, [hasTitleComponent, titleComponent, title, additionalTitleClassName]);

  const subtitleElement = useMemo(() => {
    if (hasSubtitleComponent) {
      return <>{subtitleComponent}</>;
    }

    return (
      <TitleText
        className={`${additionalSubtitleClassName} vms_fairbreakup_rightview_titletext`}
      >
        {subtitle}
      </TitleText>
    );
  }, [hasSubtitleComponent, subtitleComponent, subtitle, additionalSubtitleClassName]);

  const expandIconElement = useMemo(() => {
    if (!showIcon) return null;

    return (
      <ExpandIconComponent
        isExpanded={expandedState}
        expandedIcon={expandedIcon}
        collapsedIcon={collapsedIcon}
        additionalIconClassName={additionalIconClassName}
        onIconClick={handleExpansionToggle}
      />
    );
  }, [showIcon, expandedState, expandedIcon, collapsedIcon, additionalIconClassName, handleExpansionToggle]);

  const dataRows = useMemo(() => {
    if (!hasData) return null;

    return data.map((item, i) => (
      <FairBreakupRow
        key={item.id || i}
        className="vms_fairbreakup_row"
      >
        {showIcon && item.icon && (
          <ExpandIcon
            style={{ color: "red" }}
            className={`${additionalIconClassName} vms_fairbreakup_expandicon`}
            customIcon={true}
            onClick={handleItemIconClick(item)}
            role="button"
            tabIndex={0}
            aria-label={`Icon for ${item.title}`}
          >
            <img
              className="vms_fairbreakup_expandiconimg"
              height={20}
              width={20}
              style={{ objectFit: "scale-down" }}
              src={item.icon}
              alt={`${item.title} icon`}
            />
          </ExpandIcon>
        )}

        <FairBreakUpLeftView className="vms_fairbreakup_leftview">
          <TitleText
            customText={true}
            className={`${additionalTitleClassName} vms_fairbreakup_leftview_titletext_title`}
          >
            {item.title}
          </TitleText>
        </FairBreakUpLeftView>

        <FairBreakUpRightView className="vms_fairbreakup_rightview">
          <CurrencySymbol
            customIcon={true}
            className={`${additionalSubtitleSymbolClassName} vms_fairbreakup_rightview_currencysymbol_subtitle`}
            customText={true}
          >
            {item.subtitleSymbol}
          </CurrencySymbol>
          <TitleText
            className={`${additionalSubtitleClassName} vms_titletext_subtitle`}
            customText={true}
          >
            {item.subtitle}
          </TitleText>
        </FairBreakUpRightView>
      </FairBreakupRow>
    ));
  }, [hasData, data, showIcon, additionalIconClassName, handleItemIconClick, additionalTitleClassName, additionalSubtitleSymbolClassName, additionalSubtitleClassName]);

  const bodyContent = useMemo(() => {
    if (hasData) {
      return dataRows;
    }
    return <>{children}</>;
  }, [hasData, dataRows, children]);

  // 7. ERROR HANDLING
  useEffect(() => {
    if (!title && !titleComponent) {
      console.warn('FairBreakup: Either title or titleComponent prop is required');
    }
    if (!subtitle && !subtitleComponent) {
      console.warn('FairBreakup: Either subtitle or subtitleComponent prop is required');
    }
  }, [title, titleComponent, subtitle, subtitleComponent]);

  return (
    <ThemeWrapper isMobile={isMobile || isMobileView || false}>
      <Container className="vms_fairbreakup_container">
        <FairBreakUpView
          className="vms_fairbreakup_view"
          onClick={handleExpansionToggle}
          onKeyDown={handleKeyDown}
          id={id}
          role="button"
          tabIndex={0}
          aria-expanded={expandedState}
          aria-label="Toggle fair breakup details"
        >
          <FairBreakUpLeftView className="vms_fairbreakup_leftview">
            {titleElement}
          </FairBreakUpLeftView>
          
          {expandIconElement}

          <FairBreakUpRightView className="vms_fairbreakup_rightview">
            <CurrencySymbol
              className={`${additionalSubtitleSymbolClassName} vms_fairbreakup_rightview_currencysymbol`}
            >
              {subtitleSymbol}
            </CurrencySymbol>
            {subtitleElement}
          </FairBreakUpRightView>
        </FairBreakUpView>
        
        {expandedState && (
          <FairBreakUpBody 
            className="vms_fairbreakup_body"
            role="region"
            aria-label="Fair breakup details"
          >
            {bodyContent}
          </FairBreakUpBody>
        )}
      </Container>
    </ThemeWrapper>
  );
});

FairBreakup.displayName = 'FairBreakup';

FairBreakup.defaultProps = {
  title: "",
  subtitle: "",
  subtitleSymbol: "₹",
  showIcon: true,
  onExpansionToggled: () => { },

};
FairBreakup.propTypes = {
  /**
   * id: unique identifier for every accordion
   */
  id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),

  /**
   * key should be provided when multiple Accordions are passed
   */
  key: PropTypes.any,
  /**
   * Title for the accordion NOTE: either title or titleComponent is required for the accordion
   */
  title: PropTypes.string,

  /**
   * SubTitle for the accordion NOTE: either subtitle or subtitleComponent is required for the fairbreakup
   */
  subtitle: PropTypes.string,

  /**
   *subtitle symbol Default: ₹ symbol
   */
  subtitleSymbol: PropTypes.string,

  /**
   *Title component to be displayed on the accordion. NOTE: If title is provided then it has precedence over titleComponent.
   */
  titleComponent: PropTypes.oneOfType([PropTypes.node, PropTypes.string]),

  /**
   *SubTitle component to be displayed on the accordion. NOTE: If subtitle is provided then it has precedence over subtitleComponent.
   */
  subtitleComponent: PropTypes.oneOfType([PropTypes.node, PropTypes.string]),

  /**
   *By passing icon url to expansionIconAfter, expansion icon can be changed after the accordion is expanded
   */
  expandedIcon: PropTypes.string,

  /**
   *expansion icon can be changed by passing icon url to expansionIcon
   */
  collapsedIcon: PropTypes.string,

  /**
   *defaultExpanded can be used to expand the accordion on load If true, expands the panel by default.
   */
  expanded: PropTypes.bool,

  /**
   *defaultExpanded can be used to expand the accordion on load If true, expands the panel by default.
   */
  data: PropTypes.arrayOf(
    PropTypes.shape({
      title: PropTypes.string,
      subtitle: PropTypes.string,
      subtitleSymbol: PropTypes.string,
      icon: PropTypes.string,
    })
  ),

  /**
   * Body of the card component
   */
  children: PropTypes.oneOfType([PropTypes.node, PropTypes.string]),

  /**
   * callback function which will be triggered after toggling the accordion expansion
(event: object, expanded: boolean) => void
   */
  onExpansionToggled: PropTypes.func,

  /**
   * title custom class name
   */
  titleClassName: PropTypes.arrayOf(PropTypes.string),

  /**
   * sub-title custom class name
   */
  subtitleClassName: PropTypes.arrayOf(PropTypes.string),

  /**
   *icon custom class name
   */
  iconClassName: PropTypes.arrayOf(PropTypes.string),

  /**
   *sub-title symbol custom class name
   */
  subtitleSymbolClassName: PropTypes.arrayOf(PropTypes.string),

  /**
   *show expanded icon
   */
  showIcon: PropTypes.bool,
};

export { FairBreakup };
