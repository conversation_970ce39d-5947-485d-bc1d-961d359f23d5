import React from "react";
import { FairBreakup } from "./FairBreakup";
import { Card } from "../Card/Card";
import { InputText } from "../InputText/InputText";
import { InputCheckbox } from "../InputCheckbox/InputCheckbox";
import { RangeSelector } from "../RangeSelector/RangeSelector";

import "./FairBreakup.css";
export default {
  title: "VMS_REACT/FairBreakup",
  component: FairBreakup,
  argTypes: {
    children: { control: { type: "" } },
  },
  parameters: {
    controls: {
      exclude: /.*/g,
    },
  },
};

const baseFair = [
  {
    title: "Base Amount",
    subtitle: "1444.00",
    subtitleSymbol: "₹",
    icon: "",
  },
];

const taxCharges = [
  {
    title: "Tax & Charges",
    subtitle: "50.00",
    subtitleSymbol: "₹",
    icon: "",
  },
  {
    title: "Convenience Fee",
    subtitle: "10.00",
    subtitleSymbol: "₹",
    icon: "",
  },
];

const addOns = [
  {
    title: "Travel Insurance",
    subtitle: "144.00",
    subtitleSymbol: "₹",
    icon: "https://img.icons8.com/material/24/000000/multiply--v2.png",
  },
];

const sale = [
  {
    title: "Independence Sale",
    subtitle: "576.00",
    subtitleSymbol: "- ₹",
    icon: "",
  },
];

const total = [
  {
    title: "Discount",
    subtitle: "576 InterMiles",
    icon: "",
  },
];

const Template = (args) => {
  return (
    <>
      <div style={{ width: "100%" }}>
        <FairBreakup {...args}>
          Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse
          malesuada lacus ex, sit amet blandit leo lobortis eget. Lorem ipsum
          dolor sit amet, consectetur adipiscing elit. Suspendisse malesuada
          lacus ex, sit amet blandit leo lobortis eget.
        </FairBreakup>
      </div>
    </>
  );
};

const CustomView = (args) => {
  return (
    <Card
      title="Fare Details"
      additionalStyle={{ width: args.isMobile ? "100%" : 411 }}
    >
      <div style={{ margin: "0 auto", padding: 20 }}>
        <div style={{ margin: "0 auto", marginTop: 12 }}>
          <FairBreakup title="Privacy Policy" subtitle="" subtitleSymbol="">
            <InputCheckbox
              id="chkTermsLink"
              label={"Checkbox"}
              value="terms"
              islinkwithtext="true"
            />
          </FairBreakup>
        </div>

        <div style={{ margin: "0 auto", marginTop: 12 }}>
          <FairBreakup title="Username" subtitleSymbol="">
            <InputText
              id="0"
              type="text"
              messageMode="success"
              placeholder="Enter username or email id"
            />
          </FairBreakup>
        </div>

        <div style={{ margin: "0 auto", marginTop: 12 }}>
          <FairBreakup title="Select Amount" subtitleSymbol="">
            <RangeSelector
              labelPrefix="₹"
              formatNumber={false}
              additionalClassName={["rangeAdditionalClass"]}
            />
          </FairBreakup>
        </div>
      </div>
    </Card>
  );
};

const CustomViewTemplate = (args) => {
  return (
    <>
      <h4>Custom Fair Breakup View</h4>
      <Card
        title="Fair Details"
        additionalStyle={{ width: args.isMobile ? "100%" : 411 }}
      >
        <div style={{ margin: "0 auto", padding: 20 }}>
          <div style={{ margin: "0 auto", marginTop: 12 }}>
            <FairBreakup
              title="Base Fair"
              subtitle="11000.00"
              subtitleSymbol="₹"
              expanded={args.type == "openByDefault" ? true : false}
              data={baseFair}
            />
          </div>
          <div style={{ margin: "0 auto", marginTop: 12 }}>
            <FairBreakup
              title="Taxes & Charges"
              subtitle="300.00"
              subtitleSymbol="₹"
              data={taxCharges}
            />
          </div>
          <div style={{ margin: "0 auto", marginTop: 12 }}>
            <FairBreakup
              title="Add ons"
              subtitle="576.00"
              subtitleSymbol="₹"
              data={addOns}
            />
          </div>
          <div style={{ margin: "0 auto", marginTop: 12 }}>
            <FairBreakup
              title="Discount"
              subtitle="- ₹ 576.00"
              subtitleSymbol=""
              expandedIcon={
                "https://tdsitflights.vernost.in/static/img/blue_arrow_up.jpg"
              }
              collapsedIcon={
                "https://tdsitflights.vernost.in/static/img/blue_arrow_down.jpg"
              }
              data={sale}
              titleClassName={["green"]}
              subtitleClassName={["green"]}
              iconClassName={["green"]}
              subtitleSymbolClassName={["green"]}
            />
          </div>
          <div
            style={{
              margin: "0 auto",
              marginTop: 20,
              display: "flex",
              flex: 1,
              position: "relative",
            }}
          >
            <div className="Custom-text-container">
              <span className="Custom-text">Payable Amount</span>
            </div>
            <span
              className="Custom-text"
              style={{ position: "absolute", right: 0 }}
            >
              ₹ 11,944.00
            </span>
          </div>
          <div
            style={{
              margin: "0 auto",
              marginTop: 20,
              height: 1,
              backgroundColor: "#ebebec",
            }}
          />
          <div
            style={{
              margin: "0 auto",
              marginTop: 20,
            }}
          >
            <FairBreakup
              title="Total Earnings"
              subtitle="9,000 InterMiles"
              subtitleSymbol=""
              titleClassName={["normalBlue"]}
              subtitleClassName={["normalBlue"]}
              iconClassName={["normalBlue"]}
              subtitleSymbolClassName={["normalBlue"]}
              expandedIcon={
                "https://tdsitflights.vernost.in/static/img/blue_arrow_up.jpg"
              }
              collapsedIcon={
                "https://tdsitflights.vernost.in/static/img/blue_arrow_down.jpg"
              }
              
              data={total}
            />
          </div>
        </div>
      </Card>
    </>
  );
};

export const Default = Template.bind({});
Default.args = {
  title: "Base Fair",
  subtitle: "11000.00",
  subtitleSymbol: "₹",
};

export const CustomTitleAndSubTitle = Template.bind({});
CustomTitleAndSubTitle.args = {
  titleComponent: <div style={{ fontWeight: "600" }}>Title Component</div>,
  subtitleComponent: <div style={{ fontWeight: "600" }}>2000</div>,
};

export const DefaultData = Template.bind({});
DefaultData.args = {
  title: "Base Fair",
  subtitle: "11000.00",
  subtitleSymbol: "₹",
  data: [
    {
      title: "Travel Insurance",
      subtitle: "144.00",
      subtitleSymbol: "₹",
      icon: "https://img.icons8.com/material-sharp/24/000000/unchecked-circle.png",
    },
    {
      title: "Independence Sale",
      subtitle: "576.00",
      subtitleSymbol: "₹",
      icon: "https://img.icons8.com/material/24/000000/multiply--v2.png",
    },
    {
      title: "Discount",
      subtitle: "50.00",
      subtitleSymbol: "₹",
      icon: "https://img.icons8.com/material-outlined/24/000000/plus.png",
    },
  ],
};

export const openByDefault = CustomViewTemplate.bind({});
openByDefault.args = {
  type: "openByDefault",
};
export const CustomFairBreakView = CustomViewTemplate.bind({});
export const CustomFairBreakUpView = CustomView.bind({});

export const MobWebDefault = Template.bind({});
MobWebDefault.story = {
  name: "MoWeb- Default",
  parameters: { viewport: { defaultViewport: "iphone5" } },
};
MobWebDefault.args = {
  title: "Base Fair",
  subtitle: "11000.00",
  subtitleSymbol: "₹",
};

export const MobWebDefaultData = Template.bind({});
MobWebDefaultData.story = {
  name: "MoWeb- Default Data",
  parameters: { viewport: { defaultViewport: "iphone5" } },
};
MobWebDefaultData.args = {
  title: "Base Fair",
  subtitle: "11000.00",
  subtitleSymbol: "₹",
  data: [
    {
      title: "Travel Insurance",
      subtitle: "144.00",
      subtitleSymbol: "₹",
      icon: "https://img.icons8.com/material-sharp/24/000000/unchecked-circle.png",
    },
    {
      title: "Independence Sale",
      subtitle: "576.00",
      subtitleSymbol: "₹",
      icon: "https://img.icons8.com/material/24/000000/multiply--v2.png",
    },
    {
      title: "Discount",
      subtitle: "50.00",
      subtitleSymbol: "₹",
      icon: "https://img.icons8.com/material-outlined/24/000000/plus.png",
    },
  ],
};

export const MobWebCustomView = CustomViewTemplate.bind({});
MobWebCustomView.story = {
  name: "MoWeb- Custom fair Breakup view",
  parameters: { viewport: { defaultViewport: "iphone5" } },
};
MobWebCustomView.args = {
  isMobile: true,
};

export const MobWebOpnebyDefault = CustomViewTemplate.bind({});
MobWebOpnebyDefault.story = {
  name: "MoWeb- Open By Default",
  parameters: { viewport: { defaultViewport: "iphone5" } },
};
MobWebOpnebyDefault.args = {
  type: "openByDefault",
  isMobile: true,
};

export const MobWebCustom = CustomView.bind({});
MobWebCustom.story = {
  name: "MoWeb- Custom view",
  parameters: { viewport: { defaultViewport: "iphone5" } },
};
MobWebCustom.args = {
  isMobile: true,
};
