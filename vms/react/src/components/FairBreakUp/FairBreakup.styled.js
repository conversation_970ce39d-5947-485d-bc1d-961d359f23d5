import styled from "styled-components";

export const Container = styled.div``;
export const FairBreakUpView = styled.div`
  display: flex;
  flex: 1 1;
  position: relative;
`;

export const FairBreakUpLeftView = styled.div`
  justify-content: flex-start;
  display: flex;
  text-align: left;
`;

export const FairBreakUpRightView = styled.div`
  align-self: flex-end;
  position: absolute;
  right: 0;
  display: flex;
  text-align: right;
`;

export const FairBreakUpBody = styled.div`
  padding: 0;
`;
export const ExpandIcon = styled.div`
  height: 20px;
  width: 20px;
  margin-left: ${({ customIcon }) => (customIcon ? "" : "11px")};
  margin-right: ${({ customIcon }) => (customIcon ? "8px" : "")};
  align-self: center;
`;

export const TitleText = styled.span`
  font-size: 14px;
  line-height: 20px;
  letter-spacing: 0.3px;
  font-weight: 400;
  font-family: ${({ theme }) => theme?.typography?.fontFamily || ""};
  justify-content: center;
  align-self: center;
  color: ${({ customText }) => (customText ? "#939598" : "#4d4d4f")};
`;

export const CurrencySymbol = styled.div`
  font-weight: 300;
  font-family: ${({ theme }) => theme?.typography?.fontFamily || ""};
  line-height: 20px;
  letter-spacing: 0.3px;
  color: ${({ customText }) => (customText ? "#939598" : "#4d4d4f")};
  margin-right: 3px;
`;

export const FairBreakupRow = styled.div`
  display: flex;
  margin-right: 8px;
  flex: 1 1;
  position: relative;
`;
