import PropTypes from "prop-types";
import React, { Fragment, useState, memo, useCallback, useMemo } from "react";
import { useClassName } from "../../hooks/useClassName";
import {
  FilterTagWrapper,
  FilterTxtDiv,
  FilterSpan,
  FilterImgDiv,
  Svg,
  CrossImg,
} from "./FilterTag.styled";
import { ThemeWrapper } from "../Theme/ThemeContext";

// Utility function (placed before main component)
const isNonNull = (prop) => prop !== null && prop !== undefined;

export const FilterTag = memo((props) => {
  // 1. PROP DESTRUCTURING & VALIDATION
  const {
    viewStyle,
    imagePath,
    imageAlt = "Filter tag icon",
    imageStyle,
    textStyle,
    children,
    additionalClassName,
    onPress,
    isMobile,
    isMobileView,
    ...otherProps
  } = props;

  // 2. PERFORMANCE OPTIMIZATIONS
  const computedClassName = useMemo(
    () => useClassName(props, additionalClassName),
    [props, additionalClassName]
  );

  const hasImage = useMemo(() => isNonNull(imagePath), [imagePath]);

  // 3. EVENT HANDLING with useCallback
  const handlePress = useCallback(
    (event) => {
      event?.stopPropagation();

      if (isNonNull(onPress)) {
        onPress(event);
      }
    },
    [onPress]
  );

  const handleKeyDown = useCallback(
    (event) => {
      if (event.key === "Enter" || event.key === " ") {
        handlePress(event);
      }
    },
    [handlePress]
  );

  // 4. CONDITIONAL RENDERING (memoized)
  const imageElement = useMemo(() => {
    if (!hasImage) {
      return <CrossIcon imageStyle={imageStyle} />;
    }

    return (
      <CrossImg
        src={imagePath}
        alt={imageAlt}
        style={imageStyle}
      />
    );
  }, [hasImage, imagePath, imageAlt, imageStyle]);

  // 5. ERROR HANDLING
  React.useEffect(() => {
    if (!isNonNull(onPress)) {
      console.warn(
        "FilterTag: onPress callback is required for meaningful interaction"
      );
    }
    if (!children) {
      console.warn("FilterTag: children prop is required to display tag content");
    }
  }, [onPress, children]);

  return (
    <ThemeWrapper isMobile={isMobile || isMobileView || false}>
      <FilterTagWrapper
        className={`${computedClassName} vms_filtertag`}
        style={viewStyle}
        role="button"
        tabIndex={0}
        onClick={handlePress}
        onKeyDown={handleKeyDown}
        aria-label={`Remove ${children} filter`}
        {...otherProps}
      >
        <FilterTxtDiv className="vms_filtertag_text">
          <FilterSpan style={textStyle}>
            {children}
          </FilterSpan>
        </FilterTxtDiv>
        <FilterImgDiv
          className="vms_filtertag_icon"
          onClick={handlePress}
          role="button"
          tabIndex={0}
          aria-label="Remove filter"
        >
          {imageElement}
        </FilterImgDiv>
      </FilterTagWrapper>
    </ThemeWrapper>
  );
});

// Memoized Cross Icon component
const CrossIcon = memo(({ imageStyle }) => (
  <Svg
    x="0px"
    y="0px"
    width="24px"
    height="24px"
    viewBox="366.9 359.3 23.6 23.6"
    style={imageStyle}
    role="img"
    aria-hidden="true"
  >
    <g>
      <path
        fill="#8B8D8F"
        d="M386.631,366.739l-1.361-1.362l-9.265,9.265l-9.265-9.265l-1.361,1.362l9.265,9.264l-9.265,9.265l1.361,1.361l9.265-9.264
        l9.265,9.264l1.361-1.361l-9.265-9.265L386.631,366.739z"
      />
    </g>
  </Svg>
));

// Component display names for debugging
FilterTag.displayName = "FilterTag";
CrossIcon.displayName = "CrossIcon";

FilterTag.defaultProps = {
  viewStyle: null,
  imagePath: null,
  imageAlt: null,
  imageStyle: null,
  textStyle: null,
  children: null,
  additionalClassName: null,
  onPress: null,
};

FilterTag.propTypes = {
  /**
   * Style of background view with border properties and background color
   * CSSProperties
   */
  viewStyle: PropTypes.object,
  /**
   * Path or URL of the image to render
   */
  imagePath: PropTypes.string,
  /**
   * Alternate text for the image.
   */
  imageAlt: PropTypes.string,
  /**
   * Additional style for Image
   */
  imageStyle: PropTypes.object,
  /**
   * Additional style for Text
   */
  textStyle: PropTypes.object,
  /**
   * Name of filter
   */
  children: PropTypes.string,
  /**
   * Property to append additional css class
   */
  additionalClassName: PropTypes.arrayOf(PropTypes.string),
  /**
   * Callback on image click
   * () => void
   */
  onPress: PropTypes.func.isRequired,
};
