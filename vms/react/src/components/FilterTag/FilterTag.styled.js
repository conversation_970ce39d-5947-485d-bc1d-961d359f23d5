import styled from "styled-components";

export const FilterTagWrapper = styled.div`
  display: flex;
  width: fit-content;
  justify-content: center;
  align-items: center;
  flex-direction: row;
  background-color: ${({ theme }) => theme.palette.primary[theme.mode]};
  border-radius: 3px;
  padding: 7px;
  gap: 5px;
`;
export const FilterTxtDiv = styled.div`
  align-items: center;
  display: flex;
`;
export const FilterSpan = styled.span`
  font-weight: ${({ theme }) => theme.typography.text.fontWeight};
  font-family: ${({ theme }) => theme.typography.fontFamily};
  font-size: ${({ theme }) => theme.typography.text.fontSize};
  line-height: ${({ theme }) => theme.typography.lineHeight};
  letter-spacing: ${({ theme }) => theme.typography.letterSpacing};
  color: ${({ theme }) => theme.palette.primary.contrastText};
`;
export const FilterImgDiv = styled.div`
  align-items: center;
  display: flex;
`;
export const Svg = styled.svg`
  cursor: pointer;
  color: ${({ theme }) => theme.palette.primary.contrastText};
  width: 14px;
  height: 22px;
`;
export const CrossImg = styled.img`
  cursor: pointer;
`;
