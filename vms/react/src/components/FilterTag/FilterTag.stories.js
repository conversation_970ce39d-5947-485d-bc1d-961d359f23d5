import React, { Fragment, useState } from "react";
import { FilterTag } from "./FilterTag";
import { InputCheckbox } from "../InputCheckbox/InputCheckbox";
export default {
  title: "VMS_REACT/FilterTag",
  component: FilterTag,
  argTypes: {
    additionalClassName: { control: { type: "" } },
  },
};
const Template = (args) => (
  <FilterTag
    {...args}
    onPress={() => {
      console.log("Cross button Pressed");
    }}
  />
);
export const Default = Template.bind({});
Default.args = {
  children: "Villa",
};
export const TagwithoutIcon = Template.bind({});
TagwithoutIcon.storyName = "Tag Without Icon";
TagwithoutIcon.args = {
  imagePath: "",
  children: "Villa",
};

export const CustomBackgroundview = Template.bind({});
CustomBackgroundview.storyName = "Custom Background View";
CustomBackgroundview.args = {
  children: "Color",
  viewStyle: {
    backgroundColor: "rgb(210, 212, 209)",
    border: "3px solid black",
    borderRadius: "10px",
  },
};
export const CustomImage = Template.bind({});
CustomImage.args = {
  children: " Cash On Arrival",
  imagePath: "https://img.icons8.com/pastel-glyph/64/000000/plus.png",
  imageStyle: {
    width: "25px",
    marginRight: "4px",
    marginLeft: "6px",
  },
};

export const CustomView = Template.bind({});
CustomView.args = {
  children: "Free WiFi",
  viewStyle: {
    marginTop: 10,
    border: "2px solid #FF0000",
    borderRadius: "5px",
    backgroundColor: "#FFFFFF",
  },
  textStyle: { color: "#FF0000", fontSize: "12px" },
  imageStyle: { color: "#FF0000" },
};

export const FilterTagByApplication = () => {
  const filtersByName = ["Free Wifi", "Villa", "Cash On Arrival"];
  const [selectedFilters, setSelectedFilters] = useState([]);
  const onFilterSelectHandler = (value) => {
    let updatedSelectFilter = [...selectedFilters];
    if (updatedSelectFilter.includes(value)) {
      updatedSelectFilter = updatedSelectFilter.filter(
        (element) => element != value
      );
      setSelectedFilters(updatedSelectFilter);
    } else {
      updatedSelectFilter.push(value);
      setSelectedFilters(updatedSelectFilter);
    }
  };
  return (
    <Fragment>
      <div style={{ display: "flex", flexDirection: "column" }}>
        <p>Please select Filter</p>
        <div>
          {filtersByName.map((filter) => {
            return (
              <InputCheckbox
              id="filtertag-id"
                key={filter}
                value={filter}
                label={filter}
                onChange={(e) => {
                  onFilterSelectHandler(e.target.value);
                }}
                checked={selectedFilters.includes(filter)}
              />
            );
          })}
        </div>
        <div style={{ display: "flex",flexDirection:"column", gap: "10px" }}>
          {selectedFilters.length>0&&(
          <p>Selected Filters are</p>
          )}
          <div style={{ display: "flex", gap: "10px" }}>
            {selectedFilters.map((filterSelected,i) => (
              <FilterTag
                key={i}
                onPress={(e) => {
                  onFilterSelectHandler(filterSelected);
                }}
              >
                {filterSelected}
              </FilterTag>
            ))}
          </div>
        </div>
      </div>
    </Fragment>
  );
};
