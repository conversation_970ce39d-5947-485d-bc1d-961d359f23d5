import FullRatingSvg from "../../assets/images/RatingBar/fullRating.svg";
import HalfRatingSvg from "../../assets/images/RatingBar/halfRating.svg";
import EmptyRatingSvg from "../../assets/images/RatingBar/emptyRating.svg";

export function checkRatingStatus(rating, index) {
  if (rating > 0) {
    const RatingValue = Math.ceil(rating);
    if (index <= RatingValue) {
      if (rating < index) {
        return `half`;
      } else {
        return `full`;
      }
    } else {
      return `empty`;
    }
  } else {
    return `empty`;
  }
}

export function checkRatingClassName(type) {
  switch (type) {
    case "half":
      return "halfImageClass";
    case "full":
      return "fullImageClass";
    case "empty":
      return "emptyImageClass";
    default:
      return "";
  }
}

export function checkRatingAlt(
  type,
  emptyImageAlt,
  halfImageAlt,
  fullImageAlt
) {
  switch (type) {
    case "half":
      return halfImageAlt ? halfImageAlt : "hafl circle";
    case "full":
      return fullImageAlt ? fullImageAlt : "full circle";
    case "empty":
      return emptyImageAlt ? emptyImageAlt : "empty circle";
    default:
      return "";
  }
}

export function getRatingImg(
  type,
  emptyImageClass,
  halfImageClass,
  fullImageClass
) {
  switch (type) {
    case "half":
      return halfImageClass ? halfImageClass : HalfRatingSvg;
    case "full":
      return fullImageClass ? fullImageClass : FullRatingSvg;
    case "empty":
      return emptyImageClass ? emptyImageClass : EmptyRatingSvg;
    default:
      return EmptyRatingSvg;
  }
}

// export function getBackgroundColor(rating) {
//   if(rating > 4){
//     return '#1fac34';
//   }else if(rating >= 3 && rating<=4 ){
//     return '#e59500';
//   }else if(rating <=3){
//     return '#d0021b';
//   }else{
//     return '#d0021b';
//   }
// }

export function getBackgroundColor(rating) {
  if (rating > 4) {
    return '#1fac34'; // Green
  } else if (rating >= 3 && rating<=4) {
    return '#e59500'; // Orange
  } else {
    return '#d0021b'; // Red
  }
}
