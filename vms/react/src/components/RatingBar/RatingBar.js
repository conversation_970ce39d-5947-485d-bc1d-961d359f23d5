import React, { useEffect, useState, memo, useCallback, useMemo } from "react";
import PropTypes from "prop-types";
import {
  Container,
  LogoContainer,
  LogoContainerImg,
  LogoImg,
  NewLineReviewContainer,
  RatingBubble,
  RatingText,
  RatingValue,
  ReviewContainer,
  ReviewValue,
} from "./RatingBar.styled";
import { useClassName } from "../../hooks/useClassName";
import { checkRatingAlt, checkRatingStatus, getRatingImg } from "./Common";
import LogoImage from "../../assets/images/RatingBar/logoImg.svg";
import { ThemeWrapper } from "../Theme/ThemeContext";

// Utility function (placed before main component)
const isNonNull = (prop) => prop !== null && prop !== undefined;

const RatingBar = memo((props) => {
  // 1. PROP DESTRUCTURING & VALIDATION
  const {
    componentsToShow,
    ratings = 0,
    logoImageClass,
    logoImageAlt,
    emptyImageAlt,
    halfImageAlt,
    fullImageAlt,
    emptyImageClass,
    halfImageClass,
    fullImageClass,
    reviews,
    displayReviewOnNextLine = false,
    reviewsHref,
    maxRatings = 5,
    onTripAdvisorClick = () => {},
    onTripAdvisorKeyDown = () => {},
    additionalClassName,
    additionalStyle,
    isMobile,
    isMobileView,
    ...otherProps
  } = props;

  // 2. STATE MANAGEMENT
  const [bubbleData, setBubbleData] = useState([]);

  // 3. PERFORMANCE OPTIMIZATIONS
  const logoImageClassName = useMemo(() => 
    useClassName(props, logoImageClass), 
    [props, logoImageClass]
  );

  const computedAdditionalClassName = useMemo(() => 
    useClassName(props, additionalClassName), 
    [props, additionalClassName]
  );

  const isMobileDevice = useMemo(() => 
    isMobile || isMobileView || false, 
    [isMobile, isMobileView]
  );

  const shouldShowLogo = useMemo(() => 
    componentsToShow.includes("logo"), 
    [componentsToShow]
  );

  const shouldShowRatingBox = useMemo(() => 
    componentsToShow.includes("ratingBox"), 
    [componentsToShow]
  );

  const shouldShowBubble = useMemo(() => 
    componentsToShow.includes("bubble"), 
    [componentsToShow]
  );

  const shouldShowReviewsCount = useMemo(() => 
    componentsToShow.includes("reviewsCount"), 
    [componentsToShow]
  );

  // 4. EVENT HANDLING with useCallback
  const handleTripAdvisorClick = useCallback((e) => {
    if (isNonNull(onTripAdvisorClick)) {
      onTripAdvisorClick(e);
    }
  }, [onTripAdvisorClick]);

  const handleTripAdvisorKeyDown = useCallback((e) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      handleTripAdvisorClick(e);
    }
    if (isNonNull(onTripAdvisorKeyDown)) {
      onTripAdvisorKeyDown(e);
    }
  }, [handleTripAdvisorClick, onTripAdvisorKeyDown]);

  // 5. BUBBLE DATA GENERATION with useCallback
  const generateBubbleData = useCallback(() => {
    if (!shouldShowBubble) return;

    const bubbleImage = [];
    for (let index = 0; index < maxRatings; index++) {
      const getRatingsStatus = checkRatingStatus(ratings, index + 1);
      bubbleImage.push({
        alt: checkRatingAlt(
          getRatingsStatus,
          emptyImageAlt,
          halfImageAlt,
          fullImageAlt
        ),
        image: getRatingImg(
          getRatingsStatus,
          emptyImageClass,
          halfImageClass,
          fullImageClass
        ),
        type: getRatingsStatus,
      });
    }
    setBubbleData(bubbleImage);
  }, [
    shouldShowBubble,
    maxRatings,
    ratings,
    emptyImageAlt,
    halfImageAlt,
    fullImageAlt,
    emptyImageClass,
    halfImageClass,
    fullImageClass
  ]);

  // 6. EFFECTS
  useEffect(() => {
    generateBubbleData();
  }, [generateBubbleData]);

  // 7. MEMOIZED COMPONENTS
  const logoComponent = useMemo(() => {
    if (!shouldShowLogo) return null;

    return (
      <LogoContainerImg className="vms_ratingbar_logoimg">
        <img
          className={logoImageClassName || 'logoCss'}
          alt={logoImageAlt || "rating-bar-logo"}
          src={LogoImage}
        />
      </LogoContainerImg>
    );
  }, [shouldShowLogo, logoImageClassName, logoImageAlt]);

  const ratingBoxComponent = useMemo(() => {
    if (!shouldShowRatingBox) return null;

    return (
      <RatingValue 
        className="vms_ratingbar_value" 
        rating={ratings} 
        onClick={handleTripAdvisorClick}
        onKeyDown={handleTripAdvisorKeyDown}
        role="button"
        tabIndex={0}
        aria-label={`Rating: ${ratings} out of ${maxRatings}`}
      >
        <RatingText className="vms_ratingbar_text">{ratings}</RatingText>
      </RatingValue>
    );
  }, [shouldShowRatingBox, ratings, handleTripAdvisorClick, handleTripAdvisorKeyDown, maxRatings]);

  const bubbleComponent = useMemo(() => {
    if (!shouldShowBubble || !bubbleData.length) return null;

    return (
      <LogoContainerImg className="vms_ratingbar_logocontainerimg">
        <div role="img" aria-label={`${ratings} out of ${maxRatings} stars`}>
          {bubbleData.map((item, index) => (
            <RatingBubble 
              key={`rating-bubble-${index}`} 
              path={item.image} 
              index={index + 1} 
              className="vms_ratingbar_ratingbubble"
              alt={item.alt}
              aria-hidden="true"
            />
          ))}
        </div>
      </LogoContainerImg>
    );
  }, [shouldShowBubble, bubbleData, ratings, maxRatings]);

  const reviewsComponent = useMemo(() => {
    if (!shouldShowReviewsCount || !reviews) return null;

    const reviewElement = (
      <ReviewValue
        className={displayReviewOnNextLine ? "vms_ratingbar_reviewvalue" : "vms_ratingbar_review_value"}
        href={reviewsHref || null}
        cursor={reviewsHref ? "pointer" : "default"}
        target={reviewsHref ? "_blank" : undefined}
        rel={reviewsHref ? "noopener noreferrer" : undefined}
        role={reviewsHref ? "link" : "text"}
        aria-label={`${reviews} reviews${reviewsHref ? ' (opens in new tab)' : ''}`}
      >
        {reviews}
      </ReviewValue>
    );

    if (displayReviewOnNextLine) {
      return (
        <NewLineReviewContainer 
          className="vms_ratingbar_newlinereviewcontainer" 
          onClick={handleTripAdvisorClick}
          onKeyDown={handleTripAdvisorKeyDown}
          role="button"
          tabIndex={0}
        >
          {reviewElement}
        </NewLineReviewContainer>
      );
    }

    return (
      <ReviewContainer className="vms_ratingbar_reviewcontainer">
        {reviewElement}
      </ReviewContainer>
    );
  }, [
    shouldShowReviewsCount,
    reviews,
    displayReviewOnNextLine,
    reviewsHref,
    handleTripAdvisorClick,
    handleTripAdvisorKeyDown
  ]);

  // 8. ERROR HANDLING
  useEffect(() => {
    if (!Array.isArray(componentsToShow) && typeof componentsToShow !== 'string') {
      console.warn('RatingBar: componentsToShow should be an array or string');
    }
    if (ratings < 0 || ratings > maxRatings) {
      console.warn(`RatingBar: ratings (${ratings}) should be between 0 and ${maxRatings}`);
    }
    if (maxRatings <= 0) {
      console.warn('RatingBar: maxRatings should be greater than 0');
    }
  }, [componentsToShow, ratings, maxRatings]);

  return (
    <ThemeWrapper isMobile={isMobileDevice}>
      <div 
        role="img" 
        aria-label={`Rating: ${ratings} out of ${maxRatings}${reviews ? `, ${reviews}` : ''}`}
        className={computedAdditionalClassName}
        style={additionalStyle}
        {...otherProps}
      >
        <Container role="presentation" className="vms_ratingbar_container">
          <LogoContainer className="vms_ratingbar_logo">
            {logoComponent}
            {ratingBoxComponent}
            {bubbleComponent}
          </LogoContainer>
          {reviewsComponent}
        </Container>
      </div>
    </ThemeWrapper>
  );
});

RatingBar.displayName = 'RatingBar';

RatingBar.defaultProps = {
  ratings: 0,
  displayReviewOnNextLine: false,
  maxRatings: 5,
  onTripAdvisorClick: () => { },
  onTripAdvisorKeyDown: () => { },
};
RatingBar.propTypes = {
  /**
    
  * Defines a string value that labels the current element.
   */
  componentsToShow: PropTypes.oneOf([
    "logo",
    "ratingBox",
    "bubble",
    "reviewsCount",
  ]).isRequired,

  /**
    
  * Imagepath of Logo image.
   */
  logoImageClass: PropTypes.string,

  /**
   * Alternate text for the logo.
   */
  logoImageAlt: PropTypes.string,

  /**
    
  * Imagepath of Empty Rating logo.
   */
  emptyImageClass: PropTypes.string,

  /**
   * Alternate text for the Empty Rating logo.
   */
  emptyImageAlt: PropTypes.string,

  /**
    
  * Imagepath of of the Half Rating logo.
   */
  halfImageClass: PropTypes.string,

  /**
   * Alternate text for the Half Rating logo.
   */
  halfImageAlt: PropTypes.string,

  /**
    
  * Imagepath of of the Full Rating logo.
   */
  fullImageClass: PropTypes.string,

  /**
   * Alternate text for the Full Rating logo.
   */
  fullImageAlt: PropTypes.string,

  /**
    
  * Imagepath of Empty Rating logo.
   */
  // emptyImageClass: PropTypes.string,

  /**
   * Alternate text for the Empty Rating logo.
   */
  // emptyImageAlt: PropTypes.string,

  /**
    
  * Current rating number to be displayed. Default value 0
   */
  ratings: PropTypes.number,

  /**
    
  * Defines a string value that labels the current element.
   */
  arialabel: PropTypes.string,

  /**
    
  * Max ratings. Default value 5
   */
  maxRatings: PropTypes.number,

  /**
    
  * reviewsHref prop can be used to pass the address/link of the view user wants to show
   */

  reviewsHref: PropTypes.string,
  /**
   * Text to be displayed as Reviews.
   */
  reviews: PropTypes.string,
  /**
   *displayReviewOnNextLine flag will enable css styles to display the reviews either on same line or next line
   */

  displayReviewOnNextLine: PropTypes.bool,

  /**
   *Event to be triggered on keypress.
   */

  onTripAdvisorKeyDown: PropTypes.func,

  /**
   *On click of block of Advisor.
   */

  onTripAdvisorClick: PropTypes.func,

  /**
   * Classes to add additional styling in the parent container
   */
  additionalClassName: PropTypes.arrayOf(PropTypes.string),
  /**
   * Inline styles to add additional styling in the parent container
   */
  additionalStyle: PropTypes.object,
};

export { RatingBar };
