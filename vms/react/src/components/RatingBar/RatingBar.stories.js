import * as React from "react";
import { RatingBar } from "./RatingBar";
import "./Rating.css";
const logoImageClass = "logoClass";
const emptyImageClass =
  "https://img.icons8.com/material/32/000000/final-state.png";
const halfImageClass =
  "https://img.icons8.com/material/32/000000/final-state.png";
const fullImageClass =
  "https://img.icons8.com/material/32/000000/final-state.png";

export default {
  title: "VMS_REACT/RatingBar",
  component: RatingBar,
};

export const UsingExternalImages = () => (
  <RatingBar
    ratings={3.3}
    componentsToShow={["logo", "ratingBox", "bubble", "reviewsCount"]}
    // logoImageClass={logoImageClass}
    reviews="(150 reviews)"
  />
);

UsingExternalImages.story = {
  name: "using external images, all options",
};

export const UsingExternalImages1 = () => (
  <RatingBar componentsToShow={["logo"]} />
);

UsingExternalImages1.story = {
  name: "using external images, only logo",
};

export const UsingExternalImages2 = () => (
  <RatingBar componentsToShow={["logo", "ratingBox"]} ratings={2.9} />
);

UsingExternalImages2.story = {
  name: "using external images, logo and rating box",
};

export const UsingExternalImages3 = () => (
  <RatingBar
    componentsToShow={["ratingBox", "bubble"]}
    ratings={2.9}
    reviews="(150 reviews)"
  />
);

UsingExternalImages3.story = {
  name: "using rating and bubble",
};

export const UsingExternalImages4 = () => (
  <RatingBar
    componentsToShow={["ratingBox", "bubble", "reviewsCount"]}
    ratings={2.9}
    reviews="(150 reviews)"
  />
);

UsingExternalImages4.story = {
  name: "using  rating, bubble and review Count",
};

export const UsingExternalImages5 = () => (
  <RatingBar
    componentsToShow={["ratingBox", "logo", "reviewsCount"]}
    ratings={2.9}
    reviews="(150 reviews)"
  />
);

UsingExternalImages5.story = {
  name: "using  rating, logo and review Count",
};

export const WithBubbleAndRatingBelow3 = () => (
  <RatingBar
    componentsToShow={["ratingBox", "logo", "bubble", "reviewsCount"]}
    ratings={2.9}
    reviews="(150 reviews)"
  />
);

WithBubbleAndRatingBelow3.story = {
  name: "With bubble, and rating below 3",
};

export const WithBubbleAndRating3 = () => (
  <RatingBar
    componentsToShow={["ratingBox", "logo", "bubble", "reviewsCount"]}
    ratings={3}
    reviews="(150 reviews)"
  />
);

WithBubbleAndRating3.story = {
  name: "With bubble, and rating = 3",
};

export const WithBubbleAndRatingBetween3And4 = () => (
  <RatingBar
    componentsToShow={["ratingBox", "logo", "bubble", "reviewsCount"]}
    ratings={3.4}
    reviews="(150 reviews)"
  />
);

WithBubbleAndRatingBetween3And4.story = {
  name: "With bubble, and rating between 3 and 4",
};

export const WithBubbleAndRating4 = () => (
  <RatingBar
    componentsToShow={["ratingBox", "logo", "bubble", "reviewsCount"]}
    ratings={4}
    reviews="(150 reviews)"
  />
);

WithBubbleAndRating4.story = {
  name: "With bubble, and rating = 4",
};

export const WithBubbleAndRatingAbove4 = () => (
  <RatingBar
    componentsToShow={["ratingBox", "logo", "bubble", "reviewsCount"]}
    ratings={4.1}
    reviews="(150 reviews)"
  />
);

WithBubbleAndRatingAbove4.story = {
  name: "With bubble, and rating above 4",
};

export const WithBubbleAndRating5 = () => (
  <RatingBar
    componentsToShow={["ratingBox", "logo", "bubble", "reviewsCount"]}
    ratings={5}
    reviews="(150 reviews)"
    displayReviewOnNextLine={true}
  />
);

WithBubbleAndRating5.story = {
  name: "With bubble, and rating = 5",
};

export const WithoutBubbleAndRatingBelow3 = () => (
  <RatingBar
    componentsToShow={["ratingBox", "logo", "reviewsCount"]}
    ratings={2.9}
    reviews="(150 reviews)"
    displayReviewOnNextLine={true}
  />
);

WithoutBubbleAndRatingBelow3.story = {
  name: "Without bubble, and rating below 3",
};

export const WithoutBubbleAndRatingBetween3And4 = () => (
  <RatingBar
    componentsToShow={["ratingBox", "logo", "reviewsCount"]}
    ratings={3.14}
    reviews="(150 reviews)"
    displayReviewOnNextLine={true}
  />
);

WithoutBubbleAndRatingBetween3And4.story = {
  name: "Without bubble, and rating between 3 and 4",
};

export const WithoutBubbleAndRatingAbove4 = () => (
  <div style={{ float: "right" }}>
    <RatingBar
      componentsToShow={["ratingBox", "logo", "reviewsCount"]}
      ratings={4.9}
      reviews="(150 reviews)"
      reviewsHref="#"
      displayReviewOnNextLine={true}
    />
  </div>
);

WithoutBubbleAndRatingAbove4.story = {
  name: "Without bubble, and rating above 4",
};

export const OnClickEvent = () => (
  <div>
    <RatingBar
      ratings={3.3}
      componentsToShow={["logo", "ratingBox", "bubble", "reviewsCount"]}
      // logoImageClass={logoImageClass}
      onTripAdvisorClick={(e) => alert("Advisor Click")}
      reviews="(150 reviews)"
      displayReviewOnNextLine={true}
    />
  </div>
);

OnClickEvent.story = {
  name: "Click Event",
};
