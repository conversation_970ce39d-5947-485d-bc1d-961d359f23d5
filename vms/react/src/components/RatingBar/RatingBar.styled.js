import styled from "styled-components";
import LogoImage from "../../assets/images/RatingBar/logoImg.svg";
import { getBackgroundColor } from "./Common";

export const Container = styled.div`
  outline: none;
  /* width: 300px; */
`;

export const LogoContainer = styled.div`
  outline: none;
`;

export const LogoContainerImg = styled.div`
  float: left;
`;

export const LogoImg = styled.img`
  width: 24px;
  height: 24px;
  vertical-align: middle;
  content:  ${`url(${LogoImage})`};
  fill: #000;
`;

export const RatingValue = styled.div`
  width: 31px;
  height: 22px;
  border-radius: 2px;
  text-align: center;
  float: left;
  margin-left: 9px;
  cursor: context-menu;
  background-color: ${({rating}) => getBackgroundColor(rating)};
`;
export const RatingText = styled.span`
  font-weight: 700;
  font-family: ${({ theme }) => theme?.typography?.fontFamily || ""};
  font-size: 12px;
  font-weight: bold;
  font-style: normal;
  font-stretch: normal;
  line-height: 18px;
  letter-spacing: normal;
  color: #fff;
  position: relative;
  padding: 2px 6px;
`;

export const RatingBubble = styled.img`
  height: 24px;
  height: 24px;
  float: left;
  margin-left: ${({ index }) => (index === 1 ? "5px" : "")};
  content: ${({ path }) => `url(${path})`};
`;

export const ReviewContainer = styled.div`
  height: 18px;
  float: left;
  font-weight: 400;
  font-family: ${({ theme }) => theme?.typography?.fontFamily || ""};
  font-size: 14px;
  color: #000;
  position: relative;
  top: 1px;
  margin-left: 8px;
  cursor: context-menu;
`;

export const NewLineReviewContainer = styled.div`
  font-weight: 400;
  font-family: ${({ theme }) => theme?.typography?.fontFamily || ""};
  font-size: 10px;
  color: #000;
  display: block;
  clear: both;
  cursor: context-menu;
`;

export const ReviewValue = styled.a`
  color: inherit;
  text-decoration: none;
`;
