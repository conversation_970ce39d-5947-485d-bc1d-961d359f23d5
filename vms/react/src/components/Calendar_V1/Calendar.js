import React, { useMemo } from "react";
import PropTypes from "prop-types";
import "./style.css";
import { MobileCalendar } from "./MobileCalendar";
import { DesktopCalendar } from "./DesktopCalendar";
import { ThemeWrapper } from "../Theme/ThemeContext";

const Calendar_V1 = React.memo((props) => {
   const { isMobile } = props;
   
   // Memoize the mobile flag calculation
   const mobileFlag = useMemo(() => {
      return props?.isMobile || props?.isMobileView || false;
   }, [props?.isMobile, props?.isMobileView]);
   
   // Memoize the calendar component selection
   const calendarComponent = useMemo(() => {
      return isMobile ? (
         <MobileCalendar {...props} />
      ) : (
         <DesktopCalendar {...props} />
      );
   }, [isMobile, props]);
   
   return (
      <ThemeWrapper isMobile={mobileFlag}>
         {calendarComponent}
      </ThemeWrapper>
   );
});

Calendar_V1.displayName = 'Calendar_V1';

Calendar_V1.defaultProps = {
   withLabel: true,
   onDateSelected: () => { },
   view: "day",
   openCalendar: false,
   className: "",
   isMobile: false,
   closeButtonType: "primary",
   closeButtonText: "Done",
   dateDisplayFormat: "dd MMM yyyy",
   additionalClassName: null,
   additionalStyle: null,
   inputBoxLabel: "DATE",
   inputPlaceholder: "Select Date",
   isOverlay: false,
   calendarType: "primary",
};

Calendar_V1.propTypes = {
   /**
     
   * ID for the input
    */
   id: PropTypes.string,

   /**
    * Variations of Calendar Type
    */
   calendarType: PropTypes.oneOf(["primary", "secondary"]),

   /**
    * Selected Date that application want to be selected default
 Date
    */
   selectedDate: PropTypes.instanceOf(Date),

   /**
    * Whether show up the label of the Date input
    */
   withLabel: PropTypes.bool,

   /**
    * To display label above input box. Default value DATE
 string
    */

   inputBoxLabel: PropTypes.string,

   /**
    * 	
 Defines minimum date. Disabled earlier dates.
 When isMobile is false, it will take current Date - 100 years as default value.
 When isMobile is true, it will take current Date as default value.
 
 Date
    */
   minDate: PropTypes.instanceOf(Date),

   /**
    * Defines maximum date. Disabled later dates.
 When isMobile is false, it will take current Date + 100 years as default value.
 When isMobile is true, it will take current Date + 1 year as default value.
 
 Date
    */
   maxDate: PropTypes.oneOfType([PropTypes.instanceOf(Date)]),

   /**
    * Only applicable when @property isMobile is false.
 It will replace up and down arrow with given values.
 you can pass direct web URL or local image path
 
 { upArrow: string; downArrow: string; disabledUpArrow: string; disabledDownArrow: string; }
    */
   arrowImages: PropTypes.shape({
      upArrow: PropTypes.string,
      downArrow: PropTypes.string,
      disabledUpArrow: PropTypes.string,
      disabledDownArrow: PropTypes.string,
   }),
   /**
    * To enable mobile view. Default value false
 boolean
    */
   isMobile: PropTypes.bool,

   /**
    * If @property isMobile is true then this function will be called when user click on done button
 If @property isMobile is false then this function will be called when user click on any date
 it will return {
    date: Date | undefined,
    format1 (ISO format): string,
    format2 (DD-MM-YYYY): string,
    };
 }
 
 (result: CalendarOutput) => void
    */
   onDateSelected: PropTypes.func,
   /**
    * Only applicable when @property isMobile value true.
 Close button text. it will take Done as default value.
 
 string
    */
   closeButtonText: PropTypes.string,
   /**
    * Only applicable when @property isMobile value true.
 Close button type. it will take primary as default value.
 
 "primary" | "link" | "secondary" | "tertiary" | "ghost"
    */
   closeButtonType: PropTypes.oneOf([
      "primary",
      "secondary",
      "link",
      "tertiary",
   ]),
   /**
    * Displayed Date format . Default value dd MMM yyyy
 Please refer https://date-fns.org/v2.12.0/docs/format for various allowed format
 
 string
    */
   dateDisplayFormat: PropTypes.string,

   /**
    * Only applicable when @property isMobile value false.
 If you want to open calendar pass true
 
 boolean
    */
   openCalendar: PropTypes.bool,
   /**
    *Inline styles to add additional styling in the parent container
 CSSProperties
    */
   additionalStyle: PropTypes.object,
   /**
    * Classes to add additional styling in the parent container
 string[]
    */

   additionalClassName: PropTypes.arrayOf(PropTypes.string),
   /**
    * Place holder text for input box. Default value Select Date
 string
    */
   inputPlaceholder: PropTypes.string,
   /**
    *Only applicable when @property isMobile value false.
 To wrap the component with overlay. Default value false
 
 boolean
    */
   isOverlay: PropTypes.bool,
};
export { Calendar_V1 };
