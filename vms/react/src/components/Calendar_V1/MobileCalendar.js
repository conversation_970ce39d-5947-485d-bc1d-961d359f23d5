import React, { PureComponent } from "react";
import PropTypes from "prop-types";
import { CalendarCloseButton } from "./Components/CalendarContainer";
import { DateInput } from "./Components/DateInput";
import {
  addMonth,
  addYear,
  calcFocusDate,
  formatWithLocale,
  getMonthDisplayRange,
  subMonth,
  subYear,
} from "../../hooks/calendarHooks";
import { Button } from "../Button/Button";
import "./style.css";
import Month from "./Components/Month";
import ReactList from "react-list";
import {
  addDays,
  addMonths,
  addYears,
  differenceInCalendarDays,
  differenceInCalendarMonths,
  eachDayOfInterval,
  endOfMonth,
  endOfWeek,
  format,
  startOfMonth,
  startOfWeek,
} from "date-fns";
import styled from "styled-components";

const StyledInfiniteMonths = styled.div`
  height: calc(100vh - 335px);
  margin-top: -2px;
  overflow: auto;

  &::-webkit-scrollbar {
    width: 0;
    background: transparent;
  }
  &::-webkit-scrollbar-thumb {
    background: transparent;
  }
`;
const formatMonthYear = "LLL yyyy";
let result = {};

class MobileCalendar extends PureComponent {
  constructor(props, context) {
    super(props, context);
    this.dateOptions = { locale: "en-US" };
    this.listSizeCache = {};
    this.isFirstRender = true;
    this.state = {
      // focusedDate: calcFocusDate(null, props),
      scrollArea: this.calcScrollArea(props),
      editting: props.openCalendar,
      selectedDate: null,
      showPlaceholder: props.selectedDate ? false : true,
      isDateChange: false,
      newSelectedDate: null,
    };
  }
 
  componentDidMount() {
    console.log("focusedDate", this.state.selectedDate);
    if (this.props.isMobile) {
      this.setState(
        {
          selectedDate: calcFocusDate(null, this.props),
          newSelectedDate:this.props.selectedDate ? this.props.selectedDate  : null
        },
        () => {
          this.focusToDate(this.state.selectedDate);
        }
      );
      // prevent react-list's initial render focus problem
    }
  }

  calcScrollArea = (props) => {
    const direction = "vertical";
    const months = 1;
    const scroll = {
      enabled: true,
    };
    if (!scroll.enabled) return { enabled: false };

    const longMonthHeight = scroll.longMonthHeight || scroll.monthHeight;
    if (direction === "vertical") {
      return {
        enabled: true,
        monthHeight: scroll.monthHeight || 220,
        longMonthHeight: longMonthHeight || 260,
        calendarWidth: "auto",
        calendarHeight:
          (scroll.calendarHeight || longMonthHeight || 240) * months,
      };
    }
    return {
      enabled: true,
      monthWidth: scroll.monthWidth || 332,
      calendarWidth:
        (scroll.calendarWidth || scroll.monthWidth || 332) * months,
      monthHeight: longMonthHeight || 300,
      calendarHeight: longMonthHeight || 300,
    };
  };

  estimateMonthSize = (index, cache) => {
    const { direction, minDate } = this.props;
    const { scrollArea } = this.state;
    if (cache) {
      if (cache[index]) return cache[index];
    }
    if (direction === "horizontal") return scrollArea.monthWidth;
    const monthStep = addMonths(minDate, index);
    const { start, end } = getMonthDisplayRange(monthStep, "en-us");
    const isLongMonth =
      differenceInCalendarDays(end, start, "en-us") + 1 > 7 * 5;
    return isLongMonth ? scrollArea.longMonthHeight : scrollArea.monthHeight;
  };

  focusToDate = (date, props = this.props) => {
    console.log("date", date, props.minDate);
    const targetMonthIndex = differenceInCalendarMonths(
      date,
      props.minDate,
      this.dateOptions
    );
    console.log("targetMonthIndex", targetMonthIndex, date);
    const visibleMonths = this.list.getVisibleRange();
    if (visibleMonths.includes(targetMonthIndex)) return;
    this.list.scrollTo(targetMonthIndex);
    this.setState({ selectedDate: date });
  };


  clearDate = () => {
    this.setState({
      selectedDate: new Date(),
      editting: false,
      showPlaceholder: true,
    });
  };

  onDateClick = (day, showSubView, view) => {
    console.log("showSubView", day, showSubView);
    const { onDateSelected } = this.props;
    const { showPlaceholder } = this.state;
    this.setState({
      selectedDate: day,
      newSelectedDate: day,
      isDateChange: true,
    });
    if (showPlaceholder) {
      this.setState({
        showPlaceholder: false,
      });
    }
    // this.onSave(showSubView);
    result = {
      Date: day,
      format1: formatWithLocale(day, "yyyy-MM-dd'T'HH:mm:ss'Z'"),
      format2: formatWithLocale(day, "dd-MM-yyyy"),
    };
    if (view !== "mobile") {
      onDateSelected(result);
    }
  };

  onSave = (showView) => {
    if (showView) {
      this.setState({
        editting: !editting,
      });
    }
  };

  // onYearViewChange = () => {
  //   setYearView(!showYearView);
  // };
  // onShowSubView = () => {
  //   setSubView(!showSubView);
  // };

  onDoneClick = () => {
    const { onDateSelected } = this.props;
    const {editting } = this.state;
    console.log("sdjksd", result);
    onDateSelected(result);
    this.setState({
      editting: !editting,
    });
  };

  renderWeekdays = () => {
    const now = new Date();
    return (
      <div className="mobileRdrWeekDays vms_Calendar_weekdaysContainer">
        {eachDayOfInterval({
          start: startOfWeek(now, "en-US"),
          end: endOfWeek(now, "en-US"),
        }).map((day, i) => (
          <span className="mobileRdrWeekDay vms_Calendar_weekdaysText" key={i}>
            {format(day, "eeeee", "en-US")}
          </span>
        ))}
        {/* <h1>sdsjdjsdh</h1> */}
      </div>
    );
  };

  render() {
    const {
      inputBoxLabel,
      withLabel,
      minDate,
      maxDate,
      view,
      inputPlaceholder,
      arrowImages,
      dateDisplayFormat,
      isOverlay,
      calendarType,
      isMobile,
    } = this.props;
    const {
      selectedDate,
      showPlaceholder,
      editting,
      isDateChange,
      scrollArea,
      newSelectedDate,
    } = this.state;
   
    console.log(differenceInCalendarDays(
      endOfMonth(maxDate),
      addDays(startOfMonth(minDate), -1),
      this.dateOptions
    ),"maxDate")
    return (
      <>
        <div className="calendar-container mobile vms_Calendar_CalendarContainer">
          <DateInput
            selectedDate={selectedDate}
            formatDateInput={dateDisplayFormat}
            withLabel={withLabel}
            labelMessage={inputBoxLabel}
            inputPlaceholder={inputPlaceholder}
            showPlaceholder={showPlaceholder}
            onClearIconClick={() => this.clearDate()}
            isMobile={true}
            editting={editting}
            calendarType={calendarType}
            isDateChange={isDateChange}
          />
          {editting && scrollArea && (
            <div id="calendarPicker">
              <div className="rdrCalendarWrapper vms_Calendar_rdrCalendarWrapper">
                <div>
                  {this.renderWeekdays()}

                  <StyledInfiniteMonths
                    style={{
                      height: `calc(((((100vh - 62px) - 10px) - 68px) - 5px) - 109px)`,
                    }}
                  >
                    <ReactList
                      length={differenceInCalendarMonths(
                        endOfMonth(maxDate),
                        addDays(startOfMonth(minDate), -1),
                        this.dateOptions
                      )}
                      treshold={500}
                      type="variable"
                      axis={"y"}
                      ref={(target) => (this.list = target)}
                      itemSizeEstimator={this.estimateMonthSize}
                      // itemSizeEstimator={estimateMonthSize}
                      itemRenderer={(index, key) => {
                        const monthStep = addMonths(minDate, index);
                        return (
                          <Month
                            key={index}
                            month={monthStep}
                            selectedDate={selectedDate}
                            fixedHeight={false}
                            onDayClick={(value) => {
                              this.onDateClick(
                                value,
                                isMobile ? false : true,
                                "mobile"
                              );
                            }}
                            showMonthName
                            showWeekDays={false}
                            isDateChange={isDateChange}
                            isMobile={isMobile}
                            newSelectedDate={newSelectedDate}
                            Monthstyle={{
                              height: this.estimateMonthSize(index),
                            }}
                          />
                        );
                      }}
                    />
                  </StyledInfiniteMonths>
                </div>
              </div>
            </div>
          )}

          {isMobile && editting && (
            <CalendarCloseButton
              className={`vms_Calendar_CloseButtonContainer`}
            >
              <Button
                buttonType={this.props.closeButtonType}
                additionalStyle={{
                  maxWidth: "100%",
                  margin: "10px 0px 10px 0",
                }}
                onClick={(e) => this.onDoneClick()}
              >
                {this.props.closeButtonText}
              </Button>
            </CalendarCloseButton>
          )}
        </div>
      </>
    );
  }
}

MobileCalendar.defaultProps = {
  withLabel: true,
  maxDate: addYears(new Date(), 1),
  minDate: new Date(),
  onDateSelected: () => {},
  view: "day",
  openCalendar: false,
  className: "",
  isMobile: false,
  closeButtonType: "primary",
  closeButtonText: "Done",
  dateDisplayFormat: "dd MMM yyyy",
  additionalClassName: null,
  additionalStyle: null,
  inputBoxLabel: "DATE",
  inputPlaceholder: "Select Date",
  isOverlay: false,
  calendarType: "primary",
};

MobileCalendar.propTypes = {
  /**
    
  * ID for the input
   */
  id: PropTypes.string,

  /**
   * Variations of Calendar Type
   */
  calendarType: PropTypes.oneOf(["primary", "secondary"]),

  /**
   * Selected Date that application want to be selected default
Date
   */
  selectedDate: PropTypes.instanceOf(Date),

  /**
   * Whether show up the label of the Date input
   */
  withLabel: PropTypes.bool,

  /**
   * To display label above input box. Default value DATE
string
   */

  inputBoxLabel: PropTypes.string,

  /**
   * 	
Defines minimum date. Disabled earlier dates.
When isMobile is false, it will take current Date - 100 years as default value.
When isMobile is true, it will take current Date as default value.

Date
   */
  minDate: PropTypes.instanceOf(Date),

  /**
   * Defines maximum date. Disabled later dates.
When isMobile is false, it will take current Date + 100 years as default value.
When isMobile is true, it will take current Date + 1 year as default value.

Date
   */
  maxDate: PropTypes.oneOfType([PropTypes.instanceOf(Date)]),

  /**
   * Only applicable when @property isMobile is false.
It will replace up and down arrow with given values.
you can pass direct web URL or local image path

{ upArrow: string; downArrow: string; disabledUpArrow: string; disabledDownArrow: string; }
   */
  arrowImages: PropTypes.shape({
    upArrow: PropTypes.string,
    downArrow: PropTypes.string,
    disabledUpArrow: PropTypes.string,
    disabledDownArrow: PropTypes.string,
  }),
  /**
   * To enable mobile view. Default value false
boolean
   */
  isMobile: PropTypes.bool,

  /**
   * If @property isMobile is true then this function will be called when user click on done button
If @property isMobile is false then this function will be called when user click on any date
it will return {
   date: Date | undefined,
   format1 (ISO format): string,
   format2 (DD-MM-YYYY): string,
   };
}

(result: CalendarOutput) => void
   */
  onDateSelected: PropTypes.func,
  /**
   * Only applicable when @property isMobile value true.
Close button text. it will take Done as default value.

string
   */
  closeButtonText: PropTypes.string,
  /**
   * Only applicable when @property isMobile value true.
Close button type. it will take primary as default value.

"primary" | "link" | "secondary" | "tertiary" | "ghost"
   */
  closeButtonType: PropTypes.oneOf([
    "primary",
    "secondary",
    "link",
    "tertiary",
  ]),
  /**
   * Displayed Date format . Default value dd MMM yyyy
Please refer https://date-fns.org/v2.12.0/docs/format for various allowed format

string
   */
  dateDisplayFormat: PropTypes.string,

  /**
   * Only applicable when @property isMobile value false.
If you want to open calendar pass true

boolean
   */
  openCalendar: PropTypes.bool,
  /**
   *Inline styles to add additional styling in the parent container
CSSProperties
   */
  additionalStyle: PropTypes.object,
  /**
   * Classes to add additional styling in the parent container
string[]
   */

  additionalClassName: PropTypes.arrayOf(PropTypes.string),
  /**
   * Place holder text for input box. Default value Select Date
string
   */
  inputPlaceholder: PropTypes.string,
  /**
   *Only applicable when @property isMobile value false.
To wrap the component with overlay. Default value false

boolean
   */
  isOverlay: PropTypes.bool,
};
export { MobileCalendar };
