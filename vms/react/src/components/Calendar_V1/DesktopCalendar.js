import React, { useState, useEffect, useRef } from "react";
import PropTypes from "prop-types";
import {
  OverlayDiv,
  OverlayContainer,
  CalendarFlyOut,
} from "./Components/CalendarContainer";
import { DateInput } from "./Components/DateInput";
import { Header } from "./Components/Header";
import {
  addMonth,
  addYear,
  formatWithLocale,
  subMonth,
  subYear,
} from "../../hooks/calendarHooks";
import "./style.css";
import Month from "./Components/Month";
import { addMonths, addYears } from "date-fns";
import { SubView } from "./Components/SubView";
import { useClassName } from "../../hooks/useClassName";

const formatMonthYear = "LLL yyyy";
let result = {};
const isNonNull = (prop) => {
  return prop !== null && prop !== undefined;
};
const DesktopCalendar = (props) => {
  const className = useClassName(props, props.additionalClassName);
  const [editting, setEditting] = useState(props.openCalendar);
  const [selectedDate, setSelectedDate] = useState(
    props.selectedDate ? props.selectedDate : new Date()
  );
  const [showPlaceholder, setShowPlaceholder] = useState(
    props.selectedDate ? false : true
  );
  const [showYearView, setYearView] = useState(false);
  const [showSubView, setSubView] = useState(false);
  const [isDateChange, setIsDateChange] = useState(false);
  const [newSelectedDate, setNewSelectedDate] = useState();

 
  const nextMonth = () => {
    setSelectedDate(addMonth(selectedDate, 1));
  };

  const prevMonth = () => {
    setSelectedDate(subMonth(selectedDate, 1));
  };

  const nextYear = () => {
    setSelectedDate(addYear(selectedDate, 1));
  };
  const prevYear = () => {
    setSelectedDate(subYear(selectedDate, 1));
  };

  const clearDate = () => {
    setSelectedDate(new Date());
    setEditting(false);
    setShowPlaceholder(true);
  };

  const onDateClick = (day, showSubView, view) => {
    const { onDateSelected } = props;
    setSelectedDate(day);
    setNewSelectedDate(day);
    if (showPlaceholder) {
      setShowPlaceholder(false);
    }
    onSave(showSubView);
    result = {
      Date: day,
      format1: formatWithLocale(day, "yyyy-MM-dd'T'HH:mm:ss'Z'"),
      format2: formatWithLocale(day, "dd-MM-yyyy"),
    };
    if (view !== "mobile") {
      onDateSelected(result);
    }
    setIsDateChange(true);
  };

  const onSave = (showView) => {
    if (showView) {
      setEditting(!editting);
    }
  };

  const onYearViewChange = () => {
    setYearView(!showYearView);
  };
  const onShowSubView = () => {
    setSubView(!showSubView);
  };

  useEffect(() => {
    if (props.selectedDate) {
      setNewSelectedDate(selectedDate);
    }
  }, []);

  const {
    inputBoxLabel,
    withLabel,
    minDate,
    maxDate,
    view,
    inputPlaceholder,
    arrowImages,
    dateDisplayFormat,
    isOverlay,
    calendarType,
    isMobile,
    onBlur,
    onInputClick,
    customInputView
  } = props;
  return (
    <>
      <>
        <OverlayContainer className={`vms_Calendar_OverlayContainer`} isOverlay={isOverlay} isMobile={isMobile}>
          <div className="calendar-container vms_Calendar_CalendarContainer">
            {isNonNull(customInputView)? 
            <>{customInputView}</>
            :(
            <DateInput
              selectedDate={selectedDate}
              formatDateInput={dateDisplayFormat}
              withLabel={withLabel}
              labelMessage={inputBoxLabel}
              inputPlaceholder={inputPlaceholder}
              showPlaceholder={showPlaceholder}
              onButtonClick={(e) => {
                setEditting(!editting)
                if(isNonNull(onInputClick)){
                  onInputClick(!editting)
                }
              }}
              onClearIconClick={() => clearDate()}
              isMobile={isMobile}
              editting={editting}
              calendarType={calendarType}
              isDateChange={isDateChange}
              onBlur={(e) => {
                if(isNonNull(onBlur)){
                  onBlur(e)
                }  }}
            />
            )
          }
            {editting && (
              <CalendarFlyOut
                className={`${className} vms_Calendar_CalendarFlyout`}
                style={props.additionalStyle}
                isMobile={isMobile}
                type={calendarType}
              >
                <div className="rdrCalendarWrapper vms_Calendar_rdrCalendarWrapper">
                  <Header
                    selectedDate={selectedDate}
                    view={view}
                    formatMonthYear={formatMonthYear}
                    prev={() => prevMonth()}
                    next={() => nextMonth()}
                    arrowImages={arrowImages}
                    showSubView={() => onShowSubView()}
                  />
                  <div className={"rdrMonths rdrMonthsVertical vms_Calendar_monthContainer"}>
                    {new Array(1).fill(null).map((_, i) => {
                      let monthStep = addMonths(selectedDate, i);
                      return (
                        <Month
                          key={i}
                          month={monthStep}
                          showWeekDays
                          selectedDate={selectedDate}
                          fixedHeight={false}
                          onDayClick={(value) => {
                            onDateClick(value, true, "day");
                          }}
                          isDateChange={isDateChange}
                          newSelectedDate={newSelectedDate}
                        />
                      );
                    })}
                  </div>
                </div>
                {showSubView && (
                  <div className="month-navigation-container vms_Calendar_subViewContainer">
                    <Header
                      selectedDate={selectedDate}
                      view={"year"}
                      prevYear={() => prevYear()}
                      nextYear={() => nextYear()}
                      formatMonthYear={formatMonthYear}
                      prev={() => prevMonth()}
                      next={() => nextMonth()}
                      changeView={() => onYearViewChange()}
                      arrowImages={arrowImages}
                    />
                    <div className="row-container vms_Calendar_row-container">
                      <SubView
                        minDate={minDate}
                        maxDate={maxDate}
                        selectedDate={selectedDate}
                        onItemClick={(value, showSubView) =>
                          onDateClick(value, showSubView)
                        }
                        view={showYearView ? "year" : "month"}
                      />
                    </div>
                  </div>
                )}
              </CalendarFlyOut>
            )}
          </div>
        </OverlayContainer>
        {isOverlay && !isMobile && (
          <OverlayDiv className="vms_Calendar_OverlayDiv" editting={editting} />
        )}
      </>
    </>
  );
};

DesktopCalendar.defaultProps = {
  withLabel: true,
  maxDate: addYears(new Date(), 20),
  minDate: addYears(new Date(), -100),
  onDateSelected: () => {},
  view: "day",
  openCalendar: false,
  className: "",
  isMobile: false,
  closeButtonType: "primary",
  closeButtonText: "Done",
  dateDisplayFormat: "dd MMM yyyy",
  additionalClassName: null,
  additionalStyle: null,
  inputBoxLabel: "DATE",
  inputPlaceholder: "Select Date",
  isOverlay: false,
  calendarType: "primary",
};

DesktopCalendar.propTypes = {
  /**
    
  * ID for the input
   */
  id: PropTypes.string,

  /**
   * Variations of Calendar Type
   */
  calendarType: PropTypes.oneOf(["primary", "secondary"]),

  /**
   * Selected Date that application want to be selected default
Date
   */
  selectedDate: PropTypes.instanceOf(Date),

  /**
   * Whether show up the label of the Date input
   */
  withLabel: PropTypes.bool,

  /**
   * To display label above input box. Default value DATE
string
   */

  inputBoxLabel: PropTypes.string,

  /**
   * 	
Defines minimum date. Disabled earlier dates.
When isMobile is false, it will take current Date - 100 years as default value.
When isMobile is true, it will take current Date as default value.

Date
   */
  minDate: PropTypes.instanceOf(Date),

  /**
   * Defines maximum date. Disabled later dates.
When isMobile is false, it will take current Date + 100 years as default value.
When isMobile is true, it will take current Date + 1 year as default value.

Date
   */
  maxDate: PropTypes.oneOfType([PropTypes.instanceOf(Date)]),

  /**
   * Only applicable when @property isMobile is false.
It will replace up and down arrow with given values.
you can pass direct web URL or local image path

{ upArrow: string; downArrow: string; disabledUpArrow: string; disabledDownArrow: string; }
   */
  arrowImages: PropTypes.shape({
    upArrow: PropTypes.string,
    downArrow: PropTypes.string,
    disabledUpArrow: PropTypes.string,
    disabledDownArrow: PropTypes.string,
  }),
  /**
   * To enable mobile view. Default value false
boolean
   */
  isMobile: PropTypes.bool,

  /**
   * If @property isMobile is true then this function will be called when user click on done button
If @property isMobile is false then this function will be called when user click on any date
it will return {
   date: Date | undefined,
   format1 (ISO format): string,
   format2 (DD-MM-YYYY): string,
   };
}

(result: CalendarOutput) => void
   */
  onDateSelected: PropTypes.func,
  /**
   * Only applicable when @property isMobile value true.
Close button text. it will take Done as default value.

string
   */
  closeButtonText: PropTypes.string,
  /**
   * Only applicable when @property isMobile value true.
Close button type. it will take primary as default value.

"primary" | "link" | "secondary" | "tertiary" | "ghost"
   */
  closeButtonType: PropTypes.oneOf([
    "primary",
    "secondary",
    "link",
    "tertiary",
  ]),
  /**
   * Displayed Date format . Default value dd MMM yyyy
Please refer https://date-fns.org/v2.12.0/docs/format for various allowed format

string
   */
  dateDisplayFormat: PropTypes.string,

  /**
   * Only applicable when @property isMobile value false.
If you want to open calendar pass true

boolean
   */
  openCalendar: PropTypes.bool,
  /**
   *Inline styles to add additional styling in the parent container
CSSProperties
   */
  additionalStyle: PropTypes.object,
  /**
   * Classes to add additional styling in the parent container
string[]
   */

  additionalClassName: PropTypes.arrayOf(PropTypes.string),
  /**
   * Place holder text for input box. Default value Select Date
string
   */
  inputPlaceholder: PropTypes.string,
  /**
   *Only applicable when @property isMobile value false.
To wrap the component with overlay. Default value false

boolean
   */
  isOverlay: PropTypes.bool,
};
export { DesktopCalendar };
