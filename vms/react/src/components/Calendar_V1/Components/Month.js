import React, { PureComponent } from "react";
import PropTypes from "prop-types";
import DayCell from "./DayCell";
import {
  format,
  startOfDay,
  endOfDay,
  startOfWeek,
  endOfWeek,
  isWeekend,
  isWithinInterval,
  eachDayOfInterval,
} from "date-fns";
import {
  getMonthDisplayRange,
  whetherAfter,
  whetherBefore,
  whetherDisabled,
  whetherSameDay,
} from "../../../hooks/calendarHooks";
import "../style.css";
import styled from "styled-components";

const WeekDayContainer = styled.div`
  width: 100%;
  margin-top: 18px;
  margin-bottom: 16px;
  padding-left: 10px !important;
  padding-right: 10px !important;
  box-sizing: border-box;
  padding: 0px;
  display: flex;
`;

const WeekDayLabel = styled.span`
  font-size: 12px;
  line-height: 1.4;
  letter-spacing: normal;
  text-align: center;
  color: #4d4d4f;
  font-weight: 400;
  font-family: Montserrat;
  flex-basis: calc(100% / 7);
  box-sizing: inherit;
  text-align: center;
  font-weight: 400;
  line-height: 2.667em;
`;

function renderWeekdays(dateOptions, weekdayDisplayFormat) {
  const now = new Date();

  return (
    <WeekDayContainer className="vms_Calendar_weekdaysContainer">
      {eachDayOfInterval({
        start: startOfWeek(now, dateOptions),
        end: endOfWeek(now, dateOptions),
      }).map((day, i) => (
        <WeekDayLabel className="vms_Calendar_weekdaysText" key={i}>{format(day, weekdayDisplayFormat)}</WeekDayLabel>
      ))}
    </WeekDayContainer>
  );
}

class Month extends PureComponent {
  render() {
    const now = new Date();
    const {
      selectedDate,
      newSelectedDate,
      isDateChange,
      showWeekDays,
      Monthstyle,
      isMobile,
      onDayClick = () => {},
    } = this.props;
    this.dateOptions = { locale: "en-US" };
    const monthDisplay = getMonthDisplayRange(
      this.props.month,
      this.dateOptions,
      this.props.fixedHeight
    );
    return (
      <div
        className={`${isMobile ? "rdMonthMobile vms_Calendar_monthMobileContainer" : "rdrMonth vms_Calendar_monthContainer"}`}
        style={Monthstyle}
      >
        {this.props.showMonthName ? (
          <div className={"rdrMonthMobileName vms_Calendar_monthNameText"}>
            {format(this.props.month, "MMM yyyy", this.props.dateOptions)}
          </div>
        ) : null}
        {showWeekDays && renderWeekdays(this.dateOptions, "eeeee")}
        <div className={`${isMobile ? "rdrDays  vms_Calendar_rdrMobileDaysContainer" : "rdrDesktopDays vms_Calendar_rdrDesktopDaysContainer"}`}>
          {eachDayOfInterval({
            start: monthDisplay.start,
            end: monthDisplay.end,
          }).map((day,i) => {
            const isStartOfMonth = whetherSameDay(
              day,
              monthDisplay.startDateOfMonth
            );
            const isEndOfMonth = whetherSameDay(
              day,
              monthDisplay.endDateOfMonth
            );
            const isDisabledDay = whetherDisabled(
              day,
              monthDisplay.startDateOfMonth,
              this.props.minDate,
              this.props.maxDate
            );
            return (
              <div key={i}>
                <DayCell
                day={day}
                isWeekend={isWeekend(day, this.dateOptions)}
                isToday={whetherSameDay(day, now)}
                isStartOfWeek={whetherSameDay(
                  day,
                  startOfWeek(day, this.props.dateOptions)
                )}
                isEndOfWeek={whetherSameDay(
                  day,
                  endOfWeek(day, this.props.dateOptions)
                )}
                disabled={isDisabledDay}
                selectedDate={selectedDate}
                isStartOfMonth={isStartOfMonth}
                isEndOfMonth={isEndOfMonth}
                isPassive={
                  !isWithinInterval(day, {
                    start: monthDisplay.startDateOfMonth,
                    end: monthDisplay.endDateOfMonth,
                  })
                }
                isMobile={isMobile}
                newSelectedDate={newSelectedDate}
                isDateChange={isDateChange}
                onDayClick={(val) => {
                  onDayClick(val);
                }}
              />
              </div>
            );
          })}
        </div>
      </div>
    );
  }
}

export default Month;
