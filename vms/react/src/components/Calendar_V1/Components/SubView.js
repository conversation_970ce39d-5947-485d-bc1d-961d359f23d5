import React from "react";
import styled from "styled-components";
import {
  addMonth,
  formatWith<PERSON><PERSON>ale,
  getEndOfYear,
  getStartOfYear,
  getYears,
  getYearsPeriod,
  setYears,
  whetherSameMonth,
} from "../../../hooks/calendarHooks";
import "../style.css";

const YearTableContainer = styled.div`
  display: flex;
  flex-wrap: wrap;
  margin: 20px 0px 0px 10px;
`;

const YearText = styled.button`
  border: none;
  outline: none;
  background: ${(props) => (props.selected ? "#F1F6DE" : "transparent")};
  font-weight: ${(props) => (props.selected ? "600" : "400")};
  font-size: ${({ theme }) => `${theme?.typography?.text?.fontSize}` || ""};
  font-family: ${({ theme }) => theme?.typography?.fontFamily || ""};
  border-radius: 3px;
  text-align: center;
  height: auto;
  padding: 8px;
  color: #4d4d4f;
  line-height: 20px;
  letter-spacing: 0.3px;
  text-align: center;
  flex-basis: 25%;
`;

const MonthButton = styled.button`
  border: none;
  outline: none;
  background: ${(props) => (props.selected ? "#f1f6de" : "transparent")};
  font-size: ${({ theme }) => `${theme?.typography?.text?.fontSize}` || ""};
  font-family: ${({ theme }) => theme?.typography?.fontFamily || ""};
  font-weight: ${(props) => (props.selected ? "600" : "")};
  border-radius: ${(props) => (props.selected ? "3px" : "")};
  color: #4d4d4f;
  font-size: 14px;
  line-height: 20px;
  letter-spacing: 0.3px;
  text-align: center;
  width: 100%;
  height: auto;
  padding: 8px;
`;

const SubView = ({ selectedDate, view, onItemClick }) => {
  let cloneDate;
  let formattedDate;
  let itemPerCol;
  const onDateClickHandler = (e) => {
    const setYear = setYears(selectedDate, e);
    onItemClick(setYear, false);
  };

  switch (view) {
    case "year": {
      let yearsList = [];
      let selected = getYears(selectedDate);
      const { startPeriod, endPeriod } = getYearsPeriod(selectedDate, 16);
      for (let y = startPeriod; y <= endPeriod; y++) {
        yearsList.push(y);
      }
      return (
        <>
          <YearTableContainer className="vms_Calendar_YearContainer">
            {/* <Row> */}
            {yearsList.map((item,ind) => {
              return (
                <span className="button-container vms_Calendar_Year_button-container" key={ind}>
                  <YearText
                    className="vms_Calendar_YearText"
                    selected={selected === item ? true : false}
                    onClick={() => onDateClickHandler(item)}
                  >
                    {item}
                  </YearText>
                </span>
              );
            })}
            {/* </Row> */}
          </YearTableContainer>
        </>
      );
    }
    case "month": {
      const startOfYear = getStartOfYear(selectedDate);
      const endOfYear = getEndOfYear(selectedDate);
      let col = [];
      cloneDate = startOfYear;
      itemPerCol = 4;
      console.log("selectedDate",selectedDate)

      while (cloneDate < endOfYear) {
        for (let i = 0; i < itemPerCol && cloneDate < endOfYear; i += 1) {
          formattedDate = formatWithLocale(cloneDate, "LLL");
          let isSelected = whetherSameMonth(cloneDate,selectedDate);
          console.log("isSelected",isSelected,cloneDate,selectedDate)
          col.push({
            date:cloneDate,
            isSelected:isSelected
          });
          cloneDate = addMonth(cloneDate, 1);
        }
      }



      return (
        <>
          <div className="row vms_Calendar_dateTableContainer_row">
            {col.slice(0, 4).map((e,inde) => {
              return (
                <span
                  key={inde}
                  className="button-container vms_Calendar_month_button-container"
                  onClick={() => onItemClick(e.date, false)}
                >
                  <MonthButton className="vms_Calendar_MonthText" selected={e.isSelected}>{formatWithLocale(e.date, "LLL")}</MonthButton>
                </span>
              );
            })}
          </div>

          <div className="row vms_Calendar_dateTableContainer_row">
            {col.slice(5, 9).map((e,ind) => {
              return (
                <span
                  key={ind}
                  className="button-container vms_Calendar_month_button-container"
                  onClick={() => onItemClick(e.date, false)}
                >
                  <MonthButton className="vms_Calendar_MonthText" selected={e.isSelected}>{formatWithLocale(e.date, "LLL")}</MonthButton>
                </span>
              );
            })}
          </div>

          <div className="row vms_Calendar_dateTableContainer_row">
            {col.slice(9, 12).map((e,i) => {
              return (
                <span
                  key={i}
                  className="button-container vms_Calendar_month_button-container"
                  onClick={() => onItemClick(e.date, false)}
                >
                  <MonthButton className="vms_Calendar_MonthText" selected={e.isSelected}>{formatWithLocale(e.date, "LLL")}</MonthButton>
                </span>
              );
            })}
          </div>
        </>
      );
    }
    default: {
      return undefined;
    }
  }
};

export { SubView };
