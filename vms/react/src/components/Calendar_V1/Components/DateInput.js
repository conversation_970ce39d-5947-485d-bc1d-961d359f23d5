import React from "react";
import styled from "styled-components";
import { formatWithLocale } from "../../../hooks/calendarHooks";
import CrossIconSvg from "../../../assets/images/calendar/cross.svg";

function border(isMobile, editting, theme, type) {
  if (isMobile) {
    return "none";
  } else {
    return editting
      ? `solid 1px ${theme?.palette?.[type]?.main}`
      : "solid 1px #4d4d4f";
  }
}

function borderBottom(isMobile, theme, type) {
  if (isMobile) {
    return `solid 1px ${theme?.palette?.[type]?.main}`;
  } else {
    return "";
  }
}

const DateInputContainer = styled.div`
  position: ${(props) => (props.isMobile ? "relative" : "")};
  /* min-width: ${(props) => (props.isMobile ? "100%" : "320px")}; */
  width: ${(props) => (props.isMobile ? "100%" : "290px")};
  /* --widthA: calc(100% - 28px);
  --widthB: calc(var(--widthA) / 7); */
  max-width: ${(props) => (props.isMobile ? "" : "320px")};
`;

const Label = styled.label`
  font-weight: 400;
  font-family: ${({ theme }) => theme?.typography?.fontFamily || ""};
  font-size: ${({ theme }) => `${theme?.typography?.text?.fontSize}` || ""};
  font-stretch: normal;
  font-style: normal;
  line-height: 20px;
  letter-spacing: 0.3px;
  color: #4d4d4f;
  display: block;
  position: relative;
  margin: ${(props) => (props.isMobile ? "12px 0" : "")};
  text-transform: uppercase;
`;



const InputContainer = styled.input`
  display: block;
  box-sizing: border-box;
  font-weight: 700;
  font-family: ${({ theme }) => theme?.typography?.fontFamily || ""};
  font-size: ${({ theme }) => `${theme?.typography?.text?.fontSize}` || ""};
  font-stretch: normal;
  font-style: normal;
  line-height: ${({ isMobile }) => (isMobile ? "22px" : 1.43)};

  width: 100%;
  border-radius: 3px;
  letter-spacing: 0.3px;
  color: #4d4d4f;
  padding: ${({ isMobile }) => (!isMobile ? "14px 16px" : "")};
  margin-top: 12px;
  text-overflow: ellipsis;
  height: ${({ isMobile }) => (isMobile ? "auto" : "48px")};
  position: relative;
  border: ${({ theme, isMobile, calendarType, editting }) =>
    border(isMobile, editting ? true : false, theme, calendarType)};
  background-color: #fff;
  border-bottom: ${({ theme, isMobile, calendarType }) =>
    borderBottom(isMobile, theme, calendarType)};

  &::placeholder {
    font-weight: 400;
    font-family: ${({ theme }) => theme?.typography?.fontFamily || ""};
    font-size: 16px;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.43;
    border-radius: 3px;
    letter-spacing: 0.3px;
    color: #d2d3d4;
  }

  &:focus {
    outline: none;
    height: 48px;
    border-radius: 3px;
    border: ${({ theme, isMobile, calendarType }) =>
      border(isMobile, true, theme, calendarType)};
    border-bottom: ${({ theme, isMobile, calendarType }) =>
      borderBottom(isMobile, theme, calendarType)};
    background-color: #fff;
    border-bottom: 1px solid #03868b;
    color: #4d4d4f;
    outline: none;
  }
`;

const IconContainer = styled.div`
  display: inline-block;
  background-size: 20px 18px !important;
  background-repeat: no-repeat !important;
  background-position: center !important;
  width: 24px;
  height: 24px;
  text-decoration: none;
  outline: none;
  position: absolute;
  bottom: ${({ isMobile }) => (isMobile ? "0px" : "")};
  top: ${({ isMobile }) => (isMobile ? "" : "45px")};
  right: 12px;
`;

const DateInput = (props) => {
  const {
    selectedDate,
    onButtonClick,
    editting,
    formatDateInput,
    withLabel,
    labelMessage,
    inputPlaceholder,
    showPlaceholder,
    onClearIconClick,
    calendarType,
    onBlur
  } = props;
  const formattedDate = selectedDate
    ? formatWithLocale(selectedDate, formatDateInput)
    : "";

  return (
    <DateInputContainer
      className={`vms_Calendar_InputContainer`}
      isMobile={props.isMobile}
    >
      {withLabel ? (
        <Label className={`vms_Calendar_Input_lable`} isMobile={props.isMobile}>
          {labelMessage}
        </Label>
      ) : (
        ""
      )}
      <div style={{ position: "relative" }}>
        <InputContainer
          type="date-text"
          editting={editting}
          value={!showPlaceholder && formattedDate ? formattedDate : ""}
          placeholder={inputPlaceholder ? inputPlaceholder : "Select Date"}
          onClick={(e) => onButtonClick(e)}
          readOnly
          isMobile={props.isMobile}
          className={`vms_DateRangeCalendar_Input`}
          calendarType={calendarType}
          onBlur={(e)=>onBlur(e)}
        />
      </div>
      {!showPlaceholder && formattedDate && (
        <IconContainer
          className={`vms_DateRangeCalendar_IconCotainer`}
          onClick={() => onClearIconClick()}
          isMobile={props.isMobile}
        >
          <img src={CrossIconSvg} alt="plus" />
        </IconContainer>
      )}
    </DateInputContainer>
  );
};

export { DateInput };
