import React, { Component } from "react";
import PropTypes from "prop-types";
import styled from "styled-components";
import { format } from "date-fns";
import "../style.css";
import { whetherSameDay, whetherSameMonth } from "../../../hooks/calendarHooks";

const DayButton = styled.button`
  background: transparent;
  user-select: none;
  border: 0;
  padding: 0;
  line-height: 3em;
  height: 3em;
  text-align: center;
  color: #1d2429;
  box-sizing: inherit;
  width: calc(100% / 7);
  position: relative;
  font: inherit;
  &:disabled {
    pointer-events: none;
    color: #d1d3d4 !important;
  }
  &:hover {
    background: #f1f6de;
    text-align: center;
  }
`;

const Item = styled.span`
  outline: 0;
  font-weight: 300;
  font-weight: ${({ isSelected }) => (isSelected ? "bold" : 300)};
  position: ${({ isSelected }) => (isSelected ? "relative" : "absolute")};
  left: 0;
  right: 0;
  top: ${({ isSelected }) => (isSelected ? "0px" : "5px")};
  bottom: 0;
  bottom: 5px;
  display: ${({ isPassive, isMobile }) =>
    isPassive && isMobile ? "none" : "flex"};
  align-items: center;
  justify-content: center;
  z-index: ${({ isSelected }) => (isSelected ? 4 : "")};
`;

class DayCell extends Component {
  render() {
    const {
      disabled,
      selectedDate,
      day,
      newSelectedDate,
      isMobile,
      isPassive,
      onDayClick,id
    } = this.props;
    let isSelected =
      !disabled &&
      newSelectedDate &&
      whetherSameDay(this.props.day, newSelectedDate) &&
      whetherSameMonth(day, newSelectedDate);
    return (
      <DayButton
      id={id? id+"-btn" : null}
        className={`${isMobile ? "rdrMobileDays" : ""}`}
        onClick={() => {
          onDayClick(day);
        }}
        disabled={disabled}
      >
        {isSelected ? <span className="rdrSelected vms_Calendar_selectedDay"></span> : null}
        <Item className="vms_Calendar_itemMainContainer" isSelected={isSelected} isPassive={isPassive} isMobile={isMobile}>
          {<span className="vms_Calendar_dateItem">{format(this.props.day, "d")}</span>}
        </Item>
        {/* <Item></Item> */}
      </DayButton>
    );
  }
}


export default DayCell;
