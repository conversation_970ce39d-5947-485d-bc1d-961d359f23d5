import styled from "styled-components";

export const TextWrapper = styled.div`
  display: flex;
  justify-content: flex-start;
  flex-direction: ${({ iconPosition }) =>
    iconPosition === null || iconPosition === "left" || iconPosition === "right"
      ? "row"
      : "column"};
`;
const getPosition = (iconPosition) => {
  switch (iconPosition) {
    case "left":
      return "margin-right:8px";
    case "right":
      return "margin-left:8px";
    case "top":
      return "margin-bottom:8px";
    case "bottom":
      return "margin-top:8px";
  }
};
const getTextVariant = (textVariant) => {
  switch (textVariant) {
    case "header_40":
      return `${"font-size:40px"};${"line-height:44px"};${"letter-spacing:0.3px"};`;
    case "header_32":
      return `${"font-size:32px"};${"line-height:40px"};${"letter-spacing:0.3px"};`;
    case "header_24":
      return `${"font-size:24px"};${"line-height:30px"};${"letter-spacing:0.3px"};`;
    case "header_19":
      return `${"font-size:19px"};${"line-height:24px"};${"letter-spacing:0.3px"};`;
    case "header_16":
      return `${"font-size:16px"};${"line-height:22px"};${"letter-spacing:0.3px"};`;
    case "body_14":
      return `${"font-size:14px"};${"line-height:20px"};${"letter-spacing:0.3px"};`;
    case "body_12":
      return `${"font-size:12px"};${"line-height:16px"};${"letter-spacing:0.4px"};`;
  }
};
export const IconImg = styled.img`
  width: 24px;
  height: 24px;
  justify-content: center;
  align-self: center;
  ${({ iconPosition }) => getPosition(iconPosition)};
`;
export const TextSpan = styled.span`
  text-align: ${({ textAlign }) => textAlign};
  align-self: center;
  justify-content: center;
  font-weight: ${({ fontVariant }) => fontVariant};
  ${({ textVariant }) => getTextVariant(textVariant)};
`;
