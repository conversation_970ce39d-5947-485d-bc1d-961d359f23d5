import React from "react";
import { Text } from "./Text";

export default {
  title: "VMS_REACT/Text",
  component: Text,
  argTypes: {
    additionalStyle: { control: { type: "" } },
    additionalClassName: { control: { type: "" } },
  },
};

const Template = (args) => <Text {...args}>Welcome</Text>;

export const Default = (props) => {
  const displayObj = [
    { header: "Font size 40", textVariant: "header_40" },
    { header: "Font size 32", textVariant: "header_32" },
    { header: "Font size 24", textVariant: "header_24" },
    { header: "Font size 19", textVariant: "header_19" },
    { header: "Font size 16", textVariant: "header_16" },
    { header: "Default (Font size 14)", textVariant: "body_14" },
    { header: "Font size 12", textVariant: "body_12" },
  ];
  return (
    <div style={{display:"flex",flexDirection:"row", gap:"40px"}}>
      {displayObj.map((ele, index) => (
        <div key={index}>
          <h4>{ele.header}</h4>
          <Text textVariant={ele.textVariant}>Welcome</Text>
        </div>
      ))}
    </div>
  );
};

export const WithIcon = Template.bind({});
WithIcon.args = {
  iconPath: "https://img.icons8.com/pastel-glyph/24/000000/plus.png",
};
