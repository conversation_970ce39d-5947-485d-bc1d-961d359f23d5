import React, { memo, useCallback, useMemo } from "react";
import PropTypes from "prop-types";
import { useClassName } from "../../hooks/useClassName";
import { TextWrapper, IconImg, TextSpan } from "./Text.styled";
import { ThemeWrapper } from "../Theme/ThemeContext";

// Utility function (placed before main component)
const isNonNull = (prop) => prop !== null && prop !== undefined;

export const Text = memo((props) => {
  // 1. PROP DESTRUCTURING & VALIDATION
  const {
    children,
    textAlign = "left",
    iconPath,
    iconStyle,
    iconPosition = "left",
    additionalClassName,
    additionalStyle,
    fontVariant = 'regular',
    textVariant = "body_14",
    onTextClick,
    isMobile,
    isMobileView,
    ...otherProps
  } = props;

  // 2. PERFORMANCE OPTIMIZATIONS
  const computedClassName = useMemo(() => 
    useClassName(props, additionalClassName), 
    [props, additionalClassName]
  );

  const hasIcon = useMemo(() => isNonNull(iconPath), [iconPath]);

  const isLeftOrTopIcon = useMemo(() => 
    iconPosition === "left" || iconPosition === "top", 
    [iconPosition]
  );

  const isRightOrBottomIcon = useMemo(() => 
    iconPosition === "right" || iconPosition === "bottom", 
    [iconPosition]
  );

  // 3. EVENT HANDLING with useCallback
  const handleTextClick = useCallback((event) => {
    event?.stopPropagation();
    
    if (isNonNull(onTextClick)) {
      onTextClick(event);
    }
  }, [onTextClick]);

  // 4. CONDITIONAL RENDERING (memoized)
  const leftOrTopIcon = useMemo(() => {
    if (!hasIcon || !isLeftOrTopIcon) return null;
    
    return (
      <IconImg
        src={iconPath}
        style={iconStyle}
        iconPosition={iconPosition}
        alt="Icon"
      />
    );
  }, [hasIcon, isLeftOrTopIcon, iconPath, iconStyle, iconPosition]);

  const rightOrBottomIcon = useMemo(() => {
    if (!hasIcon || !isRightOrBottomIcon) return null;
    
    return (
      <IconImg
        src={iconPath}
        style={iconStyle}
        iconPosition={iconPosition}
        alt="Icon"
      />
    );
  }, [hasIcon, isRightOrBottomIcon, iconPath, iconStyle, iconPosition]);

  // 5. ERROR HANDLING
  React.useEffect(() => {
    if (!children) {
      console.warn('Text component: children prop is required');
    }
  }, [children]);

  return (
    <ThemeWrapper isMobile={isMobile || isMobileView || false}>
      <TextWrapper
        iconPosition={iconPosition}
        className={`${computedClassName} vms_text`}
        style={additionalStyle}
        role={onTextClick ? "button" : undefined}
        tabIndex={onTextClick ? 0 : undefined}
        {...otherProps}
      >
        {leftOrTopIcon}

        <TextSpan
          textVariant={textVariant}
          onClick={onTextClick ? handleTextClick : undefined}
          textAlign={textAlign}
          fontVariant={fontVariant}
          className={computedClassName}
          style={additionalStyle}
          role={onTextClick ? "button" : undefined}
          tabIndex={onTextClick ? 0 : undefined}
          onKeyDown={onTextClick ? (e) => {
            if (e.key === 'Enter' || e.key === ' ') {
              handleTextClick(e);
            }
          } : undefined}
        >
          {children}
        </TextSpan>

        {rightOrBottomIcon}
      </TextWrapper>
    </ThemeWrapper>
  );
});

// Component display name for debugging
Text.displayName = 'Text';

Text.defaultProps = {
  textAlign: "left",
  iconPath: null,
  iconStyle: null,
  iconPosition: "left",
  fontVariant: 'regular',
  textVariant: "body_14",
  onTextClick: null,
};
Text.propTypes = {
  /**
   * Children, You can pass string or custom component
   */
  children: PropTypes.any.isRequired,
  /**
   * It will display icon with text. pass base64 or url
   */
  iconPath: PropTypes.string,
  /**
   * Inline styles to add additional styling to the icon
   */
  iconStyle: PropTypes.object,
  /**
   * Icon position. default value left
   */
  iconPosition: PropTypes.oneOf(["left", "right", "top", "bottom"]),
  /**
   * Text Alignment. Default value left
   */
  textAlign: PropTypes.oneOf(["left", "right", "center"]),
  /**
   * Font weight. default value `regular`
   */
  fontVariant: PropTypes.oneOf([
    'regular',
    'bold',
    'light',
    'thin',
    'semi-bold',
  ]),
  /**
   * `header_40` - Font size - 40px, line height - 44px and letter spacing - 0.3px
   * `header_32` - Font size - 32px, line height - 40px and letter spacing - 0.3px
   * `header_24` - Font size - 24px, line height - 30px and letter spacing - 0.3px
   * `header_19` - Font size - 19px, line height - 24px and letter spacing - 0.3px
   * `header_16` - Font size - 16px, line height - 22px and letter spacing - 0.3px
   * `body_14` - Font size - 14px, line height - 20px and letter spacing - 0.3px
   * `body_12` - Font size - 12px, line height - 16px and letter spacing - 0.4px
   */
  textVariant: PropTypes.oneOf([
    "body_14",
    "header_40",
    "header_32",
    "header_24",
    "header_19",
    "header_16",
    "body_12",
  ]),
  /**
   * Property to append additional css class
   */
  additionalClassName: PropTypes.arrayOf(PropTypes.string),
  /**
   * Property to set the `inline style` object
   */
  additionalStyle: PropTypes.object,
  /**
   * Text Click event.
   * (event) => void
   */
  onTextClick: PropTypes.func,
};
