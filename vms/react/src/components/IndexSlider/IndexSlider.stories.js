import React,{useState} from "react";
import { IndexSlider } from "./IndexSlider";

export default {
  title: "VMS_REACT/Index Slider",
  component: IndexSlider,
  parameters: {
    controls: {
      exclude: /.*/g,
    },
  },
};

const Template = (args) => {
  const [sliderValue, setSliderValue] = useState(args.disabled ? 30 : 30);
 
  return (
    <div style={{ marginTop: "60px" }}>
      <IndexSlider 
      value={sliderValue}
      disabled={false}
      trackColor="red"
      onValueSelected={(event, value) => {
        setSliderValue(value);
      }}
      {...args}
      ></IndexSlider>
    </div>
  );
};


export const Default = Template.bind({});
Default.args = {
  valueLabelDisplay:"on",
  leftLabel:"left caption",
  rightLabel:"right caption",
  disabled:false,
  step:1,
  trackColor:"red",
};


export const IndexSliderWithMinimumAndMaximumLimit = Template.bind({});
IndexSliderWithMinimumAndMaximumLimit.args = {
  valueLabelDisplay:"off",
  defaultValue:30,
  leftLabel:"left caption",
  rightLabel:"right caption",
  minLimit:200,
  maxLimit:4000,
  // valueLabelDisplay:"auto"
};


export const IndexSliderWithIndexSliderInformationBubble = Template.bind({});
IndexSliderWithIndexSliderInformationBubble.args = {
  valueLabelDisplay:"auto",
  leftLabel:"left caption",
  rightLabel:"right caption"
};

// export const IndexSliderWithStyles = Template.bind({});
// IndexSliderWithStyles.args = {
//   leftLabel:"left caption",
//   rightLabel:"right caption"
// };

export const DisabledIndexSlider = Template.bind({});
DisabledIndexSlider.args = {
  disabled:true,
  leftLabel:"left caption",
  rightLabel:"right caption",
};


