import React, { useState, useEffect } from "react";
import PropTypes from "prop-types";
import { useTheme } from "styled-components";
import {
  CaptionContainer,
  CaptionDiv,
  CaptionLable,
  StyledInput,
} from "./IndexSlider.styled";
import "./slider.css";
import { useClassName } from "../../hooks/useClassName";
import { getConfig, ThemeWrapper } from "../Theme/ThemeContext";

function handleBetween(number, calc) {
  let [a, b] = calc;
  let min = Math.min(a, b),
    max = Math.max(a, b);
  return number > min && number < max;
}

const isNonNull = (prop) => {
  return prop !== null && prop !== undefined;
};

const IndexSlider = (props) => {
  const theme = getConfig(props?.isMobile || props?.isMobileView || false);
  const leftLabelclassName = useClassName(props, props.leftLabelStyle);
  const rightLabelclassName = useClassName(props, props.rightLabelStyle);
  const {
    disabled,
    valueLabelDisplay,
    ariaLabel,
    ariaLabelledby,
    ariaValuetext,
    step,
    defaultValue,
    onValueSelected,
    value,
    leftLabel,
    rightLabel,
    minLimit,
    maxLimit,
    type,
    showLabel,
    showInRange,
    onChangeComplete,
    trackColor,
    isMobile,
    isMobileView
  } = props;
  const [sliderValue, setValue] = useState();
  const [trackBackground, setTrackBackground] = useState("");
  const [leftVal, setLeftVal] = useState("");
  const [transFormVal, setTransFormVal] = useState("");

  // Helper function to get theme color with fallback
  const getThemeColor = () => {
    // First try to get color from theme with mode
    const colorWithMode = theme?.palette?.[type]?.[theme?.mode];
    if (colorWithMode) return colorWithMode;
    
    // Fallback to main color
    const mainColor = theme?.palette?.[type]?.main;
    if (mainColor) return mainColor;
    
    // Fallback to primary color
    const primaryColor = theme?.palette?.primary?.main;
    if (primaryColor) return primaryColor;
    
    // Final fallback
    return type === 'primary' ? '#1976d2' : '#dc004e';
  };

  const setBubble = (val) => {
    const range = document.getElementById("range");
    const rangeV = document.getElementById("rangeV");
    const newValue = Number(
      ((value - minLimit) * 100) / (range.max - range.min)
    )
    let newPosition = 10 - newValue * 0.2;
    setLeftVal(`calc(${newValue}% + (${newPosition}px))`);
    setTransFormVal(`translateX(-50%) scale(${val})`);
  };

  const setNewValue = (val) => {
    const range = document.getElementById("range");
    const min = minLimit ? minLimit : range.min;
    const max = maxLimit ? maxLimit : range.max;
    const value = val;
    
    // Get theme color with fallback
    const themeColor = getThemeColor();
    
    if (disabled) {
      const background = `linear-gradient(to right, #bdbdbd  
        0%, #bdbdbd 
        ${((value - min) / (max - min)) * 100}%, ${trackColor} ${((value - min) / (max - min)) * 100
        }%, ${trackColor} 100%)`;
      setTrackBackground(background);
    } else {
      const background = `linear-gradient(to right, ${themeColor} 
        0%, ${themeColor} 
        ${((value - min) / (max - min)) * 100}%, ${trackColor} ${((value - min) / (max - min)) * 100
        }%, ${trackColor} 100%)`;
      setTrackBackground(background);
    }

    setBubble(valueLabelDisplay === "off" ? 0 : 1);
  };

  useEffect(() => {
    const themeColor = getThemeColor();
    document.documentElement.style.setProperty('--bubble-color', themeColor);
    setNewValue(value);
    setValue(value)
  }, [props, value]);

  // Debug log to check theme structure
  console.log("Theme debug:", {
    theme,
    type,
    mode: theme?.mode,
    palette: theme?.palette,
    colorPath: theme?.palette?.[type],
    finalColor: getThemeColor()
  });

  return (
    <ThemeWrapper isMobile={isMobile || isMobileView || false}>
      <div
        className="vms_indexslider_container range-wrap"
        aria-label={ariaLabel}
        aria-labelledb={ariaLabelledby}
        aria-valuetext={ariaValuetext}
      >
        <div
          className="vms_indexslider_slidervalue range-value"
          id="rangeV"
          style={{ left: leftVal, transform: transFormVal }}
        >
          <span>{sliderValue}</span>
        </div>
        <StyledInput
          id="range"
          className="vms_indexslider_input"
          type="range"
          sliderType={type}
          min={minLimit}
          max={maxLimit}
          disabled={disabled}
          value={sliderValue}
          style={{ background: trackBackground }}
          onChange={(e) => {
            if (showInRange && minLimit && maxLimit) {
              const checkValueBetween = handleBetween(e.target.value, [
                minLimit,
                maxLimit,
              ]);
              if (checkValueBetween) {
                setValue(e.target.value);
                setNewValue(e.target.value);
                onValueSelected(e, e.target.value);
              }
            } else {
              setValue(e.target.value);
              setNewValue(e.target.value);
              onValueSelected(e, e.target.value);
            }
          }}
          onMouseOver={() => setBubble(valueLabelDisplay === "off" ? 0 : 1)}
          onMouseOut={() => setBubble(0)}
          onMouseUp={(e) => {
            if (isNonNull(onChangeComplete)) {
              onChangeComplete(e.target.value)
            }
          }}

          onTouchEnd={(e) => {
            if (isNonNull(onChangeComplete)) {
              onChangeComplete(e.target.value)
            }
          }}
          step={step}
          defaultValue={defaultValue}
        />
      </div>
      {showLabel && (
        <CaptionContainer className="vms_indexslider_captioncontainer">
          <CaptionDiv className="vms_indexslider_caption_left">
            <CaptionLable className={`${leftLabelclassName} vms_indexslider_caption_leftlabel`}>
              {leftLabel}
            </CaptionLable>
          </CaptionDiv>
          <CaptionDiv className="vms_indexslider_caption_right">
            <CaptionLable className={`${rightLabelclassName} vms_indexslider_caption_rightlabel`}>
              {rightLabel}
            </CaptionLable>
          </CaptionDiv>
        </CaptionContainer>
      )
      }
    </ThemeWrapper>
  );
};
IndexSlider.defaultProps = {
  disabled: false,
  valueLabelDisplay: "off",
  step: 1,
  onValueSelected: () => { },
  leftLabel: "",
  rightLabel: "",
  leftLabelStyle: null,
  rightLabelStyle: null,
  type: "primary",
  minLimit: 0,
  maxLimit: 100,
  showLabel: true,
  showInRange: false,
  trackColor: '#EBEBEC'
};
IndexSlider.propTypes = {
  /**
   *	
Callback function that is fired when the slider's value changed. Signature: function(event: object, value: number | number[]) => void event: The event source of the callback. value: The new value.
   */
  onValueSelected: PropTypes.func.isRequired,
  /**
   *	
The value of the slider.
   */
  value: PropTypes.number.isRequired,

  /**
   *	
The default element value. Pass the value on which slider should load.
   */
  defaultValue: PropTypes.number,

  /**
   *	
The granularity with which the slider can step through values. (A "discrete" slider.) The min prop serves as the origin for the valid values. We recommend (max - min) to be evenly divisible by the step. When step is null, the thumb can only be slid onto marks provided with the marks prop
   */
  step: PropTypes.number,

  /**
   *If true, the slider will be disabled.
   */
  disabled: PropTypes.bool,

  /**
   *Controls when the value label is displayed: "auto" the value label will display when the thumb is hovered or focused. "on" will display persistently. "off" will never display.
   */
  valueLabelDisplay: PropTypes.string,

  /**
   *The id of the element containing a label for the slider.
   */
  ariaLabelledby: PropTypes.string,

  /**
   *The label of the slider.
   */
  ariaLabel: PropTypes.string,

  /**
   *A string value that provides a user-friendly name for the current value of the slider.
   */
  ariaValuetext: PropTypes.string,

  /**
   *Controls when the value label is displayed: "auto" the value label will display when the thumb is hovered or focused. "on" will display persistently. "off" will never display.
   */
  // ariaLabelledby: PropTypes.string,
  /**
   *Left label
   */
  leftLabel: PropTypes.string,

  /**
   *Right label
   */
  rightLabel: PropTypes.string,

  /**
   *Mininum limit of slider
   */
  minLimit: PropTypes.number,

  /**
   *Maximum limit of slider
   */
  maxLimit: PropTypes.number,

  /**
   *  Left label style
   */

  leftLabelStyle: PropTypes.object,

  /**
   *  Right label style
   */

  rightLabelStyle: PropTypes.object,


  /**
   * Variations of Index Slider Type
   */
  type: PropTypes.oneOf(["primary", "secondary"]),

  /**
   * if to want to disable range in between the range
   */
  showInRange: PropTypes.bool
};

export { IndexSlider };
