import React, { useState, useEffect, memo, useCallback, useMemo } from "react";
import PropTypes from "prop-types";
import { useTheme } from "styled-components";
import {
  CaptionContainer,
  CaptionDiv,
  CaptionLable,
  StyledInput,
} from "./IndexSlider.styled";
import "./slider.css";
import { useClassName } from "../../hooks/useClassName";
import { getConfig, ThemeWrapper } from "../Theme/ThemeContext";

// Utility functions (placed before main component)
const handleBetween = (number, calc) => {
  let [a, b] = calc;
  let min = Math.min(a, b),
    max = Math.max(a, b);
  return number > min && number < max;
};

const isNonNull = (prop) => prop !== null && prop !== undefined;

const IndexSlider = memo((props) => {
  // 1. PROP DESTRUCTURING & VALIDATION
  const {
    disabled = false,
    valueLabelDisplay = "auto",
    ariaLabel,
    ariaLabelledby,
    ariaValuetext,
    step = 1,
    defaultValue = 0,
    onValueSelected,
    value,
    leftLabel,
    rightLabel,
    minLimit = 0,
    maxLimit = 100,
    type = "primary",
    showLabel = true,
    showInRange = false,
    onChangeComplete,
    trackColor,
    isMobile,
    isMobileView,
    leftLabelStyle,
    rightLabelStyle,
    additionalClassName,
    additionalStyle,
    ...otherProps
  } = props;

  // 2. STATE MANAGEMENT
  const [currentValue, setCurrentValue] = useState(value || defaultValue);
  const [trackBackground, setTrackBackground] = useState("");
  const [leftVal, setLeftVal] = useState("");
  const [transFormVal, setTransFormVal] = useState("");

  // 3. PERFORMANCE OPTIMIZATIONS
  const theme = useMemo(() =>
    getConfig(isMobile || isMobileView || false),
    [isMobile, isMobileView]
  );

  const leftLabelClassName = useMemo(() =>
    useClassName(props, leftLabelStyle),
    [props, leftLabelStyle]
  );

  const rightLabelClassName = useMemo(() =>
    useClassName(props, rightLabelStyle),
    [props, rightLabelStyle]
  );

  const computedClassName = useMemo(() =>
    useClassName(props, additionalClassName),
    [props, additionalClassName]
  );

  const isMobileDevice = useMemo(() =>
    isMobile || isMobileView || false,
    [isMobile, isMobileView]
  );

  // 4. UTILITY FUNCTIONS (memoized)
  const getThemeColor = useCallback(() => {
    // First try to get color from theme with mode
    const colorWithMode = theme?.palette?.[type]?.[theme?.mode];
    if (colorWithMode) return colorWithMode;

    // Fallback to main color
    const mainColor = theme?.palette?.[type]?.main;
    if (mainColor) return mainColor;

    // Fallback to primary color
    const primaryColor = theme?.palette?.primary?.main;
    if (primaryColor) return primaryColor;

    // Final fallback
    return type === 'primary' ? '#1976d2' : '#dc004e';
  }, [theme, type]);

  const setBubble = useCallback((val) => {
    const range = document.getElementById("range");
    if (!range) return;

    const newValue = Number(
      ((currentValue - minLimit) * 100) / (maxLimit - minLimit)
    );
    let newPosition = 10 - newValue * 0.2;
    setLeftVal(`calc(${newValue}% + (${newPosition}px))`);
    setTransFormVal(`translateX(-50%) scale(${val})`);
  }, [currentValue, minLimit, maxLimit]);

  const setNewValue = useCallback((val) => {
    const min = minLimit;
    const max = maxLimit;
    const sliderValue = val;

    // Get theme color with fallback
    const themeColor = getThemeColor();
    const finalTrackColor = trackColor || '#ddd';

    const percentage = ((sliderValue - min) / (max - min)) * 100;

    const background = disabled
      ? `linear-gradient(to right, #bdbdbd 0%, #bdbdbd ${percentage}%, ${finalTrackColor} ${percentage}%, ${finalTrackColor} 100%)`
      : `linear-gradient(to right, ${themeColor} 0%, ${themeColor} ${percentage}%, ${finalTrackColor} ${percentage}%, ${finalTrackColor} 100%)`;

    setTrackBackground(background);
    setBubble(valueLabelDisplay === "off" ? 0 : 1);
  }, [minLimit, maxLimit, getThemeColor, trackColor, disabled, valueLabelDisplay, setBubble]);

  // 5. EVENT HANDLING with useCallback
  const handleSliderChange = useCallback((e) => {
    const newValue = parseInt(e.target.value);
    setCurrentValue(newValue);
    setNewValue(newValue);

    if (isNonNull(onValueSelected)) {
      onValueSelected(newValue);
    }
  }, [onValueSelected, setNewValue]);

  const handleChangeComplete = useCallback((e) => {
    const newValue = parseInt(e.target.value);
    if (isNonNull(onChangeComplete)) {
      onChangeComplete(newValue);
    }
  }, [onChangeComplete]);

  // 6. EFFECTS
  useEffect(() => {
    const themeColor = getThemeColor();
    document.documentElement.style.setProperty('--bubble-color', themeColor);
    setNewValue(value || defaultValue);
    setCurrentValue(value || defaultValue);
  }, [value, defaultValue, getThemeColor, setNewValue]);

  // 7. ERROR HANDLING
  React.useEffect(() => {
    if (minLimit >= maxLimit) {
      console.warn('IndexSlider: minLimit should be less than maxLimit');
    }
    if (step <= 0) {
      console.warn('IndexSlider: step should be greater than 0');
    }
  }, [minLimit, maxLimit, step]);

  // 8. CONDITIONAL RENDERING (memoized)
  const sliderValueDisplay = useMemo(() => {
    if (valueLabelDisplay === "off") return null;

    return (
      <div
        className="vms_indexslider_slidervalue range-value"
        id="rangeV"
        style={{ left: leftVal, transform: transFormVal }}
      >
        <span>{currentValue}</span>
      </div>
    );
  }, [valueLabelDisplay, leftVal, transFormVal, currentValue]);

  const handleRangeChange = useCallback((e) => {
    const newValue = parseInt(e.target.value);

    if (showInRange && minLimit && maxLimit) {
      const checkValueBetween = handleBetween(newValue, [minLimit, maxLimit]);
      if (checkValueBetween) {
        handleSliderChange(e);
      }
    } else {
      handleSliderChange(e);
    }
  }, [showInRange, minLimit, maxLimit, handleSliderChange]);

  const handleMouseOver = useCallback(() => {
    setBubble(valueLabelDisplay === "off" ? 0 : 1);
  }, [setBubble, valueLabelDisplay]);

  const handleMouseOut = useCallback(() => {
    setBubble(0);
  }, [setBubble]);

  return (
    <ThemeWrapper isMobile={isMobileDevice}>
      <div
        className={`${computedClassName} vms_indexslider_container range-wrap`}
        style={additionalStyle}
        aria-label={ariaLabel}
        aria-labelledby={ariaLabelledby}
        aria-valuetext={ariaValuetext}
        role="group"
        {...otherProps}
      >
        {sliderValueDisplay}

        <StyledInput
          id="range"
          className="vms_indexslider_input"
          type="range"
          sliderType={type}
          min={minLimit}
          max={maxLimit}
          step={step}
          disabled={disabled}
          value={currentValue}
          style={{ background: trackBackground }}
          onChange={handleRangeChange}
          onMouseOver={handleMouseOver}
          onMouseOut={handleMouseOut}
          onMouseUp={handleChangeComplete}
          aria-label={ariaLabel || "Slider"}
          aria-valuemin={minLimit}
          aria-valuemax={maxLimit}
          aria-valuenow={currentValue}

        />

        {showLabel && (
          <CaptionContainer className="vms_indexslider_captioncontainer">
            <CaptionDiv className="vms_indexslider_caption_left">
              <CaptionLable className={`${leftLabelClassName} vms_indexslider_caption_leftlabel`}>
                {leftLabel}
              </CaptionLable>
            </CaptionDiv>
            <CaptionDiv className="vms_indexslider_caption_right">
              <CaptionLable className={`${rightLabelClassName} vms_indexslider_caption_rightlabel`}>
                {rightLabel}
              </CaptionLable>
            </CaptionDiv>
          </CaptionContainer>
        )}
      </div>
    </ThemeWrapper>
  );
});

// Component display name for debugging
IndexSlider.displayName = "IndexSlider";

IndexSlider.defaultProps = {
  disabled: false,
  valueLabelDisplay: "off",
  step: 1,
  defaultValue: 0,
  leftLabel: "",
  rightLabel: "",
  leftLabelStyle: null,
  rightLabelStyle: null,
  type: "primary",
  minLimit: 0,
  maxLimit: 100,
  showLabel: true,
  showInRange: false,
  trackColor: '#EBEBEC',
  onValueSelected: () => { },
  onChangeComplete: () => { },
};
IndexSlider.propTypes = {
  /**
   * Callback function that is fired when the slider's value changed
   */
  onValueSelected: PropTypes.func,

  /**
   * The value of the slider
   */
  value: PropTypes.number,

  /**
   * The default element value
   */
  defaultValue: PropTypes.number,

  /**
   * The granularity with which the slider can step through values
   */
  step: PropTypes.number,

  /**
   * If true, the slider will be disabled
   */
  disabled: PropTypes.bool,

  /**
   * Controls when the value label is displayed: "auto", "on", "off"
   */
  valueLabelDisplay: PropTypes.oneOf(["auto", "on", "off"]),

  /**
   * The id of the element containing a label for the slider
   */
  ariaLabelledby: PropTypes.string,

  /**
   * Aria label for the slider
   */
  ariaLabel: PropTypes.string,

  /**
   * Aria value text
   */
  ariaValuetext: PropTypes.string,

  /**
   * Left label text
   */
  leftLabel: PropTypes.string,

  /**
   * Right label text
   */
  rightLabel: PropTypes.string,

  /**
   * Minimum limit for the slider
   */
  minLimit: PropTypes.number,

  /**
   * Maximum limit for the slider
   */
  maxLimit: PropTypes.number,

  /**
   * Slider type for theming
   */
  type: PropTypes.oneOf(["primary", "secondary"]),

  /**
   * Whether to show labels
   */
  showLabel: PropTypes.bool,

  /**
   * Whether to show in range
   */
  showInRange: PropTypes.bool,

  /**
   * Callback when change is complete
   */
  onChangeComplete: PropTypes.func,

  /**
   * Track color
   */
  trackColor: PropTypes.string,

  /**
   * Whether component is in mobile mode
   */
  isMobile: PropTypes.bool,

  /**
   * Whether component is in mobile view
   */
  isMobileView: PropTypes.bool,

  /**
   * Additional CSS classes
   */
  additionalClassName: PropTypes.arrayOf(PropTypes.string),

  /**
   * Additional inline styles
   */
  additionalStyle: PropTypes.object,

  /**
   *The label of the slider.
   */
  ariaLabel: PropTypes.string,

  /**
   *A string value that provides a user-friendly name for the current value of the slider.
   */
  ariaValuetext: PropTypes.string,

  /**
   *Controls when the value label is displayed: "auto" the value label will display when the thumb is hovered or focused. "on" will display persistently. "off" will never display.
   */
  // ariaLabelledby: PropTypes.string,
  /**
   *Left label
   */
  leftLabel: PropTypes.string,

  /**
   *Right label
   */
  rightLabel: PropTypes.string,

  /**
   *Mininum limit of slider
   */
  minLimit: PropTypes.number,

  /**
   *Maximum limit of slider
   */
  maxLimit: PropTypes.number,

  /**
   *  Left label style
   */

  leftLabelStyle: PropTypes.object,

  /**
   *  Right label style
   */

  rightLabelStyle: PropTypes.object,


  /**
   * Variations of Index Slider Type
   */
  type: PropTypes.oneOf(["primary", "secondary"]),

  /**
   * if to want to disable range in between the range
   */
  showInRange: PropTypes.bool,
};

export { IndexSlider };
