:root {
  --bubble-color: #ef4044;
}

input[type="range"]:disabled {
  /* Disabled Element */
}

input[type="range"]:disabled::-webkit-slider-runnable-track {
  height: 4px;
  opacity: 1;
  border-radius: 4px;
  /* Disabled slider-runnable-track */
}

input[type="range"]:disabled::-moz-range-track {
  height: 4px;
  opacity: 1;
  border-radius: 4px;
}

input[type="range"]:disabled::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 8px;
  height: 8px;
  background: #fff;
  border-radius: 50%;
  border: 1px solid #ebebec;
  cursor: pointer;
  margin-top: -3px;
  /* Disabled slider-thumb */
}

input[type="range"]:disabled::-moz-range-thumb {
  -webkit-appearance: none;
  width: 8px;
  height: 8px;
  background: #fff;
  border-radius: 50%;
  border: 1px solid #ebebec;
  cursor: pointer;
  margin-top: -3px;
}

.range-wrap {
  position: relative;
}
.range-value {
  position: absolute;
  top: -70%;
  left: 80%;
}
.range-value span {
  position: absolute;
  height: 40px;
  width: 40px;
  transform: translateX(-50%) scale(1);
  font-weight: 500;
  top: -44px;
  line-height: 55px;
  z-index: 2;
  color: #fff;
  transform-origin: bottom;
  transition: transform 0.3s ease-in-out;
  text-align: center;
}

.range-value span:after {
  position: absolute;
  content: "";
  height: 100%;
  width: 100%;
  background: var(--bubble-color);
  border: 3px solid #fff;
  z-index: -1;
  left: 50%;
  transform: translateX(-50%) rotate(45deg);
  border-bottom-left-radius: 50%;
  border-top-left-radius: 50%;
  border-top-right-radius: 50%;
  text-align: center;
}
