import styled, { css } from "styled-components";

export const Container = styled.div``;


export const StyledInput = styled.input`
  -webkit-appearance: none;
  width: 100%;
  height: 4px;
  background: #ddd;
  border-radius: 5px;
  outline: none;
  border: none;
  z-index: 2222;

  &::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 24px;
    height: 24px;
    background: #fff;
    border-radius: 50%;
    border: ${({ theme, sliderType, color }) => (`1px solid ${theme?.palette?.[sliderType]?.[theme.mode]}`)};
    cursor: pointer;
  }
  &::-webkit-slider-runnable-track {
    -webkit-appearance: none;
    cursor: pointer;
  }
  &::-moz-range-thumb {
    -webkit-appearance: none;
    width: 24px;
    height: 24px;
    background: #fff;
    border-radius: 50%;
    background: #ef4044;
    border: ${({ theme, sliderType, color }) => (`1px solid ${theme?.palette?.[sliderType]?.[theme.mode]}`)};
    cursor: pointer;
  }
  &::-moz-range-progress {
    background: #664aff;
  }
`;


export const CaptionContainer = styled.div`
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  margin-top: 20px;
`;
export const CaptionDiv = styled.div``;

export const CaptionLable = styled.span`
  line-height: 20px;
  letter-spacing: 0.3px;
  font-size: 14px;
  color: #4d4d4f;
  font-weight: 700;
  font-family: ${({ theme }) => theme?.typography?.fontFamily || ""};
`;
