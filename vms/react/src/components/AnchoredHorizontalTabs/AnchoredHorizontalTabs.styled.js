import styled from "styled-components";

export const AnchoredHorizontalTabsWrapper = styled.div`
  width: 100%;
  display: flex;
  overflow: auto;
  align-items: center;
  justify-content: space-between;
  background-color: #fff;
`;

export const TabButtonDiv = styled.div`
  display: flex;
  align-items: center;
`;
export const TabLeftRightButton = styled.button`
  border: none;
  color: transparent;
  padding: 0px;
  margin: 0px;
  border-width: 0px;
  opacity: ${({ isDisabled }) => (isDisabled ? `0.3` : `transparent`)};
  background-color: transparent;
  cursor: pointer;
  display: flex;
  align-items: center;
`;
export const Svg = styled.svg`
  width: 24px;
  height: 24px;
  color: ${({ theme, color }) => theme.palette[color]?.main || theme.palette.primary.main};
  opacity: ${({ isVisible }) => (isVisible ? `1` : `0`)};
  transition: opacity 0.5s ease;
`;

export const Tabs = styled.ul`
  display: flex;
  width: 100%;
  overflow-x: auto;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
  list-style: none;
  scroll-behavior: smooth;
  align-items: center;
  &::-webkit-scrollbar {
    background: transparent;
    -webkit-appearance: none;
    width: 0;
    height: 0;
    display: none;
  }
`;
export const Tab = styled.button`
  border:none;
  display: flex;
  flex-direction: column;
  margin: 10px;
  white-space: nowrap;
  cursor: ${({ disabled }) => (disabled ? `default` : `pointer`)};
  text-align: center;
  align-items: center;
  padding-top: ${({paddingTop})=>paddingTop};
  padding-left: 15px;
  padding-right: 15px;
  background-color:transparent;
  ${({ theme, tabsType, isSelected, color }) =>
    tabsType === "secondary" &&
    `
    ${
      isSelected
        ? `
    background-color:${theme.palette[color]?.main || theme.palette.primary.main};
    `
        : `
    border: solid 0.5px #f4f4f4;
    box-shadow:0 1px 4px 0 rgb(209 211 212 / 50%);`
    }
  `};
  ${({ theme, tabsType, isSelected, disabled, color }) =>
    disabled == false &&
    `
    ${
      tabsType !== "secondary" &&
      `border-bottom : ${
        isSelected && `4px ${theme.palette[color]?.main || theme.palette.primary.main} solid`
      }`
    };
  `};
  border-radius: ${({ theme, tabsType, disabled, isSelected }) =>
    tabsType === "secondary"
      ? `22px`
      : `${isSelected && disabled == false && theme.shape.borderRadius}`};
  opacity: ${({ disabled }) => (disabled ? `0.7` : `1`)};
  &.isDisabled{
    color: rgba(0, 0, 0, 0.38);
  }
`;
export const TabText = styled.span`
  font-weight: ${({ theme, isMobile }) =>
    theme.typography[isMobile ? `text` : `title`].fontWeight};
  font-family: ${({ theme }) => theme.typography.fontFamily};
  font-size: ${({ theme, isMobile }) =>
    theme.typography[isMobile ? "text" : "title"].fontSize};
  font-style: normal;
  font-stretch: normal;
  line-height: ${({ theme }) => theme.typography.lineHeight};
  letter-spacing: 0.3px;
  color: ${({ theme, tabsType, isSelected, color }) =>
    tabsType === "secondary"
      ? `${isSelected ? theme.palette[color]?.contrastText || "#ffffff" : "#4D4D4F"}`
      : `${isSelected ? theme.palette[color]?.main || theme.palette.primary.main : "#4D4D4F"}`};
  user-select: none;
  padding-top: 5px;
  padding-bottom: 5px;
  opacity: ${({ isDisabled }) => isDisabled ? `0.7` : `1`};
`;
export const SubTitleText = styled.span`
  font-family: ${({ theme }) => theme.typography.fontFamily};  
  font-style: normal;
  font-stretch: normal;
  color: ${({ theme, tabsType, isSelected, color }) =>
    tabsType === "secondary"
      ? `${isSelected ? theme.palette[color].contrastText : "#4D4D4F"}`
      : `${isSelected ? theme.palette[color][theme.mode] : "#4D4D4F"}`};
  user-select: none;  
  opacity: ${({ isDisabled }) => isDisabled ? `0.7` : `1`};
`
export const TabIconImage = styled.img`
  max-width: 20px;
  max-height: 20px;
  user-select: none;
  opacity: ${({ isDisabled }) => isDisabled ? `0.7` : `1`};
`;
export const AnchorTag=styled.a`
 text-decoration:none;
 pointer-events: ${({isDisabled})=>isDisabled?"none":"pointer"};
 padding:0;
 margin:0;
 height:${({isMobile})=>(isMobile===true?'auto':'65px')};
`;
export const CustomiseIcon=styled.img`
opacity: ${({ isVisible }) => (isVisible ? `1` : `0`)};
transition: opacity 0.5s ease;
width: 24px;
height: 24px;
`;
