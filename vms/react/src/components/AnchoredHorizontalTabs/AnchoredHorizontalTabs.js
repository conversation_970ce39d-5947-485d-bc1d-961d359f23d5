import React, { Fragment, useEffect, useRef, useState, memo, useCallback, useMemo } from "react";
import PropTypes from "prop-types";
import {
  AnchoredHorizontalTabsWrapper,
  AnchorTag,
  Svg,
  Tab,
  TabButtonDiv,
  TabIconImage,
  TabLeftRightButton,
  Tabs,
  TabText,
  CustomiseIcon,
  SubTitleText
} from "./AnchoredHorizontalTabs.styled";
import { useClassName } from "../../hooks/useClassName";
import { ThemeWrapper } from "../Theme/ThemeContext";

// Utility function (placed before main component)
const isNonNull = (prop) => prop !== null && prop !== undefined;

// Memoized icon components
const RightIcon = memo(({ color, isVisible }) => (
  <Svg
    color={color}
    isVisible={isVisible}
    focusable="false"
    viewBox="0 0 24 24"
    aria-hidden="true"
    role="img"
    aria-label="Next"
  >
    <path
      fill="currentColor"
      d="M8.59 16.34l4.58-4.59-4.58-4.59L10 5.75l6 6-6 6z"
    />
  </Svg>
));

const LeftIcon = memo(({ color, isVisible }) => (
  <Svg
    color={color}
    isVisible={isVisible}
    focusable="false"
    viewBox="0 0 24 24"
    aria-hidden="true"
    role="img"
    aria-label="Previous"
  >
    <path
      fill="currentColor"
      d="M15.41 16.09l-4.58-4.59 4.58-4.59L14 5.5l-6 6 6 6z"
    />
  </Svg>
));

RightIcon.displayName = 'RightIcon';
LeftIcon.displayName = 'LeftIcon';

export const AnchoredHorizontalTabs = memo((props) => {
  // 1. PROP DESTRUCTURING & VALIDATION
  const {
    tabsType = "primary",
    hideScrollButtons = false,
    onChange,
    tabHeader = [],
    additionalClassName,
    additionalStyle,
    selectedTabIndex,
    isMobile = false,
    isMobileView,
    color = "primary",
    customiseIcons,
    id,
    ...otherProps
  } = props;

  // 2. REFS
  const allTabsRef = useRef();
  const scrollRefs = useRef([]);

  // 3. STATE MANAGEMENT
  const [scrollX, setScrollX] = useState(0);
  const [scrollEnd, setScrollEnd] = useState(false);

  // 4. PERFORMANCE OPTIMIZATIONS
  const computedClassName = useMemo(() => 
    useClassName(props, additionalClassName), 
    [props, additionalClassName]
  );

  const isMobileDevice = useMemo(() => 
    isMobile || isMobileView || false, 
    [isMobile, isMobileView]
  );

  const shouldShowScrollButtons = useMemo(() => 
    !hideScrollButtons && !isMobile, 
    [hideScrollButtons, isMobile]
  );

  const isIconAvailable = useCallback((currentTabObj) => {
    return isNonNull(currentTabObj?.icon);
  }, []);

  // 5. EVENT HANDLING with useCallback
  const onTabChangeHandler = useCallback((tabObj, tabIndex) => {
    if (isNonNull(onChange)) {
      onChange(tabObj, tabIndex);
    }
    scrollSmoothHandler(tabIndex);
  }, [onChange]);

  const slide = useCallback((direction, shift) => {
    if (!shouldShowScrollButtons || !allTabsRef.current) return;

    const paddingOfTabComponent = 15;
    allTabsRef.current.scrollLeft += shift;
    setScrollX(prev => prev + shift);

    const isEnd = Math.floor(
      allTabsRef.current.scrollWidth -
      paddingOfTabComponent -
      allTabsRef.current.scrollLeft
    ) <= allTabsRef.current.offsetWidth;
    
    setScrollEnd(isEnd);
  }, [shouldShowScrollButtons]);

  const scrollCheck = useCallback(() => {
    if (!shouldShowScrollButtons || !allTabsRef.current) return;

    const paddingOfTabComponent = 15;
    setScrollX(allTabsRef.current.scrollLeft);
    
    const isEnd = Math.floor(
      allTabsRef.current.scrollWidth -
      paddingOfTabComponent -
      allTabsRef.current.scrollLeft
    ) <= allTabsRef.current.offsetWidth;
    
    setScrollEnd(isEnd);
  }, [shouldShowScrollButtons]);

  const scrollSmoothHandler = useCallback((index) => {
    if (scrollRefs.current[index]) {
      scrollRefs.current[index].scrollIntoView({
        behavior: "smooth",
        block: "center",
        inline: "center",
      });
    }
  }, []);

  const handleLeftSlide = useCallback((e) => {
    e.stopPropagation();
    slide("left", -70);
  }, [slide]);

  const handleRightSlide = useCallback((e) => {
    e.stopPropagation();
    slide("right", +70);
  }, [slide]);

  // 6. EFFECTS
  useEffect(() => {
    if (!allTabsRef.current) return;

    const isEnd = allTabsRef.current.scrollWidth === allTabsRef.current.offsetWidth;
    setScrollEnd(isEnd);
  }, [tabHeader.length]);

  // 7. CONDITIONAL RENDERING (memoized)
  const tabItems = useMemo(() => {
    return tabHeader.map((currentTabObj, index) => (
      <Fragment key={`Tab-${currentTabObj.title}-${index}`}>
        <AnchorTag
          className="vms_anchoredhorizontaltabs_anchortag"
          href={currentTabObj?.href}
          isDisabled={currentTabObj?.isDisabled || false}
          onClick={(e) => {
            if (currentTabObj?.href?.includes("#")) {
              e.preventDefault();
            }
          }}
          isMobile={isMobile}
        >
          <Tab
            id={`Tab-${currentTabObj.title}-${index}`}
            className={`vms_anchoredhorizontaltabs_tab ${
              selectedTabIndex === index ? "active_tab" : ""
            }`}
            ref={(el) => (scrollRefs.current[index] = el)}
            onClick={(e) => {
              e.preventDefault();
              if (!currentTabObj?.isDisabled) {
                onTabChangeHandler(currentTabObj, index);
              }
            }}
            isSelected={selectedTabIndex === index}
            tabsType={tabsType}
            disabled={currentTabObj?.isDisabled || false}
            isMobile={isMobile}
            color={color}
            paddingTop={isIconAvailable(currentTabObj) ? "6px" : "0"}
            role="tab"
            aria-selected={selectedTabIndex === index}
            aria-disabled={currentTabObj?.isDisabled || false}
            tabIndex={currentTabObj?.isDisabled ? -1 : 0}
          >
            {isIconAvailable(currentTabObj) && (
              <TabIconImage
                className="vms_anchoredhorizontaltabs_tabiconimg"
                isDisabled={currentTabObj?.isDisabled || false}
                src={currentTabObj.icon}
                alt={`${currentTabObj.title} icon`}
              />
            )}
            <TabText
              className="vms_anchoredhorizontaltabs_tabtext"
              isDisabled={currentTabObj?.isDisabled || false}
              tabsType={tabsType}
              isSelected={selectedTabIndex === index}
              isMobile={isMobile}
              color={color}
            >
              {currentTabObj.title}
            </TabText>
            {isNonNull(currentTabObj.subTitle) && currentTabObj.subTitle !== "" && (
              <SubTitleText
                isDisabled={currentTabObj?.isDisabled || false}
                tabsType={tabsType}
                isSelected={selectedTabIndex === index}
                isMobile={isMobile}
                color={color}
              >
                {currentTabObj.subTitle}
              </SubTitleText>
            )}
          </Tab>
        </AnchorTag>
      </Fragment>
    ));
  }, [tabHeader, selectedTabIndex, tabsType, isMobile, color, onTabChangeHandler, isIconAvailable]);

  const leftScrollButton = useMemo(() => {
    if (!shouldShowScrollButtons) return null;

    return (
      <TabButtonDiv
        className="vms_anchoredhorizontaltabs_tab_btn"
        onClick={handleLeftSlide}
      >
        <TabLeftRightButton 
          id={id ? `${id}-left` : null} 
          className="vms_anchoredhorizontaltabs_leftrightbtn_lefticon"
          aria-label="Scroll left"
          disabled={scrollX === 0}
        >
          {isNonNull(customiseIcons) ? (
            <CustomiseIcon
              src={customiseIcons.leftIcon}
              isVisible={scrollX !== 0}
              alt="Previous"
            />
          ) : (
            <LeftIcon isVisible={scrollX !== 0} color={color} />
          )}
        </TabLeftRightButton>
      </TabButtonDiv>
    );
  }, [shouldShowScrollButtons, handleLeftSlide, id, scrollX, customiseIcons, color]);

  const rightScrollButton = useMemo(() => {
    if (!shouldShowScrollButtons) return null;

    return (
      <TabButtonDiv
        className="vms_anchoredhorizontaltabs_tabbtn"
        onClick={handleRightSlide}
      >
        <TabLeftRightButton 
          id={id ? `${id}-right` : null} 
          className="vms_anchoredhorizontaltabs_leftrightbtn_righticon"
          aria-label="Scroll right"
          disabled={scrollEnd}
        >
          {isNonNull(customiseIcons) ? (
            <CustomiseIcon
              src={customiseIcons.rightIcon}
              isVisible={!scrollEnd}
              alt="Next"
            />
          ) : (
            <RightIcon isVisible={!scrollEnd} color={color} />
          )}
        </TabLeftRightButton>
      </TabButtonDiv>
    );
  }, [shouldShowScrollButtons, handleRightSlide, id, scrollEnd, customiseIcons, color]);

  // 8. ERROR HANDLING
  React.useEffect(() => {
    if (!Array.isArray(tabHeader) || tabHeader.length === 0) {
      console.warn('AnchoredHorizontalTabs: tabHeader should be a non-empty array');
    }
    if (selectedTabIndex < 0 || selectedTabIndex >= tabHeader.length) {
      console.warn('AnchoredHorizontalTabs: selectedTabIndex is out of bounds');
    }
  }, [tabHeader, selectedTabIndex]);

  // 9. RENDER
  return (
    <ThemeWrapper isMobile={isMobileDevice}>
      <AnchoredHorizontalTabsWrapper
        id="vms_horizontal_tab"
        className={`${computedClassName} vms_anchoredhorizontaltabs_container`}
        style={additionalStyle}
        role="tablist"
        aria-label="Horizontal tabs"
        {...otherProps}
      >
        {leftScrollButton}
        <Tabs
          id="All-Tabs"
          className="vms_anchoredhorizontaltabs_tabs"
          ref={allTabsRef}
          onScroll={scrollCheck}
          role="presentation"
        >
          {tabItems}
        </Tabs>
        {rightScrollButton}
      </AnchoredHorizontalTabsWrapper>
    </ThemeWrapper>
  );
});

// Add display name for better debugging
AnchoredHorizontalTabs.displayName = 'AnchoredHorizontalTabs';

AnchoredHorizontalTabs.defaultProps = {
  hideScrollButtons: false,
  tabsType: "primary",
  onChange: null,
  tabHeader: [],
  additionalClassName: null,
  additionalStyle: null,
  isMobile: false,
  color: "primary",
  customiseIcons: null,
};

AnchoredHorizontalTabs.propTypes = {
  /**
   * A required array of tab objects with keys and label properties
   */
  tabHeader: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.number.isRequired,
      title: PropTypes.string.isRequired,
      icon: PropTypes.string,
      isDisabled: PropTypes.bool,
      href: PropTypes.string,
      subTitle: PropTypes.string,
    })
  ).isRequired,
  /**
   * Function which triggers on change of tabs. It will return selected tab object and it's index.
   * (selectedTabObject: any, selectedTabIndex: number) => void
   */
  id: PropTypes.string,
  onChange: PropTypes.func.isRequired,
  /**
   * Pass selected tab's index
   */
  selectedTabIndex: PropTypes.number.isRequired,
  /**
   * Array of CSS class name to apply to the container in addition to the default
   */
  additionalClassName: PropTypes.arrayOf(PropTypes.string),
  /**
   * Property to set the `inline style` object on anchored-tab
   */
  additionalStyle: PropTypes.any,
  /**
   * Pass true if you want to hide back and next arrows. Default value is `false`
   */
  hideScrollButtons: PropTypes.bool,
  /**
   * Tab Type. Default value is `primary`
   */
  tabsType: PropTypes.oneOf(["primary", "secondary"]),
  /**
   * Is Mobile View. Default `false`. If isMobile=true, then scroll-button are hidden
   */
  isMobile: PropTypes.bool,
  /**
   * Theme Color to apply. Default `primary`
   */
  color: PropTypes.oneOf(["primary", "secondary"]),
  /**
   * Customise icons to be provided for tabs
   */
  customiseIcons: PropTypes.shape({
    rightIcon: PropTypes.string.isRequired,
    leftIcon: PropTypes.string.isRequired,
  }),
};
