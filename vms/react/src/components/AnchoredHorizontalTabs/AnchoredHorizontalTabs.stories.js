import { Fragment, useEffect, useState } from "react";
import { AnchoredHorizontalTabs } from "./AnchoredHorizontalTabs";

export default {
  title: "VMS_REACT/Anchored Horizontal Tabs",
  component: AnchoredHorizontalTabs,
  argTypes: {
    tabHeader: { control: { type: "" } },
    additionalStyle: { control: { type: "" } },
    additionalClassName: { control: { type: "" } },
  },
};

const sampleTabHeaderData = [
  { id: 0, title: `Classic Rooms` },
  { id: 1, title: `Luxury Rooms` },
  { id: 2, title: `Junior Suites` },
  { id: 3, title: `Prestigue Suites` },
  { id: 4, title: `Beach Suites` },
  { id: 5, title: `Deluxe Suites` },
  { id: 6, title: `Opera Suites` },
  { id: 7, title: `Family Rooms` },
  { id: 8, title: `Large Rooms` },
  { id: 9, title: `Small Rooms` },
  { id: 10, title: `Big Rooms` },
  { id: 11, title: `CityView Rooms` },
  { id: 12, title: `Balcony Rooms` },
];
const tabHeaderDataWithIcon = [
  {
    id: 0,
    title: `Classic Rooms`,
    icon: `https://img.icons8.com/offices/30/000000/living-room.png`,
  },
  {
    id: 1,
    title: `Luxury Rooms`,
    icon: `https://img.icons8.com/cotton/64/000000/living-room.png`,
  },
  {
    id: 2,
    title: `Junior Suites`,
    icon: `https://img.icons8.com/dusk/64/000000/room.png`,
  },
  {
    id: 3,
    title: `Prestigue Suites`,
    icon: `https://img.icons8.com/offices/30/000000/living-room.png`,
  },
  {
    id: 4,
    title: `Beach Suites`,
    icon: `https://img.icons8.com/cotton/64/000000/living-room.png`,
  },
  {
    id: 5,
    title: `Deluxe Suites`,
    icon: `https://img.icons8.com/dusk/64/000000/room.png`,
  },
  {
    id: 6,
    title: `Opera Suites`,
    icon: `https://img.icons8.com/offices/30/000000/living-room.png`,
  },
  {
    id: 7,
    title: `Family Rooms`,
    icon: `https://img.icons8.com/cotton/64/000000/living-room.png`,
  },
  {
    id: 8,
    title: `Large Rooms`,
    icon: `https://img.icons8.com/dusk/64/000000/room.png`,
  },
  {
    id: 9,
    title: `Small Rooms`,
    icon: `https://img.icons8.com/offices/30/000000/living-room.png`,
  },
  {
    id: 10,
    title: `Big Rooms`,
    icon: `https://img.icons8.com/dusk/64/000000/room.png`,
  },
  {
    id: 11,
    title: `Luxury Rooms`,
    icon: `https://img.icons8.com/offices/30/000000/living-room.png`,
  },
  {
    id: 12,
    title: `Balcony Rooms`,
    icon: `https://img.icons8.com/offices/30/000000/living-room.png`,
  },
];
const tabHeaderDataWithIconDisabled = [
  {
    id: 0,
    title: `Classic Rooms`,
    icon: `https://img.icons8.com/offices/30/000000/living-room.png`,
    isDisabled: false,
  },
  {
    id: 1,
    title: `Luxury Rooms`,
    icon: `https://img.icons8.com/cotton/64/000000/living-room.png`,
    isDisabled: true,
  },
  {
    id: 2,
    title: `Junior Suites`,
    icon: `https://img.icons8.com/dusk/64/000000/room.png`,
  },
  {
    id: 3,
    title: `Prestigue Suites`,
    icon: `https://img.icons8.com/offices/30/000000/living-room.png`,
    isDisabled: true,
  },
  {
    id: 4,
    title: `Beach Suites`,
    icon: `https://img.icons8.com/cotton/64/000000/living-room.png`,
  },
  {
    id: 5,
    title: `Deluxe Suites`,
    icon: `https://img.icons8.com/dusk/64/000000/room.png`,
    isDisabled: true,
  },
  {
    id: 6,
    title: `Opera Suites`,
    icon: `https://img.icons8.com/offices/30/000000/living-room.png`,
  },
  {
    id: 7,
    title: `Family Rooms`,
    icon: `https://img.icons8.com/cotton/64/000000/living-room.png`,
    isDisabled: true,
  },
  {
    id: 8,
    title: `Large Rooms`,
    icon: `https://img.icons8.com/dusk/64/000000/room.png`,
  },
  {
    id: 9,
    title: `Small Rooms`,
    icon: `https://img.icons8.com/offices/30/000000/living-room.png`,
  },
  {
    id: 10,
    title: `Big Rooms`,
    icon: `https://img.icons8.com/dusk/64/000000/room.png`,
    isDisabled: true,
  },
  {
    id: 11,
    title: `Luxury Rooms`,
    icon: `https://img.icons8.com/offices/30/000000/living-room.png`,
  },
  {
    id: 12,
    title: `Balcony Rooms`,
    icon: `https://img.icons8.com/offices/30/000000/living-room.png`,
  },
];
const tabHeaderDataDisabled = [
  { id: 0, title: `Classic Rooms`, isDisabled: true },
  { id: 1, title: `Luxury Rooms` },
  { id: 2, title: `Junior Suites`, isDisabled: true },
  { id: 3, title: `Prestigue Suites` },
  { id: 4, title: `Beach Suites`, isDisabled: true },
  { id: 5, title: `Deluxe Suites` },
  { id: 6, title: `Opera Suites`, isDisabled: true },
  { id: 7, title: `Family Rooms` },
  { id: 8, title: `Large Rooms`, isDisabled: true },
  { id: 9, title: `Small Rooms` },
  { id: 10, title: `Big Rooms`, isDisabled: true },
  { id: 11, title: `Luxury Rooms` },
  { id: 12, title: `Balcony Rooms`, isDisabled: true },
];
const minimumTabHeaderData = [
  { id: 0, title: `Earn Miles`, subTitle:"subTitle1"},
  { id: 1, title: `Reedem Miles`, subTitle:"subTitle2"},
  { id: 2, title: `Other Suites`, subTitle:"subTitle3"},
];

const Template = (props) => {
  const [selectedTabIndexState, setSelectedTabIndexState] = useState(0);
  useEffect(() => {
    setSelectedTabIndexState(props.selectedTabIndex);
  }, [props.selectedTabIndex]);

  return (
    <AnchoredHorizontalTabs
    id={"tabs-id-template"}
      {...props}
      selectedTabIndex={selectedTabIndexState}
      onChange={(selectedTabObj, selectedTabIndex) => {
        setSelectedTabIndexState(selectedTabIndex);
      }}
    />
  );
};

export const DefaultTabs = Template.bind({});
DefaultTabs.args = {
  tabHeader: minimumTabHeaderData,
  tabsType: "primary",
  selectedTabIndex: 0,
  hideScrollButtons: false,
  isMobile: false,
};

export const DefaultScrollableTabs = Template.bind({});
DefaultScrollableTabs.args = {
  tabHeader: sampleTabHeaderData,
  tabsType: "primary",
  selectedTabIndex: 0,
  hideScrollButtons: false,
};

export const DefaultTabsWithIcons = Template.bind({});
DefaultTabsWithIcons.args = {
  tabHeader: tabHeaderDataWithIcon,
  tabsType: "primary",
  selectedTabIndex: 0,
  hideScrollButtons: false,
};

export const WithDisabledTabs = Template.bind({});
WithDisabledTabs.args = {
  tabHeader: tabHeaderDataDisabled,
  tabsType: "primary",
  selectedTabIndex: 1,
  hideScrollButtons: false,
};
export const WithIconsDisabledTabs = Template.bind({});
WithIconsDisabledTabs.args = {
  tabHeader: tabHeaderDataWithIconDisabled,
  tabsType: "primary",
  selectedTabIndex: 0,
  hideScrollButtons: false,
};

const DefaulScrollBodyTab = (args) => {
  const [selectedTabIndexState, setSelectedTabIndexState] = useState(0);
  return (
    <div style={{ display: "flex", flexDirection: "column" }}>
      <div
        style={{
          left: "0px",
          right: "0px",
          position: "fixed",
          overflow:"auto",
        }}
      >
        <AnchoredHorizontalTabs
          {...args}
          id={"tabs-id-scroll"}
          tabHeader={sampleTabHeaderData}
          selectedTabIndex={selectedTabIndexState}
          onChange={(selectedTabObj, selectedTabIndex) => {
            setSelectedTabIndexState(selectedTabIndex);
            const referenceElement = document.getElementById(selectedTabObj.id);
            if (referenceElement) {
              const offsetTop =
                referenceElement.getBoundingClientRect().top +
                window.pageYOffset -
                100;
              window.scroll({
                top: offsetTop,
                behavior: "smooth",
              });
            }
          }}
        />
      </div>
      <div id="my-react-application" style={{ marginTop: "60px" }}>
        <div>
          {sampleTabHeaderData.map((element) => {
            return (
              <div
                key={element.title}
                style={{ minHeight: "300px", fontWeight: "bold" }}
                id={element.id}
              >
                <span style={{ fontWeight: "bold", fontSize: "18px" }}>
                  {element.title}
                </span>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};
export const DefaultScrollBody = DefaulScrollBodyTab.bind({});
DefaultScrollBody.storyName = "Default Tabs Scroll Handle By Application";

export const DefaultMobile = (props) => {
  const [selectedTabIndexState, setSelectedTabIndexState] = useState(0);
  const [tabsType, setTabType] = useState("secondary");
  const [isMobile, setMobile] = useState(true);
  useEffect(() => {
    setSelectedTabIndexState(
      props.selectedTabIndex == undefined ? 0 : props.selectedTabIndex
    );
  }, [props?.selectedTabIndex]);
  useEffect(() => {
    setTabType(props.tabsType == undefined ? "secondary" : props.tabsType);
  }, [props?.tabsType]);
  useEffect(() => {
    setMobile(props.isMobile == undefined ? true : props.isMobile);
  }, [props?.isMobile]);
  return (
    <AnchoredHorizontalTabs
    id={"tabs-id-def-mob"}
      {...props}
      selectedTabIndex={selectedTabIndexState}
      onChange={(selectedTabObj, selectedTabIndex) => {
        setSelectedTabIndexState(selectedTabIndex);
      }}
      tabHeader={sampleTabHeaderData}
      tabsType={tabsType}
      isMobile={isMobile}
    />
  );
};
DefaultMobile.parameters = {
  viewport: {
    defaultViewport: "iphone5",
  },
};
const tabHeaderWithLink = [
  {
    id: 0,
    title: `Flights`,
    icon: `https://uatresources.jetprivilege.com/sharedresources/img/svg/fly.svg`,
    href: `https://uat.intermiles.com/flights`,
  },
  {
    id: 1,
    title: `Hotels`,
    icon: `https://uatresources.jetprivilege.com/sharedresources/img/svg/hotels-active.svg`,
    href: `https://uat.intermiles.com/hotels`,
  },
  {
    id: 2,
    title: `Shop`,
    icon: `https://uatresources.jetprivilege.com/sharedresources/img/svg/shop.svg`,
    href: `https://shopuat.intermiles.com/`,
  },
  {
    id: 3,
    title: `Cards`,
    icon: `https://uatresources.jetprivilege.com/sharedresources/img/svg/cards.svg`,
    href: `https://cardsuat.intermiles.com/`,
  },
  {
    id: 4,
    title: `Digital Store`,
    icon: `https://uatresources.jetprivilege.com/sharedresources/img/svg/digi-stores.svg`,
    href: `https://digistoresuat.intermiles.com/`,
  },
];
export const DefaultTabsWithLink = Template.bind({});
DefaultTabsWithLink.args = {
  tabHeader: tabHeaderWithLink,
  tabsType: "primary",
  selectedTabIndex: 0,
  hideScrollButtons: false,
  isMobile: false,
};
