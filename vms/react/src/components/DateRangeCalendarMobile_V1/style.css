.overlay-child {
    z-index: 99;
}

.calendar-container {
    position: relative;
    width: 290px;
    min-width: 290px;
    --widthA: calc(100% - 28px);
    --widthB: calc(var(--widthA) / 7);
}

.input-text-parent-container.input-text-container input {
    display: block;
    box-sizing: border-box;
    font-weight: 700;
    font-family: Montserrat;
    font-size: 14px;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.43;
    width: 100%;
    border-radius: 3px;
    letter-spacing: 0.3px;
    color: #4d4d4f;
    padding: 14px 16px;
    margin-top: 12px;
    text-overflow: ellipsis;
    height: 48px;
    border: solid 1px #4d4d4f;
    background-color: #fff;
}
.calendar-container .focusedInput input:focus {
    border: 1px solid #03868b;
    color: #4d4d4f;
    outline: none;
}
.calendar-container .calendar-flyout {
    display: flex;
    width: -webkit-max-content;
    width: -moz-max-content;
    width: max-content;
    border-radius: 3px;
    box-shadow: 0 2px 4px 0 rgb(0 0 0 / 12%);
    border: solid 1px #03868b;
    background-color: #ffffff;
}

 .calendar-container .rdrCalendarWrapper {
    border-radius: none;
    box-shadow: none;
    border: none;
    background-color: transparent;
    width: 288px;
}
.calendar-container .rdrCalendarWrapper {
    color: #000000;
    font-size: 12px;
}
 .calendar-container .rdrCalendarWrapper {
    box-sizing: border-box;
    background: #ffffff;
    display: inline-flex;
    flex-direction: column;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}
.calendar-container .navigation {
    position: relative;
    margin: 28px 10px 0px 24px;
    display: flex;
    align-items: center;
}

.calendar-container .rdrMonths {
    display: flex;
}

.calendar-container .rdrMonth {
    width: 300px;
    margin: 10px 17px 13px;
    padding: 0;
}
.calendar-container .rdrMonth {
    position: relative;
}
 .calendar-container .rdrMonth {
    padding: 0 0.833em 1.666em 0.833em;
}
.calendar-container .rdrMonth {
    width: 27.667em;
}

.calendar-container .rdrMonth {
    overflow: hidden;
    border-top: 1px solid #33435b73;
    width: 100%;
    margin: 0 0 24px;
    padding: 0;
    height: auto !important;
}

.rdrMonthsVertical {
    flex-direction: row;
}
.rdrMonths {
    display: flex;
}
.rdrMonth {
    padding: 0;
}

.rdrMonth {
    width: 300px;
}

.rdrMonth .rdrWeekDays {
    padding: 0;
}
.rdrWeekDays {
    padding: 0 0.833em;
}
.rdrWeekDays {
    display: flex;
}
.rdrWeekDay {
    font-weight: 400;
    line-height: 2.667em;
    color: rgb(132, 144, 149);
}
.rdrWeekDay {
    flex-basis: calc(100% / 7);
    box-sizing: inherit;
    text-align: center;
}

.rdrDays {
    display: flex;
    flex-wrap: wrap;
    /* margin: 16px 10px 10px; */
}
.rdrDesktopDays {
    display: flex;
    flex-wrap: wrap;
    margin: 16px 10px 10px;
}
.rdrDay {
    background: transparent;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    border: 0;
    padding: 0;
    line-height: 3.000em;
    height: 3em;
    text-align: center;
    color: #1d2429;
}
.rdrDay {
    box-sizing: inherit;
    width: calc(100% / 7);
    position: relative;
    font: inherit;
    cursor: pointer;
}
.rdrDayNumber {
    outline: 0;
    font-weight: 300;
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    top: 5px;
    bottom: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
}
.rdrDayNumber {
    display: block;
    position: relative;
    top: 0;
    bottom: 0;
    right: 0;
    left: 0;
    z-index: 4;
}

.rdrSelected{
    color: #4d4d4f;
    border-radius: 0;
    background: transparent url('../../assets/images/calendar/selectedIcon.svg');
    background-size: 100% 100%;
    background-position: center;
    position: absolute;
    top: 0;
    bottom: 0;
    right: 0px !important;
    left: 0px !important;
    z-index: 4;
    font-weight: bold !important;
}
.month-navigation-container {
    display: block;
    width: 260px;
    border-left: 1px solid #ebebec;
}
.row-container {
    margin: 24px 18px 8px 18px;
}
.row{
    display: flex;
    margin-top: 16px;
    height: 36px;
}
.button-container {
    flex-basis: 25%;
}


.calendar-container.mobile {
    min-width: 100%;
    padding-bottom: 70px;
    --heightA: calc(70vw - 20px);
    --heightB: calc(var(--heightA) / 7);
    height: calc((((100vh - 62px) - 10px) - 68px) - 5px);
}

.calendar-container.mobile .rdrCalendarWrapper {
    width: 100%;
    border: none;
    box-shadow: none;
}

.mobileRdrWeekDays {
    padding: 0 !important;
    margin: 0 auto !important;
    width: 100%;
    border-bottom: 1px solid #ebebec;
    z-index: 1000;
    position: relative;
    background-color: white;
    display: flex;
}
.mobileRdrWeekDay {
    font-weight: normal;
    font-size: 14px;
    line-height: 20px;
    letter-spacing: 0.3px;
    color: #4d4d4f;
    padding: 0.7em 0.3em;
    flex-basis: calc(100% / 7);
    box-sizing: inherit;
    text-align: center;
}
.rdrInfiniteMonths{
    height: calc(100vh - 335px);
    margin-top: -2px;
    overflow: auto;

}
.calendar-container .mobile .rdrInfiniteMonths .rdrCalendarWrapper  ::-webkit-scrollbar{
    width: 0px !important;
    /* width: 1em; */
background: red;
}
.rdMonthMobile {
    overflow: hidden;
    border-top: 1px solid #33435b73;
    width: 100%;
    margin: 0 0 24px;
    padding: 0;
    height: auto !important;
}

.rdrMonthMobileName{
    text-align: center;
    font-weight: 700;
    font-family: Montserrat;
    color: #4d4d4f;
    font-size: 14px;
    padding: 0.6em 0;
    line-height: 1.43;
    letter-spacing: 0.3px;
    text-align: left;
    font-size: 14px;
    line-height: 20px;
    display: block;
    margin-top: 10px;
}
.rdrMobileDays{
    height: var(--heightB) !important;
    line-height: var(--heightB);
    flex: 1 1 var(--widthB);
    font-size: 16px !important;
    line-height: 24px;
    letter-spacing: 0.3px;
    /* border: 2px solid transparent !important; */
}

.rdrStartDate {
    border-radius: 0;
    background: transparent url('../../assets/images/DateRangeCalendar/startDate.svg');
    background-size: 100% 100%;
    background-position: center;
    position: absolute;
    top: 0;
    bottom: 0;
    right: 0px !important;
    left: 0px !important;
    z-index: 4 !important;
    font-weight: bold !important;
}

.rdrEndDate {
    border-top-right-radius: 1.042em;
    border-bottom-right-radius: 1.042em;
    right: 2px;

    border-radius: 0;
    background: transparent url('../../assets/images/DateRangeCalendar/endDate.svg');
    background-size: 100% 100%;
    background-position: center;
    position: absolute;
    top: 0;
    bottom: 0;
    right: 0px !important;
    left: 0px !important;
    z-index: 4 !important;
    font-weight: bold !important;
}
