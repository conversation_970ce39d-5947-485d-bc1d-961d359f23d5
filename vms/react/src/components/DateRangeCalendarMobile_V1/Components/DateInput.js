import React from "react";
import styled from "styled-components";
import { formatWithLocale } from "../../../hooks/calendarHooks";
import PropTypes from "prop-types";


function borderBottom(isMobile,theme,type) {
  if (isMobile) {
    return "1px solid #03868b";
  } else {
    return "1px solid #4d4d4f";
  }
}

const DateInputContainer = styled.div`
  position: relative;
  display: inline-block;
  width: 100%;
`;
const Label = styled.label`
  font-weight: 400;
  font-family: ${({ theme }) => theme?.typography?.fontFamily || ""};
  font-size: ${({ theme }) => theme?.typography?.text?.fontSize || ""};
  font-stretch: normal;
  font-style: normal;
  line-height: 20px;
  letter-spacing: 0.3px;
  color: #4d4d4f;
  display: block;
  margin: 12px 0;
  position: relative;
  text-transform: uppercase;
`;

const InputContainer = styled.input`
  font-weight: 700;
  font-family: ${({ theme }) => theme?.typography?.fontFamily || ""};
  font-size: 16px;
  font-stretch: normal;
  font-style: normal;
  line-height: 22px;
  letter-spacing: 0.3px;
  color: #2a3c4d;
  display: block;
  width: 100%;
  border: none;
  border-bottom: 1px solid #4d4d4f;
  margin-top: 12px;
  background-color: #ffffff;
  height: auto;

  &::placeholder {
    font-weight: 700;
    font-family: ${({ theme }) => theme?.typography?.fontFamily || ""};
    font-size: ${({ theme }) => `${theme?.typography?.text?.fontSize}` || ""};
    font-stretch: normal;
    font-style: normal;
    line-height: 1.43;
    border-radius: 3px;
    letter-spacing: 0.3px;
    color: #4d4d4f;
  }

  &:focus {
    border-bottom: ${({ theme, calendarType }) =>(`solid 1px ${theme?.palette?.[calendarType]?.main}`) || ""};
    color: #4d4d4f;
    outline: none;
  }
`;

const DateInput = (props) => {
  const {
    inputClick,
    editting,
    fromDate,
    toDate,
    formatDateInput,
    inputBoxLabel,
    selectedDate,
    selectDateChange,
    customInputView,
    placeholder,
    calendarType,
    showSelectedDate,id
  } = props;
  const FormattedSelectedDate = selectedDate && showSelectedDate
    ? formatWithLocale(selectedDate, formatDateInput)
    : "";
  return (
    <>
      <DateInputContainer className={`vms_DateRangeCalendarMobile_InputContainer`} {...props}>
        {inputBoxLabel ? <Label id={props.id? props.id+"-label" : null} className={`vms_DateRangeCalendarMobile_Input_lable`}>{inputBoxLabel}</Label> : ""}
        <InputContainer
        id={props.id? props.id : null}
          type="date-text"
          editting={editting}
          value={FormattedSelectedDate}
          onClick={(e) => inputClick(e)}
          readOnly
          isMobile={props.isMobile}
          placeholder={placeholder}
          calendarType={calendarType}
          className={`vms_DateRangeCalendarMobile_Input`}
        />
      </DateInputContainer>
    </>
  );
};

DateInput.defaultProps = {
  inputClick: () => {},
};

DateInput.propTypes = {
  inputClick: PropTypes.func,
  placeholder: PropTypes.string,
  id:PropTypes.string
};

export { DateInput };
