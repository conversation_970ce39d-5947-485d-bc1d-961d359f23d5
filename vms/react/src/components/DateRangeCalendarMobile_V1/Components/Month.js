import React, { PureComponent } from "react";
import DayCell from "./DayCell";
import {
  format,
  startOfWeek,
  endOfWeek,
  isWithinInterval,
  eachDayOfInterval,
  isToday,
  isSameMonth,
  isBefore,
  isAfter,
} from "date-fns";
import {
  getMonthDisplayRange,
  // whetherDisabled,
  whetherSameDay,
} from "../../../hooks/calendarHooks";
import '../style.css'
import styled from "styled-components";
import {isWeekend} from 'date-fns'

const WeekDayContainer = styled.div`
  width: 100%;
  box-sizing: border-box;
  padding: 0px;
  display: flex;
`;

const WeekDayLabel = styled.span`
  font-size: 12px;
  line-height: 1.4;
  letter-spacing: normal;
  text-align: center;
  color: #4d4d4f;
  font-weight: 400;
  font-family: Montserrat;
  flex-basis: calc(100% / 7);
  box-sizing: inherit;
  text-align: center;
  font-weight: 400;
  line-height: 2.667em;
  padding: 0.7em 0.3em;
`;

const MonthNameLabel = styled.div`
  text-align: left;
  font-weight: 700;
  font-family: ${({ theme }) => theme?.typography?.fontFamily || ""};
  color: #4d4d4f;
  font-size: 14px;
  padding: 0.6em 0;
  line-height: 1.43;
  letter-spacing: 0.3px;
  margin-top: 10px;
`;

function renderWeekdays(dateOptions, weekdayDisplayFormat) {
  const now = new Date();

  return (
    <WeekDayContainer className="vms_Calendar_weekdaysContainer">
      {eachDayOfInterval({
        start: startOfWeek(now, dateOptions),
        end: endOfWeek(now, dateOptions),
      }).map((day, i) => (
        <WeekDayLabel className="vms_Calendar_weekdaysText" key={i}>
          {format(day, weekdayDisplayFormat)}
        </WeekDayLabel>
      ))}
    </WeekDayContainer>
  );
}

function whetherDisabled(day, month, minDate, maxDate,currentSelection) {
      if (day && isToday(day)) {
        return false;
      }
      if (minDate && isBefore(day, minDate)) {
        return true;
      }
      if (maxDate && isAfter(day, maxDate)) {
        return true;
      }
      return false;
}
class Month extends PureComponent {
  render() {
    const now = new Date();
    const {
      selectedDate,
      newSelectedDate,
      isDateChange,
      showWeekDays,
      Monthstyle,
      isMobile,
      preview,
      onDayClick = () => {},
      id
    } = this.props;
    this.dateOptions = { locale: "en-US" };
    const monthDisplay = getMonthDisplayRange(
      this.props.month,
      this.dateOptions,
      this.props.fixedHeight
    );


    let ranges = this.props.ranges;




    return (
      <div
        className={`${"rdrMonth vms_DateRangeCalendarMobile_WeekdayLabel"}`}
        style={Monthstyle}
      >
        {this.props.showMonthName ? (
          <MonthNameLabel className={"vms_DateRangeCalendarMobile_monthNameText"}>
            {format(this.props.month, "MMM yyyy", this.props.dateOptions)}
          </MonthNameLabel>
        ) : null}
        {showWeekDays && renderWeekdays(this.dateOptions, "eeeee")}
        <div
          className={`${
            "rdrDays  vms_DateRangeCalendarMobile_rdrDaysContainer"
          }`}
        >
          {eachDayOfInterval({
            start: monthDisplay.start,
            end: monthDisplay.end,
          }).map((day,i) => {
            const isStartOfMonth = whetherSameDay(
              day,
              monthDisplay.startDateOfMonth
            );
            const isEndOfMonth = whetherSameDay(
              day,
              monthDisplay.endDateOfMonth
            );
            const isDisabledDay = whetherDisabled(
              day,
              monthDisplay.startDateOfMonth,
              this.props.minDate,
              this.props.maxDate
            );
            return (
              <div key={i}>
                <DayCell
                id={id? id : null}
                day={day}
                ranges={ranges}
                isWeekend={isWeekend(day, this.dateOptions)}
                isToday={whetherSameDay(day, now)}
                isStartOfWeek={whetherSameDay(
                  day,
                  startOfWeek(day, this.props.dateOptions)
                )}
                isEndOfWeek={whetherSameDay(
                  day,
                  endOfWeek(day, this.props.dateOptions)
                )}
                disabled={isDisabledDay}
                selectedDate={selectedDate}
                isStartOfMonth={isStartOfMonth}
                isEndOfMonth={isEndOfMonth}
                isPassive={
                  !isWithinInterval(day, {
                    start: monthDisplay.startDateOfMonth,
                    end: monthDisplay.endDateOfMonth,
                  })
                }
                isMobile={isMobile}
                newSelectedDate={newSelectedDate}
                isDateChange={isDateChange}
                onDayClick={(val) => {
                  onDayClick(val);
                }}
                preview={preview}
                onMouseDown={this.props.onDragSelectionStart}
                onMouseUp={this.props.onDragSelectionEnd}
                onMouseEnter={this.props.onDragSelectionMove}
                onPreviewChange={this.props.onPreviewChange}
              />
              </div>
            );
          })}
        </div>
      </div>
    );
  }
}

export default Month;
