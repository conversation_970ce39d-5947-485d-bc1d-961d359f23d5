import React, { Component } from "react";
import styled from "styled-components";
import {
  endOfDay,
  format,
  isAfter,
  isBefore,
  isSameDay,
  startOfDay,
} from "date-fns";
import "../style.css";
import classNames from "classnames";
import StartDateSvg from "../../../assets/images/DateRangeCalendar/startDate.svg";
import EndDateSvg from "../../../assets/images/DateRangeCalendar/endDate.svg";

const DayButton = styled.button`
  background: transparent;
  user-select: none;
  border: 0;
  padding: 0;
  line-height: 3em;
  height: 3em;
  text-align: center;
  color: #1d2429;
  box-sizing: inherit;
  width: calc(100% / 7);
  position: relative;
  font: inherit;
  &:disabled {
    pointer-events: none;
    color: #d1d3d4 !important;
  }
  &:hover {
    background: ${({ isSelected }) => (!isSelected ? "#f1f6de" : "")};
  }
`;

const Item = styled.span`
  outline: 0;
  font-family: ${({ theme }) => theme?.typography?.fontFamily || ""};
  font-weight: ${({ isSelected }) => (isSelected ? "bold" : 300)};
  position: ${({ isSelected }) => (isSelected ? "relative" : "absolute")};
  left: 0;
  right: 0;
  top: ${({ isSelected }) => (isSelected ? "0px" : "5px")};
  bottom: 0;
  bottom: 5px;
  display: ${({ isPassive, isMobile }) =>
    isPassive && isMobile ? "none" : "flex"};
  align-items: center;
  justify-content: center;
  z-index: ${({ isSelected }) => (isSelected ? 4 : "")};
`;

const FormImage = styled.span`
  background-size: 100% 100%;
  background-position: center;
  position: absolute;
  top: 0;
  bottom: 0;
  right: 0px !important;
  left: 0px !important;
  z-index: 4 !important;
  font-weight: bold !important;
  border-radius: 0;
`;

class DayCell extends Component {
  constructor(props, context) {
    super(props, context);

    this.state = {
      hover: false,
      active: false,
    };
  }

  handleKeyEvent = (event) => {
    const { day, onMouseDown, onMouseUp } = this.props;
    if ([13 /* space */, 32 /* enter */].includes(event.keyCode)) {
      if (event.type === "keydown") onMouseDown(day);
      else onMouseUp(day);
    }
  };

  handleMouseEvent = (event) => {
    const {
      day,
      disabled,
      onPreviewChange,
      onMouseEnter,
      onMouseDown,
      onMouseUp,
    } = this.props;
    const stateChanges = {};
    if (disabled) {
      onPreviewChange();
      return;
    }

    switch (event.type) {
      case "mouseenter":
        onMouseEnter(day);
        onPreviewChange(day);
        stateChanges.hover = true;
        break;
      case "blur":
      case "mouseleave":
        stateChanges.hover = false;
        break;
      case "mousedown":
        stateChanges.active = true;
        onMouseDown(day);
        break;
      case "mouseup":
        event.stopPropagation();
        stateChanges.active = false;
        onMouseUp(day);
        break;
      // case "focus":
      //   onPreviewChange(day);
      //   break;
    }
    if (Object.keys(stateChanges).length) {
      this.setState(stateChanges);
    }
  };

  renderSelectionPlaceholders = () => {
    const { ranges, day } = this.props;

    const inRanges = ranges.reduce((result, range) => {
      let startDate = range.startDate;
      let endDate = range.endDate;
      if (startDate && endDate && isBefore(endDate, startDate)) {
        [startDate, endDate] = [endDate, startDate];
      }
      startDate = startDate ? endOfDay(startDate) : null;
      endDate = endDate ? startOfDay(endDate) : null;
      const isInRange =
        (!startDate || isAfter(day, startDate)) &&
        (!endDate || isBefore(day, endDate));
      const isStartEdge = !isInRange && isSameDay(day, startDate);
      const isEndEdge = !isInRange && isSameDay(day, endDate);
      if (isInRange || isStartEdge || isEndEdge) {
        return [
          ...result,
          {
            isStartEdge,
            isEndEdge: isEndEdge,
            isInRange,
            ...range,
          },
        ];
      }
      return result;
    }, []);
    return inRanges;
  };

  render() {
    const { disabled, day, newSelectedDate, isMobile, isPassive, onDayClick } =
      this.props;

    const selectionPlaceholder = this.renderSelectionPlaceholders();
    const isStartEdge =
      selectionPlaceholder &&
      selectionPlaceholder.length > 0 &&
      selectionPlaceholder[0].isStartEdge;
    const isEndEdge =
      selectionPlaceholder &&
      selectionPlaceholder.length > 0 &&
      selectionPlaceholder[0].isEndEdge;
    return (
      <DayButton
      id={this.props.id ? this.props.id+"-btn" : null}
        className={`${isMobile ? "rdrMobileDays vms_DateRangeCalendarMobile_DayButton" : ""}`}
        onClick={(e) => {
          e.preventDefault();
          onDayClick(day);
        }}
        disabled={disabled}
        onMouseEnter={this.handleMouseEvent}
        onMouseLeave={this.handleMouseEvent}
        onFocus={this.handleMouseEvent}
        onMouseDown={this.handleMouseEvent}
        onMouseUp={this.handleMouseEvent}
        onBlur={this.handleMouseEvent}
        onPauseCapture={this.handleMouseEvent}
        onKeyDown={this.handleKeyEvent}
        onKeyUp={this.handleKeyEvent}
      >
        {!disabled && selectionPlaceholder &&
          selectionPlaceholder.length > 0 &&
          selectionPlaceholder.map((item, i) => {
            return (
              <>
                {item.isStartEdge || item.isEndEdge ? (
                  <FormImage className="vms_DateRangeCalendarMobile_SelectedImageContainer" style={{ display: isPassive ? "none" : "" }}>
                    <img
                      src={item.isStartEdge ? StartDateSvg : EndDateSvg}
                      alt="plus"
                    />
                  </FormImage>
                ) : (
                  <span
                    key={i}
                    className={classNames({
                      ["rdrStartDate"]: item.isStartEdge,
                      ["rdrEndDate"]: item.isEndEdge,
                      ["rdrInRange"]: item.isInRange,
                    })}
                    style={{ display: isPassive ? "none" : "" }}
                  />
                )}
              </>
            );
          })}
        <Item
          className="vms_DateRangeCalendarMobile_DayButton"
          isSelected={isStartEdge || isEndEdge}
          isPassive={isPassive}
          isMobile={isMobile}
        >
          {
            <span className="vms_DateRangeCalendarMobile_dateItem">
              {format(this.props.day, "d")}
            </span>
          }
        </Item>
        {/* <Item></Item> */}
      </DayButton>
    );
  }
}

export default DayCell;
