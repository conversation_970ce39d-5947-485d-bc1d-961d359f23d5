import React, { useState } from "react";
import { addDay, getNextYear } from "../../hooks/calendarHooks";
import { Button } from "../Button/Button";
import { DateRangeCalendarMobile_V1 } from "./MobilDateRangeCalendar";

export default {
  title: "VMS_REACT/DateRangeCalendarMobile_V1",
  component: DateRangeCalendarMobile_V1,
  argTypes: {
    additionalStyle: { control: { type: "" } },
    additionalClassName: { control: { type: "" } },
    dateDisplayFormat: { control: { type: "" } },
    startDate: { control: { type: "" } },
    endDate: { control: { type: "" } },
    startDateLabelText: { control: { type: "" } },
    endDateLabelText: { control: { type: "" } },
    closeButtonText: { control: { type: "" } },
    minDate: { control: { type: "" } },
    maxDate: { control: { type: "" } },
    minBookingDays: { control: { type: "" } },
    maxBookingDays: { control: { type: "" } },
    startDatePlaceholder: { control: { type: "" } },
    endDatePlaceholder: { control: { type: "" } },

  },
};

// const Template = (args) => <DateRangeCalendarMobile_V1 {...args} />;

const DisplayCurrentState = (data) => {
  return (
    <div style={{ marginTop: "10px" }}>
      <h5>Callback Value</h5>
      <ul style={{ paddingLeft: "15px", paddingTop: "1px" }}>
        <li>Start Date : {data?.startDate.toString()}</li>
        <li>End Date :{data?.endDate.toString()}</li>
        {!data?.valid && (
          <>
            <li>Valid : {data?.valid.toString()}</li>
            <li>ErrorCode : {data?.errorCode}</li>
          </>
        )}
      </ul>
      <h5>Dates In Format</h5>
      <h5>Format 1</h5>
      <ul style={{ paddingLeft: "15px", paddingTop: "1px" }}>
        <li>{`Start Date : ${data?.datesInFormat?.format1?.startDate}`}</li>
        <li>{`End Date : ${data?.datesInFormat?.format1?.endDate}`}</li>
      </ul>
      <h5>Format 2</h5>
      <ul style={{ paddingLeft: "15px", paddingTop: "1px" }}>
        <li>{`Start Date : ${data?.datesInFormat?.format2.startDate}`}</li>
        <li>{`End Date : ${data?.datesInFormat?.format2?.endDate}`}</li>
      </ul>
    </div>
  );
};

const Template = (args) => {
  const [result, setResult] = useState();
  const [openCalendar, setOpenCalendar] = useState(false);
  return (
    <div>
      <Button
      id="btn"
        isMobile
        buttonType="primary"
        onClick={() => {
          setOpenCalendar(!openCalendar);
        }}
      >
        Open
      </Button>
      {openCalendar && (
        <DateRangeCalendarMobile_V1
          {...args}
          openCalendar={openCalendar}
          onCloseButtonClick={(result) => {
            console.log("result",result)
            setResult(result);
            setOpenCalendar(!openCalendar)
          }}
        />
      )}

      <>{result && !openCalendar ? DisplayCurrentState(result) : ""} </>
    </div>
  );
};

const InitialFocusApplication = (args) => {
  const [result, setResult] = useState();
  const [openCalendar, setOpenCalendar] = useState(false);
  const [showCallbackValue, setShowCallbackValue] = useState(false);
  const [initialFocusOn, setInitialFocusOn] = useState("startDate");
  return (
    <div>
      <div style={{ display: "flex", margin: "10px" }}>
        <Button
        id="start-btn-id"
          isMobile
          additionalStyle={{
            float: "left",
            margin: "10px 0px 10px 0",
            width: "48%",
          }}
          buttonType="primary"
          onClick={() => {
            setInitialFocusOn("startDate");
            setOpenCalendar(!openCalendar);
          }}
        >
          Open with start date
        </Button>
        <Button
        id="end-btn-id"
          isMobile
          additionalStyle={{
            float: "right",
            margin: "10px 0px 10px 0",
            width: "48%",
            marginLeft: "20px",
          }}
          buttonType="primary"
          onClick={() => {
            setInitialFocusOn("endDate");
            setOpenCalendar(!openCalendar);
          }}
          openCalendar={openCalendar}
        >
          Open with end date
        </Button>
      </div>

      {openCalendar && (
        <DateRangeCalendarMobile_V1
          additionalClassName={["date-range-positionRelative"]}
          {...args}
          onCloseButtonClick={(result) => {
            console.log("result",result)
            setResult(result);
            setShowCallbackValue(true);
            setOpenCalendar(false);
          }}
          openCalendar={openCalendar}
          initialFocusOn={initialFocusOn}
        />
      )}

      <div style={{ margin: "10px" }}>
        {showCallbackValue && !openCalendar ? DisplayCurrentState(result) : ""}{" "}
      </div>
    </div>
  );
};

export const Default = Template.bind({});
Default.args = {
  calendarType:"primary",
  closeButtonType:"primary",
  initialFocusOn:"startDate",
  openCalendar:false,
  mode:"multiDateWithLoop",
  id:"mob-dtc-id"
};

Default.parameters = {
  viewport: {
    defaultViewport: "iphone6",
  },
};
export const customLabels = Template.bind({});
customLabels.parameters = {
  viewport: {
    defaultViewport: "iphone6",
  },
};
customLabels.args = {
  viewport: {
    defaultViewport: "iphone5",
  },
  isOverlay: false,
  openButtonType: "primary",
  openCalendar: false,
  inputBoxLabel: "",
  inputPlaceholder: "",
  startDateLabelText: "CHECK-IN",
  endDateLabelText: "CHECK-OUT",
  closeButtonText: "CLOSE",
  closeButtonType: "tertiary",
  id:"cust-id"
};

export const DatePassByApplication = Template.bind({});
DatePassByApplication.parameters = {
  viewport: {
    defaultViewport: "iphone6",
  },
};
DatePassByApplication.args = {
  isOverlay: false,
  startDate: addDay(new Date(), 200),
  endDate: addDay(new Date(), 250),
  openButtonType: "primary",
  openCalendar: false,
  inputBoxLabel: "",
  inputPlaceholder: "",
  // openCalendar: true,
  startDateLabelText: "CHECK-IN",
  endDateLabelText: "CHECK-OUT",
  closeButtonText: "CLOSE",
  closeButtonType: "tertiary",
  id:"date-pass-id"
};

export const CustomDateFormat = Template.bind({});
CustomDateFormat.parameters = {
  viewport: {
    defaultViewport: "iphone6",
  },
};
CustomDateFormat.args = {
  dateDisplayFormat: "MMM dd yyyy",
  id:"cust-date-id"
};

export const InitialFocusByApplication = InitialFocusApplication.bind({});
InitialFocusByApplication.parameters = {
  viewport: {
    defaultViewport: "iphone6",
  },
};
InitialFocusByApplication.args = {
  initialFocusOn: "startDate",
};

export const DefaultViewWithoutLoopMode = Template.bind({});
DefaultViewWithoutLoopMode.parameters = {
  viewport: {
    defaultViewport: "iphone6",
  },
};
DefaultViewWithoutLoopMode.args = {
  mode: "multiDateWithoutLoop",
  startDate: addDay(new Date(), 1),
  endDate:undefined,
};

export const CustomDateFormatWithoutLoopMode = Template.bind({});
CustomDateFormatWithoutLoopMode.parameters = {
  viewport: {
    defaultViewport: "iphone6",
  },
};
CustomDateFormatWithoutLoopMode.args = {
  dateDisplayFormat: "MMM dd yyyy",
  mode: "multiDateWithoutLoop",
};

export const DatePassByApplicationWithoutLoopMode = Template.bind({});
DatePassByApplicationWithoutLoopMode.parameters = {
  viewport: {
    defaultViewport: "iphone6",
  },
};
DatePassByApplicationWithoutLoopMode.args = {
  startDate: addDay(new Date(), 10),
  endDate: addDay(new Date(), 15),
  dateDisplayFormat: "MMM dd yyyy",
  mode: "multiDateWithoutLoop",
};

export const InitialFocusByApplicationWithoutLoopMode =
  InitialFocusApplication.bind({});
  InitialFocusByApplicationWithoutLoopMode.parameters = {
    viewport: {
      defaultViewport: "iphone6",
    },
  };
InitialFocusByApplicationWithoutLoopMode.args = {
  initialFocusOn: "startDate",
  mode: "multiDateWithoutLoop",
};

export const MinAndMaxBookingDays = Template.bind({});
MinAndMaxBookingDays.parameters = {
  viewport: {
    defaultViewport: "iphone6",
  },
};
MinAndMaxBookingDays.args = {
  minBookingDays: 3,
  maxBookingDays: 20,
};

export const MinAndMaxBookingDaysWithoutLoopMode = Template.bind({});
MinAndMaxBookingDaysWithoutLoopMode.parameters = {
  viewport: {
    defaultViewport: "iphone6",
  },
};
MinAndMaxBookingDaysWithoutLoopMode.args = {
  minBookingDays: 3,
  maxBookingDays: 20,
  mode: "multiDateWithoutLoop",
};
