import React, { PureComponent } from "react";
import PropTypes from "prop-types";
import { DateInput } from "./Components/DateInput";
import {
  InputContainer,
  InputRighView,
  InputLefView,
  CalendarCloseButton,
} from "./Components/CalendarContainer";
import styled from "styled-components";
import ReactList from "react-list";
import {
  addDay,
  formatWithLocale,
  getNextYear,
  checkRangeIsValid,
  checkRangeErrorCode,
  whetherBetween,
  calcFocusDate,
  getMonthDisplayRange,
  findNextRangeIndex,
} from "../../hooks/calendarHooks";
import { Button } from "../Button/Button";
import Month from "./Components/Month";
import "./style.css";
import {
  addDays,
  addMonths,
  differenceInCalendarDays,
  differenceInCalendarMonths,
  eachDayOfInterval,
  endOfMonth,
  endOfWeek,
  format,
  isBefore,
  isSameDay,
  min,
  startOfMonth,
  startOfWeek,
} from "date-fns";
import { useClassName } from "../../hooks/useClassName";
import { ThemeWrapper } from "../Theme/ThemeContext";

const StyledInfiniteMonths = styled.div`
  height: calc(100vh - 335px);
  margin-top: -2px;
  overflow: auto;

  &::-webkit-scrollbar {
    width: 0;
    background: transparent;
  }
  &::-webkit-scrollbar-thumb {
    background: transparent;
  }
`;

class DateRangeCalendarMobile_V1 extends PureComponent {
  constructor(props, context) {
    super(props, context);
    this.dateOptions = { locale: "en-US" };
    this.listSizeCache = {};
    this.isFirstRender = true;
    this.state = {
      // focusedDate: calcFocusDate(null, props),
      scrollArea: this.calcScrollArea(props),
      editting: props.openCalendar,
      selectedDate: null,
      showPlaceholder: props.selectedDate ? false : true,
      isDateChange: false,
      newSelectedDate: null,
      ranges: [
        {
          startDate: props.startDate,
          endDate: props.endDate,
        },
      ],
      drag: {
        status: false,
        range: { startDate: null, endDate: null },
        disablePreview: false,
      },
      focusedRange: [0, 0],
      className: useClassName(props, props.additionalClassName),
      preview: null,
      whetherFirstInput: true,
      whetherEndDateSelected: false,
      initialFocusOn: props.initialFocusOn
    };
  }

  componentDidMount() {
    console.log("focusedDate", this.state.selectedDate);
    const { initialFocusOn } = this.props;
    this.setState(
      {
        selectedDate: calcFocusDate(null, this.props, this.state.ranges),
        newSelectedDate: this.props.selectedDate
          ? this.props.selectedDate
          : null,
        focusedRange: initialFocusOn === 'endDate' ? [0, 1] : [0, 0]
      },
      () => {
        this.focusToDate(this.state.selectedDate);
      }
    );
    // prevent react-list's initial render focus problem
  }


  componentDidUpdate(prevProps, prevState) {
    if (prevProps.startDate !== this.props.startDate) {
      console.log('something prop has changed.')
      this.setState({
        ranges: [{
          startDate: this.props.startDate,
          endDate: this.props.endDate,
        }]
      })
    }

    if (prevProps.endDate !== this.props.endDate) {
      console.log('something prop has changed.')
      this.setState({
        ranges: [{
          startDate: this.props.startDate,
          endDate: this.props.endDate,
        }]
      })
    }
  }



  estimateMonthSize = (index, cache) => {
    const { direction, minDate } = this.props;
    const { scrollArea } = this.state;
    if (cache) {
      if (cache[index]) return cache[index];
    }
    if (direction === "horizontal") return scrollArea.monthWidth;
    const monthStep = addMonths(minDate, index);
    const { start, end } = getMonthDisplayRange(monthStep, "en-us");
    const isLongMonth =
      differenceInCalendarDays(end, start, "en-us") + 1 > 7 * 5;
    return isLongMonth ? scrollArea.longMonthHeight : scrollArea.monthHeight;
  };

  focusToDate = (date, props = this.props) => {
    console.log("date", date, props.minDate);
    const targetMonthIndex = differenceInCalendarMonths(
      date,
      props.minDate,
      this.dateOptions
    );
    console.log("targetMonthIndex", targetMonthIndex, date);
    const visibleMonths = this.list.getVisibleRange();
    if (visibleMonths.includes(targetMonthIndex)) return;
    this.list.scrollTo(targetMonthIndex);
    this.setState({ selectedDate: date });
  };

  calcScrollArea = (props) => {
    const direction = "vertical";
    const months = 1;
    const scroll = {
      enabled: true,
    };
    if (!scroll.enabled) return { enabled: false };

    const longMonthHeight = scroll.longMonthHeight || scroll.monthHeight;
    if (direction === "vertical") {
      return {
        enabled: true,
        monthHeight: scroll.monthHeight || 220,
        longMonthHeight: longMonthHeight || 260,
        calendarWidth: "auto",
        calendarHeight:
          (scroll.calendarHeight || longMonthHeight || 240) * months,
      };
    }
    return {
      enabled: true,
      monthWidth: scroll.monthWidth || 332,
      calendarWidth:
        (scroll.calendarWidth || scroll.monthWidth || 332) * months,
      monthHeight: longMonthHeight || 300,
      calendarHeight: longMonthHeight || 300,
    };
  };

  renderWeekdays = () => {
    const now = new Date();
    return (
      <div className="mobileRdrWeekDays vms_DateRangeCalendarMobile_WeekdayContainer">
        {eachDayOfInterval({
          start: startOfWeek(now, "en-US"),
          end: endOfWeek(now, "en-US"),
        }).map((day, i) => (
          <span className="mobileRdrWeekDay vms_DateRangeCalendarMobile_WeekdayLabel" key={i}>
            {format(day, "eeeee", "en-US")}
          </span>
        ))}
        {/* <h1>sdsjdjsdh</h1> */}
      </div>
    );
  };

  calcNewSelection = (value, isSingleValue = true) => {
    const { ranges, focusedRange } = this.state;
    const { maxDate } = this.props;
    console.log("ranges", ranges);
    const focusedRangeIndex = focusedRange[0];
    const selectedRange = ranges[focusedRangeIndex];
    if (!selectedRange) return {};
    let { startDate, endDate } = selectedRange;
    const now = new Date();
    let nextFocusRange;
    if (!isSingleValue) {
      startDate = value.startDate;
      endDate = value.endDate;
    } else if (focusedRange[1] === 0) {
      // startDate selection
      const dayOffset = differenceInCalendarDays(endDate || now, startDate);
      const calculateEndDate = () => {
        return value || now;
      };
      startDate = value;
      endDate = calculateEndDate();
      if (maxDate) endDate = min([endDate, maxDate]);
      nextFocusRange = [focusedRange[0], 1];
    } else {
      endDate = value;
    }

    // reverse dates if startDate before endDate
    let isStartDateSelected = focusedRange[1] === 0;
    if (isBefore(endDate, startDate)) {
      isStartDateSelected = !isStartDateSelected;
      [startDate, endDate] = [endDate, startDate];
    }

    if (!nextFocusRange) {
      const nextFocusRangeIndex = findNextRangeIndex(ranges, focusedRange[0]);
      nextFocusRange = [nextFocusRangeIndex, 0];
    }
    return {
      range: { startDate, endDate },
      nextFocusRange: nextFocusRange,
    };
  };

  onPreviewChange = (date) => {
    // calcNewSelection(date ? date : null)

    if (!date) {
      this.setState({ preview: null });
      return;
    }
    // const { rangeColors, ranges } = this.props;
    // const color =
    //   ranges[focusedRange[0]]?.color || rangeColors[focusedRange[0]] || color;
    const newSelection = this.calcNewSelection(date);
    this.setState({
      preview: { ...newSelection.range },
    });
  };

  setSelection = (value, isSingleValue) => {
    const { ranges, focusedRange } = this.state;
    const focusedRangeIndex = focusedRange[0];
    const selectedRange = ranges[focusedRangeIndex];
    if (!selectedRange) return;
    const newSelection = this.calcNewSelection(value, isSingleValue);
    // onItemClick(value, [newSelection.range]);
    // if(isDayClicked){
    //   onItemClick(value)
    // }
    this.setState({
      ranges: [newSelection.range],
      focusedRange: newSelection.nextFocusRange,
    });
    // onRangeFocusChange && onRangeFocusChange(newSelection.nextFocusRange);
  };

  onItemClick = (date) => {
    const { initialFocusOn, whetherFirstInput, whetherEndDateSelected, ranges } = this.state;
    const { mode } = this.props;
    if (initialFocusOn === "startDate" && whetherFirstInput) {
      this.setState({
        whetherFirstInput: false,
      })
      if (
        mode === "multiDateWithoutLoop" &&
        whetherEndDateSelected &&
        !whetherBetween(date, ranges[0].startDate, ranges[0].endDate)
      ) {
        this.setState({
          initialFocusOn: 'startDate',
          whetherEndDateSelected: false
        })
      } else {
        this.setState({
          initialFocusOn: 'endDate',
          whetherEndDateSelected: false
        })
        // setWhetherEndDateSelected(true)
      }
    } else {
      this.setState({
        whetherFirstInput: true,
        whetherEndDateSelected: true,
        initialFocusOn: mode === "multiDateWithoutLoop"
          ? !whetherBetween(date, ranges[0].startDate, ranges[0].endDate)
            ? "startDate"
            : initialFocusOn
          : "startDate"
      })
      // setWhetherFirstInput(true);
      // setFromDate(fromDate);
      // setToDate(date);
      // setWhetherEndDateSelected(true);
      // setInitialFocusOn(
      //   mode === "multiDateWithoutLoop"
      //     ? !whetherBetween(date, fromDate, toDate)
      //       ? "startDate"
      //       : initialFocusOn
      //     : "startDate"
      // );
    }
  };

  onDragSelectionStart = (date) => {
    this.setState({
      drag: {
        status: true,
        range: { startDate: date, endDate: date },
        disablePreview: true,
      },
    });
    this.setSelection(date);
  };

  onDragSelectionEnd = (date) => {
    const { drag } = this.state
    const newRange = {
      startDate: drag.range.startDate,
      endDate: date,
    };
    if (isSameDay(newRange.startDate, date)) {
      this.setState({
        drag: {
          status: false,
          range: {},
          disablePreview: true,
        },
      });
    } else {
      this.setState({
        drag: {
          status: false,
          range: {},
          disablePreview: true,
        },
      });
      this.setSelection(date, false);
    }
  };

  onDragSelectionMove = (date) => {
    const { drag } = this.state
    if (!drag.status) return;
    this.setState({
      drag: {
        status: drag.status,
        range: { startDate: drag.range.startDate, endDate: date },
        disablePreview: true,
      },
    });
  };

  changeInitialFocus = (focusName, focusedRange) => {
    const { mode } = this.props;
    if (mode === "multiDateWithoutLoop") {
      this.setState({
        initialFocusOn: focusName,
        focusedRange: focusedRange
      })
    }
  };

  OnButtonCloseClick = () => {
    const { minBookingDays, maxBookingDays, onCloseButtonClick } = this.props;
    const { ranges, whetherEndDateSelected, editting } = this.state;
    let isValid = checkRangeIsValid(
      ranges[0].startDate,
      ranges[0].endDate,
      minBookingDays,
      maxBookingDays
    );
    console.log("props.endDate", this.props.endDate)
    let result = {
      startDate: ranges[0].startDate,
      endDate: whetherEndDateSelected ? ranges[0].endDate : ranges[0].endDate,
      valid: isValid,
      datesInFormat: {
        format1: {
          startDate: formatWithLocale(ranges[0].startDate, "yyyy-MM-dd'T'HH:mm:ss'Z'"),
          endDate: whetherEndDateSelected ? formatWithLocale(ranges[0].endDate, "yyyy-MM-dd'T'HH:mm:ss'Z'") : '',
        },
        format2: {}
      },
    };
    if (whetherEndDateSelected) {
      result.datesInFormat.format2 = {
        startDate: formatWithLocale(ranges[0].startDate, "dd-MM-yyyy"),
        endDate: formatWithLocale(ranges[0].endDate, "dd-MM-yyyy"),
      }
    }
    if (!isValid) {
      let errorCode = checkRangeErrorCode(
        ranges[0].startDate,
        ranges[0].endDate,
        minBookingDays,
        maxBookingDays
      );
      if (errorCode !== 0) {
        result.errorCode = errorCode;
      }
    }
    onCloseButtonClick(result);
    this.setState({
      editting: !editting
    })
  };


  render() {
    const {
      minDate,
      maxDate,
      maxBookingDays,
      mode,
      calendarType,
      startDatePlaceholder,
      endDatePlaceholder,
      startDateLabelText,
      endDateLabelText,
      dateDisplayFormat,
      additionalStyle,

    } = this.props;
    const { selectedDate, scrollArea, editting, ranges, drag, preview, focusedRange, whetherEndDateSelected, className } =
      this.state;
    console.log("scrollArea", scrollArea, ranges, this.props, selectedDate);
    return (
      <ThemeWrapper isMobile={true}>
        <div className={`${className} calendar-container  mobile vms_DateRangeCalendarMobile_CalendarContainer`} style={additionalStyle} >
          {editting && ranges && (
            <InputContainer
              className={`vms_DateRangeCalendarMobile_MainInputcontainer`}
            >
              <InputLefView
                className={`vms_DateRangeCalendarMobile_LeftInputView`}
              >
                <DateInput
                id={this.props.id ? this.props.id : null}
                  editting={editting}
                  fromDate={ranges[0].startDate}
                  toDate={ranges[0].endDate}
                  selectedDate={ranges[0].startDate}
                  mode={mode}
                  calendarType={calendarType}
                  showSelectedDate={true}
                  placeholder={startDatePlaceholder}
                  inputBoxLabel={startDateLabelText}
                  formatDateInput={dateDisplayFormat}
                  inputClick={() => this.changeInitialFocus("startDate", [0, 0])}
                />
              </InputLefView>
              <InputRighView
                className={`vms_DateRangeCalendarMobile_RightnputView`}
              >
                <DateInput
                  editting={editting}
                  fromDate={ranges[0].startDate}
                  calendarType={calendarType}
                  toDate={ranges[0].endDate}
                  showSelectedDate={whetherEndDateSelected}
                  selectedDate={ranges[0].endDate}
                  placeholder={endDatePlaceholder}
                  mode={mode}
                  inputBoxLabel={endDateLabelText}
                  formatDateInput={dateDisplayFormat}
                  inputClick={() => this.changeInitialFocus("endDate", [0, 1])}
                />
              </InputRighView>
            </InputContainer>
          )}

          {editting && scrollArea && (
            <div id="calendarPicker">
              <div className="rdrCalendarWrapper vms_DateRangeCalendarMobile_rdrCalendarWrapper">
                <div>
                  {this.renderWeekdays()}

                  <StyledInfiniteMonths
                    style={{
                      height: `calc(((((100vh - 62px) - 10px) - 68px) - 5px) - 109px)`,
                    }}
                  >
                    <ReactList
                      length={differenceInCalendarMonths(
                        endOfMonth(maxDate),
                        addDays(startOfMonth(minDate), -1),
                        this.dateOptions
                      )}
                      treshold={500}
                      type="variable"
                      axis={"y"}
                      ref={(target) => (this.list = target)}
                      itemSizeEstimator={this.estimateMonthSize}
                      // itemSizeEstimator={estimateMonthSize}
                      itemRenderer={(index, key) => {
                        const monthStep = addMonths(minDate, index);
                        return (
                          <Month
                         id={this.props.id ? this.props.id : null}
                            key={index}
                            month={monthStep}
                            selectedDate={selectedDate}
                            fixedHeight={false}
                            // onDayClick={(value) => {
                            //   this.onDateClick(
                            //     value,
                            //     isMobile ? false : true,
                            //     "mobile"
                            //   );
                            // }}
                            ranges={ranges}
                            maxBookingDays={maxBookingDays}
                            showMonthName
                            isMobile={true}
                            Monthstyle={{
                              height: this.estimateMonthSize(index),
                            }}
                            minDate={minDate}
                            maxDate={maxDate}
                            drag={drag}
                            onDragSelectionStart={this.onDragSelectionStart}
                            onDragSelectionEnd={this.onDragSelectionEnd}
                            onDragSelectionMove={this.onDragSelectionMove}
                            onMouseLeave={this.onPreviewChange}
                            onPreviewChange={this.onPreviewChange}
                            focusedRange={focusedRange}
                            preview={preview}
                            // onMouseLeave={() => onPreviewChange && onPreviewChange()}
                            onDayClick={(value) => {
                              this.onItemClick(value);
                            }}
                          />
                        );
                      }}
                    />
                  </StyledInfiniteMonths>
                </div>
              </div>
            </div>
          )}

          {editting && (
            <CalendarCloseButton className={`vms_Calendar_CloseButtonContainer`}>
              <Button
                id={this.props.id ? this.props.id + "-btn"  : null}
                buttonType={this.props.closeButtonType}
                additionalStyle={{
                  maxWidth: "100%",
                  margin: "10px 0px 10px 0",
                }}
                onClick={(e) => this.OnButtonCloseClick()}
              >
                {this.props.closeButtonText}
              </Button>
            </CalendarCloseButton>
          )}
        </div>
      </ThemeWrapper>
    );
  }
}

DateRangeCalendarMobile_V1.defaultProps = {
  minDate: new Date(),
  maxDate: getNextYear(1),
  startDate: new Date(),
  endDate: addDay(new Date(), 2),
  onCloseButtonClick: () => { },
  dateDisplayFormat: "dd MMM yyyy",
  additionalClassName: null,
  additionalStyle: null,
  calendarType: "primary",
  minBookingDays: 1,
  maxBookingDays: 30,
  openCalendar: false,
  startDateLabelText: "Check In",
  endDateLabelText: "Check Out",
  closeButtonText: "Done",
  closeButtonType: "primary",
  initialFocusOn: "startDate",
  mode: "multiDateWithLoop",
  focusedRange: [0, 0],
  displayMode: "dateRange",
  startDatePlaceholder: "Select checkin date",
  endDatePlaceholder: "Select chekout date",
};

DateRangeCalendarMobile_V1.propTypes = {
  /**
   * Variations of Calendar Type
   */
  calendarType: PropTypes.oneOf(["primary", "secondary"]),
  id:PropTypes.string,

  /**
   * Start Date label text. it will take Check In as default value.
   */
  startDateLabelText: PropTypes.string,
  /**
   * End Date label text. it will take Check Out as default value.
   */
  endDateLabelText: PropTypes.string,
  /**
   * Close button text. it will take Done as default value.
   */
  closeButtonText: PropTypes.string,
  /**
   *Close button type. it will take primary as default value.
   */

  closeButtonType: PropTypes.oneOf([
    "primary",
    "secondary",
    "link",
    "tertiary",
  ]),
  /**
   * Range start Date.
Date
   */

  startDate: PropTypes.instanceOf(Date),

  /**
   * Range End Date.
Date
   */
  endDate: PropTypes.instanceOf(Date),

  /**
   * Variations of Open Button Type
   */
  initialFocusOn: PropTypes.oneOf(["startDate", "endDate"]),

  /**
   * If I want to open calendar pass true.
   */
  openCalendar: PropTypes.bool,

  /**
   * 	
Defines minimum date. Disabled earlier dates. it will take current Date as default value.

Date
   */
  minDate: PropTypes.instanceOf(Date),

  /**
   * Defines maximum date. Disabled later dates it will take current Date + 1 year as default value.
    Date
   */
  maxDate: PropTypes.oneOfType([PropTypes.instanceOf(Date)]),

  /**
   * 
    callback function for range changes when @property mode is multiDateRange.
it will return {
   startDate: Date,
   endDate: Date,
   valid: boolean,
   errorCode?: number (optional),
   datesInFormat: {
     format1 (ISO format): {
       startDate: string;
       endDate: string;
     };
     format2 (DD-MM-YYYY): {
       startDate: string;
       endDate: string;
     };
   };
}
if valid = false then it will return errorCode.
errorCode values
1 = When start and end Date same
2 = When selected range is less than minBookingDays
3 = When selected range is more than maxBookingDays

(result: DateRangeCalendarOutput) => void
   */
  onCloseButtonClick: PropTypes.func,

  /**
   * Displayed Date format . Default value dd MMM yyyy
Please refer https://date-fns.org/v2.12.0/docs/format for various allowed format

string
   */
  dateDisplayFormat: PropTypes.string,

  /**
   *Inline styles to add additional styling in the parent container
CSSProperties
   */
  additionalStyle: PropTypes.object,
  /**
   * Classes to add additional styling in the parent container
string[]
   */

  additionalClassName: PropTypes.arrayOf(PropTypes.string),
  /**
   * Calendar Mode
multiDateWithLoop - After selection of one date it will automatically move focus to other date input box
multiDateWithoutLoop - After selection of the start date it will automatically move focus to the end date input box. The focus will remain on the end date until the user explicitly clicks on the start date input box
"multiDateWithLoop" | "multiDateWithoutLoop"
* 
   */
  mode: PropTypes.oneOf(["multiDateWithLoop", "multiDateWithoutLoop"]),

  /**
   *  Minimum days differece between start and end date. it will take 1 as default value.
number
   */
  minBookingDays: PropTypes.number,
  /**
   *  Maximum days differece between start and end date. it will take 30 as default value.
number
 */
  maxBookingDays: PropTypes.number,

  /**
   *   Place holder text for start date
   */
  startDatePlaceholder: PropTypes.string,

  /**
   *   Place holder text for start date
   */
  endDatePlaceholder: PropTypes.string,
};

export { DateRangeCalendarMobile_V1 };
