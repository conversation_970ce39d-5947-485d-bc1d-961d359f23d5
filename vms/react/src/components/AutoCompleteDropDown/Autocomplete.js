import React, { useState, useEffect, Fragment, useRef, memo, useCallback, useMemo } from "react";
import PropTypes from "prop-types";
import { MainContainer, InputContainer, MenuContainer, DropDownItem, CloseIcon } from "./Autocomplete.styled"
import { InputText } from "../InputText/InputText";
import closeIcon from "../../assets/images/Autocomplete/close.svg"
import downArrowSvg from "../../assets/images/PaxSelection/dropdown_down_icon.svg"
import upArrowSvg from "../../assets/images/PaxSelection/dropdown_up_icon.svg"
import searchIcon from "../../assets/images/Autocomplete/search.svg"
import { useClassName } from "../../hooks/useClassName";
import { useOnClickOutside } from "../../hooks/outsideClickHandler";
import { ThemeWrapper } from "../Theme/ThemeContext";

// Utility function (placed before main component)
const isNonNull = (prop) => prop !== null && prop !== undefined;

// Memoized MenuComponent
const MenuComponent = memo(({ renderMenu, filteredItems }) => (
  <Fragment>{renderMenu(filteredItems)}</Fragment>
));

MenuComponent.displayName = 'MenuComponent';

/**
 * Autocomplete UI component for user interaction
 */
const AutocompleteDropdown = memo((props) => {
  // 1. PROP DESTRUCTURING & VALIDATION
  const {
    items = [],
    value,
    onChange,
    onSelect,
    shouldItemRender,
    isItemSelectable,
    getItemValue,
    renderItem,
    renderMenu,
    menuStyle,
    inputProps,
    wrapperProps,
    wrapperStyle,
    autoHighlight = false,
    onMenuVisibilityChange,
    open,
    additionalClassName,
    additionalStyle,
    inputStyle,
    autoFocus,
    isDropdown = false,
    isMobileView,
    isMobile,
    showClearIcon = false,
    onClearIconClick,
    showSearchIcon = false,
    showUpDownIcon = false,
    leftIcon,
    leftIconClassName,
    leftIconStyle,
    label,
    secondaryLabel,
    disabled = false,
    secondPlaceHolder,
    customUpArrow,
    customDownArrow,
    leftView,
    rightView,
    name,
    onFocus,
    onBlur,
    onKeyDown,
    tabIndex,
    id,
    ...otherProps
  } = props;

  // 2. REFS
  const dropdownref = useRef();
  const inputRef = useRef();

  // 3. STATE MANAGEMENT
  const [optionArr, setOptionArr] = useState(items);
  const [inputVal, setInputVal] = useState(value || "");
  const [isOpen, setIsOpen] = useState(open || false);
  const [isItemChange, setIsItemChange] = useState(true);

  // 4. PERFORMANCE OPTIMIZATIONS
  const computedClassName = useMemo(() => 
    useClassName(props, additionalClassName), 
    [props, additionalClassName]
  );

  const leftIconComputedClassName = useMemo(() => 
    useClassName(props, leftIconClassName), 
    [props, leftIconClassName]
  );

  const isMobileDevice = useMemo(() => 
    isMobile || isMobileView || false, 
    [isMobile, isMobileView]
  );

  // Utility function for escaping regex characters
  const pregQuote = useCallback((str, delimiter) => {
    return (str + '').replace(new RegExp('[.\\\\+*?\\[\\^\\]$(){}=!<>|:\\' + (delimiter || '') + '-]', 'g'), '\\$&');
  }, []);

  // 5. EVENT HANDLING with useCallback
  const onItemClick = useCallback(async (option, id) => {
    try {
      const isSelectable = isItemSelectable ? await isItemSelectable(option) : true;
      
      const element = document.getElementById(`vms_dd_item${id}`);
      if (element) {
        element.style.pointerEvents = isSelectable ? "auto" : "none";
      }

      if (!isSelectable) return;

      const selectedValue = getItemValue ? await getItemValue(option) : option;
      setInputVal(selectedValue);
      
      if (onSelect) {
        await onSelect(selectedValue, option);
      }
      
      setIsOpen(false);
    } catch (error) {
      console.error('Error in onItemClick:', error);
    }
  }, [isItemSelectable, getItemValue, onSelect]);

  const onInputChange = useCallback((e) => {
    const newValue = e.target.value;
    const shouldOpen = open != null ? open : true;
    
    setIsOpen(shouldOpen);
    setIsItemChange(false);
    
    if (onChange) {
      onChange(e, newValue);
    }
  }, [onChange, open]);

  const handleInputFocus = useCallback((e) => {
    if (onFocus) {
      onFocus(e);
    } else {
      setIsOpen(true);
    }
  }, [onFocus]);

  const handleInputBlur = useCallback((e) => {
    if (onBlur) {
      onBlur(e);
    }
  }, [onBlur]);

  const handleClearIconClick = useCallback(() => {
    if (onClearIconClick) {
      onClearIconClick();
    }
  }, [onClearIconClick]);

  const handleUpDownIconClick = useCallback((e) => {
    e.stopPropagation();
    setIsOpen(prev => !prev);
  }, []);

  const handleContainerClick = useCallback(() => {
    if (!disabled) {
      setIsOpen(true);
    }
  }, [disabled]);

  // 6. OUTSIDE CLICK HANDLER
  useOnClickOutside(dropdownref, useCallback(() => {
    setIsOpen(false);
  }, []));

  // 7. EFFECTS
  useEffect(() => {
    if (onMenuVisibilityChange) {
      onMenuVisibilityChange(isOpen);
    }
  }, [isOpen, onMenuVisibilityChange]);

  useEffect(() => {
    setIsOpen(open || false);
  }, [open]);

  useEffect(() => {
    if (isItemChange) {
      setOptionArr(items);
    }
  }, [items, isItemChange]);

  useEffect(() => {
    setInputVal(value || "");
  }, [value]);

  // 8. CONDITIONAL RENDERING (memoized)
  const filteredItems = useMemo(() => {
    return optionArr
      .filter((item) => shouldItemRender ? shouldItemRender(item, inputVal) : true)
      .map((option, id) => {
        let highlightedOption = option;
        if (autoHighlight && !renderItem && inputVal) {
          const escapedInput = pregQuote(inputVal);
          highlightedOption = String(option).replace(
            new RegExp("(" + escapedInput + ")", 'i'), 
            "<b>$1</b>"
          );
        }
        
        return (
          <div 
            key={`${option}-${id}`} 
            onClick={() => onItemClick(option, id)} 
            onMouseDown={(e) => e.preventDefault()}
            className={`${inputVal === option ? "selected_option" : ""}`}
            role="option"
            aria-selected={inputVal === option}
          >
            {renderItem ? 
              renderItem(highlightedOption) :
              <DropDownItem 
                id={`vms_dd_item${id}`} 
                dangerouslySetInnerHTML={{ __html: highlightedOption }} 
              />
            }
          </div>
        );
      });
  }, [optionArr, shouldItemRender, inputVal, autoHighlight, renderItem, pregQuote, onItemClick]);

  const leftViewContent = useMemo(() => {
    if (showSearchIcon) {
      return (
        <CloseIcon 
          src={searchIcon} 
          align="left" 
          className="vms_autocompletedropdown_leftcloseicon"
          alt="Search"
        />
      );
    }
    if (leftIcon) {
      return (
        <CloseIcon 
          src={leftIcon} 
          align="left" 
          className={leftIconComputedClassName} 
          style={leftIconStyle}
          alt="Left icon"
        />
      );
    }
    return leftView || null;
  }, [showSearchIcon, leftIcon, leftView, leftIconComputedClassName, leftIconStyle]);

  const rightViewContent = useMemo(() => {
    if (showClearIcon) {
      return (
        <CloseIcon 
          src={closeIcon} 
          align="right" 
          onClick={handleClearIconClick}
          alt="Clear"
          role="button"
          tabIndex={0}
        />
      );
    }
    if (showUpDownIcon) {
      const iconSrc = isOpen ? 
        (customUpArrow || upArrowSvg) : 
        (customDownArrow || downArrowSvg);
      
      return (
        <CloseIcon 
          src={iconSrc} 
          align="right" 
          className="vms_autocompletedropdown_rightcloseicon"
          onClick={handleUpDownIconClick}
          alt={isOpen ? "Collapse" : "Expand"}
          role="button"
          tabIndex={0}
        />
      );
    }
    return rightView || null;
  }, [
    showClearIcon, 
    showUpDownIcon, 
    rightView, 
    isOpen, 
    customUpArrow, 
    customDownArrow, 
    handleClearIconClick, 
    handleUpDownIconClick
  ]);

  const menuContent = useMemo(() => {
    if (!isOpen) return null;

    if (isNonNull(renderMenu)) {
      return (
        <MenuComponent
          id="vms_menu_component"
          className="vms_autocompletedropdown_menu"
          renderMenu={renderMenu}
          filteredItems={filteredItems}
        />
      );
    }
    
    return (
      <MenuContainer
        className="vms_autocompletedropdown_menucontainer" 
        styles={menuStyle || ""}
        role="listbox"
        aria-label="Autocomplete options"
      >
        {filteredItems}
      </MenuContainer>
    );
  }, [isOpen, renderMenu, filteredItems, menuStyle]);

  // 9. ERROR HANDLING
  React.useEffect(() => {
    if (!Array.isArray(items)) {
      console.warn('AutocompleteDropdown: items prop should be an array');
    }
    if (typeof onChange !== 'function') {
      console.warn('AutocompleteDropdown: onChange prop should be a function');
    }
  }, [items, onChange]);

  // 10. RENDER
  return (
    <ThemeWrapper isMobile={isMobileDevice}>
      <MainContainer
        ref={dropdownref}
        {...wrapperProps}
        style={wrapperStyle}
        className={`${computedClassName} vms_autocompletedropdown_container`}
        disabled={disabled}
        role="combobox"
        aria-expanded={isOpen}
        aria-haspopup="listbox"
        {...otherProps}
      >
        <InputContainer 
          isMobileView={isMobileView} 
          className="vms_Autocompletedropdown_inputcontainer" 
          onClick={handleContainerClick}
        >
          <InputText
            ref={inputRef}
            className="vms_autocompletedropdown_inputtext"
            value={inputVal}
            secondPlaceHolder={secondPlaceHolder}
            onFocus={handleInputFocus}
            onChange={onInputChange}
            onKeyDown={onKeyDown}
            onBlur={handleInputBlur}
            additionalStyle={inputStyle}
            readOnly={isDropdown}
            tabIndex={tabIndex}
            isMobile={isMobileView}
            leftView={leftViewContent}
            rightView={rightViewContent}
            label={label}
            secondaryLabel={secondaryLabel}
            name={name}
            disabled={disabled}
            autoFocus={autoFocus}
            aria-autocomplete="list"
            aria-controls={isOpen ? "vms_menu_component" : undefined}
            {...inputProps}
            id={id}
          />
        </InputContainer>
        {menuContent}
      </MainContainer>
    </ThemeWrapper>
  );
});

// Add display name for better debugging
AutocompleteDropdown.displayName = 'AutocompleteDropdown';


AutocompleteDropdown.propTypes = {
    /**
     * The items to display in the dropdown menu
     * any[]
     */

    id: PropTypes.string,

    items: PropTypes.arrayOf(PropTypes.any).isRequired,//done

    /**
     * The value to display in the input field
     * any
     */
    value: PropTypes.any.isRequired,//done

    /**
     * Arguments: event: Event, value: String
     * Invoked every time the user changes the input's value.
     * (e: ChangeEvent<HTMLInputElement>, value: string) => void
     */
    onChange: PropTypes.func,//done

    /**
     * Arguments: value: String, item: Any
     * Invoked when the user selects an item from the dropdown menu.
     * (value: string, item: any) => void
     */
    onSelect: PropTypes.func,//done

    /**
     * Arguments: item: Any, value: String
     * Invoked for each entry in items and its return value is used to determine whether or not it should be displayed in the dropdown menu. By default all items are always rendered.
     * (item: any, value: string) => boolean
     */
    shouldItemRender: PropTypes.func,//done

    /**
     * Arguments: item: Any
     * Invoked when attempting to select an item. The return value is used to determine whether the item should be selectable or not. By default all items are selectable.
     * (item: any) => boolean
     */
    isItemSelectable: PropTypes.func,//done

    // /**
    //  * Arguments: itemA: Any, itemB: Any, value: String
    //  * The function which is used to sort items before display.
    //  * (a: any, b: any, value: string) => number
    //  */
    // sortItems: PropTypes.func,

    /**
     * Arguments: item: Any
     * Used to read the display value from each entry in items.
     * (item: any) => string
     */
    getItemValue: PropTypes.func,//done

    /**
     * Arguments: item: Any, isHighlighted: Boolean, styles: Object
     * Invoked for each entry in items that also passes shouldItemRender to generate the render tree for each item in the dropdown menu. styles is an optional set of styles that can be applied to improve the look/feel of the items in the dropdown menu.
     * (item: any, isHighlighted: boolean, styles?: CSSProperties) => ReactNode
     */
    renderItem: PropTypes.func,//done

    /**
     * Arguments: items: Array<Any>, value: String, styles: Object
     * Invoked to generate the render tree for the dropdown menu. 
     * Ensure the returned tree includes every entry in items or else the highlight order and keyboard navigation logic will break. 
     * styles will contain { top, left, minWidth } which are the coordinates of the top-left corner and the width of the dropdown menu.
     * (items: ReactNode[], value?: string, styles?: CSSProperties) => ReactNode
     */
    renderMenu: PropTypes.func,//done

    /**
     * Styles that are applied to the dropdown menu in the default renderMenu implementation. 
     * If you override renderMenu and you want to use menuStyle you must manually apply them (this.props.menuStyle).
     * CSSProperties
     */
    menuStyle: PropTypes.object,//done

    // /**
    //  * Arguments: props: Object
    //  * Invoked to generate the input element. 
    //  * The props argument is the result of merging props.
    //  * inputProps with a selection of props that are required both for functionality and accessibility. At the very least you need to apply props.
    //  * ref and all props.on<event> event handlers. 
    //  * Failing to do this will cause Autocomplete to behave unexpectedly.
    //  * (props: HTMLProps<HTMLInputElement>) => ReactNode
    //  */
    // renderInput: PropTypes.func,

    /**
     * Props passed to props.renderInput. By default these props will be applied to the <input /> element rendered by Autocomplete, unless you have specified a custom value for props.renderInput. Any properties supported by HTMLInputElement can be specified, apart from the following which are set by Autocomplete: value, autoComplete, role, aria-autocomplete. inputProps is commonly used for (but not limited to) placeholder, event handlers (onFocus, onBlur, etc.), autoFocus, etc..
     * HTMLProps<HTMLInputElement>
     */
    inputProps: PropTypes.object,//done

    /**
     * Props that are applied to the element which wraps the <input /> and dropdown menu elements rendered by Autocomplete.
     * HTMLProps<HTMLDivElement>
     */
    wrapperProps: PropTypes.object,//done

    /**
     * This is a shorthand for wrapperProps={{ style: <your styles> }}. Note that wrapperStyle is applied before wrapperProps, so the latter will win if it contains a style entry.
     * CSSProperties
     */
    wrapperStyle: PropTypes.object,//done

    /**
     * Whether or not to automatically highlight the top match in the dropdown menu.
     * boolean
     */
    autoHighlight: PropTypes.bool,//done

    /**
     * Whether or not to automatically select the highlighted item when the <input> loses focus.
     * boolean
     */
    // selectOnBlur: PropTypes.bool,

    /**
     * Arguments: isOpen: Boolean
     * Invoked every time the dropdown menu's visibility changes (i.e. every time it is displayed/hidden).
     * (isOpen: boolean) => void
     */
    onMenuVisibilityChange: PropTypes.func,//done

    /**
     * Used to override the internal logic which displays/hides the dropdown menu. This is useful if you want to force a certain state based on your UX/business logic. Use it together with onMenuVisibilityChange for fine-grained control over the dropdown menu dynamics.
     * boolean
     */
    open: PropTypes.bool,//done

    /**
     * Array of CSS class name to apply to the container in addition to the default.
     * string[]
     */
    additionalClassName: PropTypes.arrayOf(PropTypes.string),//done

    /**
     * Inline style
     * CSSProperties
     */
    additionalStyle: PropTypes.object,

    // /**
    //  * It will detach flyout from input box
    //  * boolean
    //  */
    // detachableFlyout: PropTypes.bool,

    // /**
    //  * Postion of Flyout
    //  * Note: when value assigned as center the application need to manage style of menu using menuStyle prop In that prop please assign property left: -((detached Menu width - inputBoxWidth) / 2)px
    //  * "left" | "right" | "center"
    //  */
    // detachedFlyoutPosition: PropTypes.oneOf(["left", "right", "center"]),

    /**
     * Inline style for input
     * CSSProperties
     */
    inputStyle: PropTypes.object,//done

    /**
     * It will open flyout if value is true
     * boolean
     */
    autoFocus: PropTypes.bool,

    /**
     * If you want behaviour like dropdown. Note: It will not allow to search in input box
     * boolean
     */
    isDropdown: PropTypes.bool,//done

    /**
     * For moWeb styling
     * boolean
     */
    isMobileView: PropTypes.bool,

    /**
     * To show clear icon on right side
     * boolean
     */
    showClearIcon: PropTypes.bool,//done

    /**
     * Click event of clear icon
     * () => void
     */
    onClearIconClick: PropTypes.func,//done

    /**
     * To show search icon on left side
     * boolean
     */
    showSearchIcon: PropTypes.bool,

    /**
     * To show up down icon on right side 
     * Note: If you have passed showClearIcon as true then upDownIcon will hide
     * boolean
     */
    showUpDownIcon: PropTypes.bool,//done

    /**
     * To show custom icon on left side 
     * Note: If you have passed leftIcon then searchIcon will hide
     * string
     */
    leftIcon: PropTypes.string,//done

    /**
     * Additional class for custom left icon
     * string[]
     */
    leftIconClassName: PropTypes.arrayOf(PropTypes.string),//done

    /**
     * Additional style for custom left icon
     * CSSProperties
     */
    leftIconStyle: PropTypes.object,//done

    /**
     * Label for Autocomplete/Dropdown
     * string
     */
    label: PropTypes.string,//done

    /**
     * Secondary Label for Autocomplete/Dropdown
     * string
     */
    secondaryLabel: PropTypes.string,//done

    /**
     * To Disabled Autocomplete/Dropdown
     * boolean
     */
    disabled: PropTypes.bool,//done
    secondPlaceHolder: PropTypes.string,
    /**
     * custom up arrow icon     
     */
    customUpArrow: PropTypes.string,
    /**
     * custom down arrow icon     
     */
    customDownArrow: PropTypes.string,

    // /**
    //  * Class applied to left view container for additional styling
    //  * string[]
    //  */
    // additionalLeftViewClassName: PropTypes.arrayOf(PropTypes.string),

    // /**
    // * Class applied to right view container for additional styling
    // * string[]
    // */
    // additionalRightViewClassName: PropTypes.arrayOf(PropTypes.string)
   

}

export { AutocompleteDropdown }