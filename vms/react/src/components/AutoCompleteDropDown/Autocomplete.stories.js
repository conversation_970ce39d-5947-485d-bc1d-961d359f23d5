import React, { useState } from "react";
import { AutocompleteDropdown } from "./Autocomplete";
import upArrow from "../../assets/images/FairBreakeup/up.svg";
import downArrow from "../../assets/images/FairBreakeup/down.svg";

export default {
  title: "VMS_REACT/Autocomplete",
  component: AutocompleteDropdown,
  argTypes: {
    additionalStyle: { control: { type: "" } },
    additionalClassName: { control: { type: "" } },
  },
};

// const Template = (args) => <AutocompleteDropdown {...args}>Autocomplete</AutocompleteDropdown>;

export const Default = (args) => {
  const [value, setValue] = useState("");
  return (
    <AutocompleteDropdown
      value={value}
      items={["mumbai", "delhi", "pune", "goa", "rajkot", "gujarat"]}
      autoHighlight
      onChange={(e,val) => {setValue(val)}}
     id={"autocomp"}

    >
      Autocomplete
    </AutocompleteDropdown>
  );
};

export const CustomDropdownIcon = (args) => {
  const [value, setValue] = useState("");
  return (
    <AutocompleteDropdown
      value={value}
      items={["mumbai", "delhi", "pune", "goa", "rajkot", "gujarat"]}
      placeholder="Select City"
      autoHighlight
      onChange={(e,val) => {setValue(val)}}
      showUpDownIcon
      customUpArrow={upArrow}
     customDownArrow={downArrow}
     shouldItemRender={(item, value) => item.toLowerCase().indexOf(value.toLowerCase()) > -1}
     renderMenu={(items, value, style) => {
      return (
        <div style={style}>
          {items.length > 0 ? items.map((item, index) => (
            <div key={index} style={{ padding: "5px" }}>
              {item}
            </div>
          )) : <div style={{ padding: "5px" }}>No data found</div>}
        </div>
      );}}
     id={"cust-autocomp"}
    >
      Autocomplete
    </AutocompleteDropdown>
  );
};

