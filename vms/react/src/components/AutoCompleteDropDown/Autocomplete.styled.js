import styled, { css, keyframes } from "styled-components";

export const MainContainer = styled.div`
 width: 220px;
 pointer-events:${({ disabled }) => (disabled ? "none" : "auto")}
`

// export const StyledInput = styled.input`
//     height: 32px;
//     width: 100%;
// `

export const MenuContainer = styled.div`
    width: 100%;
    border: 1px solid black;
    border-top: 0px;
    max-height: 200px;
    overflow-y: scroll;
    -ms-overflow-style: none;  /* IE and Edge */
     scrollbar-width: none;  /* Firefox */
    &::-webkit-scrollbar {
        display: none;
    }
    cursor: pointer;
`

export const DropDownItem = styled.div`
    padding: 10px;
    cursor: pointer;
`

export const InputContainer = styled.div`
    position:relative;
    /* & input{
        margin
    } */
`


export const CloseIcon = styled.img`
    top: 15px;
    position: absolute;
    right: ${({ align }) => (align == "right" ? "10px" : "auto")};
    left: ${({ align }) => (align == "left" ? "10px" : "auto")};
`

