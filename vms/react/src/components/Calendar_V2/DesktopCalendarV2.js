import React, { useState, useRef, Fragment } from "react";
import {
  OverlayDiv,
  OverlayContainer,
  CalendarFlyOut,
} from "./Components/CalendarContainer.styled";
import { DateInput } from "./Components/Input/DateInput";
import { Header } from "./Components/Header/Header";
import {
  addMonth,
  addYear,
  subMonth,
  subYear,
} from "../../hooks/calendarHooks";
import "./style.css";
import { SubView } from "./Components/SubView/SubView";
import { useClassName } from "../../hooks/useClassName";
import { useClickAway } from "../../hooks/outsideClickHandler";
import {
  DAY,
  MONTH,
  YEAR,
  convertSelectedDateToDateInputValue,
  getDateInputInitialState,
  getInitialSubView,
  getResult,
  isDateInputValueValid,
  isNonNull,
  onDateInputChange,
} from "./actions/DatePickerAction";
import { Month } from "./Components/Month/Month";

export const DesktopCalendar = (props) => {
  const {
    inputBoxLabel,
    withLabel,
    minDate,
    maxDate,
    arrowImages,
    isOverlay,
    calendarType,
    isMobile,
    onInputClick,
    dateInputFormat,
    additionalClassName,
    openCalendar,
    selectedDate,
    additionalStyle,
    inputErrorMessage,
    onDateSelected,
  } = props;
  const className = useClassName(props, additionalClassName);
  const [view,setView] = useState(DAY);
  const [editting, setEditting] = useState(openCalendar);
  const [selectedDateState, setSelectedDateState] = useState(
    selectedDate ? selectedDate : new Date()
  );
  const [tempDateState, setTempDateState] = useState(
    selectedDate ? selectedDate : new Date()
  );
  const [showYearView, setYearView] = useState(false);
  const [showSubView, setSubView] = useState(getInitialSubView(MONTH));
  const [dateInputValue, setDateInputValue] = useState(
    getDateInputInitialState(dateInputFormat)
  );
  const datePickRefDiv = useRef();

  // useClickAway(
  //   datePickRefDiv,
  //   () => {
  //     if (isOverlay === false || isOverlay == null) {
  //       setEditting(false);
  //       setSubView(getInitialSubView(MONTH));
  //       setDateInputValue((prevState) => {
  //         return {
  //           ...prevState,
  //           activeDivision: "",
  //         };
  //       });
  //       if (isNonNull(onDateSelected)) {
  //         onDateSelected(getResult(selectedDateState));
  //       }
  //     }
  //   },
  //   []
  // );

  useClickAway(
    datePickRefDiv,
    () => {
      if (isOverlay === false || isOverlay == null) {
        setEditting(false);
        setSubView(getInitialSubView(MONTH));
        setDateInputValue((prevState) => {
          return {
            ...prevState,
            activeDivision: "",
          };
        });
        if (isNonNull(onDateSelected)) {
          onDateSelected(getResult(selectedDateState));
        }
      }
    }
  );

  const nextMonth = () => {
    setTempDateState((prevSelectedDateState) => {
      const updatedSelectedDate = addMonth(prevSelectedDateState, 1);
      if(view == DAY){
        setSelectedDateState(updatedSelectedDate);
      }
      return updatedSelectedDate;
    });
    
  };

  const prevMonth = () => {
    setTempDateState((prevSelectedDateState) => {
      const updatedSelectedDate = subMonth(prevSelectedDateState, 1);
      if(view == DAY){
        setSelectedDateState(updatedSelectedDate);
      }
      return updatedSelectedDate;
    });
  };

  const nextYear = () => {
    const nextYear = addYear(tempDateState, 17);
    const dateToSet = nextYear >= maxDate ? maxDate : nextYear;
    setTempDateState(dateToSet);
  };
  const prevYear = () => {
    const prevYear = subYear(tempDateState, 17);
    const dateToSet = prevYear <= minDate ? minDate : prevYear;
    setTempDateState(dateToSet);
  };

  const onDateClick = (day, showSubView, view) => {
    if(view==DAY){ 
      setSelectedDateState(day);
      setTempDateState(day);
      setDateInputValue((prevState) => {
        const updatedState = convertSelectedDateToDateInputValue(day, prevState);
        return updatedState;
      });
    }else{
      setTempDateState(day);
    }
    onSave(showSubView);
    setSubView((prevState) => {
      const visible = prevState.subView == MONTH ? false : true;
      return {
        ...prevState,
        visible: visible,
        subView:
          visible == false
            ? MONTH
            : prevState.subView == YEAR
            ? MONTH
            : YEAR,
      };
    });

    if (view==DAY && isNonNull(onDateSelected)) {
      onDateSelected(getResult(day));
    }
  };

  const onSave = (showView) => {
    if (showView) {
      setEditting(!editting);
    }
  };

  const onYearViewChange = () => {
    setYearView(!showYearView);
    setSubView((prevState) => {
      return {
        ...prevState,
        subView: YEAR,
        visible: true,
      };
    });
  };

  const onHeaderBtnClick = (nextView) => {
    setSubView((prevState) => {
      return {
        ...prevState,
        visible: !prevState.visible,
      };
    });
    if(isNonNull(nextView)){
      setView(nextView);
    }
  };

  const clearDate = () => {
    const newDate = new Date();
    setSelectedDateState(newDate);
    setTempDateState(newDate);
    setDateInputValue(getDateInputInitialState(dateInputFormat));
    setSubView(getInitialSubView(MONTH));
  };

  const onInputClickHandler = (event, division) => {
    event.stopPropagation();
    setEditting(!editting);
    setSubView(getInitialSubView(MONTH));
    setDateInputValue((prevState) => {
      const updatedState = { ...prevState };
      updatedState.activeDivision = division;
      return updatedState;
    });
    if (isNonNull(onInputClick)) {
      onInputClick(true);
    }
  };

  const onInputChangeHandler = (event, value, division) => {
    event.stopPropagation();
    setDateInputValue((prevState) => {
      const updatedState = onDateInputChange(value, division, prevState);
      const { isValid, date, errorMsg } = isDateInputValueValid(
        updatedState,
        minDate,
        maxDate,
        inputErrorMessage
      );
      if (isValid) {
        setSelectedDateState(date);
        setTempDateState(date);
      }
      updatedState.errorMsg = errorMsg;
      return updatedState;
    });
  };
  console.log("checkEdittting*", editting)
  return (
    <Fragment>
      <OverlayContainer
        className={`vms_Calendar_OverlayContainer`}
        isOverlay={isOverlay}
        isMobile={isMobile}
      >
        <div
          className={`calendar-container vms_Calendar_CalendarContainer ${className}`}
          style={additionalStyle}
          ref={datePickRefDiv}
        >
          <DateInput
            selectedDate={selectedDateState}
            withLabel={withLabel}
            labelMessage={inputBoxLabel}
            isMobile={false}
            calendarType={calendarType}
            isEditting={editting}
            dateInputFormat={dateInputFormat}
            dateInputValue={dateInputValue}
            onClearIconClick={clearDate}
            onInputClick={onInputClickHandler}
            onInputChange={onInputChangeHandler}
            isOverlay={isOverlay}
          />

          {editting && (
            <CalendarFlyOut
              className={`${className} vms_Calendar_CalendarFlyout`}
              style={additionalStyle}
              isMobile={isMobile}
              type={calendarType}
            >
              <div className="rdrCalendarWrapper vms_Calendar_rdrCalendarWrapper">
                <Header
                  selectedDate={selectedDateState}
                  view={view}
                  formatMonthYear={"LLL yyyy"}
                  prev={prevMonth}
                  next={nextMonth}
                  arrowImages={arrowImages}
                  onHeaderBtnClick={onHeaderBtnClick}
                  minDate={minDate}
                  maxDate={maxDate}
                  isMobile={false}
                />
                <div
                  className={
                    "rdrMonths rdrMonthsVertical vms_Calendar_monthContainer"
                  }
                >
                  <Month
                    month={selectedDateState}
                    showWeekDays
                    selectedDate={selectedDateState}
                    fixedHeight={false}
                    onDayClick={(value) => {
                      onDateClick(value, true, DAY);
                    }}
                    minDate={minDate}
                    maxDate={maxDate}
                  />
                </div>
              </div>
              {showSubView.visible && (
                <div
                  className="month-navigation-container vms_Calendar_subViewContainer"
                  style={{ display: "flex", flexDirection: "column" }}
                >
                  <Header
                    selectedDate={tempDateState}
                    view={showSubView.subView}
                    minDate={minDate}
                    maxDate={maxDate}
                    formatMonthYear={"yyyy"}
                    prev={showSubView.subView == MONTH ? prevMonth : prevYear}
                    next={showSubView.subView == MONTH ? nextMonth : nextYear}
                    onHeaderBtnClick={onYearViewChange}
                    arrowImages={arrowImages}
                    isMobile={false}
                  />
                  <div
                    className="row-container vms_Calendar_row-container"
                    style={{
                      display: "flex",
                      flexWrap: "wrap",
                      flex: "0 0 53.00%",
                      padding: "5%",
                    }}
                  >
                    <SubView
                      minDate={minDate}
                      maxDate={maxDate}
                      selectedDate={selectedDate}
                      tempDate={tempDateState}
                      onItemClick={onDateClick}
                      view={showSubView.subView}
                    />
                  </div>
                </div>
              )}
            </CalendarFlyOut>
          )}
        </div>
      </OverlayContainer>
      {isOverlay && !isMobile && (
        <OverlayDiv className="vms_Calendar_OverlayDiv" editting={editting} />
      )}
    </Fragment>
  );
};
