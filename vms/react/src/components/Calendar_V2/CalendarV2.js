import React, { memo, useMemo } from "react";
import PropTypes from "prop-types";
import "./style.css";
import { MobileCalendar } from "./MobileCalendarV2";
import { DesktopCalendar } from "./DesktopCalendarV2";
import { ThemeWrapper } from "../Theme/ThemeContext";
import { addYears, subYears } from "date-fns";
import { DD_MM_YYYY_SLASH_SEPARATOR } from "./actions/DatePickerAction";

const Calendar_V2 = memo((props) => {
  // 1. PROP DESTRUCTURING & VALIDATION
  const {
    isMobile,
    isMobileView,
    ...otherProps
  } = props;

  // 2. PERFORMANCE OPTIMIZATIONS
  const isMobileDevice = useMemo(
    () => isMobile || isMobileView || false,
    [isMobile, isMobileView]
  );

  // 3. CONDITIONAL RENDERING (memoized)
  const calendarComponent = useMemo(() => {
    return isMobile ? (
      <MobileCalendar {...props} />
    ) : (
      <DesktopCalendar {...props} />
    );
  }, [isMobile, props]);

  // 4. ERROR HANDLING
  React.useEffect(() => {
    if (props.minDate && props.maxDate && props.minDate > props.maxDate) {
      console.warn('Calendar_V2: minDate should be less than maxDate');
    }
  }, [props.minDate, props.maxDate]);

  return (
    <ThemeWrapper isMobile={isMobileDevice}>
      {calendarComponent}
    </ThemeWrapper>
  );
});

// Component display name for debugging
Calendar_V2.displayName = "Calendar_V2";

Calendar_V2.defaultProps = {
  withLabel: true,
  onDateSelected: () => { },
  openCalendar: false,
  isMobile: false,
  isMobileView: false,
  closeButtonType: "primary",
  closeButtonText: "Done",
  additionalClassName: null,
  additionalStyle: null,
  inputBoxLabel: "DATE",
  isOverlay: false,
  calendarType: "primary",
  onInputClick: () => { },
  minDate: subYears(new Date(), 100),
  maxDate: addYears(new Date(), 20),
  dateInputFormat: DD_MM_YYYY_SLASH_SEPARATOR,
  inputErrorMessage: "Invalid Date-Range"
};

Calendar_V2.propTypes = {
  /**
   * ID for the input element
   */
  id: PropTypes.string,

  /**
   * Variations of Calendar Type. Default `primary`
   */
  calendarType: PropTypes.oneOf(["primary", "secondary"]),

  /**
   * Selected Date that application want to be selected default Date
   */
  selectedDate: PropTypes.instanceOf(Date),

  /**
   * Input will be taken in this format in the input element<br>
   * Default input format DD/MM/YYYY 
   */
  dateInputFormat: PropTypes.oneOf(["DD/MM/YYYY", "YYYY/MM/DD", "DD-MM-YYYY", "YYYY-MM-DD"]),
  /**
   * Whether show up the label of the Date input
   */
  withLabel: PropTypes.bool,

  /**
   * To display label above input box<br>
   * Default value DATE
   */
  inputBoxLabel: PropTypes.string,

  /**
   * To define min date to be displayed in calendar<br>
   * Default is current date - 100 years
   */
  minDate: PropTypes.instanceOf(Date),

  /**
   * To define max date to be displayed in calendar<br>
   * Default is current date + 20 years
   */
  maxDate: PropTypes.oneOfType([PropTypes.instanceOf(Date)]),

  /**
   * On Date selection this funtion is invoked<br>
   * return object { date: Date | undefined, format1 (ISO format): string, format2 (DD-MM-YYYY): string}
   * @returns { date: Date | undefined, format1 (ISO format): string, format2 (DD-MM-YYYY): string}
   */
  onDateSelected: PropTypes.func.isRequired,

  /**
   * Only applicable when @property isMobile value true<br>
   * Close button text. it will take Done as default value
   */
  closeButtonText: PropTypes.string,

  /**
   * Close button type. Only applicable when @property isMobile value true<br>
   * Default `primary`<br>
   * "primary" | "link" | "secondary" | "tertiary" | "ghost"
   */
  closeButtonType: PropTypes.oneOf([
    "primary",
    "secondary",
    "link",
    "tertiary",
  ]),

  /**
   * When user clicks on Done button this function will be invoked.<br>
   * To be used to close the calendar<br>
   * onCloseButtonClick = (result : CalendarOutput) => void
   */
  onCloseButtonClick: PropTypes.func,
  /**
   * When click event occur on input box this function is invoked
   */
  onInputClick: PropTypes.func,
  /**
   * To customize the view of previous and next button<br>
   * Note: It is mandatory to provide arrow and its disabled arrow
   */
  arrowImages: PropTypes.shape({
    upArrow: PropTypes.string,
    downArrow: PropTypes.string,
    disabledUpArrow: PropTypes.string,
    disabledDownArrow: PropTypes.string,
  }),
  /**
   * To open or close the calender.
   */
  openCalendar: PropTypes.bool,

  /**
   * Inline styles to add additional styling in the parent container CSSProperties
   */
  additionalStyle: PropTypes.object,

  /**
   * Classes to add additional styling in the parent container string[]
   */
  additionalClassName: PropTypes.arrayOf(PropTypes.string),

  /**
   * Only applicable when @property isMobile value false<br>
   * To wrap the component with overlay. Default value false boolean
   */
  isOverlay: PropTypes.bool,

  /**
   * Whether component is in mobile view
   */
  isMobileView: PropTypes.bool,

  /**
   * Error message to be displayed on error<br>
   * error is displayed when user entered/selected date is not in range on minDate and maxDate
   */
  inputErrorMessage: PropTypes.string,
};

export { Calendar_V2 };
