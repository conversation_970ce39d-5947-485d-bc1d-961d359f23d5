import React, { Fragment, useState } from "react";
import {
  DAY,
  MONTH,
  YEAR,
  convertSelectedDateToDateInputValue,
  getDateInputInitialState,
  getInitialSubView,
  getResult,
  isDateInputValueValid,
  isNonNull,
  onDateInputChange,
} from "./actions/DatePickerAction";
import { Month } from "./Components/Month/Month";
import {
  CalendarCloseButton,
  StyledInfiniteMonths,
} from "./Components/CalendarContainer.styled";
import { Button } from "../Button/Button";
import { DateInput } from "./Components/Input/DateInput";
import { SubView } from "./Components/SubView/SubView";
import { Header } from "./Components/Header/Header";
import {
  addMonth,
  addYear,
  subMonth,
  subYear,
} from "../../hooks/calendarHooks";
import { useClassName } from "../../hooks/useClassName";

export const MobileCalendar = (props) => {
  const {
    inputBoxLabel,
    withLabel,
    minDate,
    maxDate,
    arrowImages,
    calendarType,
    onInputClick,
    dateInputFormat,
    additionalClassName,
    openCalendar,
    selectedDate,
    additionalStyle,
    inputErrorMessage,
    closeButtonType,
    closeButtonText,
    onCloseButtonClick,
    onDateSelected,
  } = props;

  const className = useClassName(props, additionalClassName);
  const [editting, setEditting] = useState(openCalendar);
  const [selectedDateState, setSelectedDateState] = useState(
    selectedDate ? selectedDate : new Date()
  );
  const [tempDateState, setTempDateState] = useState(
    selectedDate ? selectedDate : new Date()
  );
  const [showSubView, setSubView] = useState(getInitialSubView(DAY));
  const [dateInputValue, setDateInputValue] = useState(
    getDateInputInitialState(dateInputFormat)
  );

  const nextMonth = () => {
    setTempDateState((prevSelectedDateState) => {
      const updatedSelectedDate = addMonth(prevSelectedDateState, 1);
      return updatedSelectedDate;
    });
  };

  const prevMonth = () => {
    setTempDateState((prevSelectedDateState) => {
      const updatedSelectedDate = subMonth(prevSelectedDateState, 1);
      return updatedSelectedDate;
    });
  };

  const nextYear = () => {
    const nextYear = addYear(tempDateState, 17);
    const dateToSet = nextYear >= maxDate ? maxDate : nextYear;
    setTempDateState(dateToSet);
  };
  const prevYear = () => {
    const prevYear = subYear(tempDateState, 17);
    const dateToSet = prevYear <= minDate ? minDate : prevYear;
    setTempDateState(dateToSet);
  };

  const onDateClick = (day, showSubView, view) => {
    if(view==DAY){ 
      setSelectedDateState(day);
      setTempDateState(day);
      setDateInputValue((prevState) => {
        const updatedState = convertSelectedDateToDateInputValue(day, prevState);
        return updatedState;
      });
    }else{
      setTempDateState(day);
    }
    onSave(showSubView);
    setSubView((prevState) => {
      const updateSubViewState = { ...prevState };
      if (prevState.subView == MONTH) {
        updateSubViewState.subView = DAY;
        updateSubViewState.visible = false;
      } else if (prevState.subView == YEAR) {
        updateSubViewState.subView = MONTH;
        updateSubViewState.visible = true;
      } else if (prevState.subView == DAY) {
        updateSubViewState.subView = DAY;
        updateSubViewState.visible = false;
      }
      return updateSubViewState;
    });

    if (isNonNull(onDateSelected)) {
      onDateSelected(getResult(day));
    }
  };

  const onSave = (showView) => {
    if (showView) {
      setEditting(!editting);
    }
  };

  // const onHeaderBtnClick = () => {
  //   setSubView((prevState) => {
  //     const updateSubViewState = { ...prevState };
  //     updateSubViewState.visible = true;
  //     if (prevState.subView == MONTH) {
  //       updateSubViewState.subView = YEAR;
  //     } else if (prevState.subView == YEAR) {
  //       updateSubViewState.subView = MONTH;
  //     } else if (prevState.subView == DAY) {
  //       updateSubViewState.subView = MONTH;
  //     }
  //     return updateSubViewState;
  //   });
  // };

  const onHeaderBtnClick = () => {
    setSubView((prevState) => {
      const updateSubViewState = { ...prevState };
      updateSubViewState.visible = true;
      if (prevState.subView === MONTH) {
        updateSubViewState.subView = YEAR;
    } else if (prevState.subView === YEAR || prevState.subView === DAY) {
        updateSubViewState.subView = MONTH;
    }
      return updateSubViewState;
    });
  };

  const onDoneClick = () => {
    if (onCloseButtonClick != null) {
      onCloseButtonClick(getResult(selectedDateState));
    }
    setEditting(false);
  };

  const clearDate = () => {
    const date = new Date();
    setSelectedDateState(date);
    setTempDateState(date);
    setDateInputValue(getDateInputInitialState(dateInputFormat));
    setSubView(getInitialSubView(DAY));
  };

  const onInputClickHandler = (event, division) => {
    event.stopPropagation();
    setEditting(true);
    setSubView(getInitialSubView(DAY));
    setDateInputValue((prevState) => {
      const updatedState = { ...prevState };
      updatedState.activeDivision = division;
      return updatedState;
    });
    if (isNonNull(onInputClick)) {
      onInputClick(true);
    }
  };

  const onInputChangeHandler = (event, value, division) => {
    event.stopPropagation();
    setDateInputValue((prevState) => {
      const updatedState = onDateInputChange(value, division, prevState);
      const { isValid, date, errorMsg } = isDateInputValueValid(
        updatedState,
        minDate,
        maxDate,
        inputErrorMessage
      );
      if (isValid) {
        setSelectedDateState(date);
        setTempDateState(date);
      }
      updatedState.errorMsg = errorMsg;
      return updatedState;
    });
  };

  return (
    <Fragment>
      <div className={`calendar-container mobile vms_Calendar_CalendarContainer ${className}`} style={additionalStyle}>
        <DateInput
          selectedDate={selectedDateState}
          withLabel={withLabel}
          labelMessage={inputBoxLabel}
          isMobile={true}
          calendarType={calendarType}
          isEditting={editting}
          dateInputFormat={dateInputFormat}
          dateInputValue={dateInputValue}
          onClearIconClick={clearDate}
          onInputClick={onInputClickHandler}
          onInputChange={onInputChangeHandler}
        />
        {editting && (
          <div id="calendarPicker">
            <div className="rdrCalendarWrapper vms_Calendar_rdrCalendarWrapper">
              {tempDateState != null && (
                <Fragment>
                  <Header
                    selectedDate={tempDateState}
                    isMobile={true}
                    minDate={minDate}
                    maxDate={maxDate}
                    formatMonthYear={
                      showSubView.subView == DAY ? "LLL yyyy" : "yyyy"
                    }
                    prev={showSubView.subView != YEAR ? prevMonth : prevYear}
                    next={showSubView.subView != YEAR ? nextMonth : nextYear}
                    onHeaderBtnClick={onHeaderBtnClick}
                    arrowImages={arrowImages}
                    view={showSubView.subView}
                  />

                  <div>
                    <StyledInfiniteMonths
                      style={{
                        height: `calc(((((100vh - 62px) - 10px) - 68px) - 5px) - 109px)`,
                      }}
                    >
                      {showSubView.visible ? (
                        <div
                          className="row-container vms_Calendar_row-container"
                          style={{
                            display: "flex",
                            flexWrap: "wrap",
                            flex: "0 0 53.00%",
                          }}
                        >
                          <SubView
                            minDate={minDate}
                            maxDate={maxDate}
                            selectedDate={selectedDate}
                            tempDate={tempDateState}
                            onItemClick={onDateClick}
                            view={showSubView.subView}
                          />
                        </div>
                      ) : (
                        <Month
                          month={tempDateState}
                          selectedDate={selectedDateState}
                          fixedHeight={false}
                          onDayClick={(value) => {
                            onDateClick(value, false, DAY);
                          }}
                          showMonthName={true}
                          showWeekDays={true}
                          isMobile={true}
                        />
                      )}
                    </StyledInfiniteMonths>
                  </div>
                </Fragment>
              )}
            </div>
          </div>
        )}

        {editting && (
          <CalendarCloseButton className={`vms_Calendar_CloseButtonContainer`}>
            <Button
              buttonType={closeButtonType}
              additionalStyle={{
                maxWidth: "100%",
                margin: "10px 0px 10px 0",
              }}
              onClick={onDoneClick}
            >
              {closeButtonText}
            </Button>
          </CalendarCloseButton>
        )}
      </div>
    </Fragment>
  );
};
