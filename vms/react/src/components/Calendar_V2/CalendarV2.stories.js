import React, { useState } from "react";
import { Calendar_V2 } from "./CalendarV2";
import { Button } from "../Button/Button";
import { addMonth } from "../../hooks/calendarHooks";
import { startOfYear } from "date-fns";

export default {
  title: "VMS_REACT/Calendar_V2",
  component: Calendar_V2,
  parameters: { controls: { sort: "requiredFirst" } },
  argTypes: {
    id: { control: { type: "" } },
    additionalStyle: { control: { type: "" } },
    additionalClassName: { control: { type: "" } },
    dateDisplayFormat: { control: { type: "" } },
    selectedDate: { control: { type: "" } },
    value: { control: { type: "" } },
    arrowImages: { control: { type: "" } },
    minDate: { control: { type: "" } },
    maxDate: { control: { type: "" } },
  },
};

const Template = (args) => {
  const [openCalendar, setOpenCalendar] = useState(false);
  const [result, setResult] = useState();
  return (
    <div>
      <Calendar_V2
        {...args}
        onDateSelected={(result) => {
          setResult(result);
          setOpenCalendar(false);
        }}
      />
      <>{result && !openCalendar ? DisplayCurrentState(result) : ""} </>
    </div>
  );
};

const DisplayCurrentState = (data) => {
  return (
    <div style={{ marginTop: "10px" }}>
      <h5>Callback Value</h5>
      <ul style={{ paddingLeft: "15px", paddingTop: "1px" }}>
        <li>Date : {data?.Date.toString()}</li>
      </ul>
      <h5>Dates In Format</h5>
      <ul style={{ paddingLeft: "15px", paddingTop: "1px" }}>
        <li>{`Format 1 : ${data?.format1}`}</li>
      </ul>
      <ul style={{ paddingLeft: "15px", paddingTop: "1px" }}>
        <li>{`Format 2 : ${data?.format2}`}</li>
      </ul>
    </div>
  );
};

const MobileTemplate = (args) => {
  const [openCalendar, setOpenCalendar] = useState(false);
  const [result, setResult] = useState(null);
  return (
    <div>
      {!openCalendar && (
        <>
          <Button
            isMobile
            buttonType="primary"
            onClick={() => {
              setOpenCalendar(!openCalendar);
            }}
          >
            Open
          </Button>
          <h3>Click to open Calender Mobile view</h3>
        </>
      )}

      {openCalendar && (
        <Calendar_V2
          {...args}
          openCalendar={openCalendar}
          onDateSelected={(result) => {
            console.log("result", result);
            setResult(result);
          }}
          onCloseButtonClick={(result) => {
            console.log("On Application level onCloseButtonClick invoked");
            setResult(result);
            setOpenCalendar(false);
          }}
          // selectedDate={addMonths(new Date(),5)}
        />
      )}
      <>{result && !openCalendar ? DisplayCurrentState(result) : ""} </>
    </div>
  );
};

export const Default = Template.bind({});
Default.args = {
  value: "",
  isOverlay: false,
  isMobile: false,
  withLabel: true,
  openCalendar: false,
  dateDisplayFormat: "dd MMM yyyy",
  inputBoxLabel: "Date",
  inputPlaceholder: "Select Date",
  calendarType: "primary",
};

export const openCalendarByApplication = Template.bind({});
openCalendarByApplication.args = {
  withLabel: true,
  openCalendar: true,
  inputBoxLabel: "Date",
  inputPlaceholder: "Select Date",
  calendarType: "primary",
};

export const selectedDatePassByApplication = Template.bind({});
selectedDatePassByApplication.args = {
  withLabel: true,
  selectedDate: addMonth(new Date(), 1),
  inputBoxLabel: "Date",
  inputPlaceholder: "Select Date",
  calendarType: "primary",
};

export const customArrowImages = Template.bind({});
customArrowImages.args = {
  withLabel: true,
  arrowImages: {
    upArrow: "https://img.icons8.com/pastel-glyph/24/000000/minus.png",
    downArrow: "https://img.icons8.com/pastel-glyph/24/000000/plus.png",
    disabledUpArrow: "https://img.icons8.com/pastel-glyph/24/d1d3d4/minus.png",
    disabledDownArrow: "https://img.icons8.com/pastel-glyph/24/d1d3d4/plus.png",
  },
  inputBoxLabel: "Date",
  inputPlaceholder: "Select Date",
  calendarType: "primary",
};

export const MoDefault = MobileTemplate.bind({});
MoDefault.storyName = "MoWeb Default";
MoDefault.parameters = {
  viewport: {
    defaultViewport: "iphone5",
  },
};
MoDefault.args = {
  value: "",
  isOverlay: false,
  isMobile: true,
  withLabel: true,
  openCalendar: false,
  dateDisplayFormat: "dd MMM yyyy",
  inputBoxLabel: "Date",
  inputPlaceholder: "Select Date",
  calendarType: "primary",
  closeButtonType: "primary",
  closeButtonText: "Done",
  // minDate : startOfYear(new Date),
  // maxDate : new Date()
};

export const WithOverlay = () => {
  const [openCalendar, setOpenCalendar] = useState(false);
  const [result, setResult] = useState(null);
  const [overlay, setShowOverlay] = useState(false);
  return (
    <div>
      <h3>Click on input box to open calendar in overlay-mode</h3>
      <Calendar_V2
        openCalendar={openCalendar}
        withLabel={true}
        isMobile={false}
        isOverlay={openCalendar}
        dateDisplayFormat={"dd MMM yyyy"}
        inputBoxLabel={"Date"}
        inputPlaceholder={"Select Date"}
        calendarType={"primary"}
        onInputClick={() => {
          setShowOverlay(!overlay);
          setOpenCalendar(!openCalendar);
        }}
        onDateSelected={(result) => {
          setResult(result);
          setOpenCalendar(false);
          setShowOverlay(false);
        }}
      />
      <>{result && !openCalendar ? DisplayCurrentState(result) : ""} </>
    </div>
  );
};

export const MinMaxDateControl = () => {
  const [openCalendar, setOpenCalendar] = useState(false);
  const [result, setResult] = useState(null);
  const minDate = startOfYear(new Date());
  const maxDate = new Date();
  return (
    <div style={{ display: "flex", flexDirection: "column", gap: "20px" }}>
      <div style={{ display: "flex", flexDirection: "column", gap: "15px" }}>
        <div>
          <b
            style={{ textDecoration: "underline" }}
          >{`Minimum Date (start of year):`}</b>
          <p>{`${minDate}`}</p>
        </div>
        <div>
          <b
            style={{ textDecoration: "underline" }}
          >{`Maximum Date (current-date):`}</b>
          <p>{`${maxDate}`}</p>
        </div>
      </div>
      <Calendar_V2
        openCalendar={openCalendar}
        withLabel={true}
        isMobile={false}
        isOverlay={false}
        dateDisplayFormat={"dd MMM yyyy"}
        inputBoxLabel={"Date"}
        inputPlaceholder={"Select Date"}
        calendarType={"primary"}
        minDate={minDate}
        maxDate={maxDate}
        //maxDate={endOfYear(new Date())}
        // arrowImages={{
        //   upArrow: "https://img.icons8.com/pastel-glyph/24/000000/minus.png",
        //   downArrow: "https://img.icons8.com/pastel-glyph/24/000000/plus.png",
        //   disabledUpArrow: "https://img.icons8.com/pastel-glyph/24/d1d3d4/minus.png",
        //   disabledDownArrow: "https://img.icons8.com/pastel-glyph/24/d1d3d4/plus.png",
        // }}
        onInputClick={() => {
          setOpenCalendar(!openCalendar);
        }}
        onDateSelected={(result) => {
          setResult(result);
          setOpenCalendar(false);
        }}
      />
    </div>
  );
};

export const MinMaxDateControlMobile = () => {
  const [openCalendar, setOpenCalendar] = useState(false);
  const [result, setResult] = useState(null);
  const minDate = startOfYear(new Date());
  const maxDate = new Date();
  return (
    <div style={{ display: "flex", flexDirection: "column", gap: "20px" }}>
      {!openCalendar && (
        <>
          <Button
            isMobile
            buttonType="primary"
            onClick={() => {
              setOpenCalendar(!openCalendar);
            }}
          >
            Open
          </Button>
          <h3>Click to open Calender Mobile view</h3>
          <div
            style={{ display: "flex", flexDirection: "column", gap: "15px" }}
          >
            <div>
              <b
                style={{ textDecoration: "underline" }}
              >{`Minimum Date (start of year):`}</b>
              <p>{`${minDate}`}</p>
            </div>
            <div>
              <b
                style={{ textDecoration: "underline" }}
              >{`Maximum Date (current-date):`}</b>
              <p>{`${maxDate}`}</p>
            </div>
          </div>
        </>
      )}
      {openCalendar && (
        <Calendar_V2
          openCalendar={openCalendar}
          withLabel={true}
          isMobile={true}
          isOverlay={false}
          dateDisplayFormat={"dd MMM yyyy"}
          inputBoxLabel={"Date"}
          inputPlaceholder={"Select Date"}
          calendarType={"primary"}
          minDate={minDate}
          maxDate={maxDate}
          //maxDate={endOfYear(new Date())}
          // arrowImages={{
          //   upArrow: "https://img.icons8.com/pastel-glyph/24/000000/minus.png",
          //   downArrow: "https://img.icons8.com/pastel-glyph/24/000000/plus.png",
          //   disabledUpArrow: "https://img.icons8.com/pastel-glyph/24/d1d3d4/minus.png",
          //   disabledDownArrow: "https://img.icons8.com/pastel-glyph/24/d1d3d4/plus.png",
          // }}
          onInputClick={() => {
            setOpenCalendar(true);
          }}
          onDateSelected={(result) => {
            setResult(result);
          }}
          onCloseButtonClick={(result) => {
            setResult(result);
            setOpenCalendar(false);
          }}
        />
      )}
    </div>
  );
};
MinMaxDateControlMobile.parameters = {
  viewport: {
    defaultViewport: "iphone5",
  },
};
