import { format } from "date-fns";
import { formatWithLocale } from "../../../hooks/calendarHooks";

export const DD_MM_YYYY_SLASH_SEPARATOR = "DD/MM/YYYY";
export const DD_MM_YYYY_DASH_SEPARATOR = "DD-MM-YYYY";
export const YYYY_MM_DD_SLASH_SEPARATOR = "YYYY/MM/DD";
export const YYYY_MM_DD_DASH_SEPARATOR = "YYYY-MM-DD";
export const WEEK_DAYS_DISPLAY_FORMAT = "eeeee";

export const DAY = "day";
export const MONTH = "month";
export const YEAR = "year";

const DD = "DD";
const MM = "MM";
const YYYY = "YYYY";
export const isNonNull = (prop) => {
  return prop !== null && prop !== undefined;
};
export const getDateInputInitialState = (dateInputFormat) => {
  const initalStateToReturn = {
    DD: "",
    MM: "",
    YYYY: "",
    activeDivision: "",
    errorMsg: "",
    inputformat_splitted: getDateInputFormatSplitted(dateInputFormat),
    seprator: getSepratorByFormat(dateInputFormat),
  };
  return initalStateToReturn;
};

export const getInitialSubView = (initalView) => {
  return {
    visible: false,
    subView: initalView,
  };
}

export const onDateInputChange = (value, division, dateInputValueState) => {
  const updatedDateInputValueState = { ...dateInputValueState };
  updatedDateInputValueState[division] = value;
  if (value.length == division.length) {
    updatedDateInputValueState.activeDivision = nextActiveDivision(
      division,
      updatedDateInputValueState
    );
  } else {
    updatedDateInputValueState.errorMsg = "";
  }
  //console.log("updated-state", updatedDateInputValueState);
  return updatedDateInputValueState;
};
export const getResult = (selectedDate) => {
  return {
    Date: selectedDate,
    format1: formatWithLocale(
      selectedDate,
      "yyyy-MM-dd'T'HH:mm:ss'Z'"
    ),
    format2: formatWithLocale(selectedDate, "dd-MM-yyyy"),
  }
}
export const isDateInputValueValid = (
  dateInputValue,
  minDate,
  maxDate,
  errorMsg
) => {
  const { inputformat_splitted } = dateInputValue;
  let toReturn = {
    isValid: false,
    date: new Date(),
    errorMsg: "",
  };
  for (let index = 0; index < inputformat_splitted.length; index++) {
    const element = inputformat_splitted[index];
    const value = dateInputValue[element];
    if (value != null && value != "") {
      switch (element) {
        case DD:
          if (value.length == 2) {
            toReturn.isValid = Number(value) <= 31 && Number(value) > 0;
            toReturn.date.setDate(value);
          }
          break;
        case MM:
          if (value.length == 2) {
            toReturn.isValid = Number(value) <= 12 && Number(value) > 0;
            toReturn.date.setMonth(value - 1);
          }
          break;
        case YYYY:
          if (value.length == 4) {
            toReturn.isValid =
              Number(value) <= maxDate.getFullYear() &&
              Number(value) >= minDate.getFullYear();
            toReturn.date.setFullYear(value);
          }
          break;

        default:
          toReturn.isValid = false;
          break;
      }
    } else {
      toReturn.isValid = false;
      break;
    }
  }
  if (toReturn.isValid) {
    toReturn.isValid = toReturn.date >= minDate && toReturn.date <= maxDate;
  } else if (!isDateInputValueAllNonEmpty(dateInputValue)) {
    toReturn.errorMsg = errorMsg;
  }
  return toReturn;
};

export const matchValueBy = (value, division) => {
  if (value.trim() != "") {
    switch (division) {
      case DD:
      case MM:
      case YYYY:
        return /^[\d]+$/.test(value);

      default:
        return false;
    }
  } else {
    return true;
  }
};

const nextActiveDivision = (currentDivision, dateInputValue) => {
  const { inputformat_splitted } = dateInputValue;
  const nextDivisionIndex =
  inputformat_splitted.findIndex((item) => item == currentDivision) + 1;
  if (nextDivisionIndex > inputformat_splitted.length) {
    return inputformat_splitted[inputformat_splitted.length - 1];
  } else {
    return inputformat_splitted[nextDivisionIndex];
  }
};
export const convertSelectedDateToDateInputValue = (selectedDate,dateInputValue) => {
  const updatedDateInputValue = {...dateInputValue};
  updatedDateInputValue[DD] = format(selectedDate,"dd");
  updatedDateInputValue[MM] = format(selectedDate,"MM");
  updatedDateInputValue[YYYY] = format(selectedDate,"yyyy");
  return updatedDateInputValue;
}
export const isDateInputValueAllNonEmpty = (dateInputValue) => {
  const { inputformat_splitted } = dateInputValue;
  return inputformat_splitted.filter((element) => (dateInputValue[element] == "")).length != 0;
};

export const isAnyNonEmpty = (dateInputValue) => {
  const { inputformat_splitted } = dateInputValue;
  return inputformat_splitted.filter((element) => (dateInputValue[element] != "")).length > 0;
}

export const getDateInputFormatSplitted = (dateInputFormat) => {
  const spliter = getSepratorByFormat(dateInputFormat);
  return dateInputFormat.split(spliter);
};

export const getSepratorByFormat = (dateInputFormat) => {
  switch (dateInputFormat) {
    case DD_MM_YYYY_SLASH_SEPARATOR:
    case YYYY_MM_DD_SLASH_SEPARATOR:
      return "/";
    case DD_MM_YYYY_DASH_SEPARATOR:
    case YYYY_MM_DD_DASH_SEPARATOR:
      return "-";

    default:
      break;
  }
};
