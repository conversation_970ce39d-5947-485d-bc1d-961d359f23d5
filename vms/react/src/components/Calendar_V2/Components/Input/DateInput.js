import React, { useEffect, useState } from "react";
import { DateClearIcon, DateSelectorIcon } from "../Icons/DateSelectorIcons";
import { DateDivision } from "./DateDivision";
import {
  DateInputContainer,
  DateInputSubContainer,
  ErrorMessage,
  IconContainer,
  IconWrapper,
  InputErrorDiv,
  Label,
} from "./DateInput.styled";
import { isAnyNonEmpty } from "../../actions/DatePickerAction";

export const DateInput = ({
  withLabel,
  labelMessage,
  calendarType,
  isMobile,
  isEditting,
  dateInputFormat,
  dateInputValue,
  onClearIconClick,
  isOverlay,
  onInputClick,
  onInputChange
}) => {
  
  const [showClearIconState, setShowClearIconState] = useState(false);
  
  useEffect(()=>{
    if(dateInputValue.activeDivision == ""){
      setShowClearIconState(false);
    }else if(isAnyNonEmpty(dateInputValue)){
      setShowClearIconState(true);
    }
  },[dateInputValue]);

  
  const onInputContainerMouseEnter = (event) => {
    event.stopPropagation();
    if(dateInputValue.activeDivision == ""){
      setShowClearIconState(false);
    }else if(isAnyNonEmpty(dateInputValue)){
      setShowClearIconState(true);
    }
  };
  const onInputContainerMouseLeave = (event) => {
    event.stopPropagation();
    setShowClearIconState(false);
  };
  return (
    <DateInputContainer
      className={`vms_Calendar_InputContainer`}
      isMobile={isMobile}
    >
      {withLabel && (
        <Label className={`vms_Calendar_Input_lable`} isMobile={isMobile}>
          {labelMessage}
        </Label>
      )}
      <DateInputSubContainer
        calendarType={calendarType}
        isDateInvalid={dateInputValue.errorMsg != ""}
        isFocused={dateInputValue.activeDivision != ""}
        className={`vms_calendar_dateinputsubcontainer`}
        onMouseEnter={onInputContainerMouseEnter}
        onMouseLeave={onInputContainerMouseLeave}
        isOverlay={isOverlay}
      >
        <DateDivision
          className={`vms_calendar_datedivision`}
          dateInputValue={dateInputValue}
          dateInputFormat={dateInputFormat}
          onInputChange={onInputChange}
          onInputClick={onInputClick}
        />

        <IconWrapper className={`vms_calendar_iconwrapper`}>
          {showClearIconState && (
            <IconContainer
              className={`vms_DateRangeCalendar_IconCotainer`}
              onClick={onClearIconClick}
              isMobile={isMobile}
              iconType="clear"
            >
              <DateClearIcon className={`vms_calendar_dateclearnicon`} />
            </IconContainer>
          )}
          {!isMobile && (
            <IconContainer
              className={`vms_DateRangeCalendar_IconCotainer`}
              onClick={onInputClick}
              isMobile={isMobile}
              iconType="date_selector"
            >
              <DateSelectorIcon className={`vms_calendar_dateselectoricon`} />
            </IconContainer>
          )}
        </IconWrapper>
      </DateInputSubContainer>
      {dateInputValue.errorMsg != "" && (
        <InputErrorDiv
          calendarType={calendarType}
          isMobile={isMobile}
          isEditting={isEditting}
        >
          <ErrorMessage>{dateInputValue.errorMsg}</ErrorMessage>
        </InputErrorDiv>
      )}
    </DateInputContainer>
  );
};
