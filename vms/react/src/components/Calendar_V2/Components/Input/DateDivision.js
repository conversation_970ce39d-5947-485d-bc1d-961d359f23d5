import React, { Fragment, useEffect, useRef } from "react";
import { matchValueBy } from "../../actions/DatePickerAction";

export const DateDivision = ({
  dateInputValue,
  onInputChange,
  onInputClick,
}) => {
  return (
    <Fragment>
      <div
        style={{
          display: "flex",
          gap: "3px",
          height: "100%",
          alignItems: "center",
        }}
      >
        {dateInputValue.inputformat_splitted.map((currentDivision, index) => {
          return (
            <DDMMYYYY
              key={currentDivision}
              division={currentDivision}
              activeDivision={dateInputValue.activeDivision}
              seprator={
                index < dateInputValue.inputformat_splitted.length - 1
                  ? dateInputValue.seprator
                  : null
              }
              maxLength={currentDivision.length}
              value={dateInputValue[currentDivision]}
              onInputClick={(e, division) => {
                onInputClick(e, division);
              }}
              onInputChange={(e, value, division) => {
                onInputChange(e, value, division);
              }}
            />
          );
        })}
      </div>
    </Fragment>
  );
};

const DDMMYYYY = ({
  value,
  seprator,
  division,
  activeDivision,
  maxLength,
  onInputChange,
  onInputClick,
  width,
  placeholder,
}) => {
  const inputRef = useRef();
  useEffect(() => {
    if (activeDivision === division) {
      inputRef.current.focus();
    }
  }, [activeDivision]);
  const onInputClickHandler = (e) => {
    inputRef.current.focus();
    onInputClick(e, division);
  };
  const onInputChangeHandler = (e) => {
    let { value } = e.target;
    value = value.trim();
    if (matchValueBy(value, division)) {
      onInputChange(e, value, division);
    }
  };
  return (
    <div style={{ display: "flex" }}>
      <input
        ref={inputRef}
        type="text"
        placeholder={placeholder != null ? placeholder : division}
        value={value}
        maxLength={maxLength}
        autoComplete="off"
        inputMode="text"
        style={{
          border: "none",
          outline: "none",
          width: width != null ? width : getWidth(division),
          userSelect: "none",
        }}
        onChange={onInputChangeHandler}
        onClick={onInputClickHandler}
      />
      {seprator != null && (
        <span style={{ userSelect: "none" }}>{seprator}</span>
      )}
    </div>
  );
};

const getWidth = (division) => {
  return division == "DD" ? "20px" : division == "MM" ? "25px" : "35px";
};
