import styled from "styled-components";

function border(isMobile, editting, theme, type) {
  if (isMobile) {
    return "none";
  } else {
    return editting
      ? `solid 1px ${theme?.palette?.[type]?.main}`
      : "solid 1px #4d4d4f";
  }
}

function borderBottom(isMobile, theme, type) {
  if (isMobile) {
    return `solid 1px ${theme?.palette?.[type]?.main}`;
  } else {
    return "";
  }
}

export const DateInputContainer = styled.div`
  position: ${(props) => (props.isMobile ? "relative" : "")};
  /* min-width: ${(props) => (props.isMobile ? "100%" : "320px")}; */
  width: ${(props) => (props.isMobile ? "100%" : "290px")};
  /* --widthA: calc(100% - 28px);
    --widthB: calc(var(--widthA) / 7); */
  max-width: ${(props) => (props.isMobile ? "" : "320px")};
`;

export const Label = styled.label`
  font-weight: 400;
  font-family: ${({ theme }) => theme?.typography?.fontFamily || ""};
  font-size: ${({ theme }) => `${theme?.typography?.text?.fontSize}` || ""};
  font-stretch: normal;
  font-style: normal;
  line-height: 20px;
  letter-spacing: 0.3px;
  color: #4d4d4f;
  display: block;
  position: relative;
  margin: ${(props) => (props.isMobile ? "12px 0" : "")};
  text-transform: uppercase;
`;

export const InputContainer = styled.input`
  display: block;
  box-sizing: border-box;
  font-weight: 700;
  font-family: ${({ theme }) => theme?.typography?.fontFamily || ""};
  font-size: ${({ theme }) => `${theme?.typography?.text?.fontSize}` || ""};
  font-stretch: normal;
  font-style: normal;
  line-height: ${({ isMobile }) => (isMobile ? "22px" : 1.43)};
  width: 100%;
  border-radius: 3px;
  letter-spacing: 0.3px;
  color: #4d4d4f;
  padding: ${({ isMobile }) => (!isMobile ? "14px 16px" : "")};
  margin-top: 12px;
  text-overflow: ellipsis;
  height: ${({ isMobile }) => (isMobile ? "auto" : "12px")};
  position: relative;
  border: ${({ theme, isMobile, calendarType, editting }) =>
    border(isMobile, editting ? true : false, theme, calendarType)};
  background-color: #fff;
  border-bottom: ${({ theme, isMobile, calendarType }) =>
    borderBottom(isMobile, theme, calendarType)};

  &::placeholder {
    font-weight: 400;
    font-family: ${({ theme }) => theme?.typography?.fontFamily || ""};
    font-size: 16px;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.43;
    border-radius: 3px;
    letter-spacing: 0.3px;
    color: #d2d3d4;
  }

  &:focus {
    outline: none;
    height: 48px;
    border-radius: 3px;
    border: ${({ theme, isMobile, calendarType }) =>
      border(isMobile, true, theme, calendarType)};
    border-bottom: ${({ theme, isMobile, calendarType }) =>
      borderBottom(isMobile, theme, calendarType)};
    background-color: #fff;
    border-bottom: 1px solid #03868b;
    color: #4d4d4f;
    outline: none;
  }
`;

export const IconWrapper = styled.div`
  display: flex;
  gap: 3px;
  height: 100%;
  align-items: center;
`;

export const IconContainer = styled.div`
  display: inline-block;
  background-size: 20px 18px !important;
  background-repeat: no-repeat !important;
  background-position: center !important;
  width: 24px;
  height: 24px;
  text-decoration: none;
  outline: none;
  bottom: ${({ isMobile }) => (isMobile ? "0px" : "")};
  top: ${({ isMobile }) => (isMobile ? "" : "45px")};
  right: ${({ iconType }) => (iconType == "clear" ? `40px` : `12px`)};
  cursor: pointer;
`;

export const DateInputSubContainer = styled.div`
  position: relative;
  display: flex;
  gap: 1%;
  justify-content: space-between;
  align-items: center;
  width: auto;
  border-radius: 3px 3px;
  padding: 1.5%;
  height: 24px;
  background-color: ${({isOverlay}) => isOverlay && `#fff`};
  border: ${({ theme, calendarType, isFocused, isDateInvalid }) =>
    `1px solid ${
      isFocused
        ? isDateInvalid
          ? "red"
          : theme.palette[calendarType][theme.mode]
        : `#4d4d4d`
    }`};
`;

export const InputErrorDiv = styled.div`
  border-left: ${({ theme, isMobile, isEditting, calendarType }) =>
    !isMobile &&
    isEditting &&
    `1px solid ${theme.palette[calendarType][theme.mode]}`};
  border-right: ${({ theme, isMobile, isEditting, calendarType }) =>
    !isMobile &&
    isEditting &&
    `1px solid ${theme.palette[calendarType][theme.mode]}`};
  padding: 5px;
`;

export const ErrorMessage = styled.span`
  color: ${({ theme }) => theme.palette.error[theme.mode]};
`;
