import styled from "styled-components";

const YearTableContainer = styled.div`
  display: flex;
  flex-wrap: wrap;
  margin: 20px 0px 0px 10px;
`;

export const YearText = styled.button`
  border: none;
  outline: none;
  background: ${(props) => (props.selected ? "#F1F6DE" : "transparent")};
  font-weight: ${(props) => (props.selected ? "600" : "400")};
  font-size: ${({ theme }) => `${theme?.typography?.text?.fontSize}` || ""};
  font-family: ${({ theme }) => theme?.typography?.fontFamily || ""};
  border-radius: 3px;
  text-align: center;
  height: auto;
  padding: 8px;
  color: #4d4d4f;
  line-height: 20px;
  letter-spacing: 0.3px;
  text-align: center;
  flex-basis: 25%;
  border-radius: 3px 3px;
  cursor: ${({ disabled }) => (disabled ? `not-allowed` : `pointer`)};
  &:hover {
    background-color: ${({ disabled }) =>
      disabled ? `transparent` : `#f1f6de`};
    filter: brightness(102%);
  }
`;

export const MonthButton = styled.button`
  border: none;
  outline: none;
  background: ${(props) => (props.selected ? "#f1f6de" : "transparent")};
  font-size: ${({ theme }) => `${theme?.typography?.text?.fontSize}` || ""};
  font-family: ${({ theme }) => theme?.typography?.fontFamily || ""};
  font-weight: ${(props) => (props.selected ? "600" : "")};
  border-radius: ${(props) => (props.selected ? "3px" : "")};
  color: #4d4d4f;
  font-size: 14px;
  line-height: 20px;
  letter-spacing: 0.3px;
  text-align: center;
  width: 100%;
  height: auto;
  padding: 8px;
  border-radius: 3px 3px;
  cursor: ${({ disabled }) => (disabled ? `not-allowed` : `pointer`)};
  &:hover {
    background-color: ${({ disabled }) =>
      disabled ? `transparent` : `#f1f6de`};
    filter: brightness(102%);
  }
`;
