import React from "react";
import {
  addMonth,
  formatWithLocale,
  getEndOfYear,
  getStartOfYear,
  getYears,
  getYearsPeriod,
  setYears,
  whetherSameMonth,
} from "../../../../hooks/calendarHooks";
import "../../style.css";
import { MonthButton, YearText } from "./SubView.styled";
import { DAY } from "../../actions/DatePickerAction";

export const SubView = ({
  selectedDate,
  tempDate,
  view,
  onItemClick,
  minDate,
  maxDate,
}) => {
  let cloneDate;
  let itemPerCol;
  const onDateClickHandler = (e) => {
    const setYear = setYears(tempDate, e);
    onItemClick(setYear, false);
  };

  switch (view) {
    case "year": {
      let yearsList = [];
      let selected = getYears(tempDate);
      const { startPeriod, endPeriod } = getYearsPeriod(tempDate, 16);
      for (let y = startPeriod; y <= endPeriod; y++) {
        yearsList.push(y);
      }

      return (
        <>
          {yearsList.map((item, index) => {
            const isYearInRange =
              item >= minDate.getFullYear() && item <= maxDate.getFullYear();
            return (
              <span
                key={`year-${index}`}
                className="button-container vms_Calendar_Year_button-container"
                style={{
                  opacity: isYearInRange ? `1` : `0.5`,
                  cursor: isYearInRange ? `pointer` : `not-allowed`,
                }}
              >
                <YearText
                  className="vms_Calendar_YearText"
                  selected={selected === item ? true : false}
                  disabled={!isYearInRange}
                  onClick={
                    isYearInRange ? () => onDateClickHandler(item) : null
                  }
                >
                  {item}
                </YearText>
              </span>
            );
          })}
        </>
      );
    }
    case "month": {
      const startOfYear = getStartOfYear(tempDate);
      const endOfYear = getEndOfYear(tempDate);
      let col = [];
      cloneDate = startOfYear;
      itemPerCol = 4;
      while (cloneDate < endOfYear) {
        for (let i = 0; i < itemPerCol && cloneDate < endOfYear; i += 1) {
          let isSelected = whetherSameMonth(cloneDate, tempDate);
          col.push({
            date: cloneDate,
            isSelected: isSelected,
          });
          cloneDate = addMonth(cloneDate, 1);
        }
      }

      return (
        <>
          {col.map((e, index) => {
            const isYearInRange =
              e.date.getFullYear() == minDate.getFullYear() ||
              e.date.getFullYear() == maxDate.getFullYear();
            let isMonthInRange = true;
            if (isYearInRange) {
              isMonthInRange =
                e.date.getMonth() >= minDate.getMonth() &&
                e.date.getMonth() <= maxDate.getMonth();
            }

            return (
              <span
                key={`month-${index}`}
                className="button-container vms_Calendar_month_button-container"
                onClick={() => isMonthInRange && onItemClick(e.date, false,DAY)}
                style={{
                  opacity: isMonthInRange ? `1` : `0.5`,
                  cursor: isMonthInRange ? `pointer` : `not-allowed`,
                }}
              >
                <MonthButton
                  className="vms_Calendar_MonthText"
                  selected={e.isSelected}
                  disabled={!isMonthInRange}
                >
                  {formatWithLocale(e.date, "LLL")}
                </MonthButton>
              </span>
            );
          })}
        </>
      );
    }
    default: {
      return undefined;
    }
  }
};
