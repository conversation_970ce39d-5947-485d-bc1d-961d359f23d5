import React from "react";
import ArrowUpSvg from "../../../../assets/images/calendar/arrow-up.svg";
import ArrowDownSvg from "../../../../assets/images/calendar/arrow-down.svg";
import {
  HeaderContainer,
  NavButton,
  NavDownButton,
  NavUpButton,
  TitleButton,
} from "./Header.styled";
import { formatWithLocale } from "../../../../hooks/calendarHooks";
export const DesktopHeader = ({
  selectedDate,
  next,
  prev,
  formatMonthYear,
  view,
  arrowImages,
  onHeaderBtnClick,
  isMinBtnDisabled,
  isMaxBtnDisabled,
}) => {
  return (
    <HeaderContainer className={"vms_Calendar_HeaderContainer"}>
      {view !== "year" ? (
        <TitleButton
          className={"vms_Calendar_HeaderTitle"}
          onClick={onHeaderBtnClick}
        >
          {formatWithLocale(selectedDate, formatMonthYear)}
        </TitleButton>
      ) : (
        <div></div>
      )}
      <NavButton>
        <NavUpButton
          isDisabled={isMinBtnDisabled}
          isCustomIcon={
            arrowImages?.upArrow != null && arrowImages?.disabledUpArrow != null
          }
          onClick={() => {
            if (!isMinBtnDisabled) {
              prev();
            }
          }}
          className={"vms_Calendar_Header_PrevButton"}
        >
          {arrowImages?.upArrow != null &&
          arrowImages?.disabledUpArrow != null ? (
            <img
              src={
                !isDateLessThanMinDate
                  ? arrowImages.upArrow
                  : arrowImages.disabledUpArrow
              }
              alt="prev"
            />
          ) : (
            <img src={ArrowUpSvg} alt="prev" />
          )}
        </NavUpButton>
        <NavDownButton
          isDisabled={isMaxBtnDisabled}
          isCustomIcon={
            arrowImages?.downArrow != null &&
            arrowImages?.disabledDownArrow != null
          }
          onClick={() => {
            if (!isMaxBtnDisabled) {
              next();
            }
          }}
          className={"vms_Calendar_Header_NextButton"}
        >
          {arrowImages?.downArrow != null &&
          arrowImages?.disabledDownArrow != null ? (
            <img
              src={
                !isMaxBtnDisabled
                  ? arrowImages.downArrow
                  : arrowImages.disabledDownArrow
              }
              alt="prev"
            />
          ) : (
            <img src={ArrowDownSvg} alt="next" />
          )}
        </NavDownButton>
      </NavButton>
    </HeaderContainer>
  );
};
