import React, { Fragment } from "react";
import { MobileHeader } from "./MobileHeader";
import { DesktopHeader } from "./DesktopHeader";
import { DAY, MONTH, YEAR } from "../../actions/DatePickerAction";

export const Header = ({
  selectedDate,
  next,
  prev,
  formatMonthYear,
  view,
  arrowImages,
  onHeaderBtnClick,
  minDate,
  maxDate,
  isMobile,
}) => {
  let isMinBtnDisabled = false;
  let isMaxBtnDisabled = false;
  const isMinDateSameYear = selectedDate.getYear() == minDate.getYear();
  if (view == YEAR) {
    isMinBtnDisabled = selectedDate.getFullYear() <= minDate.getFullYear();
    isMaxBtnDisabled = selectedDate.getFullYear() >= maxDate.getFullYear();
  } else if (view == MONTH || DAY) {
    if (isMinDateSameYear) {
      isMinBtnDisabled = selectedDate.getMonth() <= minDate.getMonth();
      isMaxBtnDisabled = selectedDate.getMonth() >= maxDate.getMonth();
    } else {
      isMinBtnDisabled = false;
      isMaxBtnDisabled = false;
    }
  }
  return (
    <Fragment>
      {isMobile ? (
        <MobileHeader
          arrowImages={arrowImages}
          formatMonthYear={formatMonthYear}
          isMaxBtnDisabled={isMaxBtnDisabled}
          isMinBtnDisabled={isMinBtnDisabled}
          next={next}
          prev={prev}
          selectedDate={selectedDate}
          onHeaderBtnClick={onHeaderBtnClick}
        />
      ) : (
        <DesktopHeader
          arrowImages={arrowImages}
          formatMonthYear={formatMonthYear}
          isMaxBtnDisabled={isMaxBtnDisabled}
          isMinBtnDisabled={isMinBtnDisabled}
          next={next}
          prev={prev}
          selectedDate={selectedDate}
          view={view}
          onHeaderBtnClick={onHeaderBtnClick}
        />
      )}
    </Fragment>
  );
};
