import styled from "styled-components";

/** DESKTOP VIEW STYLED COMPONENTS */
export const HeaderContainer = styled.div`
  position: relative;
  padding: ${(props) => (props.isMobile ? `20px 0px 0px 0px` : "0% 5%")};
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 47px;
  max-height: 47px;
`;

export const TitleButton = styled.button`
  border: none;
  display: inline-block;
  font-weight: 700;
  font-family: ${({ theme }) => theme?.typography?.fontFamily || ""};
  font-size: ${({ theme }) => `${theme?.typography?.text?.fontSize}` || ""};
  line-height: 20px;
  letter-spacing: 0.3px;
  outline: none;
  padding: 2% 5%;
  margin: 2% 0%;
  border-radius: 3px 3px;
  cursor: pointer;
  margin-left: ${(view) => (view === "year" ? "8px" : "")};
  box-shadow: 0 1px 1.5px rgba(0, 0, 0, 0.2);
`;

export const NavButton = styled.div`
  display: flex;
  right: 10px;
`;

export const NavUpButton = styled.div`
  display: inline-block;
  margin-right: 8px;
  height: 24px;
  width: 24px;
  cursor: ${({ isDisabled }) => (isDisabled ? `default` : `pointer`)};
  opacity: ${({ isDisabled, isCustomIcon }) =>
    isCustomIcon === false && isDisabled ? `0.6` : `1`};
`;

export const NavDownButton = styled.div`
  display: inline-block;
  height: 24px;
  width: 24px;
  cursor: ${({ isDisabled }) => (isDisabled ? `default` : `pointer`)};
  opacity: ${({ isDisabled, isCustomIcon }) =>
    isCustomIcon === false && isDisabled ? `0.6` : `1`};
`;

/** MOBILE VIEW STYLED COMPONENTS */
export const MobileHeaderWrapper = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  height: 40px;
  max-height: 40px;
  text-align: center;
  gap: 5px;
  padding: 5px;
`;
export const PrevAndNxtBtn = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  /* border: 1px solid red; */
  width: 15%;
  height: 30px;
  max-height: 30px;
  text-align: center;
  cursor: ${({ isDisabled }) =>
    isDisabled == true ? `not-allowed` : `pointer`};
  opacity: ${({ isDisabled }) => (isDisabled == true ? `0.5` : `1`)};
`;

export const MonthYearTitle = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  /* border: 1px solid blue; */
  width: 100%;
  height: 30px;
  max-height: 30px;
  text-align: center;
  background-color: lightgrey;
  filter: brightness(110%);
  box-shadow: rgba(0, 0, 0, 0.16) 0px 1px 4px;
  font-weight: 700;
  font-family: ${({ theme }) => theme?.typography?.fontFamily || ""};
  font-size: ${({ theme }) => `12px` || ""};
  line-height: 20px;
  letter-spacing: 0.3px;
`;

export const LeftRightIconSvg = styled.svg`
  width: 24px;
  color: ${({ theme }) => theme.palette.primary[theme.mode]};
`;
