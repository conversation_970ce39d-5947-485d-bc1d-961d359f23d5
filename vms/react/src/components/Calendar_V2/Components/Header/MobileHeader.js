import React from "react";
import { formatWithLocale } from "../../../../hooks/calendarHooks";
import {
  LeftRightIconSvg,
  MobileHeaderWrapper,
  MonthYearTitle,
  PrevAndNxtBtn,
} from "./Header.styled";

export const MobileHeader = ({
  prev,
  next,
  selectedDate,
  formatMonthYear,
  arrowImages,
  onHeaderBtnClick,
  isMinBtnDisabled,
  isMaxBtnDisabled,
}) => {
  return (
    <MobileHeaderWrapper>
      <PrevAndNxtBtn
        isDisabled={isMinBtnDisabled}
        onClick={isMinBtnDisabled ? null : prev}
      >
        {arrowImages?.upArrow != null ? (
          <img src={arrowImages?.upArrow} />
        ) : (
          <Icon direction={"prev"} />
        )}
      </PrevAndNxtBtn>
      <MonthYearTitle onClick={onHeaderBtnClick}>
        {formatWithLocale(selectedDate, formatMonthYear)}
      </MonthYearTitle>
      <PrevAndNxtBtn
        onClick={isMaxBtnDisabled ? null : next}
        isDisabled={isMaxBtnDisabled}
      >
        {arrowImages?.downArrow != null ? (
          <img src={arrowImages?.downArrow} />
        ) : (
          <Icon direction={"next"} />
        )}
      </PrevAndNxtBtn>
    </MobileHeaderWrapper>
  );
};

const Icon = ({ direction }) => {
  return (
    <LeftRightIconSvg focusable="false" viewBox="0 0 24 24" aria-hidden="true">
      <path
        fill="currentColor"
        d={
          direction == "prev"
            ? `M15.41 16.09l-4.58-4.59 4.58-4.59L14 5.5l-6 6 6 6z`
            : `M8.59 16.34l4.58-4.59-4.58-4.59L10 5.75l6 6-6 6z`
        }
      ></path>
    </LeftRightIconSvg>
  );
};
