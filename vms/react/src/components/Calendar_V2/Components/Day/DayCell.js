import React from "react";
import { format } from "date-fns";
import "../../style.css";
import { DayButton, Item } from "./DayCell.styled";
import {
  whetherSameDay,
  whetherSameMonth,
} from "../../../../hooks/calendarHooks";

export const DayCell = ({
  disabled,
  day,
  newSelectedDate,
  isMobile,
  isPassive,
  onDayClick,
}) => {
  const isSelected =
    !disabled &&
    newSelectedDate &&
    whetherSameDay(day, newSelectedDate) &&
    whetherSameMonth(day, newSelectedDate);
  return (
    <DayButton
      className={`${isMobile ? "rdrMobileDays" : ""}`}
      onClick={() => {
        onDayClick(day);
      }}
      disabled={disabled}
    >
      {isSelected ? (
        <span className="rdrSelected vms_Calendar_selectedDay"></span>
      ) : null}
      <Item
        className="vms_Calendar_itemMainContainer"
        isSelected={isSelected}
        isPassive={isPassive}
        isMobile={isMobile}
      >
        {<span className="vms_Calendar_dateItem">{format(day, "d")}</span>}
      </Item>
    </DayButton>
  );
};
