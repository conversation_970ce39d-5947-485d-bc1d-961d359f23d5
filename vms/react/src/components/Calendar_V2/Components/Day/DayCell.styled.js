import styled from "styled-components";

export const DayButton = styled.button`
  background: transparent;
  user-select: none;
  border: 0;
  padding: 0;
  line-height: 3em;
  height: 3em;
  text-align: center;
  color: #1d2429;
  box-sizing: inherit;
  width: calc(100% / 7);
  position: relative;
  font: inherit;
  &:disabled {
    pointer-events: none;
    color: #d1d3d4 !important;
  }
  &:hover {
    background: #f1f6de;
    text-align: center;
    filter: brightness(102%);
    border-radius: 5px;
  }
`;

export const Item = styled.span`
  outline: 0;
  font-weight: 300;
  font-weight: ${({ isSelected }) => (isSelected ? "bold" : 300)};
  position: ${({ isSelected }) => (isSelected ? "relative" : "absolute")};
  left: 0;
  right: 0;
  top: ${({ isSelected }) => (isSelected ? "0px" : "5px")};
  bottom: 0;
  bottom: 5px;
  display: ${({ isPassive, isMobile }) =>
    isPassive && isMobile ? "none" : "flex"};
  align-items: center;
  justify-content: center;
  z-index: ${({ isSelected }) => (isSelected ? 4 : "")};
`;
