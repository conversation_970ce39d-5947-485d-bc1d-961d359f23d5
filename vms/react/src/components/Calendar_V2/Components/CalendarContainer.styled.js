import styled from "styled-components";

export const Row = styled.div`
  margin: 0;
  padding: 0;
  flex-grow: 1;
  flex-basis: auto;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  align-items: flex-start;
  justify-content: ${(props) =>
    props.justifyContent ? props.justifyContent : ""};
`;

export const OverlayDiv = styled.div`
  top: 0%;
  left: 0%;
  width: 100%;
  height: 100%;
  position: fixed;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 99;
  display: ${(props) => (props.editting ? "block" : "none")};
`;

export const OverlayContainer = styled.div`
  position: ${({ isOverlay, isMobile }) =>
    isOverlay && !isMobile ? "relative" : ""};
  z-index: ${({ isOverlay, isMobile }) => (isOverlay && !isMobile ? 100 : "")};
`;

export const Divider = styled.div`
  overflow: hidden;
  border-top: 1px solid #33435b73;
  width: 100%;
  margin: 0 0 24px;
  padding: 0;
  height: auto !important;
`;

export const CalendarCloseButton = styled.div`
  margin: 0 auto;
  text-align: center;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  /* box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px; */
  box-shadow: rgba(0, 0, 0, 0.35) 0px 5px 15px;
`;

export const CalendarFlyOut = styled.div`
  display: flex;
  width: -webkit-max-content;
  width: -moz-max-content;
  width: max-content;
  border-radius: 3px;
  box-shadow: 0 2px 4px 0 rgb(0 0 0 / 12%);
  border: ${({ theme, type, isMobile }) =>
    (isMobile ? "none" : `solid 1px ${theme?.palette?.[type]?.main}`) || ""};
  background-color: #ffffff;
`;

/** MOBILE VIEW */
export const StyledInfiniteMonths = styled.div`
  height: calc(100vh - 335px);
  margin-top: -2px;
  overflow: auto;

  &::-webkit-scrollbar {
    width: 0;
    background: transparent;
  }
  &::-webkit-scrollbar-thumb {
    background: transparent;
  }
`;
