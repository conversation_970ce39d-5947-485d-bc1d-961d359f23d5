import React from "react";
import { eachDayOfInterval, endOfWeek, format, startOfWeek } from "date-fns";
import { WeekDayContainer, WeekDayLabel } from "./WeekDays.styled";

export const WeekDays = ({ dateOptions, weekdayDisplayFormat, now }) => {
  return (
    <WeekDayContainer className="vms_Calendar_weekdaysContainer">
      {eachDayOfInterval({
        start: startOfWeek(now, dateOptions),
        end: endOfWeek(now, dateOptions),
      }).map((day, i) => (
        <WeekDayLabel className="vms_Calendar_weekdaysText" key={`week-${i}`}>
          {format(day, weekdayDisplayFormat)}
        </WeekDayLabel>
      ))}
    </WeekDayContainer>
  );
};
