import React from "react";

import {
  format,
  isWithinInterval,
  eachDayOfInterval,
} from "date-fns";

import "../../style.css";
import { WeekDays } from "../Week/WeekDays";
import { WEEK_DAYS_DISPLAY_FORMAT } from "../../actions/DatePickerAction";
import { DayCell } from "../Day/DayCell";
import { getMonthDisplayRange, whetherDisabled } from "../../../../hooks/calendarHooks";

export const Month = ({
  selectedDate,
  showWeekDays,
  Monthstyle,
  isMobile,
  onDayClick,
  month,
  fixedHeight,
  showMonthName,
  dateOptions,
  minDate,
  maxDate,
}) => {
  const now = new Date();
  //const dateOptions = { locale: "en-US" };
  const monthDisplay = getMonthDisplayRange(month, dateOptions, fixedHeight);
  return (
    <div
      className={`${
        isMobile
          ? "rdMonthMobile vms_Calendar_monthMobileContainer"
          : "rdrMonth vms_Calendar_monthContainer"
      }`}
      style={Monthstyle}
    >
      {showMonthName ? (
        <div className={"rdrMonthMobileName vms_Calendar_monthNameText"}>
          {format(month, "MMM yyyy", dateOptions)}
        </div>
      ) : null}
      {showWeekDays && (
        <WeekDays
          dateOptions={dateOptions}
          now={now}
          weekdayDisplayFormat={WEEK_DAYS_DISPLAY_FORMAT}
        />
      )}
      <div
        className={`${
          isMobile
            ? "rdrDays  vms_Calendar_rdrMobileDaysContainer"
            : "rdrDesktopDays vms_Calendar_rdrDesktopDaysContainer"
        }`}
      >
        {eachDayOfInterval({
          start: monthDisplay.start,
          end: monthDisplay.end,
        }).map((day) => {
          const isDisabledDay = whetherDisabled(
            day,
            monthDisplay.startDateOfMonth,
            minDate,
            maxDate
          );
          return (
            <DayCell
              key={`day-${day}`}
              day={day}
              disabled={isDisabledDay}
              isPassive={
                !isWithinInterval(day, {
                  start: monthDisplay.startDateOfMonth,
                  end: monthDisplay.endDateOfMonth,
                })
              }
              isMobile={isMobile}
              newSelectedDate={selectedDate}
              onDayClick={(val) => {
                onDayClick(val);
              }}
            />
          );
        })}
      </div>
    </div>
  );
};
