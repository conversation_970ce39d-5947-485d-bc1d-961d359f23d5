import React, { Fragment, useEffect, useRef, useState } from "react";
import { LazyImage } from "../LazyImage/LazyImage";
import { Carousel_V1 as Carousel } from "./Carousel";
import { AnchoredHorizontalTabs } from "../AnchoredHorizontalTabs";
import { Skeleton } from "../Skeleton";
import { ModalPopup } from "../ModalPopup";
import "./Carousel.stories.css";

export default {
  title: "VMS_REACT/Carousel_V1",
  component: Carousel,
  argTypes: {
    additionalStyle: { control: { type: "" } },
    additionalClassName: { control: { type: "" } },
  },
};

const CustomDot = ({ onClick, active, index, carouselState }) => {
  console.log("CUSTOM DOT",onClick,active,index,carouselState);
  return (
    <div
      style={{
        boxShadow: "rgba(0, 0, 0, 0.35) 0px 5px 15px",
        background: active ? "white" : "grey",
        height: "10px",
        width: "30px",
        marginBottom: "5px",
        marginRight: "10px",
        borderRadius: "10px",
      }}
      onClick={() => onClick()}
    />
  );
};

const CustomButtonGroup = ({ next, previous, goToSlide, carouselState }) => {
  const { totalItems, currentSlide } = carouselState;
  return (
    <div className={"custom-button-group"}>
      <div>Current slide is {currentSlide}</div>
      <button onClick={() => previous()}>Previous slide</button>
      <button onClick={() => next()}>Next slide</button>
      <button
        onClick={() => goToSlide(Math.floor(Math.random() * totalItems + 1))}
      >
        Go to a random slide
      </button>
    </div>
  );
};

const nestedCarouselDataSix = [
  "#E6E5A3",
  "#A9AF7E",
  "#7D8F69",
  "#557153",
  "#FAD6A5",
  "#FA7070",
  "#81CACF",
];
const nestedCarouselDataFour = ["#E6E5A3", "#A9AF7E", "#7D8F69", "#557153"];
const images = [
  "https://images.unsplash.com/photo-1549989476-69a92fa57c36?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=crop&w=800&q=60",
  "https://images.unsplash.com/photo-1549396535-c11d5c55b9df?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=60",
  "https://images.unsplash.com/photo-1550133730-695473e544be?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=crop&w=800&q=60",
  "https://images.unsplash.com/photo-1550167164-1b67c2be3973?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=crop&w=800&q=60",
  "https://images.unsplash.com/photo-1550338861-b7cfeaf8ffd8?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=crop&w=800&q=60",
  "https://images.unsplash.com/photo-1550223640-23097fc71cb2?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=crop&w=800&q=60",
  "https://images.unsplash.com/photo-1550353175-a3611868086b?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=crop&w=800&q=60",
  "https://images.unsplash.com/photo-1550330039-a54e15ed9d33?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=crop&w=800&q=60",
  "https://images.unsplash.com/photo-1549737328-8b9f3252b927?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=crop&w=800&q=60",
  "https://images.unsplash.com/photo-1549833284-6a7df91c1f65?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=crop&w=800&q=60",
  "https://images.unsplash.com/photo-1549985908-597a09ef0a7c?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=crop&w=800&q=60",
  "https://images.unsplash.com/photo-1550064824-8f993041ffd3?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=crop&w=800&q=60",
];
const responsive = {
  desktop: {
    breakpoint: {
      max: 3000,
      min: 1024,
    },
    items: 1,
  },
  mobile: {
    breakpoint: {
      max: 464,
      min: 0,
    },
    items: 1,
    partialVisibilityGutter: 30,
  },
  tablet: {
    breakpoint: {
      max: 1024,
      min: 464,
    },
    items: 2,
    partialVisibilityGutter: 30,
  },
};
export const Default = (args) => {
  return (
    <div style={{ margin: "5%" }}>
      <Carousel id="carousal-id" responsive={responsive}>
        {images.map((element, index) => {
          return (
            <div key={index} style={{ height: "100%", width: "100%" }}>
              <img src={images[index]} width={"100%"} height="400px" />
            </div>
          );
        })}
      </Carousel>
    </div>
  );
};
Default.args = {
  responsive: responsive,
  deviceType: "desktop",
  showDots: false,
  // autoPlay:true,
};

export const NestedCarousel = (args) => {
  const itemss = nestedCarouselDataSix;
  return (
    <div style={{ margin: "2%" }}>
      <Carousel {...args} id="outer">
        {itemss.map((element, index) => {
          return (
            <div
              key={`carousel-child-${index + 1}`}
              id={`carousel-child-${index + 1}`}
              style={{
                // height:"350px",
                background: "#fff",

                textAlign: "center",
                color: "white",
                fontWeight: "bold",
                display: "block",
                borderRadius: "5px",
                boxShadow: "rgb(149 157 165 / 20%) 0px 8px 24px",
                marginBottom: "20px",
                // display: "flex",
                flexDirection: "column",
              }}
            >
              <div id="outer-custom-wrap">
                <Carousel
                  responsive={{
                    desktop: { breakpoint: { max: 3000, min: 1024 }, items: 1 },
                  }}
                  deviceType="desktop"
                  showDots={true}
                  arrows={false}
                  id="inner"
                  customLeftArrow={<p>{`<`}</p>}
                  customRightArrow={<p>{`>`}</p>}
                  autoPlay={true}
                >
                  {Array.from({ length: nestedCarouselDataFour.length }).map(
                    (element, index) => {
                      return (
                        <div
                          key={`nested-carousel-child-${index + 1}`}
                          id={`nested-carousel-child-${index + 1}`}
                          style={{
                            background: nestedCarouselDataFour[index],
                            minHeight: "175px",
                            width: "100%",
                            textAlign: "center",
                            color: "white",
                            fontWeight: "bold",
                            display: "block",
                          }}
                        >
                          <p style={{ margin: "0px", paddingTop: "40px" }}>
                            {index + 1}
                          </p>
                          <p>{`Color-Code: ${nestedCarouselDataFour[index]}`}</p>
                        </div>
                      );
                    }
                  )}
                </Carousel>
              </div>
              <div id={`word-index-${index}`} style={{ minHeight: "175px" }}>
                <div
                  style={{
                    display: "flex",
                    flexDirection: "column",
                    color: "black",
                    padding: "20px",
                  }}
                >
                  <b>Carousel Item</b>
                  <span>{index + 1}</span>
                </div>
              </div>
            </div>
          );
        })}
      </Carousel>
    </div>
  );
};
NestedCarousel.args = {
  responsive: {
    desktop: {
      breakpoint: { max: 3000, min: 1024 },
      items: 3,
    },
  },
  deviceType: "desktop",
  showDots: false,
  spacing: 5,
};

export const TwoSlideCarousel = (args) => {
  const carouselItems = nestedCarouselDataSix;
  return (
    <div style={{ margin: "2%" }}>
      <Carousel id={"2-slide-cid"} {...args}>
        {carouselItems.map((element, index) => {
          return (
            <div
              key={`carousel-child-${index + 1}`}
              id={`carousel-child-${index + 1}`}
              style={{
                height: "300px",
                background: element,

                textAlign: "center",
                color: "white",
                fontWeight: "bold",
                display: "block",
              }}
            >
              <div style={{ paddingTop: "15%" }}>
                <span>{index + 1}</span>
              </div>
            </div>
          );
        })}
      </Carousel>
    </div>
  );
};
TwoSlideCarousel.storyName = "Two Slides Carousel";
TwoSlideCarousel.args = {
  responsive: {
    desktop: {
      breakpoint: { max: 3000, min: 1024 },
      items: 2.5,
    },
  },
  deviceType: "desktop",
  showDots: true,
  partialVisible: true,
};

export const ThirdSlidePartialCarousel = (args) => {
  const carouselItems = nestedCarouselDataSix;
  return (
    <div style={{ margin: "2%" }}>
      <Carousel  id={"3-slide-cid"} {...args}>
        {carouselItems.map((element, index) => {
          return (
            <div
              key={`carousel-child-${index + 1}`}
              id={`carousel-child-${index + 1}`}
              style={{
                height: "300px",
                background: element,
                width: "100%",
                textAlign: "center",
                color: "white",
                fontWeight: "bold",
                display: "flex",
              }}
            >
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  height: "100%",
                  width: "100%",
                }}
              >
                <span>{index + 1}</span>
              </div>
            </div>
          );
        })}
      </Carousel>
    </div>
  );
};
ThirdSlidePartialCarousel.storyName = "Third Slide Partial Visible";
ThirdSlidePartialCarousel.args = {
  responsive: {
    desktop: {
      breakpoint: { max: 3000, min: 1024 },
      items: 3.5,
      partialVisibilityGutter: 10,
    },
  },
  deviceType: "desktop",
  showDots: true,
};

export const AutoPlayCarousel = (args) => {
  const carouselItems = nestedCarouselDataSix;
  return (
    <div style={{ margin: "2%" }}>
      <Carousel id={"auto-id"} {...args}>
        {carouselItems.map((element, index) => {
          return (
            <div
              key={`carousel-child-${index + 1}`}
              id={`carousel-child-${index + 1}`}
              style={{
                height: "300px",
                background: element,

                textAlign: "center",
                color: "white",
                fontWeight: "bold",
                display: "block",
              }}
            >
              <div style={{ paddingTop: "15%" }}>
                <span>{index + 1}</span>
              </div>
            </div>
          );
        })}
      </Carousel>
    </div>
  );
};

AutoPlayCarousel.storyName = "Auto-Play Carousel";
AutoPlayCarousel.args = {
  responsive: {
    desktop: {
      breakpoint: { max: 3000, min: 1024 },
      items: 1,
      partialVisibilityGutter: 0,
    },
  },
  deviceType: "desktop",
  showDots: true,
  autoPlay: true,
  autoPlaySpeed: 1000,
};

export const AutoPlayCarouselWithRenwind = (args) => {
  const carouselItems = nestedCarouselDataSix;
  return (
    <div style={{ margin: "2%" }}>
      <Carousel id={"auto-renwind-id"} {...args}>
        {carouselItems.map((element, index) => {
          return (
            <div
              key={`carousel-child-${index + 1}`}
              id={`carousel-child-${index + 1}`}
              style={{
                height: "300px",
                background: element,

                textAlign: "center",
                color: "white",
                fontWeight: "bold",
                display: "block",
              }}
            >
              <div style={{ paddingTop: "15%" }}>
                <span>{index + 1}</span>
              </div>
            </div>
          );
        })}
      </Carousel>
    </div>
  );
};

AutoPlayCarouselWithRenwind.storyName = "Auto Play Carousel With Renwind ";
AutoPlayCarouselWithRenwind.args = {
  responsive: {
    desktop: {
      breakpoint: { max: 3000, min: 1024 },
      items: 1,
      partialVisibilityGutter: 0,
    },
  },
  deviceType: "desktop",
  showDots: true,
  autoPlay: true,
  autoPlaySpeed: 1000,
  rewind: true,
};

export const AutoPlayCarouselInfinite = (args) => {
  const carouselItems = nestedCarouselDataSix;
  return (
    <div style={{ margin: "2%" }}>
      <Carousel id={"auto-id"} {...args}>
        {carouselItems.map((element, index) => {
          return (
            <div
              key={`carousel-child-${index + 1}`}
              id={`carousel-child-${index + 1}`}
              style={{
                height: "300px",
                background: element,

                textAlign: "center",
                color: "white",
                fontWeight: "bold",
                display: "block",
              }}
            >
              <div style={{ paddingTop: "15%" }}>
                <span>{index + 1}</span>
              </div>
            </div>
          );
        })}
      </Carousel>
    </div>
  );
};

AutoPlayCarouselInfinite.storyName = "Auto play in infinite mode ";
AutoPlayCarouselInfinite.args = {
  responsive: {
    desktop: {
      breakpoint: { max: 3000, min: 1024 },
      items: 1,
      partialVisibilityGutter: 0,
    },
  },
  deviceType: "desktop",
  showDots: true,
  autoPlay: true,
  infinite: true,
  autoPlaySpeed: 1000,
};

export const WithDots = (args) => {
  const carouselItems = nestedCarouselDataSix;
  return (
    <div style={{ margin: "2%" }}>
      <Carousel id={"dot-id"} {...args}>
        {carouselItems.map((element, index) => {
          return (
            <div
              key={`carousel-child-${index + 1}`}
              id={`carousel-child-${index + 1}`}
              style={{
                height: "300px",
                background: element,

                textAlign: "center",
                color: "white",
                fontWeight: "bold",
                display: "block",
              }}
            >
              <div style={{ paddingTop: "15%" }}>
                <span>{index + 1}</span>
              </div>
            </div>
          );
        })}
      </Carousel>
    </div>
  );
};

WithDots.storyName = "With Dots";
WithDots.args = {
  responsive: {
    desktop: {
      breakpoint: { max: 3000, min: 1024 },
      items: 1,
      partialVisibilityGutter: 0,
    },
  },
  deviceType: "desktop",
  showDots: true,
};

export const RenderedDotsOutside = (args) => {
  const carouselItems = nestedCarouselDataSix;
  return (
    <div style={{ padding: "0 80px" }}>
      <div
        style={{ margin: "2%", position: "relative", paddingBottom: "30px" }}
      >
        <Carousel id={"outside-id"} {...args}>
          <img
            src="https://images.unsplash.com/photo-1549989476-69a92fa57c36?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=crop&w=800&q=60"
            style={{
              display: "block",
              height: "100%",
              margin: "auto",
              width: "100%",
            }}
          />
          <img
            src="https://images.unsplash.com/photo-1549396535-c11d5c55b9df?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=60"
            style={{
              display: "block",
              height: "100%",
              margin: "auto",
              width: "100%",
            }}
          />
          <img
            src="https://images.unsplash.com/photo-1550133730-695473e544be?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=crop&w=800&q=60"
            style={{
              display: "block",
              height: "100%",
              margin: "auto",
              width: "100%",
            }}
          />
          <img
            src="https://images.unsplash.com/photo-1550167164-1b67c2be3973?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=crop&w=800&q=60"
            style={{
              display: "block",
              height: "100%",
              margin: "auto",
              width: "100%",
            }}
          />
          <img
            src="https://images.unsplash.com/photo-1550338861-b7cfeaf8ffd8?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=crop&w=800&q=60"
            style={{
              display: "block",
              height: "100%",
              margin: "auto",
              width: "100%",
            }}
          />
        </Carousel>
      </div>
    </div>
  );
};

RenderedDotsOutside.storyName =
  "With dots rendered outside of/after list container within custom div for styling";
RenderedDotsOutside.args = {
  responsive: {
    desktop: {
      breakpoint: { max: 3000, min: 1024 },
      items: 1,
      partialVisibilityGutter: 0,
    },
  },
  deviceType: "desktop",
  showDots: true,
  renderedDotsOutside: true,
};

export const CustomdotsTemplate = (args) => {
  const carouselItems = nestedCarouselDataSix;
  const carouselRef = useRef();
  const [selectedIndex, setSelectedIndex] = useState(0);
  return (
    <div style={{ padding: "0 80px" }}>
      <div
        style={{ margin: "2%", position: "relative", paddingBottom: "30px" }}
      >
        <Carousel
        id={"cust-dot-id"}
          {...args}
          activeCarouselIndex={selectedIndex}
          onCarouselSlideChange={(previousSlide, { currentSlide, onMove }) => {
            setSelectedIndex(currentSlide);
          }}
          customDot={<CustomDot 
          //   active={selectedIndex} onClick={(index)=>{
          //   setSelectedIndex(index);
          // }} 
          />}
        >
          <img
            src="https://images.unsplash.com/photo-1549989476-69a92fa57c36?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=crop&w=800&q=60"
            style={{
              display: "block",
              height: "100%",
              margin: "auto",
              width: "100%",
            }}
          />
          <img
            src="https://images.unsplash.com/photo-1549396535-c11d5c55b9df?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=60"
            style={{
              display: "block",
              height: "100%",
              margin: "auto",
              width: "100%",
            }}
          />
          <img
            src="https://images.unsplash.com/photo-1550133730-695473e544be?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=crop&w=800&q=60"
            style={{
              display: "block",
              height: "100%",
              margin: "auto",
              width: "100%",
            }}
          />
          <img
            src="https://images.unsplash.com/photo-1550167164-1b67c2be3973?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=crop&w=800&q=60"
            style={{
              display: "block",
              height: "100%",
              margin: "auto",
              width: "100%",
            }}
          />
          <img
            src="https://images.unsplash.com/photo-1550338861-b7cfeaf8ffd8?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=crop&w=800&q=60"
            style={{
              display: "block",
              height: "100%",
              margin: "auto",
              width: "100%",
            }}
          />
        </Carousel>
      </div>
    </div>
  );
};

CustomdotsTemplate.storyName = "Custom Dots";
CustomdotsTemplate.args = {
  responsive: {
    desktop: {
      breakpoint: { max: 3000, min: 1024 },
      items: 1,
      partialVisibilityGutter: 0,
    },
  },
  deviceType: "desktop",
  showDots: true,
};

export const CallbackFunction = (args) => {
  const carouselItems = nestedCarouselDataSix;
  return (
    <div style={{ padding: "0 80px" }}>
      <div
        style={{ margin: "2%", position: "relative", paddingBottom: "30px" }}
      >
        <Carousel
          {...args}
          slidesToSlide={1}
          onCarouselSlideChange={(previousSlide, { currentSlide, onMove }) =>
            alert(
              "onCarouselSlideChange callback" +
                "previous slide is " +
                previousSlide +
                " currentSlide is " +
                currentSlide
            )
          }
          onCarouselbeforeSlideChange={(
            previousSlide,
            { currentSlide, onMove }
          ) =>
            alert(
              "onCarouselbeforeSlideChange callback" +
                "previous slide is " +
                previousSlide +
                " currentSlide is " +
                currentSlide
            )
          }
        >
          <img
            src="https://images.unsplash.com/photo-1549989476-69a92fa57c36?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=crop&w=800&q=60"
            style={{
              display: "block",
              height: "100%",
              margin: "auto",
              width: "100%",
            }}
          />
          <img
            src="https://images.unsplash.com/photo-1549396535-c11d5c55b9df?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=60"
            style={{
              display: "block",
              height: "100%",
              margin: "auto",
              width: "100%",
            }}
          />
          <img
            src="https://images.unsplash.com/photo-1550133730-695473e544be?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=crop&w=800&q=60"
            style={{
              display: "block",
              height: "100%",
              margin: "auto",
              width: "100%",
            }}
          />
          <img
            src="https://images.unsplash.com/photo-1550167164-1b67c2be3973?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=crop&w=800&q=60"
            style={{
              display: "block",
              height: "100%",
              margin: "auto",
              width: "100%",
            }}
          />
          <img
            src="https://images.unsplash.com/photo-1550338861-b7cfeaf8ffd8?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=crop&w=800&q=60"
            style={{
              display: "block",
              height: "100%",
              margin: "auto",
              width: "100%",
            }}
          />
        </Carousel>
      </div>
    </div>
  );
};

CallbackFunction.storyName = "callback function when Carousel changes";
CallbackFunction.args = {
  responsive: {
    desktop: {
      breakpoint: { max: 3000, min: 1024 },
      items: 1,
      partialVisibilityGutter: 0,
    },
  },
  deviceType: "desktop",
  showDots: true,
};

export const CustomSlideCarouselTransition = (args) => {
  const carouselItems = nestedCarouselDataSix;
  return (
    <div style={{ display: "flex", flexDirection: "column" }}>
      <div>{`Custom Carousel Item transition is ${`"all 1s ease-in-out"`}`}</div>
      <div style={{ margin: "2%" }}>
        <Carousel {...args}>
          {carouselItems.map((element, index) => {
            return (
              <div
                key={`carousel-child-${index + 1}`}
                id={`carousel-child-${index + 1}`}
                style={{
                  height: "300px",
                  background: element,

                  textAlign: "center",
                  color: "white",
                  fontWeight: "bold",
                  display: "block",
                }}
              >
                <div style={{ paddingTop: "15%" }}>
                  <span>{index + 1}</span>
                </div>
              </div>
            );
          })}
        </Carousel>
      </div>
    </div>
  );
};
CustomSlideCarouselTransition.storyName = "Custom Transition Carousel";
CustomSlideCarouselTransition.args = {
  responsive: {
    desktop: {
      breakpoint: { max: 3000, min: 1024 },
      items: 1,
      partialVisibilityGutter: 0,
    },
  },
  deviceType: "desktop",
  showDots: true,
  autoPlay: true,
  autoPlaySpeed: 1,
  customTransition: "all 1s ease-in-out",
  infinite: true,
};

export const LazyLoadingCarousel = (args) => {
  const carouselItems = images;
  return (
    <div style={{ margin: "5%" }}>
      <Carousel {...args}>
        {carouselItems.map((element, index) => {
          return (
            <LazyImage
              key={index}
              src={index == 0 ? "https://images" : element}
              alt={"."}
              aspectRatio={[2, 1]}
              height="400px"
              placeholderImage={
                "https://img.icons8.com/cute-clipart/50/000000/nothing-found.png"
              }
            />
          );
        })}
      </Carousel>
    </div>
  );
};
LazyLoadingCarousel.storyName = "Lazy Loading Carousel";
LazyLoadingCarousel.args = {
  responsive: {
    desktop: {
      breakpoint: { max: 3000, min: 1024 },
      items: 1,
    },
  },
  deviceType: "desktop",
  showDots: false,
};

export const CustomControl = (args) => {
  const carouselItems = images;
  const carouselRef = React.useRef();
  const [currentIndex, setCurrentIndex] = useState(0);
  const [maxIndex, setMaxIndex] = useState(0);

  // Add methods to control the carousel
  const nextSlide = () => {
    setCurrentIndex((prevIndex) => prevIndex + 1);
  };

  const prevSlide = () => {
    setCurrentIndex((prevIndex) => prevIndex - 1);
  };
  console.log("carosel**", carouselRef);
  // console.log("currentIndex", currentIndex <= 0, currentIndex , currentIndex >= carouselItems.length - 1)

  return (
    <div style={{ margin: "5%" }}>
      <Carousel
        // {...args}
        ref={carouselRef}
        responsive={responsive}
        arrows={false}
        deviceType={"desktop"}
        showDot={false}
      >
        {carouselItems.map((element, index) => {
          return (
            <div key={index} style={{ height: "100%", width: "100%" }}>
              <img src={images[index]} width={"100%"} height="400px" />
            </div>
          );
        })}
      </Carousel>
      <button
        onClick={() => {
          carouselRef?.current.next();
        }}
      >
        next
      </button>
      <button
        onClick={() => {
          carouselRef?.current.previous();
        }}
      >
        prev
      </button>
    </div>
  );
};
CustomControl.args = {
  responsive: {
    desktop: {
      breakpoint: { max: 3000, min: 1024 },
      items: 1,
    },
  },
  arrows: false,
  deviceType: "desktop",
  showDots: false,
  // customButtonGroup: <CustomButtonGroup />,
};

export const MobileCarousel = (args) => {
  const carouselItems = nestedCarouselDataSix;
  return (
    <div>
      <Carousel {...args}>
        {carouselItems.map((element, index) => {
          return (
            <div
              key={`carousel-child-${index + 1}`}
              id={`carousel-child-${index + 1}`}
              style={{
                padding: "10px",
              }}
            >
              <div
                id={`Card-${index}`}
                style={{
                  boxShadow:
                    "rgba(0, 0, 0, 0.02) 0px 1px 3px 0px, rgba(27, 31, 35, 0.15) 0px 0px 0px 1px",
                  display: "flex",
                  flexDirection: "column",
                  borderRadius: "10px",
                }}
              >
                <div id="outer-custom-wrap" style={{ padding: "10px" }}>
                  <Carousel
                    responsive={args.responsive}
                    deviceType="mobile"
                    showDots={true}
                    arrows={false}
                    id="inner"
                    customLeftArrow={<p>{`<`}</p>}
                    customRightArrow={<p>{`>`}</p>}
                    additionalStyle={{ borderRadius: "5px" }}
                  >
                    {Array.from({ length: 4 }).map((element, index) => {
                      return (
                        <div
                          key={`nested-carousel-child-${index + 1}`}
                          id={`nested-carousel-child-${index + 1}`}
                          style={{
                            background: element,
                            minHeight: "175px",

                            textAlign: "center",
                            color: "white",
                            fontWeight: "bold",
                            display: "block",
                          }}
                        >
                          <p
                            style={{
                              margin: "0px",
                              paddingTop: "40px",
                              color: "black",
                            }}
                          >
                            {index + 1}
                          </p>
                          <p style={{ color: "black" }}>{`Color-Code: ${
                            index + 1
                          }`}</p>
                        </div>
                      );
                    })}
                  </Carousel>
                </div>
                <div
                  style={{
                    minHeight: "250px",
                    textAlign: "center",
                    display: "block",
                  }}
                >
                  <div
                    style={{
                      display: "flex",
                      flexDirection: "column",
                      color: "black",
                      paddingTop: "20px",
                    }}
                  >
                    <b>Carousel Item</b>
                    <b>{index + 1}</b>
                  </div>
                </div>
              </div>
            </div>
          );
        })}
      </Carousel>
    </div>
  );
};
MobileCarousel.storyName = "Mobile Carousel";
MobileCarousel.parameters = {
  viewport: { defaultViewport: "iphone5" },
};
MobileCarousel.args = {
  responsive: {
    mobile: {
      breakpoint: {
        max: 464,
        min: 0,
      },
      items: 1,
      partialVisibilityGutter: 0,
    },
  },
  deviceType: "mobile",
  showDots: true,
  arrows: true,
  swipeable: true,
  draggable: true,
};

export const SliderWithTabs = (args) => {
  const [sliderData, setSliderData] = useState([]);
  const [tabIndex, setSelectedTabIndex] = useState(1);
  const [activeSliderData, setActiveSliderData] = useState([]);
  const [showPopup, setShowPopup] = useState(false);
  const tabData = [
    {
      id: 1,
      title: "Image-Set-1",
    },
    {
      id: 2,
      title: "Image-Set-2",
    },
    {
      id: 3,
      title: "Image-Set-3",
    },
  ];
  const fetchData = async () => {
    const data = [];
    for (let count = 1; count <= 3; count++) {
      let limit = 7;
      if (count == 1) {
        limit = 7;
      } else if (count == 2) {
        limit = 3;
      } else {
        limit = 1;
      }
      const response = await fetch(
        `https://picsum.photos/v2/list?page=${count + 10}&limit=${limit}`
      );
      const jsonImgs = await response.json();
      const images = [];
      jsonImgs.forEach((e, index) => {
        images.push(e);
      });
      data.push({
        id: count - 1,
        images: images,
      });
      if (count == 3) {
        setSliderData(data);
        setActiveSliderData(data[1]);
      }
      console.log("json images", jsonImgs);
    }
  };
  useEffect(() => {
    console.log("useEffect fetchData");
    fetchData();
  }, []);
  console.log("sliderData", sliderData, activeSliderData);

  const onTabChangeHandler = (selectedTabIndex) => {
    const data = sliderData.find((data) => data.id == selectedTabIndex);
    console.log("onTabChangeHandler", data);
    setActiveSliderData(data);
    setSelectedTabIndex(selectedTabIndex);
  };

  return (
    <Fragment>
      <div
        style={{
          display: "flex",
          margin: "5%",
          flexDirection: "column",
          gap: "20px",
        }}
      >
        {/* <CarouselItems activeSliderData={activeSliderData}/>       */}
        {activeSliderData?.images?.length > 0 ? (
          <div>
            <Carousel
              responsive={{
                desktop: {
                  breakpoint: { max: 3000, min: 1024 },
                  items: activeSliderData?.images?.length > 1 ? 1.3 : 1,
                  partialVisibilityGutter: 0,
                },
              }}
              deviceType="desktop"
              showDots={true}
              autoPlay={false}
            >
              {activeSliderData.images.map((image, index) => {
                console.log("running map of images", image);
                return (
                  <div
                    key={`tab-${index}-${image.download_url}`}
                    style={{ height: "100%", width: "100%" }}
                  >
                    <img
                      key={image.download_url}
                      height={"300px"}
                      width={"100%"}
                      src={image.download_url}
                    />
                  </div>
                );
              })}
            </Carousel>
            <div
              style={{
                position: "absolute",
                top: "60%",
                height: "auto",
                left: "80%",
              }}
            >
              <button
                style={{
                  color: "blue",
                  background: "white",
                  height: "34px",
                  borderRadius: "15px",
                }}
                onClick={(e) => {
                  e.stopPropagation();
                  setShowPopup(true);
                }}
              >{`Show +${activeSliderData.images.length}`}</button>
            </div>
          </div>
        ) : (
          <div>
            <Skeleton height={"300px"} width={"100%"} />
          </div>
        )}
        <div>
          <AnchoredHorizontalTabs
            tabHeader={tabData}
            onChange={(selectedTabObject, selectedTabIndex) => {
              onTabChangeHandler(selectedTabIndex);
            }}
            selectedTabIndex={tabIndex}
          />
        </div>
        <div>
          <span>
            Lorem ipsum dolor sit amet, consectetur adipiscing elit. Curabitur
            id tellus in magna condimentum rutrum. Nulla magna purus,
            scelerisque vitae fringilla fringilla, rhoncus a nisl. Aliquam erat
            volutpat. Donec interdum, nunc ac porta molestie, purus ante
            tristique tortor, id molestie mauris arcu at est. Nulla facilisi.
            Cras tincidunt quam eget nisl accumsan, nec lacinia mauris iaculis.
            Aenean commodo, nulla pretium posuere blandit, elit orci finibus
            nisi, at accumsan tellus risus sed velit. Etiam nec fringilla ante,
            id condimentum nibh. Aliquam sed bibendum dolor. Nulla sagittis
            ullamcorper mauris et dictum. Nulla id dolor lectus. In ullamcorper
            non elit sed imperdiet. Vivamus orci est, efficitur ornare convallis
            nec, tristique id dui. Donec lobortis metus vitae commodo mattis. In
            hac habitasse platea dictumst. In tincidunt, nibh porta suscipit
            cursus, orci nunc luctus tortor, sed pellentesque diam orci a diam.
            Fusce nibh arcu, vulputate sit amet lacus bibendum, accumsan
            venenatis massa. Suspendisse potenti. Cras maximus posuere velit sed
            hendrerit. Mauris varius orci quam. Quisque imperdiet magna a nibh
            tempus, ut dignissim velit eleifend. Ut rutrum augue a neque feugiat
            rutrum. Quisque lobortis, mauris a congue tristique, tortor augue
            feugiat turpis, sed consectetur erat neque id felis. In hac
            habitasse platea dictumst.
          </span>
        </div>
      </div>
      {showPopup && (
        <ModalPopup
          closeButtonAlignment="right"
          isOpen={showPopup}
          onRequestClose={() => {
            setShowPopup(false);
          }}
          additionalStyle={{
            // left:"5px",
            // right:"5px",
            // top:"5px",
            // bottom:"5px",
            inset: "1%",
            // backgroundColor:"transparent"
          }}
          // overlayClassName={[styles.overlayBckgColor]}
        >
          <div
            style={{ display: "flex", flexDirection: "column", gap: "20px" }}
          >
            <div>
              <AnchoredHorizontalTabs
                tabHeader={tabData}
                onChange={(selectedTabObject, selectedTabIndex) => {
                  onTabChangeHandler(selectedTabIndex);
                }}
                selectedTabIndex={tabIndex}
                additionalStyle={{
                  position: "absolute",
                  top: "10px",
                  backgroundColor: "transparent",
                  width: "90%",
                }}
              />
              <div>
                <Carousel
                  responsive={{
                    desktop: {
                      breakpoint: { max: 3000, min: 1024 },
                      items: activeSliderData?.images?.length > 1 ? 1.2 : 1,
                      partialVisibilityGutter: 10,
                    },
                  }}
                  deviceType="desktop"
                  showDots={false}
                  autoPlay={false}
                  // additionalClassName={[styles.carousel_items]}
                >
                  {activeSliderData?.images?.map((image, index) => {
                    console.log("running map of images", image);
                    return (
                      <div
                        key={`tab-${index}-${image.download_url}`}
                        style={{ height: "100%", width: "100%" }}
                      >
                        <img
                          key={image.download_url}
                          height={400}
                          width={"100%"}
                          src={image.download_url}
                        />
                      </div>
                    );
                  })}
                </Carousel>
              </div>
            </div>
          </div>
        </ModalPopup>
      )}
    </Fragment>
  );
};
