import React, { useRef, useEffect } from "react";
import PropTypes from "prop-types";
import { useClassName } from "../../hooks/useClassName";
import {
  CarouselWrapperDiv,
  NextButton,
  NextButtonSvg,
  NextDiv,
  PrevButton,
  PrevButtonSvg,
  PrevDiv,
} from "./Carousel.styled";
import { ThemeWrapper } from "../Theme/ThemeContext";
import Carousel from "react-multi-carousel";
import "react-multi-carousel/lib/styles.css";
import styled from "styled-components";

const StyledCarousel = styled(Carousel)``;

export const Carousel_V1 = React.forwardRef((props, ref) => {
  const {
    id,
    children,
    responsive,
    deviceType,
    showDots,
    arrows,
    customLeftArrow,
    customRightArrow,
    infinite,
    customDot,
    autoPlay,
    autoPlaySpeed,
    draggable,
    swipeable,
    renderDotsOutside,
    containerClass,
    itemClass,
    dotClass,
    sliderClass,
    partialVisible,
    customTransition,
    additionalClassName,
    additionalStyle,
    onCarouselSlideChange,
    onCarouselbeforeSlideChange,
    spacing,
    rewind,
    slidesToSlide,
    customButtonGroup,
    shouldResetAutoplay,
    additionalTransfrom,
    centerMode,
    transitionDuration,
    keyBoardControl,
    minimumTouchDrag,
    renderArrowsWhenDisabled,
    removeArrowOnDeviceType,
    rewindWithAnimation,
    focusOnSelect,
    renderButtonGroupOutside,
    isMobile,
    isMobileView,
    activeSelectedIndex
  } = props;
  const className = useClassName(props, additionalClassName);
  const carouselWrapperRef = useRef(null);
  const carouselRef = useRef(null);
  // useEffect(() => {
  //   if (carouselWrapperRef.current) {
  //     const elements = document.getElementsByClassName('vms_carousel_container');


  //     const container = document.querySelector('.react-multi-carousel-list');
  //     console.log("container",container?.clientWidth)
  //     // if (container) {
  //     //   setContainerWidth(container.clientWidth);
  //     // }
  //     if (elements.length > 0) {
  //       const width = elements[0].offsetWidth;
  //       alert("width",container)
  //       console.log('Width of element with class "vms_carousel_container":',elements ,width);
  //       if(props.spacing !== null){
  //         const itemWidth = width/props.responsive[props.deviceType].items;
  //         alert("itemWidth",itemWidth)
  //         setItemWidth(itemWidth)
  //       }
  //     }
  //   }
  // }, [props.responsive,props.spacing]);

  // Added useEffect to handle activeSelectedIndex
  useEffect(() => {
    if (carouselRef.current && activeSelectedIndex !== undefined) {
      carouselRef.current.goToSlide(activeSelectedIndex);
    }
  }, [activeSelectedIndex]);

  useEffect(() => {
    const updateContainerWidth = () => {
      // const container = document.querySelector('.vms_carousel_container');

      // console.log("container",container)
      // if (container && spacing !== null) {
      //   const itemWidth = container.clientWidth/props.responsive[props.deviceType].items;
      //   console.log("itemWidth",itemWidth)
      //   setItemWidth(itemWidth)
      // }
    };

    // Initial update after render
    updateContainerWidth();

    // Update on window resize
    window.addEventListener('resize', updateContainerWidth);

    // Cleanup on component unmount
    return () => {
      window.removeEventListener('resize', updateContainerWidth);
    };
  }, [props]);



  const CustomLeftArrowDiv = ({ onClick }) => {
    return (
      <>
        {arrows && (
          <PrevDiv className="vms_carousel_previousdiv">
            {isNonNull(customLeftArrow) ? (
              customLeftArrow
            ) : (
              <PrevButton
              id={id ? id + "-prev" : null}
                className="vms_carousel_previousbutton"
                onClick={() => {
                  onClick();
                }}
              >
                <PrevSvg />
              </PrevButton>
            )}
          </PrevDiv>
        )}
      </>
    );
  };

  const CustomRightArrowDiv = ({ onClick }) => {
    return (
      <>
        {arrows && (
          <NextDiv className="vms_carousel_nextdiv">
            {isNonNull(customRightArrow) ? (
              customRightArrow
            ) : (
              <NextButton
              id={id ? id + "-next" : null}

                className="vms_carousel_nextbutton"
                onClick={() => {
                  onClick();
                }}
              >
                <NextSvg />
              </NextButton>
            )}
          </NextDiv>
        )}
      </>
    );
  };

  return (
    <ThemeWrapper isMobile={isMobile || isMobileView || false}>
      <CarouselWrapperDiv
        id={id}
        className={`${className} vms_carousel_container`}
        style={additionalStyle}
        ref={carouselWrapperRef}
      >
        <StyledCarousel
          additionalTransfrom={additionalTransfrom}
          arrows={arrows}
          draggable={draggable}
          ref={(el) => {
            carouselRef.current = el; // Set carouselRef
            if (typeof ref === 'function') {
              ref(el);
            } else if (ref) {
              ref.current = el;
            }
          }}
          infinite={infinite}
          autoPlay={autoPlay}
          autoPlaySpeed={autoPlaySpeed}
          customLeftArrow={<CustomLeftArrowDiv />}
          customRightArrow={<CustomRightArrowDiv />}
          responsive={responsive}
          rtl={false}
          showDots={showDots}
          swipeable={swipeable}
          partialVisible={partialVisible}
          deviceType={deviceType}
          spacing={spacing}
          rewind={rewind}
          renderDotsOutside={renderDotsOutside}
          customDot={customDot}
          slidesToSlide={slidesToSlide}
          afterChange={onCarouselSlideChange}
          beforeChange={onCarouselbeforeSlideChange}
          customTransition={customTransition}
          customButtonGroup={customButtonGroup}
          itemClass={itemClass}
          containerClass={containerClass}
          dotListClass={dotClass}
          sliderClass={sliderClass}
          rewindWithAnimation={rewindWithAnimation}
          shouldResetAutoplay={shouldResetAutoplay}
          centerMode={centerMode}
          transitionDuration={transitionDuration}
          focusOnSelect={focusOnSelect}
          keyBoardControl={keyBoardControl}
          minimumTouchDrag={minimumTouchDrag}
          renderArrowsWhenDisabled={renderArrowsWhenDisabled}
          removeArrowOnDeviceType={removeArrowOnDeviceType}
          renderButtonGroupOutside={renderButtonGroupOutside}
        >
          {children}
        </StyledCarousel>
      </CarouselWrapperDiv>
    </ThemeWrapper>
  );
});
Carousel_V1.defaultProps = {
  showDots: false,
  deviceType: "desktop",
  draggable: false,
  arrows: true,
  swipeable: true,
  removeArrowOnDeviceType: null,
  children: null,
  customLeftArrow: null,
  customRightArrow: null,
  customDot: null,
  autoPlay: false,
  autoPlaySpeed: 3000,
  // showDots: false,
  id: null,
  renderDotsOutside: false,
  itemClass: null,
  dotClass: null,
  partialVisible: false,
  customTransition: null,
  transitionDuration: null,
  slideDirection: "",
  activeCarouselIndex: 0,
  onCarouselLoad: null,
  onCarouselbeforeSlideChange: null,
  onCarouselSlideChange: null,
  infinite: false,
  customButtonGroup: null,
  containerClass: null,
  rewindWithAnimation: false,
  spacing: null,
  activeSelectedIndex: null,
};
Carousel_V1.propTypes = {
  /**
   * id of carousel item
   */
  id: PropTypes.string,
  /**
   * This prop will accept a object for the responsive type.
   * eg. desktop: { breakpoint: { max: 3000; min: 1024 }; items: 2; partialVisibilityGutter: 40; };
   */
  responsive: PropTypes.shape({
    ["desktop|mobile"]: PropTypes.shape({
      breakpoint: PropTypes.shape({
        min: PropTypes.number.isRequired,
        max: PropTypes.number.isRequired,
      }).isRequired,
      items: PropTypes.number.isRequired,
      partialVisibilityGutter: PropTypes.number,
    }),
  }).isRequired,
  /**
   * This prop can types of device which is currently consumed by, default will be desktop
   */
  deviceType: PropTypes.oneOf(["desktop", "mobile"]),
  /**
   * This prop will allow slide to be draggable. Default: `false`
   */
  draggable: PropTypes.bool,
  /**
   * To show or hide arrow in component. Default `true`
   */
  arrows: PropTypes.bool,
  /**
   * this prop will allow slide to be swipe able. Default: `true`
   */
  swipeable: PropTypes.bool,
  /**
   * this prop will display all the content in carousel component. `ReactNode`
   */
  children: PropTypes.node,
  /**
   * this prop will allow you configure the custom left arrow
   */
  customLeftArrow: PropTypes.node,
  /**
   * this prop will allow you to configure the custom right arrow
   */
  customRightArrow: PropTypes.node,
  /**
   * this prop will allow you to configure custom dots
   */
  customDot: PropTypes.node,
  // /**
  //  * After any slide has changed in carousel it will fire this event.
  //  * (previousSlide: number) => void
  //  */
  // afterChange: PropTypes.func,
  // /**
  //  * This event / callback will be called before a slide changed.
  //  * (nextSlide: number) => void
  //  */
  // beforeChange: PropTypes.func,

  /**
   * Use this to style the whole container. For example add padding to allow the "dots" or "arrows" to go to other places without being overflown.
   */

  containerClass: PropTypes.string,
  /**
   * Allows you to customize the custom item class for items in component.
   */
  itemClass: PropTypes.string,
  /**
   * this prop allows to customize the dot
   */
  dotClass: PropTypes.string,

  /**
   * CSS class for inner slider div, use this to style your own track list.
   */
  sliderClass: PropTypes.string,
  /**
   * Auto play for the items in component. Default `false`
   */
  autoPlay: PropTypes.bool,
  /**
   * Configure auto play speed, default `3000ms`
   */
  autoPlaySpeed: PropTypes.number,
  /**
   * show or hide dots. Default `false`
   */
  showDots: PropTypes.bool,
  /**
   * show dots outside of the container for custom styling. Default `false`
   */
  renderDotsOutside: PropTypes.bool,
  /**
   * make next slide paritalVisible. Default `false`
   */
  partialVisible: PropTypes.bool,
  /**
   * allows you to add any custom transition for the slide
   * `Example: all 1s linear`
   */
  customTransition: PropTypes.string,
  /**
   * configure the transition duration
   */
  transitionDuration: PropTypes.number,
  /**
   * Property to append additional css class
   */
  additionalClassName: PropTypes.arrayOf(PropTypes.string),
  /**
   * Property to set the `inline style` object
   */
  additionalStyle: PropTypes.object,
  /**
   * Ref element
   */
  ref: PropTypes.oneOfType([
    // Either a function
    PropTypes.func,
    // Or the instance of a DOM native element (see the note about SSR)
    PropTypes.shape({ current: PropTypes.instanceOf(Carousel) }),
  ]),
  /**
   * To set active index of a carousel
   * Note: please use this property alongwith
   * onCarouselSlideChange and onCarouselLoad props to control the entire carousel functionality
   */
   // Added prop type for activeSelectedIndex
  activeSelectedIndex: PropTypes.number,
  /**
   * Callback executed after carousel slide changed
   */
  onCarouselSlideChange: PropTypes.func,
  /**
   * A callback before sliding everytime.
   */
  onCarouselbeforeSlideChange: PropTypes.func,

  /**
   *Enables infinite scrolling in both directions. Carousel items are cloned in the DOM to achieve this.
   */
  infinite: PropTypes.bool,

  /**
   Use keyboard to navigate to next/previous slide
.
   */

  keyBoardControl: PropTypes.bool,

  /**
   *The unit is ms, if you are using customTransition, make sure to put the duration here as this is needed for the resizing to work.
   */
  transitionDuration: PropTypes.bool,

  /**
   *Go to slide on click and make the slide a current slide.
   */
  focusOnSelect: PropTypes.bool,

  /**
   *Shows the next items and previous items partially.
   */
  centerMode: PropTypes.bool,
  /**
   *additional transfrom to the current one.
   */

  additionalTransfrom: PropTypes.number,
  /**
   *resets autoplay when clicking next, previous button and the dots

   */

  shouldResetAutoplay: PropTypes.bool,

  /**
   *if infinite is not enabled and autoPlay explicitly is, this option rewinds the carousel when the end is reached (Lightweight infinite mode alternative without cloning).
   */
  rewind: PropTypes.bool,

  /**
   when rewinding the carousel back to the beginning, this decides if the rewind process should be instant or with transition.

   */
  rewindWithAnimation: PropTypes.bool,
  /**
  The amount of distance to drag / swipe in order to move to the next slide.
   */

  minimumTouchDrag: PropTypes.number,

  /**
 Fully customize your own control functionality if you don't want arrows or dots

   */
  customButtonGroup: PropTypes.node,

  /**
 Replace the default dots with your own

   */

  // customDot: PropTypes.node,
  /**
Allow for the arrows to have a disabled attribute instead of not showing them
   */
  renderArrowsWhenDisabled: PropTypes.bool,
  /**
 Hide the default arrows at different break point, should be used with responsive props. Value could be mobile or ['mobile', 'tablet'], can be a string or array

   */
  removeArrowOnDeviceType: PropTypes.string,
};

const PrevSvg = () => {
  return (
    <PrevButtonSvg
      xmlns="http://www.w3.org/2000/svg"
      width="9"
      height="14"
      viewBox="0 0 9 14"
    >
      <g fill="none" fillRule="evenodd">
        <path d="M0 0H24V24H0z" transform="translate(-8 -5)" />
        <g stroke="currentColor" strokeLinecap="round">
          <path
            d="M0 7L7 0M0 7L2.994 9.994 7 14"
            transform="translate(-8 -5) matrix(1 0 0 -1 9 19)"
          />
        </g>
      </g>
    </PrevButtonSvg>
  );
};

const NextSvg = () => {
  return (
    <NextButtonSvg
      xmlns="http://www.w3.org/2000/svg"
      width="9"
      height="14"
      viewBox="0 0 9 14"
    >
      <g fill="none" fillRule="evenodd">
        <path d="M0 0H24V24H0z" transform="translate(-8 -5)" />
        <g stroke="currentColor" strokeLinecap="round">
          <path
            d="M0 7L7 0M0 7L2.994 9.994 7 14"
            transform="translate(-8 -5) matrix(-1 0 0 1 16 5)"
          />
        </g>
      </g>
    </NextButtonSvg>
  );
};
const isNonNull = (prop) => {
  return prop !== null && prop !== undefined;
};
