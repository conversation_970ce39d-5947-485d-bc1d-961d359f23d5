import React from "react";

import { <PERSON> } from "./<PERSON>";

export default {
  title: "VMS_REACT/Link",
  component: Link,
  argTypes: {
    id: { control: { type: "" } },
    additionalStyle: { control: { type: "" } },
    additionalClassName: { control: { type: "" } },
    href: { control: { type: "" } },
    children: { control: { type: "" } },
    textVariant: { control: { type: "" } },
    color: { control: { type: "" } },
  },
};

const Template = (args) => <Link {...args}>Hotels in Ahmedabad</Link>;

export const Default = Template.bind({});
Default.args = {
  type: "primary",
  href:"https://interbook.intermiles.com/",
  isMobile:false,
  target:"_blank"
};

export const mobileLink = Template.bind({});
mobileLink.storyName = "MoWeb Default"
mobileLink.args = {
  type: "primary",
  href:"https://interbook.intermiles.com/",
  isMobile:true,
  target:"_blank"
};
