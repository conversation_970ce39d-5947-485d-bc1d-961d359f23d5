import React, { memo, useCallback, useMemo } from "react";
import PropTypes from "prop-types";
import { StyledLink } from "./Link.styled";
import { useClassName } from "../../hooks/useClassName";
import { ThemeWrapper } from "../Theme/ThemeContext";

// Utility function (placed before main component)
const isNonNull = (prop) => prop !== null && prop !== undefined;

const Link = memo((props) => {
  // 1. PROP DESTRUCTURING & VALIDATION
  const {
    children,
    href,
    target = "_self",
    onClick,
    disabled = false,
    additionalClassName,
    additionalStyle,
    isMobile,
    isMobileView,
    ...otherProps
  } = props;

  // 2. PERFORMANCE OPTIMIZATIONS
  const computedClassName = useMemo(
    () => useClassName(props, additionalClassName),
    [props, additionalClassName]
  );

  // 3. EVENT HANDLING with useCallback
  const handleClick = useCallback(
    (event) => {
      if (disabled) {
        event.preventDefault();
        return;
      }

      if (isNonNull(onClick)) {
        onClick(event);
      }
    },
    [disabled, onClick]
  );

  const handleKeyDown = useCallback(
    (event) => {
      if (event.key === "Enter" || event.key === " ") {
        handleClick(event);
      }
    },
    [handleClick]
  );

  // 5. ERROR HANDLING
  React.useEffect(() => {
    if (!href && !onClick) {
      console.warn(
        "Link: Either href or onClick should be provided for meaningful interaction"
      );
    }
  }, [href, onClick]);

  return (
    <ThemeWrapper isMobile={isMobile || isMobileView || false}>
      <StyledLink
        className={`${computedClassName} vms_link`}
        style={additionalStyle}
        href={disabled ? undefined : href}
        target={target}
        onClick={handleClick}
        onKeyDown={handleKeyDown}
        disabled={disabled}
        role={!href ? "button" : undefined}
        tabIndex={disabled ? -1 : 0}
        aria-disabled={disabled}
        {...otherProps}
      >
        {children}
      </StyledLink>
    </ThemeWrapper>
  );
});

// Component display name for debugging
Link.displayName = "Link";

Link.defaultProps = {
  additionalStyle: null,
  additionalClassName: null,
  isMobile: false,
  target: "_self",
  type: "primary",
};

Link.propTypes = {
  /**
    
  * Unique ID for the field. Required for web accessibility
   */
  id: PropTypes.string,

  /**
   * Property to append additional css class
   */
  additionalClassName: PropTypes.arrayOf(PropTypes.string),
  /**
   * Property to set the `style` object on button
   */
  additionalStyle: PropTypes.object,
  /**
   * Link url
   */
  href: PropTypes.string.isRequired,
  /**
   * Text or child component
   */
  children: PropTypes.oneOfType([PropTypes.node, PropTypes.string]).isRequired,

  /**
    * _blank - Opens the linked document in a new tab <br/>
      _self - Opens the linked document in the same frame as it was clicked (this is default) <br/>
    _parent - Opens the linked document in the parent frame <br/>
    _top - Opens the linked document in the full body of the window <br/>
    other - Opens the linked document in particular frame. Please pass frame name in prop @property otherTargetName <br/>
    "other" | "_self" | "_blank" | "_parent" | "_top"
    */
  target: PropTypes.oneOf(["_self", "_blank", "_parent", "_top", "other"]),
  /**
   * To render Mobile view, pass true to enable mobile view
   */
  isMobile: PropTypes.bool,
  /**
    * 	
      Text variant
      For isMobile false,<br/> default variant will be 14px <br/>
      For isMobile true, it will be 12px
    */
  textVariant: PropTypes.string,
  /**
    * 	
      string
    */
  color: PropTypes.string,
  /**
    * 	
      Variations of Link Type
    */
  type: PropTypes.oneOf(["primary", "secondary"]),
};

export { Link };
