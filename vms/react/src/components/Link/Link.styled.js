import styled from "styled-components";

export const StyledLink = styled.a`
  color: ${({ theme, type, color }) => (color ? color : theme?.palette?.[type]?.[theme.mode])};
  font-size: ${({ theme, isMobile }) => isMobile ? "12px" : theme?.typography?.text?.fontSize || ""};
  font-family: ${({ theme }) => theme?.typography?.fontFamily || ""};
  font-weight: ${({ theme }) => theme?.typography?.text?.fontWeight || ""};
  font-stretch: normal;
  font-style: normal;
  line-height: ${({ theme }) => theme?.typography?.lineHeight || ""};
  letter-spacing: ${({ theme }) => theme?.typography?.letterSpacing || ""};
  text-decoration: none;
`;