import styled from "styled-components";

export const WrapperDiv = styled.div``;
export const PaginationDiv = styled.div`
  display: flex;
  align-items: center;
  gap: 3px;
`;
export const LeftView = styled.div`
  display: flex;
  gap: 5px;
  align-items: center;
  opacity: ${({ isDisable }) => isDisable === true && 0.5};
  cursor: ${({ isDisable }) => (isDisable === false ? "pointer" : "initial")};
  color: ${({ theme }) => theme.palette.primary[theme.mode]};
`;
export const LeftArrowSvg = styled.svg`
  color: ${({ theme }) => theme.palette.primary[theme.mode]};
`;
export const LeftLabelView = styled.span`
  font-weight: ${({ theme }) => theme?.typography?.text?.fontWeight};
  font-family: ${({ theme }) => theme?.typography?.fontFamily};
  font-size: ${({ theme }) => theme?.typography?.text?.fontSize};
  color: ${({ theme }) => theme.palette.primary[theme.mode]};
  user-select: none;
`;
export const AllPagesWrapperDiv = styled.div``;
export const TotalPages = styled.ul`
  list-style: none;
  display: flex;
  padding: 0px;
  margin: 0px;
`;
export const Page = styled.li`
  display: flex;
  flex-direction: row;
  user-select: none;
  padding: 0px 2px;
  cursor: pointer;
  font-weight: ${({ theme, isCurrentPage }) =>
    isCurrentPage === true ? "bold" : theme?.typography?.text?.fontWeight};
  font-family: ${({ theme }) => theme?.typography?.fontFamily};
  font-size: ${({ theme }) => theme?.typography?.text?.fontSize};
  color: ${({ theme, isCurrentPage }) =>
    isCurrentPage === false && theme.palette.primary[theme.mode]};
`;
export const RightView = styled.div`
  display: flex;
  gap: 5px;
  align-items: center;
  opacity: ${({ isDisable }) => isDisable === true && 0.5};
  cursor: ${({ isDisable }) => (isDisable === false ? "pointer" : "initial")};
  color: ${({ theme }) => theme.palette.primary[theme.mode]};
`;
export const RightLabelView = styled.span`
  font-weight: ${({ theme }) => theme?.typography?.text?.fontWeight};
  font-family: ${({ theme }) => theme?.typography?.fontFamily};
  font-size: ${({ theme }) => theme?.typography?.text?.fontSize};
  user-select: none;
  color: ${({ theme }) => theme.palette.primary[theme.mode]};
`;
export const RightArrowSvg = styled.svg`
  color: ${({ theme }) => theme.palette.primary[theme.mode]};
`;
