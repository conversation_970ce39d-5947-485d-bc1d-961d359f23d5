import React from "react";

import { Pagination } from "./Pagination";

export default {
  title: "VMS_REACT/Pagination",
  component: Pagination,
  argTypes: {
    additionalStyle: { control: { type: "" } },
    additionalClassName: { control: { type: "" } },
  },
};

const Template = (args) => <Pagination {...args} />;

export const Default = Template.bind({});
Default.args = {
  marginPagesDisplayed: 1,
  pageRangeDisplayed: 2,
  initialPage: 0,
  disableInitialCallback: false,
  dataLength: 500,
  pageDataLimit: 25,
};
export const InitialPageStory = Template.bind({});
InitialPageStory.storyName = "Initial page number by application";
InitialPageStory.args = {
  marginPagesDisplayed: 2,
  pageRangeDisplayed: 2,
  initialPage: 5,
  disableInitialCallback: false,
  dataLength: 500,
  pageDataLimit: 25,
};
export const HiddenPageStory = Template.bind({});
HiddenPageStory.storyName = "Hidden if only one page data";
HiddenPageStory.args = {
  marginPagesDisplayed: 1,
  pageRangeDisplayed: 2,
  initialPage: 0,
  disableInitialCallback: false,
  dataLength: 20,
  pageDataLimit: 10,
};

export const CustomPreviewStory = Template.bind({});
CustomPreviewStory.storyName = "Custom previous and next button";
CustomPreviewStory.args = {
  marginPagesDisplayed: 2,
  pageRangeDisplayed: 2,
  initialPage: 5,
  disableInitialCallback: false,
  dataLength: 500,
  pageDataLimit: 25,
  previousLabel: (
    <div>
      <span>Back</span>
    </div>
  ),
  nextLabel: (
    <div>
      <span>Forward</span>
    </div>
  ),
};

export const CustomBreakLabelStory = Template.bind({});
CustomBreakLabelStory.storyName = "Custom break label (***)";
CustomBreakLabelStory.args = {
  marginPagesDisplayed: 1,
  pageRangeDisplayed: 2,
  initialPage: 0,
  disableInitialCallback: false,
  dataLength: 500,
  pageDataLimit: 25,
  breakLabel: "***",
};
