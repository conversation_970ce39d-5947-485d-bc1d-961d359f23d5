import React, { Fragment, useEffect, useState, memo, useCallback, useMemo } from "react";
import PropTypes from "prop-types";
import {
  WrapperDiv,
  PaginationDiv,
  LeftArrowSvg,
  TotalPages,
  Page,
  RightArrowSvg,
  LeftView,
  RightView,
  LeftLabelView,
  RightLabelView,
  AllPagesWrapperDiv,
} from "./Pagination.styled";
import { useClassName } from "../../hooks/useClassName";
import { ThemeWrapper } from "../Theme/ThemeContext";

// Utility functions (placed before main component)
const isNonNull = (prop) => prop !== null && prop !== undefined;
const isEven = (num) => num % 2 === 0;
const rangeDivisibleNumber = 2;

const addPageNumberToArray = (pagesData, pageInfo) => {
  const addElement = pagesData.filter((element) => element.pageNumber === pageInfo.pageNumber).length === 0;
  if (addElement) {
    pagesData.push({
      ...pageInfo,
      pageNumber: pageInfo.pageNumber,
    });
  }
  return pagesData;
};

const getAllPagesNumber = (totalPagesCount) => {
  const totalPagesToReturn = [];
  for (let count = 0; count < totalPagesCount; count++) {
    totalPagesToReturn.push(count + 1);
  }
  return totalPagesToReturn;
};

export const Pagination = memo((props) => {
  // 1. PROP DESTRUCTURING & VALIDATION
  const {
    dataLength,
    pageDataLimit,
    additionalStyle,
    additionalClassName,
    activeClassName,
    marginPagesDisplayed = 2,
    pageRangeDisplayed = 4,
    initialPage = 0,
    disableInitialCallback = false,
    previousLabel = "Previous",
    nextLabel = "Next",
    breakLinkClassName,
    pageClassName,
    pageLinkClassName,
    breakLabel = "...",
    breakClassName,
    onPageChange,
    activeLinkClassName,
    containerClassName,
    isMobile,
    isMobileView,
    ...otherProps
  } = props;

  // 2. STATE MANAGEMENT
  const [pageDetails, setPageDetails] = useState([]);
  const [allPagesNumber, setAllPagesNumber] = useState([]);
  const [leftViewDisable, setLeftViewDisable] = useState(true);
  const [rightViewDisable, setRightViewDisable] = useState(true);

  // 3. PERFORMANCE OPTIMIZATIONS
  const computedClassName = useMemo(() => 
    useClassName(props, additionalClassName), 
    [props, additionalClassName]
  );

  const totalPagesCount = useMemo(() => 
    Math.trunc(dataLength / pageDataLimit), 
    [dataLength, pageDataLimit]
  );

  const shouldShowPagination = useMemo(() => 
    totalPagesCount > 1 && totalPagesCount >= marginPagesDisplayed, 
    [totalPagesCount, marginPagesDisplayed]
  );

  // 4. EVENT HANDLING with useCallback
  const pageChangeHandler = useCallback((pageInfo) => {
    let currentPage = pageInfo.pageNumber;

    if (currentPage === breakLabel) {
      return;
    }

    const rangeDividedPage = Math.trunc(pageRangeDisplayed / rangeDivisibleNumber);
    let startPageNumber = currentPage - rangeDividedPage;
    let endPageNumber = currentPage + rangeDividedPage - 1;

    if (startPageNumber <= 0) {
      startPageNumber = 1;
      endPageNumber = pageRangeDisplayed;
    } else if (endPageNumber > totalPagesCount) {
      startPageNumber = totalPagesCount - pageRangeDisplayed + 1;
      endPageNumber = totalPagesCount;
    }

    let pageDataToAdd = [];
    
    if (startPageNumber > marginPagesDisplayed + 1) {
      for (let count = 1; count <= marginPagesDisplayed; count++) {
        pageDataToAdd = addPageNumberToArray(pageDataToAdd, {
          pageNumber: count,
          isEllipses: false,
        });
      }
      pageDataToAdd = addPageNumberToArray(pageDataToAdd, {
        pageNumber: breakLabel,
        isEllipses: true,
      });
    }

    for (let count = startPageNumber; count <= endPageNumber; count++) {
      pageDataToAdd = addPageNumberToArray(pageDataToAdd, {
        pageNumber: count,
        isEllipses: false,
      });
    }

    if (endPageNumber < totalPagesCount - marginPagesDisplayed) {
      pageDataToAdd = addPageNumberToArray(pageDataToAdd, {
        pageNumber: breakLabel,
        isEllipses: true,
      });
      for (let count = totalPagesCount - marginPagesDisplayed + 1; count <= totalPagesCount; count++) {
        pageDataToAdd = addPageNumberToArray(pageDataToAdd, {
          pageNumber: count,
          isEllipses: false,
        });
      }
    }

    setPageDetails(pageDataToAdd);
    setLeftViewDisable(currentPage === allPagesNumber[0]);
    setRightViewDisable(currentPage === allPagesNumber[allPagesNumber.length - 1]);

    if (isNonNull(onPageChange)) {
      onPageChange(pageInfo);
    }
  }, [pageRangeDisplayed, totalPagesCount, marginPagesDisplayed, breakLabel, allPagesNumber, onPageChange]);

  const handlePreviousPage = useCallback(() => {
    if (leftViewDisable) return;
    
    const currentPageIndex = pageDetails.findIndex(page => 
      !page.isEllipses && page.pageNumber > 1
    );
    
    if (currentPageIndex > 0) {
      const previousPage = pageDetails[currentPageIndex - 1];
      if (!previousPage.isEllipses) {
        pageChangeHandler(previousPage);
      }
    }
  }, [leftViewDisable, pageDetails, pageChangeHandler]);

  const handleNextPage = useCallback(() => {
    if (rightViewDisable) return;
    
    const currentPageIndex = pageDetails.findIndex(page => 
      !page.isEllipses && page.pageNumber < totalPagesCount
    );
    
    if (currentPageIndex < pageDetails.length - 1) {
      const nextPage = pageDetails[currentPageIndex + 1];
      if (!nextPage.isEllipses) {
        pageChangeHandler(nextPage);
      }
    }
  }, [rightViewDisable, pageDetails, totalPagesCount, pageChangeHandler]);

  // 5. EFFECTS
  useEffect(() => {
    if (!isEven(pageRangeDisplayed)) {
      throw new Error(
        `props pageRangeDisplayed must be even number. Current prop value is ${pageRangeDisplayed}`
      );
    }

    if (shouldShowPagination) {
      const updatedAllPagesNumber = getAllPagesNumber(totalPagesCount);
      setAllPagesNumber(updatedAllPagesNumber);
      const currentPage = updatedAllPagesNumber[initialPage];
      pageChangeHandler({ pageNumber: currentPage, isEllipses: false });
      setLeftViewDisable(currentPage === allPagesNumber[0]);
      setRightViewDisable(currentPage === allPagesNumber[allPagesNumber.length - 1]);
    }
  }, [pageRangeDisplayed, shouldShowPagination, totalPagesCount, initialPage, pageChangeHandler]);

  // 6. CONDITIONAL RENDERING (memoized)
  const paginationContent = useMemo(() => {
    if (!shouldShowPagination) return null;

    return (
      <PaginationDiv className="vms_pagination_container">
        <LeftView 
          isDisable={leftViewDisable} 
          onClick={handlePreviousPage}
          role="button"
          tabIndex={leftViewDisable ? -1 : 0}
          aria-disabled={leftViewDisable}
        >
          <LeftArrowSvg />
          <LeftLabelView>{previousLabel}</LeftLabelView>
        </LeftView>

        <AllPagesWrapperDiv className="vms_pagination_pages">
          {pageDetails.map((pageInfo, index) => (
            <Page
              key={index}
              onClick={() => !pageInfo.isEllipses && pageChangeHandler(pageInfo)}
              isEllipses={pageInfo.isEllipses}
              className={pageInfo.isEllipses ? breakClassName : pageClassName}
              role={pageInfo.isEllipses ? "presentation" : "button"}
              tabIndex={pageInfo.isEllipses ? -1 : 0}
              aria-current={!pageInfo.isEllipses ? "page" : undefined}
            >
              {pageInfo.pageNumber}
            </Page>
          ))}
        </AllPagesWrapperDiv>

        <RightView 
          isDisable={rightViewDisable} 
          onClick={handleNextPage}
          role="button"
          tabIndex={rightViewDisable ? -1 : 0}
          aria-disabled={rightViewDisable}
        >
          <RightLabelView>{nextLabel}</RightLabelView>
          <RightArrowSvg />
        </RightView>
      </PaginationDiv>
    );
  }, [
    shouldShowPagination,
    leftViewDisable,
    rightViewDisable,
    pageDetails,
    previousLabel,
    nextLabel,
    breakClassName,
    pageClassName,
    handlePreviousPage,
    handleNextPage,
    pageChangeHandler
  ]);

  // 7. ERROR HANDLING
  React.useEffect(() => {
    if (dataLength < 0) {
      console.warn('Pagination: dataLength should be a positive number');
    }
    if (pageDataLimit <= 0) {
      console.warn('Pagination: pageDataLimit should be greater than 0');
    }
  }, [dataLength, pageDataLimit]);

  return (
    <ThemeWrapper isMobile={isMobile || isMobileView || false}>
      <WrapperDiv 
        className={`${computedClassName} vms_pagination`}
        style={additionalStyle}
        role="navigation"
        aria-label="Pagination Navigation"
        {...otherProps}
      >
        {paginationContent}
      </WrapperDiv>
    </ThemeWrapper>
  );
});

// Component display name for debugging
Pagination.displayName = 'Pagination';

Pagination.defaultProps = {
  activeClassName: null,
  marginPagesDisplayed: 1,
  pageRangeDisplayed: 2,
  initialPage: 0,
  disableInitialCallback: false,
  additionalClassName: null,
  additionalStyle: null,
  breakLabel: "...",
  pageLinkClassName: null,
  pageClassName: null,
  breakClassName: null,
  breakLinkClassName: null,
  // activeClassName: null,
  activeLinkClassName: null,
};

Pagination.propTypes = {
  /**
   *Total data length that you want to display. it will be used to count page no
   */
  dataLength: PropTypes.number.isRequired,
  /**
   * Data per page. it will be used to count page no
   */
  pageDataLimit: PropTypes.number.isRequired,
  /**
   * The method to call when a page is clicked. Exposes the current page object as an argument.
   * (e: any) => void
   */
  onPageChange: PropTypes.func,
  /**
   * Label for the previous button. you need to pass custom created html tag here
   */
  previousLabel: PropTypes.object,
  /**
   * Label for the next button. you need to pass custom created html tag here
   */
  nextLabel: PropTypes.object,
  /**
   * Set label for ellipsis Label. Default value `...`
   * string
   */
  breakLabel: PropTypes.string,
  /**
   * The classname on tag li of the ellipsis element.
   */
  breakClassName: PropTypes.string,
  /**
   * The classname on tag a of the ellipsis element.
   */
  breakLinkClassName: PropTypes.string,
  /**
   * The classname of the pagination container.
   */
  containerClassName: PropTypes.string,
  /**
   * The classname for the active page
   */
  activeClassName: PropTypes.string,
  /**
   * The classname on the active tag a.
   */
  activeLinkClassName: PropTypes.string,
  /**
   * The number of pages to display for margins from both the end.
   * Default value `1` ex: if you have total 20 pages and marginPagesDisplayed = 2 and current selected page is 5 then
   * it will display `1 2 ... 4 5 6 ... 19 20`
   */
  marginPagesDisplayed: PropTypes.number,
  /**
   * The range of pages displayed. Default value `2` You need to pass `even numbers`.
   * it will be divided by 2 and the result will be added before and after to current selected page ex: if you
   * have total 20 pages and pageRangeDisplayed = 2 and current selected page is 5
   * then it will display `1 ... 4 5 6 ... 20`
   */
  pageRangeDisplayed: PropTypes.number,
  /**
   * The initial page selected. Default value `0`
   * Note: index start from `0`
   */
  initialPage: PropTypes.number,
  /**
   * The classname on tag li of each page element.
   */
  pageClassName: PropTypes.string,
  /**
   * The classname on tag a of each page element.
   */
  pageLinkClassName: PropTypes.string,
  /**
   * Disable onPageChange callback with initial page.
   * Default: `false`
   */
  disableInitialCallback: PropTypes.bool,
  /**
   * Property to append additional css class
   */
  additionalClassName: PropTypes.arrayOf(PropTypes.string),

  /**
   * Property to set the `inline style` object
   */
  additionalStyle: PropTypes.object,
};

const LeftArrow = (props) => {
  return (
    <LeftArrowSvg
      width="11px"
      height="11px"
      viewBox="0 0 20 18"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g stroke="none" fill="none" fillRule="evenodd">
        <g id="pagination-left-arrow" fill="currentColor">
          <path
            d="M0.326,8.114 C-0.109,8.552 -0.109,9.402 0.327,9.841 L8.094,17.671 C8.30256683,17.8823102 8.58709574,18.0012565 8.884,18.0012565 C9.18090426,18.0012565 9.46543317,17.8823102 9.674,17.671 C10.1095208,17.2300814 10.1095208,16.5209186 9.674,16.08 L2.65,9 L9.673,1.92 C10.1078627,1.47920075 10.1078627,0.770799255 9.673,0.33 C9.46443317,0.118689761 9.17990426,-0.000256452415 8.883,-0.000256452415 C8.58609574,-0.000256452415 8.30156683,0.118689761 8.093,0.33 L0.327,8.114 L0.326,8.114 Z"
            id="Path"
          ></path>
          <path
            d="M10.326,8.114 C9.891,8.552 9.891,9.402 10.327,9.841 L18.094,17.671 C18.3025668,17.8823102 18.5870957,18.0012565 18.884,18.0012565 C19.1809043,18.0012565 19.4654332,17.8823102 19.674,17.671 C20.1095208,17.2300814 20.1095208,16.5209186 19.674,16.08 L12.65,9 L19.673,1.92 C20.1078627,1.47920075 20.1078627,0.770799255 19.673,0.33 C19.4644332,0.118689761 19.1799043,-0.000256452415 18.883,-0.000256452415 C18.5860957,-0.000256452415 18.3015668,0.118689761 18.093,0.33 L10.326,8.114 L10.326,8.114 Z"
            id="Path"
          ></path>
        </g>
      </g>
    </LeftArrowSvg>
  );
};

const RightArrow = (props) => {
  return (
    <RightArrowSvg
      width="11px"
      height="11px"
      viewBox="0 0 20 18"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
      xmlnsXlink="http://www.w3.org/1999/xlink"
    >
      <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g id="pagination-right-arrow" fill="currentColor">
          <path
            d="M19.673,8.159 L11.906,0.329 C11.6974332,0.117689761 11.4129043,-0.00125645242 11.116,-0.00125645242 C10.8190957,-0.00125645242 10.5345668,0.117689761 10.326,0.329 C9.89047919,0.769918637 9.89047919,1.47908136 10.326,1.92 L17.35,9 L10.327,16.08 C9.89213729,16.5207993 9.89213729,17.2292007 10.327,17.67 C10.5355668,17.8813102 10.8200957,18.0002565 11.117,18.0002565 C11.4139043,18.0002565 11.6984332,17.8813102 11.907,17.67 L19.674,9.886 C20.109,9.448 20.109,8.598 19.673,8.159 Z"
            id="Path"
          ></path>
          <path
            d="M9.674,9.886 C10.109,9.448 10.109,8.598 9.673,8.159 L1.906,0.329 C1.69743317,0.117689761 1.41290426,-0.00125645242 1.116,-0.00125645242 C0.81909574,-0.00125645242 0.534566829,0.117689761 0.326,0.329 C-0.109520811,0.769918637 -0.109520811,1.47908136 0.326,1.92 L7.35,9 L0.327,16.08 C-0.107862714,16.5207993 -0.107862714,17.2292007 0.327,17.67 C0.535566829,17.8813102 0.82009574,18.0002565 1.117,18.0002565 C1.41390426,18.0002565 1.69843317,17.8813102 1.907,17.67 L9.674,9.886 Z"
            id="Path"
          ></path>
        </g>
      </g>
    </RightArrowSvg>
  );
};
