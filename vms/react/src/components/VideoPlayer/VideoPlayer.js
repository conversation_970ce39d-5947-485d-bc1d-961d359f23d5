import React, { useRef, useEffect, Fragment, memo, useCallback, useMemo } from "react";
import PropTypes from "prop-types";
import { ThemeWrapper } from "../Theme/ThemeContext";
import { Video, VideoOverlay, VideoWrapper } from "./VideoPlayer.styled";
import { useClassName } from "../../hooks/useClassName";

const isNonNull = (prop) => {
  return prop !== null && prop !== undefined;
};

// Memoized VideoSources component
const VideoSources = memo(({ url }) => {
  const sources = useMemo(() => {
    if (typeof url === "string") {
      return (
        <source src={url} className="vms_video_player_source" type="video/mp4" />
      );
    }
    
    return url.map((currentUrl, index) => (
      <source 
        key={`${currentUrl}-${index}`} 
        src={currentUrl} 
        className="vms_video_player_source"
        type="video/mp4"
      />
    ));
  }, [url]);

  return <Fragment>{sources}</Fragment>;
});

VideoSources.displayName = 'VideoSources';

export const VideoPlayer = memo((props) => {
  const {
    url,
    width,
    height,
    play = false,
    controls = true,
    muted = false,
    poster,
    loop = false,
    autoPlay = false,
    additionalClassName,
    pausedOverlay,
    onMouseEnter,
    onMouseLeave,
    onClick,
    onPause,
    onPlay,
    onLoadedData,
    onError,
    startTime,
    isMobile,
    isMobileView,
    ...otherProps
  } = props;

  const videoElement = useRef(null);

  // Memoized className
  const className = useMemo(() => 
    useClassName(props, additionalClassName), 
    [props, additionalClassName]
  );

  // Optimized event handlers with useCallback
  const handleMouseEnter = useCallback((e) => {
    if (onMouseEnter) {
      try {
        onMouseEnter(e);
      } catch (error) {
        console.error("VideoPlayer: Error in onMouseEnter callback", error);
      }
    }
  }, [onMouseEnter]);

  const handleMouseLeave = useCallback((e) => {
    if (onMouseLeave) {
      try {
        onMouseLeave(e);
      } catch (error) {
        console.error("VideoPlayer: Error in onMouseLeave callback", error);
      }
    }
  }, [onMouseLeave]);

  const handleClick = useCallback((e) => {
    if (onClick) {
      try {
        onClick(e);
      } catch (error) {
        console.error("VideoPlayer: Error in onClick callback", error);
      }
    }
  }, [onClick]);

  const handlePause = useCallback((e) => {
    if (onPause) {
      try {
        onPause(e);
      } catch (error) {
        console.error("VideoPlayer: Error in onPause callback", error);
      }
    }
  }, [onPause]);

  const handlePlay = useCallback((e) => {
    if (onPlay) {
      try {
        onPlay(e);
      } catch (error) {
        console.error("VideoPlayer: Error in onPlay callback", error);
      }
    }
  }, [onPlay]);

  const handleLoadedData = useCallback((e) => {
    if (onLoadedData) {
      try {
        onLoadedData(e);
      } catch (error) {
        console.error("VideoPlayer: Error in onLoadedData callback", error);
      }
    }
  }, [onLoadedData]);

  const handleError = useCallback((e) => {
    console.error("VideoPlayer: Video loading error", e);
    if (onError) {
      try {
        onError(e);
      } catch (error) {
        console.error("VideoPlayer: Error in onError callback", error);
      }
    }
  }, [onError]);

  const handleKeyDown = useCallback((e) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      handleClick(e);
    }
  }, [handleClick]);

  // Video control effects
  useEffect(() => {
    const video = videoElement.current;
    if (!video) return;

    const playVideo = async () => {
      try {
        if (isNonNull(startTime) && video.currentTime !== undefined) {
          video.currentTime = startTime;
        }
        
        if (play && video.play) {
          await video.play();
        } else if (!play && video.pause) {
          video.pause();
        }
      } catch (error) {
        console.error("VideoPlayer: Error controlling video playback", error);
        if (onError) {
          onError(error);
        }
      }
    };

    playVideo();
  }, [startTime, play, onError]);

  // Error handling for props
  useEffect(() => {
    if (!url) {
      console.warn('VideoPlayer: url prop is required');
    }
    if (!width || !height) {
      console.warn('VideoPlayer: width and height props are required');
    }
  }, [url, width, height]);

  // Memoized video element
  const videoElement_memo = useMemo(() => (
    <Video
      width={width}
      height={height}
      ref={videoElement}
      muted={muted}
      poster={poster}
      autoPlay={autoPlay}
      className={`vms_video_player ${className}`}
      controls={controls}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onClick={handleClick}
      onPause={handlePause}
      onPlay={handlePlay}
      onLoadedData={handleLoadedData}
      onError={handleError}
      onKeyDown={handleKeyDown}
      loop={loop}
      role="video"
      aria-label={`Video player${poster ? ' with poster' : ''}`}
      tabIndex={0}
      preload="metadata"
      {...otherProps}
    >
      <VideoSources url={url} />
      <p>Your browser does not support the video tag.</p>
    </Video>
  ), [
    width, height, muted, poster, autoPlay, className, controls, loop,
    handleMouseEnter, handleMouseLeave, handleClick, handlePause, 
    handlePlay, handleLoadedData, handleError, handleKeyDown, url, otherProps
  ]);

  // Memoized overlay
  const overlayElement = useMemo(() => {
    if (!isNonNull(pausedOverlay)) return null;
    
    return (
      <VideoOverlay 
        visible={!play} 
        className="vms_video_player_videooverlay"
        role="img"
        aria-hidden={play}
      >
        {pausedOverlay}
      </VideoOverlay>
    );
  }, [pausedOverlay, play]);

  return (
    <ThemeWrapper isMobile={isMobile || isMobileView || false}>
      <VideoWrapper 
        width={width} 
        height={height} 
        className="vms_video_player_wrapper"
        role="region"
        aria-label="Video player container"
      >
        {videoElement_memo}
        {overlayElement}
      </VideoWrapper>
    </ThemeWrapper>
  );
});

VideoPlayer.displayName = 'VideoPlayer';

VideoPlayer.defaultProps = {
  controls: true,
  play: false,
  muted: false,
  loop: false,
  autoPlay: false,
};

VideoPlayer.propTypes = {
  url: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.arrayOf(PropTypes.string),
  ]).isRequired,
  /**
   * The width of the video's display area, in CSS pixels (absolute values only; no percentages).
   * Mandatory to give the value
   */
  width: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
  /**
   * The height of the video's display area, in CSS pixels (absolute values only; no percentages).
   *  Mandatory to give the value
   */
  height: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
  play: PropTypes.bool,
  /**
   * A Boolean attribute that indicates the default setting of the audio contained in the video.
   * If set, the audio will be initially silenced.
   * Its default value is false, meaning that the audio will be played when the video is played.
   */
  muted: PropTypes.bool,
  /**
   * A URL for an image to be shown while the video is downloading.
   * If this attribute isn't specified, nothing is displayed until the first frame is available, then the first frame is shown as the poster frame.
   */
  poster: PropTypes.string,
  /**
   * A Boolean attribute; if specified, the video automatically begins to play back as soon as it can do so without stopping to finish loading the data.
   */
  autoPlay: PropTypes.bool,
  /**
   * A Boolean attribute; if specified, the browser will automatically seek back to the start upon reaching the end of the video.
   */
  loop: PropTypes.bool,
  /**
   *
   */
  onClick: PropTypes.func,
  onPlay: PropTypes.func,
  onLoadedData: PropTypes.func,
  onError: PropTypes.func,
  /**
   * If this attribute is present, the browser will offer controls to allow the user to control video playback, including volume, seeking, and pause/resume playback.
   */
  controls: PropTypes.bool,
  /**
   * This optional prop accepts any renderable content that you would like to be displayed over the video while it is in a paused.
   * When the video starts playing, this content will be faded out.
   */
  pausedOverlay: PropTypes.node,
  /**
   * Property to append additional css class
   */
  additionalClassName: PropTypes.arrayOf(PropTypes.string),

  /**
   * Property to set the `inline style` object
   */
  additionalStyle: PropTypes.object,
  
  startTime: PropTypes.number,
  isMobile: PropTypes.bool,
  isMobileView: PropTypes.bool,
};
