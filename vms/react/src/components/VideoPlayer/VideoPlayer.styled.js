import styled from "styled-components";
export const VideoWrapper = styled.div`
  position: relative;
  width: ${({ width }) => `${isNaN(width) ? width : `${width}px`}`};
  height: ${({ height }) => `${isNaN(height) ? height : `${height}px`}`};
`;

export const Video = styled.video`
  object-fit: fill;
`;

export const VideoOverlay = styled.div`
  position: absolute;
  top: 0;
  height: 100%;
  width: 100%;
  display: ${({ visible }) => (visible === false ? "none" : "block")};
  transform: translateZ(100vw);
  transition: transform 1s 0s;
`;
