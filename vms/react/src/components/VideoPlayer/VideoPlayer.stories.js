import { useState } from "react";
import { VideoPlayer } from "./VideoPlayer";
import { Carousel } from "../Carousel/Carousel";
import posterSrc from "../../assets/images/VideoPlayer/Poster_Movie.jpeg";
import posterSrc2 from "../../assets/images/VideoPlayer/Poster_Movie_2.jpeg";
import overLayImage from "../../assets/images/VideoPlayer/vernostlogo.png";

export default {
  title: "VMS_REACT/VideoPlayer",
  component: VideoPlayer,
  argTypes: {
    additionalStyle: { control: { type: "" } },
    additionalClassName: { control: { type: "" } },
    children: { control: { type: "" } },
  },
};

const videoSrc = `http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4`;
const videoSrc2 = `http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4`;
const OverlayVideoView = ({ onPlayHand<PERSON>, onMouseEnterHandler }) => {
  return (
    <div style={{ height: "100%" }}>
      <div
        style={{
          width: "100%",
          height: "100%",
          backgroundColor: "#00000099",
          display: "flex",
          flexDirection: "column",
          justifyContent: "space-between",
        }}
        onClick={onPlayHandler}
        onMouseEnter={onMouseEnterHandler}
      >
        <img
          src={overLayImage}
          alt="some text alternare"
          style={{
            width: "50%",
            marginTop: "20%",
            marginLeft: "10%",
          }}
        />
        <div style={{ marginBottom: "25%", marginLeft: "10%" }}>
          <h2 style={{ color: "white" }}>VMS Video Library</h2>
        </div>
      </div>
    </div>
  );
};

export const Default = (args) => {
  return <VideoPlayer url={videoSrc2} width={277} height={"457px"} {...args} />;
};
Default.args = {};

export const VideoWithPoster = () => {
  return (
    <VideoPlayer url={videoSrc} width={277} height={457} poster={posterSrc} />
  );
};

export const PlayPauseOnMouseEvent = () => {
  const [playing, setPlaying] = useState(false);
  return (
    <VideoPlayer
      url={videoSrc}
      width={277}
      height={"475px"}
      play={playing}
      onMouseEnter={(e) => {
        console.log("mouse enter");
        setPlaying(true);
      }}
      onMouseLeave={(e) => {
        console.log("mouse leave");
        setPlaying(false);
      }}
      controls={true}
    />
  );
};

export const PauseOverlay = () => {
  const [playing, setPlaying] = useState(false);
  return (
    <div style={{ display: "flex", flexDirection: "row", gap: "30px" }}>
      <VideoPlayer
        url={videoSrc}
        width={277}
        height={"475px"}
        play={playing}
        onPause={(e) => {
          setPlaying(false);
        }}
        pausedOverlay={
          <OverlayVideoView
            onPlayHandler={() => {
              setPlaying(!playing);
            }}
          />
        }
        controls={true}
      />
    </div>
  );
};

export const ResetVideoOnMouseEvent = () => {
  const [playing, setPlaying] = useState(false);
  return (
    <VideoPlayer
      url={videoSrc}
      width={277}
      height={"475px"}
      pausedOverlay={
        <OverlayVideoView
          onMouseEnterHandler={() => {
            setPlaying(true);
          }}
        />
      }
      play={playing}
      startTime={playing ? 0 : null}
      onMouseLeave={(e) => {
        console.log("mouse leave");
        setPlaying(false);
      }}
      controls={true}
    />
  );
};

export const CarouselVideoPlayer = (props) => {
  const size = 10;
  const [playing, setPlaying] = useState(
    Array.from({ length: size }).fill(false)
  );
  return (
    <Carousel
      additionalStyle={{ height: "400px" }}
      responsive={{
        desktop: {
          items: 3.5,
          partialVisibilityGutter: 5,
        },
      }}
    >
      {Array.from({ length: size }).map((item, index) => {
        return (
          <div key={index}>
           <VideoPlayer
            url={index % 2 == 0 ? videoSrc : videoSrc2}
            width={277}
            height={400}
            pausedOverlay={
              <OverlayVideoView
                onMouseEnterHandler={() => {
                  setPlaying((prevState) => {
                    const updatedState = [...prevState];
                    updatedState[index] = true;
                    return updatedState;
                  });
                }}
              />
            }
            poster={index % 2 == 0 ? posterSrc : posterSrc2}
            play={playing[index]}
            startTime={playing[index] ? 0 : null}
            onMouseLeave={(e) => {
              console.log("mouse leave");
              setPlaying((prevState) => {
                const updatedState = [...prevState];
                updatedState[index] = false;
                return updatedState;
              });
            }}
            controls={false}
          />
          </div>
        );
      })}
    </Carousel>
  );
};
