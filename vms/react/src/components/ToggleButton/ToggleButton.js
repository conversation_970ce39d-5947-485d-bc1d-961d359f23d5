import React, { useState, memo, useCallback, useMemo } from "react";
import PropTypes from "prop-types";
import { MainContainer, ToggleButtonContainer, Styled<PERSON>heckBox, StyledToggleButton, StyledOn<PERSON><PERSON>le, StyledOffHandle, StyledLabel } from "./ToggleButton.styled";
import { useClassName } from "../../hooks/useClassName";
import { ThemeWrapper } from "../Theme/ThemeContext";

/**
 * Toggle Button UI component for user interaction
 */
const ToggleButton = memo((props) => {
    // Destructure props for better performance
    const {
        id,
        width,
        height,
        thumbBorderColor,
        thumbBorderWidth,
        boxShadow,
        activeBoxShadow,
        OnHandleColor,
        offHandleColor,
        handleDiameter,
        offColor,
        OnColor,
        disabled = false,
        checked = false,
        title,
        titleTextStyle,
        checkedIcon,
        uncheckedIcon,
        onChange,
        additionalClassName,
        isMobile,
        isMobileView,
        ...otherProps
    } = props;

    // Memoize className computation
    const computedClassName = useMemo(() => 
        useClassName(props, additionalClassName), 
        [props, additionalClassName]
    );

    // Memoize derived values
    const labelId = useMemo(() => id ? `${id}-label` : null, [id]);
    const buttonId = useMemo(() => id ? `${id}-btn` : null, [id]);
    const isMobileDevice = useMemo(() => 
        isMobile || isMobileView || false, 
        [isMobile, isMobileView]
    );

    // Memoize event handlers
    const handleChange = useCallback((event) => {
        if (onChange && !disabled) {
            onChange(event);
        }
    }, [onChange, disabled]);

    const handleKeyDown = useCallback((event) => {
        if ((event.key === 'Enter' || event.key === ' ') && !disabled) {
            event.preventDefault();
            const syntheticEvent = {
                target: { checked: !checked },
                preventDefault: () => {},
                stopPropagation: () => {}
            };
            handleChange(syntheticEvent);
        }
    }, [disabled, checked, handleChange]);

    return (
        <ThemeWrapper isMobile={isMobileDevice}>
            <MainContainer
                className={`${computedClassName} vms_togglebutton_container`}
                role="group"
                aria-labelledby={labelId}
            >
                <ToggleButtonContainer
                    id={labelId}
                    width={width}
                    height={height}
                    className="vms_togglebutton_btn"
                >
                    <StyledCheckBox
                        type="checkbox"
                        disabled={disabled}
                        checked={checked}
                        id={id}
                        onChange={handleChange}
                        onKeyDown={handleKeyDown}
                        className="vms_togglebutton_checkbox"
                        aria-checked={checked}
                        aria-disabled={disabled}
                        aria-describedby={title ? labelId : undefined}
                    />
                    <StyledToggleButton
                        id={buttonId}
                        width={width}
                        thumbBorderColor={thumbBorderColor}
                        thumbBorderWidth={thumbBorderWidth}
                        boxShadow={boxShadow}
                        activeBoxShadow={activeBoxShadow}
                        OnHandleColor={OnHandleColor}
                        offHandleColor={offHandleColor}
                        handleDiameter={handleDiameter}
                        offColor={offColor}
                        OnColor={OnColor}
                        className="vms_togglebutton_handle"
                        role="presentation"
                    >
                        {checkedIcon && (
                            <StyledOnHandle 
                                className="vms_togglebutton_onhandle" 
                                src={checkedIcon}
                                alt=""
                                role="presentation"
                            />
                        )}
                        {uncheckedIcon && (
                            <StyledOffHandle 
                                className="vms_togglebutton_offhandle" 
                                src={uncheckedIcon}
                                alt=""
                                role="presentation"
                            />
                        )}
                    </StyledToggleButton>
                </ToggleButtonContainer>
                {title && (
                    <StyledLabel 
                        id={labelId} 
                        className="vms_togglebutton_label" 
                        style={titleTextStyle}
                    >
                        {title}
                    </StyledLabel>
                )}
            </MainContainer>
        </ThemeWrapper>
    );
});

ToggleButton.displayName = 'ToggleButton';


ToggleButton.propTypes = {
    /**
     * Optional click handler
     */
    // onClick: PropTypes.func,

    /**
     * the width of the background of switch
     */
    width: PropTypes.number,

    /**
     * the height of the background of switch
     */
    height: PropTypes.number,

    /**
     * default Box Shadow of the handle
     */
    boxShadow: PropTypes.string,

    /**
     * the activeBoxShadow of the handle
     */
    activeBoxShadow: PropTypes.string,

    checkedIcon: PropTypes.any,

    uncheckedIcon: PropTypes.any,

    handleDiameter: PropTypes.number,

    OnHandleColor: PropTypes.string,

    offHandleColor: PropTypes.string,

    /**
     * the switch will take on this color when it is checked
     */
    OnColor: PropTypes.string,

    /**
     * the switch will take on this color when it is unchecked
     */
    offColor: PropTypes.string,

    /**
     * defines whether the switch should be disable or not
     */
    disabled: PropTypes.bool,

    /**
   * Property to append additional css class
   */
    additionalClassName: PropTypes.arrayOf(PropTypes.string),

    /**
     * Property to switch to checked if true and unchecked if false
     */
    checked: PropTypes.bool,

    /**
     * Property to set id to switch
     */
    id: PropTypes.string,

    /**
     * Property to set title to switch
     */
    title: PropTypes.string,

    /**
     * Property to set title to switch title
     */
    titleTextStyle: PropTypes.object,

    /**
    * Property to set thumb border color
    */
    thumbBorderColor: PropTypes.string,

    /**
     * Property to set thumb border width
     */
    thumbBorderWidth: PropTypes.string,

    /**
   * Property to set ClassName
   */
    className: PropTypes.string,

    /**
         * Optional change handler
         */
    onChange: PropTypes.func,

}

export { ToggleButton };