import React from "react";
import { ToggleButton } from "./ToggleButton";

import ButtonOnSvg from "../../assets/images/ToggleButton/ButtonOn.svg";
import ButtonOffSvg from "../../assets/images/ToggleButton/ButtonOff.svg";

export default {
  title: "VMS_REACT/ToggleButton",
  component: ToggleButton,
  argTypes: {
    additionalClassName: { control: { type: "" } },
  },
};

const Template = (args) => <ToggleButton {...args}></ToggleButton>;
export const Default = Template.bind({});
Default.args = {
  width: 53,
  height: 15,
  OnColor: "#f3b5ad",
  offColor: "#f3b5ad",
  handleDiameter: 26,
  OnHandleColor: "#ee270d",
  offHandleColor: "#ee270d",
  id:"toggle-id"
};

export const Disabled = Template.bind({});

Disabled.args = {
  width: 53,
  height: 15,
  OnColor: "#dedbdb",
  offColor: "#dedbdb",
  handleDiameter: 26,
  OnHandleColor: "#aeaaa9",
  offHandleColor: "#aeaaa9",
  disabled: true,
  id:"dis-id"
}


export const WithTitle = Template.bind({});
WithTitle.args = {
  width: 53,
  height: 15,
  OnColor: "#6b1b1b",
  offColor: "#6b1b1b",
  handleDiameter: 26,
  OnHandleColor: "#6b1b1b",
  offHandleColor: "#6b1b1b",
  title: "Notification",
  id:"id-1"
};


export const OffOnWithoutImage = Template.bind({});
OffOnWithoutImage.args = {
  width: 53,
  height: 26,
  OnColor: "#dbd5d4",
  offColor: "#dbd5d4",
  handleDiameter: 26,
  OnHandleColor: "#aeaaa9",
  offHandleColor: "#aeaaa9",
  id:"id-2"

};


export const OffOnWithImage = (args) => <ToggleButton {...args} checkedIcon={ButtonOnSvg} uncheckedIcon={ButtonOffSvg}></ToggleButton>;
OffOnWithImage.args = {
  width: 53,
  height: 26,
  OnColor: "#dbd5d4",
  offColor: "#dbd5d4",
  handleDiameter: 26,
  OnHandleColor: "#aeaaa9",
  offHandleColor: "#aeaaa9",
  id:"id-3"

};

export const SelectedOff = Template.bind({});
SelectedOff.args = {
  width: 53,
  height: 15,
  OnColor: "#dbd5d4",
  offColor: "#dbd5d4",
  handleDiameter: 26,
  OnHandleColor: "#aeaaa9",
  offHandleColor: "#aeaaa9",
  checked: false,
  id:"selectedOff"
};