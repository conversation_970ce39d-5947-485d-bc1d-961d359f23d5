import styled from "styled-components"

function _fontWeight(theme) {
  return theme?.typography?.text?.fontWeight || "";
}
function _fontFamily(theme) {
  return theme?.typography?.fontFamily || "";
}
function _fontSize(theme) {
  return theme?.typography?.text?.fontSize?.desktop || "";
}
function _lineHeight(theme) {
  return theme?.typography?.lineHeight || "";
}
function _letterSpacing(theme) {
  return theme?.typography?.letterSpacing || "";
}

export const MainContainer = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
`

export const ToggleButtonContainer = styled.label`
  position: relative;
  display: inline-block;
  /* width of toggle button */
  width: ${({ width }) => (width ? width + "px" : "53px")}; 
  /* height of toggle button */
  height: ${({ height }) => (height ? height + "px" : "15px")};
`
export const StyledCheckBox = styled.input`
display: none;
`

export const StyledOnHandle = styled.img`
  position: absolute;
  transform: translate(-50%,-50%);
  top: 50%;
  left: 22%;
  display:none;
  object-fit: fill;
`

export const StyledOffHandle = styled.img`
  position: absolute;
  transform: translate(-50%,-50%);
  top: 50%;
  right:-22%;
  object-fit: fill;
  background-color: transparent;
`


export const StyledToggleButton = styled.div`
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  /*button off background color */
  background-color:  ${({ offColor }) => (offColor ? offColor : "#f3b5ad")};
  -webkit-transition: .4s;
  transition: .4s;
  border-radius: 34px;
  /* Default Box Shadow */
  box-shadow:${({ boxShadow }) => (boxShadow ? boxShadow : "")};
  ${'' /* border-color:${({ thumbBorderColor }) => (thumbBorderColor ? thumbBorderColor : "")};
  border-width:${({ thumbBorderWidth }) => (thumbBorderWidth ? thumbBorderWidth : "")}; */}

  

  &::before{
    position: absolute;
    content: "";
    height: ${({ handleDiameter }) => (handleDiameter ? handleDiameter + "px" : "26px")};
    width:  ${({ handleDiameter }) => (handleDiameter ? handleDiameter + "px" : "26px")};
    top: 0;
    margin: auto;
    bottom: 0;
    border-color:${({ thumbBorderColor }) => (thumbBorderColor ? thumbBorderColor : "")};
    border-width:${({ thumbBorderWidth }) => (thumbBorderWidth ? thumbBorderWidth : "")};
    /* off handle background */
    background-color:  ${({ offHandleColor }) => (offHandleColor ? offHandleColor : "#ee270d")};
    -webkit-transition: .4s;
    transition: .4s;
    border-radius: 50%;

  }

  ${StyledCheckBox}:checked + &::before{
    -webkit-transform: ${({ width }) => (width ? `translateX(${(parseInt(width) / 2)}px)` : `translateX(26px)`)};
    -ms-transform: ${({ width }) => (width ? `translateX(${(parseInt(width) / 2)}px)` : `translateX(26px)`)};
    transform:  ${({ width }) => (width ? `translateX(${(parseInt(width) / 2)}px)` : `translateX(26px)`)};
    /* on handle background */
    background-color:  ${({ OnHandleColor }) => (OnHandleColor ? OnHandleColor : "#ee270d")};
  }

  ${StyledCheckBox}:checked + & {
    /* on background */
    background-color:  ${({ OnColor }) => (OnColor ? OnColor : "#f3b5ad")};
    /* Active Box Shadow */
    box-shadow:  ${({ activeBoxShadow }) => (activeBoxShadow ? activeBoxShadow : "")};
  }

  ${StyledCheckBox}:checked ~ & ${StyledOnHandle} {
    display: block; 
  }

  ${StyledCheckBox}:checked ~ & ${StyledOffHandle} {
    display: none; 
  }
`
export const StyledLabel = styled.div`
  padding-left:20px;
  font-family: ${({ theme }) => _fontFamily(theme)};
  font-size: ${({ theme }) => _fontSize(theme)};
  font-weight: ${({ theme }) => _fontWeight(theme)};
  line-height: ${({ theme }) => _lineHeight(theme)};
  letter-spacing: ${({ theme }) => _letterSpacing(theme)};
`









