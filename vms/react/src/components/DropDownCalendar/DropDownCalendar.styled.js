import styled from "styled-components";

// Styled components for dropdown styling
export const Container = styled.div`
  display: flex;
  align-items: flex-start;
  gap: 20px;
  position: relative;
`;

export const Label = styled.label`
  font-weight: bold;
  margin-bottom: 5px;
  display: block;
`;

export const Dropdown = styled.div`
  position: relative;
  flex: 0 0 auto;
  min-width: ${props => {
    if (props.type === 'day') return '90px';
    if (props.type === 'month') return '90px';
    if (props.type === 'year') return '90px';
    return 'auto';
  }};
  width:90px;

`;

export const Icon = styled.span`
  margin-left: 0.5rem;
`;

export const Button = styled.button`
  padding: 8px;
  font-size: 14px;
  border: 1px solid #ccc;
  border-radius: 4px;
  background: white;
  cursor: pointer;
  min-width: 70px;
  text-align: left;
`;

export const Input = styled.input`
  padding: 8px 30px 8px 12px;
  font-size: 14px;
  border: 1px solid #ccc;
  border-radius: 4px;
  background: white;
  width: 100%;
  text-align: left;
  cursor: pointer;
  box-sizing: border-box;
  
  &:focus {
    outline: none;
    border-color: #007bff;
  }
  
  &::placeholder {
    color: #999;
    text-align: left;
  }
`;

// Add a new styled component for the dropdown icon
export const DropdownIcon = styled.img`
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;
  width: 16px;
  height: 16px;
  transition: transform 0.2s ease-in-out;
  user-select: none;
`;

export const List = styled.ul`
list-style: none;
margin: 0;
padding: 0;
max-height: 150px;
overflow-y: auto;
position: absolute;
background: white;
border: 1px solid #ccc;
width: 97%;
z-index: 1000;
`;

export const ListItem = styled.li`
padding: 8px;
cursor: pointer;
&:hover {
  background-color: #f0f0f0;
}
`;


export const Select = styled.select`
  padding: 5px;
  font-size: 16px;
  border: 1px solid #ccc;
  border-radius: 5px;
  cursor: pointer;
`;

export const Error = styled.p`
  color: red;
  font-size: 14px;
  margin-top: 5px;
`;
export const ErrorMessage = styled.p`
  color: red;
  font-size: 0.85em;
  margin-top: 0.25rem;
`;