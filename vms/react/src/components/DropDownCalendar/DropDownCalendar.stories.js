import React, { useState } from "react";
import { DropDownCalendar } from "./DropDownCalendar";
import downArrowSvg from "../../assets/images/PaxSelection/dropdown_down_icon.svg"
import upArrowSvg from "../../assets/images/PaxSelection/dropdown_up_icon.svg"

export default {
    title: "VMS_REACT/DropDownCalendar",
    component: DropDownCalendar,
    argTypes: {
        onChange: { control: { type: "" } },
    },
  };

export const Default = () => {
  const [selectedDate, setSelectedDate] = useState(null);

  const handleDateChange = (date) => {
    setSelectedDate(date);
    console.log("Selected Date:", date);
  };

  return (
    <div>
      <h2>Select Your Date of Birth</h2>
      <DropDownCalendar 
        onChange={handleDateChange} 
        yearRange={100} 
        error={false} 
        showFutureYears={false} 
        value={selectedDate}
        CalendarTitle="Date of Birth"
        DayLabel="Day"
        MonthLabel="Month"
        YearLabel="Year"
        isRequiredSign={true}
        requiredColor="red"
      />
      {selectedDate ? (
        <>
          <p>Selected Date: {selectedDate?.raw}</p>
          <p>Is Valid: {selectedDate?.isValid ? 'Yes' : 'No'}</p>
        </>
      ) : (
        <p>Please select a date</p>
      )}
    </div>
  );
};

export const WithFutureYears = () => {
  const [selectedDate, setSelectedDate] = useState(null);

  const handleDateChange = (date) => {
    setSelectedDate(date);
    console.log("Selected Date:", date);
  };

  return (
    <div>
      <h2>Select Future Date</h2>
      <DropDownCalendar 
        onChange={handleDateChange} 
        yearRange={50} 
        error={false} 
        showFutureYears={true} 
        value={selectedDate}
        CalendarTitle="Future Date"
        DayLabel="Day"
        MonthLabel="Month"
        YearLabel="Year"
      />
      {selectedDate ? (
        <>
          <p>Selected Date: {selectedDate?.raw}</p>
        </>
      ) : (
        <p>Please select a date</p>
      )}
    </div>
  );
};

export const WithCustomDropdownIcons = () => {
  const [selectedDate, setSelectedDate] = useState(null);

  const handleDateChange = (date) => {
    setSelectedDate(date);
    console.log("Selected Date:", date);
  };

  return (
    <div>
      <h2>Custom Dropdown Icons</h2>
      <DropDownCalendar 
        onChange={handleDateChange} 
        yearRange={100} 
        error={false} 
        showFutureYears={false} 
        value={selectedDate}
        CalendarTitle="Date of Birth"
        DayLabel="Day"
        MonthLabel="Month"
        YearLabel="Year"
        isRequiredSign={true}
        requiredColor="red"
        customDropdownIcon={downArrowSvg}
        customDropdownOpenIcon={upArrowSvg}
      />
      {selectedDate ? (
        <>
          <p>Selected Date: {selectedDate?.raw}</p>
          <p>Is Valid: {selectedDate?.isValid ? 'Yes' : 'No'}</p>
        </>
      ) : (
        <p>Please select a date</p>
      )}
    </div>
  );
};


