import React, { useState, useEffect, useRef, useMemo, useCallback } from "react";
import { Label, Container ,List,ListItem,Dropdown,Button,Icon,Input,DropdownIcon} from "./DropDownCalendar.styled.js";
import PropTypes from "prop-types";
import { useClassName } from "../../hooks/useClassName.js";
import downArrowSvg from "../../assets/images/PaxSelection/dropdown_down_icon.svg"
import upArrowSvg from "../../assets/images/PaxSelection/dropdown_up_icon.svg"

const DropDownCalendar = React.memo((props) => {
  const {
    id,
    onChange,
    yearRange = 100,
    showFutureYears = false,
    error = false,
    DayLabel,
    MonthLabel,
    YearLabel,
    additionalClassName,
    isRequiredSign,
    requiredColor,
    CalendarTitle,
    value,
    errorMessage,
    customDropdownIcon,
    customDropdownOpenIcon,
  } = props;

  const dropdownRef = useRef(null);

  const [dob, setDob] = useState({ day: "", month: "", year: "" });
  const [open, setOpen] = useState({ day: false, month: false, year: false });
  const [inputValues, setInputValues] = useState({ day: "", month: "", year: "" });
  const [isSelecting, setIsSelecting] = useState(false);

  // Memoize months array
  const months = useMemo(() => ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"], []);

  // Memoize days array
  const days = useMemo(() => Array.from({ length: 31 }, (_, i) => i + 1), []);

  // Memoize years array
  const years = useMemo(() => {
    const currentYear = new Date().getFullYear();
    return showFutureYears
      ? Array.from({ length: yearRange }, (_, i) => currentYear + i)
      : Array.from({ length: yearRange }, (_, i) => currentYear - i);
  }, [showFutureYears, yearRange]);

  // Memoize checkAndTriggerChange function
  const checkAndTriggerChange = useCallback((newDob) => {
    const { day, month, year } = newDob;
    if (day && month && year) {
      const d = Number(day), m = Number(month), y = Number(year);
      const testDate = new Date(y, m - 1, d);
      const isValid = testDate.getFullYear() === y && testDate.getMonth() === m - 1 && testDate.getDate() === d;

      const raw = `${String(day).padStart(2, "0")}-${String(month).padStart(2, "0")}-${year}`;

      onChange?.({ day, month, year, raw, date: isValid ? testDate : null, isValid });
    }
  }, [onChange]);

  useEffect(() => {
    if (value === "") {
      setDob({ day: "", month: "", year: "" });
      setInputValues({ day: "", month: "", year: "" });
    } else if (value && typeof value === "string") {
      const [day, month, year] = value.split("-");
      setDob({ day, month, year });
      
      // Set display values for inputs
      setInputValues({
        day: day || "",
        month: month ? months[Number(month) - 1] || month : "",
        year: year || ""
      });
    } else if (value && value.raw) {
      const { day, month, year } = value;
      setDob({ day, month, year });
      
      // Set display values for inputs
      setInputValues({
        day: day || "",
        month: month ? months[Number(month) - 1] || month : "",
        year: year || ""
      });
    }
  }, [value, months]);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setOpen({ day: false, month: false, year: false });
      }
    };
  
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const toggleDropdown = useCallback((type) => {
    setOpen((prev) => ({
      day: type === "day" ? !prev.day : false,
      month: type === "month" ? !prev.month : false,
      year: type === "year" ? !prev.year : false,
    }));
  }, []);

  const handleInputChange = useCallback((type) => (e) => {
    const inputValue = e.target.value;
    
    setInputValues((prev) => ({ ...prev, [type]: inputValue }));
    
    if (type === 'day') {
      if (/^\d{0,2}$/.test(inputValue)) {
        const dayNum = parseInt(inputValue);
        if (inputValue === '' || (dayNum >= 1 && dayNum <= 31)) {
          setDob((prev) => {
            const newDob = { ...prev, day: inputValue };
            checkAndTriggerChange(newDob);
            return newDob;
          });
          
          if (dayNum >= 1 && dayNum <= 31) {
            setOpen((prev) => ({ ...prev, day: false }));
          }
        } else {
          setInputValues((prev) => ({ ...prev, day: dob.day }));
        }
      } else {
        setInputValues((prev) => ({ ...prev, day: dob.day }));
      }
    } else if (type === 'month') {
      if (/^\d{0,2}$/.test(inputValue)) {
        const monthNum = parseInt(inputValue);
        if (inputValue === '' || (monthNum >= 1 && monthNum <= 12)) {
          setDob((prev) => {
            const newDob = { ...prev, month: inputValue };
            checkAndTriggerChange(newDob);
            return newDob;
          });
          if (monthNum >= 1 && monthNum <= 12) {
            setInputValues((prev) => ({ ...prev, month: months[monthNum - 1] }));
           setOpen((prev) => ({ ...prev, month: false }));
          }
        } else {
          setInputValues((prev) => ({ 
            ...prev, 
            month: dob.month ? months[Number(dob.month) - 1] || dob.month : ""
          }));
        }
      } else {
        const monthMatch = months.find(month => month.toLowerCase().startsWith(inputValue.toLowerCase()));
        if (inputValue === '' || monthMatch) {
          if (monthMatch && monthMatch.toLowerCase() === inputValue.toLowerCase()) {
            const monthIndex = months.indexOf(monthMatch) + 1;
            setDob((prev) => {
              const newDob = { ...prev, month: String(monthIndex) };
              checkAndTriggerChange(newDob);
              return newDob;
            });
            setOpen((prev) => ({ ...prev, month: false }));
          } else if (inputValue === '') {
            setDob((prev) => {
              const newDob = { ...prev, month: '' };
              checkAndTriggerChange(newDob);
              return newDob;
            });
          }
        } else {
          setInputValues((prev) => ({ 
            ...prev, 
            month: dob.month ? months[Number(dob.month) - 1] || dob.month : ""
          }));
        }
      }
    } else if (type === 'year') {
      if (/^\d{0,4}$/.test(inputValue)) {
        setDob((prev) => {
          const newDob = { ...prev, year: inputValue };
          checkAndTriggerChange(newDob);
          return newDob;
        });
        
        if (inputValue.length === 4) {
         setOpen((prev) => ({ ...prev, year: false }));
        }
      } else {
        setInputValues((prev) => ({ ...prev, year: dob.year }));
      }
    }
  }, [dob, checkAndTriggerChange, months]);

  const handleInputClick = useCallback((type) => (e) => {
    e.preventDefault();
    // Toggle the dropdown for the clicked input
    setOpen((prev) => ({
      day: type === "day" ? !prev.day : false,
      month: type === "month" ? !prev.month : false,
      year: type === "year" ? !prev.year : false,
    }));
  }, []);

  const handleInputFocus = useCallback((type) => () => {
    // Do nothing on focus - let click handle the toggle
  }, []);

  const handleInputBlur = useCallback((type) => () => {
    // Only close if we're not selecting from dropdown
    if (!isSelecting) {
      setOpen((prev) => ({ ...prev, [type]: false }));
      
      if (type === 'month') {
        const currentInputValue = inputValues.month;
        if (currentInputValue && currentInputValue.trim() !== '') {
          const monthMatch = months.find(month => 
            month.toLowerCase().startsWith(currentInputValue.toLowerCase())
          );
          
          if (monthMatch) {
            const monthIndex = months.indexOf(monthMatch) + 1;
            setDob((prev) => {
              const newDob = { ...prev, month: String(monthIndex) };
              checkAndTriggerChange(newDob);
              return newDob;
            });
            setInputValues((prev) => ({ ...prev, month: monthMatch }));
          }
        }
      }
    }
    setIsSelecting(false);
  }, [isSelecting, inputValues.month, months, checkAndTriggerChange]);
  
  const handleChange = useCallback((type, value) => {
    setIsSelecting(true); 
    
    const newDob = { ...dob, [type]: value };
    
    setDob(newDob);

    if (type === 'month') {
      setInputValues((prev) => ({ ...prev, month: months[Number(value) - 1] || value }));
    } else {
      setInputValues((prev) => ({ ...prev, [type]: value }));
    }

    const { day, month, year } = newDob;
    
    if (day || month || year) {
      const d = day ? Number(day) : null;
      const m = month ? Number(month) : null;
      const y = year ? Number(year) : null;
      
      let testDate = null;
      let isValid = false;
      let raw = "";

      if (day && month && year) {
        testDate = new Date(y, m - 1, d);
        isValid = testDate.getFullYear() === y && testDate.getMonth() === m - 1 && testDate.getDate() === d;
        raw = `${String(day).padStart(2, "0")}-${String(month).padStart(2, "0")}-${year}`;
      }

      onChange?.({ day, month, year, raw, date: testDate, isValid });
    }

    setOpen((prev) => ({ ...prev, [type]: false }));
  }, [dob, months, onChange]);

  const className = useClassName(props, additionalClassName);

  return (
    <div>
      <Label>{CalendarTitle || "Date of birth"} {isRequiredSign && <span style={{ color: requiredColor || "red" }}>*</span>}</Label>
      <Container className={`${className} vms_dropdownCalendar_container`} ref={dropdownRef}>

        {/* Day Dropdown */}
        <Dropdown className="vms_list_dropdown-day" type="day">
          <Input 
            className="vms_list_input-day" 
            value={inputValues.day}
            placeholder={DayLabel || "Day"}
            onChange={handleInputChange('day')}
            onClick={handleInputClick('day')}
            onFocus={handleInputFocus('day')}
            onBlur={handleInputBlur('day')}
            autoComplete="off"
          />
          <DropdownIcon 
            className="vms_list_icon" 
            src={open.day ? (customDropdownOpenIcon || upArrowSvg) : (customDropdownIcon || downArrowSvg)}
            alt="dropdown icon"
          />
          {open.day && (
            <List className="vms_list_items-day" >
               <ListItem className="vms_list_item-placeholder" as="div">{DayLabel || 'Day'}</ListItem>
              {days.map((d) => (
                <ListItem  key={d} className="vms_list_item" onMouseDown={() => handleChange("day", String(d))}>{d}</ListItem>
              ))}
            </List>
          )}
        </Dropdown>

        {/* Month Dropdown */}
        <Dropdown className="vms_list_dropdown-month" type="month">
          <Input 
            className="vms_list_input-month" 
            value={inputValues.month}
            placeholder={MonthLabel || "Month"}
            onChange={handleInputChange('month')}
            onClick={handleInputClick('month')}
            onFocus={handleInputFocus('month')}
            onBlur={handleInputBlur('month')}
            autoComplete="off"
          />
          <DropdownIcon 
            className="vms_list_icon" 
            src={open.month ? (customDropdownOpenIcon || upArrowSvg) : (customDropdownIcon || downArrowSvg)}
            alt="dropdown icon"
          />
          {open.month && (
            <List className="vms_list_items-month">
               <ListItem className="vms_list_item-placeholder" as="div">{MonthLabel || 'Month'}</ListItem>
              {months.map((m, i) => (
                <ListItem key={m} className="vms_list_item" onMouseDown={() => handleChange("month", String(i + 1))}>{m}</ListItem>
              ))}
            </List>
          )}
        </Dropdown>

        {/* Year Dropdown */}
        <Dropdown className="vms_list_dropdown-year" type="year">
          <Input 
            className="vms_list_input-year" 
            value={inputValues.year}
            placeholder={YearLabel || "Year"}
            onChange={handleInputChange('year')}
            onClick={handleInputClick('year')}
            onFocus={handleInputFocus('year')}
            onBlur={handleInputBlur('year')}
            autoComplete="off"
          />
          <DropdownIcon 
            className="vms_list_icon" 
            src={open.year ? (customDropdownOpenIcon || upArrowSvg) : (customDropdownIcon || downArrowSvg)}
            alt="dropdown icon"
          />
          {open.year && (
            <List className="vms_list_items-year">
              <ListItem className="vms_list_item-placeholder" as="div">{ YearLabel || 'Year'}</ListItem>
              {years.map((y) => (
                <ListItem key={y} className="vms_list_item" onMouseDown={() => handleChange("year", String(y))}>{y}</ListItem>
              ))}
            </List>
          )}
        </Dropdown>
      </Container>
     
    </div>
  );
});

DropDownCalendar.displayName = 'DropDownCalendar';

DropDownCalendar.defaultProps = {
  onChange: null,
  yearRange: 100,
  showFutureYears: false,
  error: false,
  value: null,
};

DropDownCalendar.propTypes = {
  id: PropTypes.string,
  additionalClassName: PropTypes.string,
  onChange: PropTypes.func,
  yearRange: PropTypes.number,
  showFutureYears: PropTypes.bool,
  error: PropTypes.bool,
  CalendarTitle: PropTypes.string,
  isRequiredSign: PropTypes.bool,
  requiredColor: PropTypes.string,
  DayLabel: PropTypes.string,
  MonthLabel: PropTypes.string,
  YearLabel: PropTypes.string,
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.instanceOf(Date)]),
  /**
   * Custom dropdown icon when dropdown is closed
   */
  customDropdownIcon: PropTypes.string,
  /**
   * Custom dropdown icon when dropdown is open
   */
  customDropdownOpenIcon: PropTypes.string,
};

export { DropDownCalendar };
