import React, { Component } from "react";
// import { RangeSlider } from "reactrangeslider"; // eslint-disable-line import/no-unresolved
import {
  Container,
  LeftLabel,
  RightLabel,
  TextViewContainer,
} from "./RangeSelector.styled";
import { withTheme } from "styled-components";
import { useClassName } from "../../../hooks/useClassName";
import Slider from 'rc-slider';

class CustomRangeSlider extends Component {
  onSliderChange = (value) => {
    this.props.ValChange(value);
  };

  onComplete = (value) => {
    console.log("value onComplete",value)
    this.props.afterValChange(value);
  };

  getPrice= (value) => {
    if (value || value === 0) {
      return parseInt(value)?.toLocaleString('en-IN')
    }
    return value || 0;
  }

  render() {
    const {
      min,
      max,
      step,
      labelPosition,
      labelSuffix,
      labelPrefix,
      showLabel,
      readOnly,
      trackStyle,
      disabled,
      labelTextStyle,
      defaultValue,
      tabIndex,
      handleStyle,
      value,
      theme,
      type,
      additionalClassName,
      railStyle,
      dotStyle,
      activeDotStyle,
      customHandle,
      onChange,
      onChangeComplete,
      minValueLabel,
      maxValueLabel
    } = this.props;
    console.log("thhhhed",theme)
    const styles = {
      sliderWrapper: {
        width: "75%",
      },
      slider: {
        height: 34,
      },
      trackStyle: {
        height: 3,
        border: "3px",
        backgroundColor: `${theme?.palette?.[type]?.[theme.mode]}`,
        // border: "none",
      },
      highlightedTrackStyle: {
        height: 3,
        backgroundColor: theme?.palette?.[type]?.[theme.mode],
        border: "none",
      },
      handleStyle1: {
        height: 24,
        width: 24,
        border: `1px solid ${theme?.palette?.[type]?.[theme.mode]}`,
        backgroundColor: "#fff",
        top: -1,
        opacity:1,
        // left:0,
        cursor:'pointer'
      },
      handleStyle2: {
        height: 24,
        width: 24,
        border: `1px solid ${theme?.palette?.[type]?.[theme.mode]}`,
        backgroundColor: "#fff",
        top: -1,
        // left:'unset',
        // right:0,
        opacity:1,
        cursor:'pointer'
      },
      railStyle:{
        backgroundColor: 'grey'
      },
      dotStyle:{},
      activeDotStyle:{}
    };
    const className = useClassName(this.props, additionalClassName);
    return (
      <Container className={`${className} vms_RangeSelector_Container`}>
      <Slider
          range={true}
          step={step}
          value={value}
          min={min}
          max={max}
          disabled={disabled}
          readOnly={readOnly}
          tabIndex={tabIndex}
          defaultValue={defaultValue}
          handleStyle={handleStyle ? [handleStyle,handleStyle] : 
              [styles.handleStyle1, styles.handleStyle2]
          }
          trackStyle={trackStyle ? trackStyle : [styles.trackStyle]}
          railStyle={railStyle ?railStyle :styles.railStyle}
          dotStyle={dotStyle?dotStyle : styles.dotStyle}
          activeDotStyle={activeDotStyle?activeDotStyle:styles.activeDotStyle}
          handleRender={customHandle}
          onChange={(val) => onChange(val)}
        // onBeforeChange={(val)=>onBeforeChange(val)}
        onChangeComplete={(val) => onChangeComplete(val)}

/>
     {showLabel && (
    <TextViewContainer className={"vms_RangeSelector_Text_Container"}>
      {minValueLabel != null ? minValueLabel : (
      <LeftLabel className={"vms_RangeSelector_LeftText"} style={labelTextStyle}>
        {labelPosition === "suffix"
          ? (value && this.getPrice(value[0])) + " " + (labelSuffix || "")
          : (labelPrefix || "") + " " + (value && this.getPrice(value[0]))}
      </LeftLabel>
      )}
      {/* <LabelDiv> */}
      {maxValueLabel != null ? maxValueLabel: (
        <RightLabel className={"vms_RangeSelector_RightText"} style={labelTextStyle}>
          {labelPosition === "suffix"
            ? (value && this.getPrice(value[1])) + " " + (labelSuffix || "")
            : (labelPrefix || "") + " " + (value && this.getPrice(value[1]))}
        </RightLabel>
      )}
      {/* </LabelDiv> */}
    </TextViewContainer>
  )}
  </Container>
    );
  }
}

export default withTheme(CustomRangeSlider);
