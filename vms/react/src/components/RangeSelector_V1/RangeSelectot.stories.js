import React, { useState } from "react";

import { RangeSelector_V1 } from "./RangeSelector";
import "./RangeSelector.css";
import { min } from "date-fns";
export default {
  title: "VMS_REACT/RangeSelector_V1",
  component: RangeSelector_V1,
  parameters: { controls: { sort: "requiredFirst" } },
  argTypes: {
    id: { control: { type: "" } },
    min: { control: { type: "" } },
    max: { control: { type: "" } },
    step: { control: { type: "" } },
    tabIndex: { control: { type: "" } },
    defaultValue: { control: { type: "" } },
    showLabel: { control: { type: "" } },
    additionalClassName: { control: { type: "" } },
    activeHandleStyle: { control: { type: "" } },
    hoveredHandleStyle: { control: { type: "" } },
    focusedHandleStyle: { control: { type: "" } },
    handleStyle: { control: { type: "" } },
    disabledHighlightedTrackStyle: { control: { type: "" } },
    highlightedTrackStyle: { control: { type: "" } },
    disabledTrackStyle: { control: { type: "" } },
    // highlightedTrackStyle: { control: { type: "" } },
    trackStyle: { control: { type: "" } },
    wrapperStyle: { control: { type: "" } },
    labelTextStyle: { control: { type: "" } },
    labelPrefix: { control: { type: "" } },
    labelSuffix: { control: { type: "" } },
    disabledHandleStyle: { control: { type: "" } },
    wrapperClassName: { control: { type: "" } },
    handleClassName: { control: { type: "" } },
    disabledHandleClassName: { control: { type: "" } },
    trackClassName: { control: { type: "" } },
    highlightedTrackClassName: { control: { type: "" } },
    disabledHighlightedTrackClassName: { control: { type: "" } },
  },
};

const Template = (args) => {
  const [value, setValue] = useState([720, 66000]);
  return (
    <RangeSelector_V1
      step={1000}
      onChangeComplete={(val) => {
        console.log("valueonvalchange", val);
        setValue(val);
      }}
      {...args}
    />
  );
};

export const Default = Template.bind({});
Default.args = {
  showLabel: true,
  disabled: false,
  readOnly: false,
  min: 0,
  max: 100000,
};

export const WithSuffix = Template.bind({});
WithSuffix.args = {
  labelPosition: "suffix",
  labelSuffix: "Miles",
};

export const WithoutLabels = Template.bind({});
WithoutLabels.args = {
  showLabel: false,
};

export const WithoutLabelsPrefix = Template.bind({});
WithoutLabelsPrefix.args = {
  labelPrefix: "",
};

export const CustomLabelStyle = Template.bind({});
CustomLabelStyle.args = {
  labelTextStyle: {
    fontSize: 20,
    color: "#987564",
    fontWeight: "normal",
    fontFamily: "monospace",
  },
};

export const TestRangeSelector = (props) => {
  return (
    <RangeSelector_V1
      min={0}
      max={6000}
      step={100}
      onChangeComplete={(value) => {
        console.log("TestRangeSelector", value);
        //update your state if required
      }}
    />
  );
};

export const TestRangeSelectorWithDefaultValue = (props) => {
  return (
    <RangeSelector_V1
      min={0}
      max={6000}
      step={100}
      defaultValue={[0, 4000]}
      // value={sliderValue}
      onChangeComplete={(value) => {
        console.log("TestRangeSelectorWithDefaultValue", value);
      }}
    />
  );
};

export const TestRangeSelectorCustomLabel = (props) => {
  const [sliderValueState, setSliderValueState] = useState([2.55, 20.55]);
  return (
    <div style={{ display: "flex", flexDirection: "column", gap: "20px" }}>
      <div>
        <button
          onClick={() => {
            setSliderValueState([2.55, 20.55]);
          }}
        >
          Reset Range
        </button>
      </div>
      <div>
        <RangeSelector_V1
          min={2.55}
          max={20.55}
          step={1}
          value={sliderValueState}
          minValueLabel={<label>{`${sliderValueState[0]} hours`}</label>}
          maxValueLabel={<label>{`${sliderValueState[1]} hours`}</label>}
          onChangeComplete={(value) => {
            console.log("TestRangeSelectorCustomLabel", value);
            setSliderValueState(value);
          }}
        />
      </div>
    </div>
  );
};
