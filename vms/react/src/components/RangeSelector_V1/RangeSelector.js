import React, { useEffect, useState, memo, useCallback, useMemo } from "react";
import PropTypes from "prop-types";
import { ThemeWrapper } from "../Theme/ThemeContext";
import "rc-slider/assets/index.css";
import CustomRangeSlider from "./component/RangeSelector";

// Utility function (placed before main component)
const isNonNull = (prop) => prop !== null && prop !== undefined;

const RangeSelector_V1 = memo((props) => {
  // 1. PROP DESTRUCTURING & VALIDATION
  const {
    onChange,
    onBeforeChange,
    value,
    onChangeComplete,
    min = 0,
    max = 30000,
    defaultValue,
    isMobile,
    isMobileView,
    ...otherProps
  } = props;

  // 2. STATE MANAGEMENT
  const [sliderValue, setSliderValue] = useState([0, 0]);

  // 3. PERFORMANCE OPTIMIZATIONS
  const isMobileDevice = useMemo(() => 
    isMobile || isMobileView || false, 
    [isMobile, isMobileView]
  );

  const isValueValid = useCallback((value, min, max) => {
    return value?.length === 2 && value[0] >= min && value[1] <= max;
  }, []);

  const defaultSliderValue = useMemo(() => 
    isValueValid(defaultValue, min, max) ? defaultValue : [min, max], 
    [defaultValue, min, max, isValueValid]
  );

  // 4. EVENT HANDLING with useCallback
  const handleSliderChange = useCallback((val) => {
    setSliderValue(val);
    if (isNonNull(onChange)) {
      onChange(val);
    }
  }, [onChange]);

  const handleSliderBeforeChange = useCallback((val) => {
    setSliderValue(val);
    if (isNonNull(onBeforeChange)) {
      onBeforeChange(val);
    }
  }, [onBeforeChange]);

  const handleSliderChangeComplete = useCallback((val) => {
    setSliderValue(val);
    if (isNonNull(onChangeComplete)) {
      onChangeComplete(val);
    }
  }, [onChangeComplete]);

  const onSliderChangeHandler = useCallback((val) => {
    console.log("onchange", val);
    handleSliderChange(val);
  }, [handleSliderChange]);

  // 5. EFFECTS
  useEffect(() => {
    setSliderValue(defaultSliderValue);
  }, [defaultSliderValue]);

  useEffect(() => {
    if (isValueValid(value, min, max)) {
      setSliderValue(value);
    }
  }, [value, min, max, isValueValid]);

  // 6. ERROR HANDLING
  useEffect(() => {
    if (defaultValue && !isValueValid(defaultValue, min, max)) {
      console.warn(`RangeSelector_V1: defaultValue [${defaultValue}] is not valid for range [${min}, ${max}]`);
    }
    if (min >= max) {
      console.warn(`RangeSelector_V1: min (${min}) should be less than max (${max})`);
    }
  }, [defaultValue, min, max, isValueValid]);

  return (
    <ThemeWrapper isMobile={isMobileDevice}>
      <CustomRangeSlider
        {...otherProps}
        min={min}
        max={max}
        value={sliderValue}
        onChange={onSliderChangeHandler}
        onBeforeChange={handleSliderBeforeChange}
        onChangeComplete={handleSliderChangeComplete}
      />
    </ThemeWrapper>
  );
});

RangeSelector_V1.displayName = 'RangeSelector_V1';

RangeSelector_V1.defaultProps = {
  min: 0,
  max: 30000,
  step: 300,
  mode: "icon",
  type: "primary",
  labelPosition: "prefix",
  labelPrefix: "₹",
  showLabel: true,
  readOnly: false,
  wrapperStyle: null,
  wrapperClassName: null,
  labelTextStyle: null,
  disabled: false,
  onChange: () => { },
  afterChange: () => { },
};

RangeSelector_V1.propTypes = {
  /**
   * Classes to add additional styling in the parent container
   */
  additionalClassName: PropTypes.arrayOf(PropTypes.string),
  /**
   * id of the root div element
   */
  id: PropTypes.string,

  /**
   * Minimum value in the range Default is 0
   */
  min: PropTypes.number,
  /**
   * maximum value in the range Default is 30000
   */
  max: PropTypes.number,

  /**
   * Amount by which the position of slider will change in one movement Default is 300
   */
  step: PropTypes.number,

  /**
   * This function will executed after the user has stopped moving the slider
   * (params: any) => void
   */
  afterChange: PropTypes.func,

  /**
   * This is used to set the tabIndex of handles which are moved to change value of slider
   */
  tabIndex: PropTypes.number,

  /**
   * It is used to initialize uncontrolled components
   */
  defaultValue: PropTypes.number,

  /**
   * Decides wheather to show bottom label or not Default is true
   */
  showLabel: PropTypes.bool,

  /**
   * Label position, if prefix then labelPrefix else labelSuffix will be displayed Default is prefix
   */
  labelPosition: PropTypes.oneOf(["prefix", "suffix"]),

  /**
   * Label prefix to added before amount. It will displayed only if labelPosition value is prefix Default is ₹
   */
  labelPrefix: PropTypes.string,

  /**
   * Label suffix to added after amount. It will displayed only if labelPosition value is suffix
   */
  labelSuffix: PropTypes.string,

  /**
   * Property used to make component readOnly, it can still be focused
   */
  readOnly: PropTypes.bool,

  /**
   * Property used to disable component, disable component can not even receive focus
   */
  disabled: PropTypes.bool,

  /**
   * Label text style
   */
  labelTextStyle: PropTypes.object,

  /**
   * Style applied to wrapper div element
   */
  wrapperStyle: PropTypes.objectOf(PropTypes.string),

  /**
   * Style applied to track
   */
  trackStyle: PropTypes.objectOf(PropTypes.string),
  /**
   * Style applied to track of disabled component
   */
  disabledTrackStyle: PropTypes.objectOf(PropTypes.string),

  /**
   * Style applied to highlighted track
   */
  highlightedTrackStyle: PropTypes.objectOf(PropTypes.string),

  /**
   * Style applied to highlighted track of disabled component
   */
  disabledHighlightedTrackStyle: PropTypes.objectOf(PropTypes.string),

  /**
   * Style applied to handle
   */
  handleStyle: PropTypes.objectOf(PropTypes.string),

  /**
   * Style applied to focused handle
   */
  focusedHandleStyle: PropTypes.objectOf(PropTypes.string),

  /**
   * Style applied to hovered handle
   */
  hoveredHandleStyle: PropTypes.objectOf(PropTypes.string),

  /**
   * Style applied to active handle
   */
  activeHandleStyle: PropTypes.objectOf(PropTypes.string),

  /**
   * Style applied to disabled handle
   */
  disabledHandleStyle: PropTypes.objectOf(PropTypes.string),

  /**
   * Class applied to wrapper div element
   */
  wrapperClassName: PropTypes.string,

  /**
   * Either of these classes is applied to the handle depending on whether its enabled or disabled
   */
  handleClassName: PropTypes.string,

  /**
   * Either of these classes is applied to the handle depending on whether its enabled or disabled
   */

  disabledHandleClassName: PropTypes.string,

  /**
   * Either of these classes is applied to the track depending on whether its enabled or disabled
   */

  trackClassName: PropTypes.string,

  /**
   * Either of these classes is applied to the track depending on whether its enabled or disabled
   */
  highlightedTrackClassName: PropTypes.string,

  /**
   * Either of these classes is applied to the highlighted handle depending on whether its enabled or disabled
   */

  disabledHighlightedTrackClassName: PropTypes.string,

  /**
    * 	
      Variations of Range selector
    */
  type: PropTypes.oneOf(["primary", "secondary"]),

  /**
   * Custom Label to be shown on min value
   */
  minValueLabel: PropTypes.node,
  /**
   * Custom Label to be shown on max value
   */
  maxValueLabel: PropTypes.node,
  /**
   * To control the value from application
   */
  value: PropTypes.arrayOf(PropTypes.number)
};

export { RangeSelector_V1 };
