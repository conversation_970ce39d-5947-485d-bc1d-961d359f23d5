import React from "react";
import { Accordion } from "./Accordion";
import { RangeSelector } from '../RangeSelector/index';
import { decorateStory } from "storybook/internal/preview-api";

export default {
  title: "VMS_REACT/Accordion",
  component: Accordion,
  parameters: {
    docs: {
      description: {
        component: 'A customizable accordion component for VMS React library.',
      },
    },
  },
  argTypes: {
    additionalStyle: { 
      control: { type: "object" },
      description: "Custom inline styles"
    },
    additionalClassName: { 
      control: { type: "object" },
      description: "Additional CSS classes"
    },
    defaultExpanded: {
      control: { type: "boolean" },
      description: "Whether accordion starts expanded"
    },
    isCardClickExpansion: {
      control: { type: "boolean" },
      description: "Enable clicking entire card to expand"
    },
    isTextExpansion: {
      control: { type: "boolean" },
      description: "Enable clicking only text to expand"
    },
    isCardAndTextExpansion: {
      control: { type: "boolean" },
      description: "Enable both card and text expansion"
    },
  },
};

// Use function declarations instead of arrow functions for better React 19 compatibility
export function Default(args) {
  return (
    <Accordion {...args}>
      <p>
        Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse
        malesuada lacus ex, sit amet blandit leo lobortis eget.
        <br />
        Lorem ipsum dolor sit amet, consectetur adipiscing elit. <br />
        Suspendisse malesuada lacus ex, sit amet blandit leo lobortis eget.
      </p>
    </Accordion>
  );
}

Default.args = {
  id: "accordion-1",
  defaultExpanded: false,
  onExpansionToggled: (event, expanded) => {
    console.log("Accordion toggled", expanded);
  },
  title: "Default Accordion",
  isCardClickExpansion: true,
};

Default.parameters = {
  docs: {
    description: {
      story: 'Basic accordion with default configuration.',
    },
  },
};

export function AccordionWithTitleComponent(args) {
  return (
    <Accordion {...args}>
      <p>
        Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse
        malesuada lacus ex, sit amet blandit leo lobortis eget.
        <br />
        Lorem ipsum dolor sit amet, consectetur adipiscing elit. <br />
        Suspendisse malesuada lacus ex, sit amet blandit leo lobortis eget.
      </p>
    </Accordion>
  );
}

AccordionWithTitleComponent.args = {
  id: "accordion-title-component",
  titleComponent: <div style={{ fontWeight: "600" }}>Title Component</div>,
  defaultExpanded: false,
  isCardClickExpansion: true,
  titleIcon: "https://cdn.zeplin.io/5e6c836215857e16f95b9f5a/assets/9D25FE77-8E3B-4B83-B960-41A3129C4545.svg",  
};

AccordionWithTitleComponent.parameters = {
  docs: {
    description: {
      story: 'Basic accordion with default configuration.',
    },
  },
};

export function AccordionWithExpansionAndTitleIcon(args) {
  return (
    <Accordion {...args}>
      <p>
        Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse
        malesuada lacus ex, sit amet blandit leo lobortis eget.
        <br />
        Lorem ipsum dolor sit amet, consectetur adipiscing elit. <br />
        Suspendisse malesuada lacus ex, sit amet blandit leo lobortis eget.
      </p>
    </Accordion>
  );
}

AccordionWithExpansionAndTitleIcon.args = {
  id: "accordion-expansion-title-icon",
  titleComponent: (
    <div style={{ display: "flex", flexDirection: "column" }}>
      <div style={{ fontSize: 14, fontWeight: "bold" }}>Accordion Example</div>
      <div style={{ fontSize: 12, fontStyle: "italic" }}>
        Accordion + titleComponent + titleIcon + expansionIcon
      </div>
    </div>
  ),
  titleIcon: "https://cdn.zeplin.io/5e6c836215857e16f95b9f5a/assets/9D25FE77-8E3B-4B83-B960-41A3129C4545.svg",
  expansionIcon: "https://cdn2.iconfinder.com/data/icons/ios-7-icons/50/plus-512.png",
  expansionIconAfter: "https://cdn4.iconfinder.com/data/icons/ionicons/512/icon-minus-512.png",
  isCardClickExpansion: true,
};

AccordionWithExpansionAndTitleIcon.storyName = "Accordion + titleComponent + titleIcon + expansionIcon";

export function AccordionWithExpansionText(args) {
  return (
    <Accordion {...args}>
      <p>
        Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse
        malesuada lacus ex, sit amet blandit leo lobortis eget.
        <br />
        Lorem ipsum dolor sit amet, consectetur adipiscing elit. <br />
        Suspendisse malesuada lacus ex, sit amet blandit leo lobortis eget.
      </p>
    </Accordion>
  );
}

AccordionWithExpansionText.args = {
  id: "accordion-expansion-text",
  titleComponent: (
    <div style={{ display: "flex", flexDirection: "column" }}>
      <div style={{ fontSize: 14, fontWeight: "bold" }}>Accordion Example</div>
      <div style={{ fontSize: 12, fontStyle: "italic" }}>
        Accordion + titleComponent + titleIcon + expansionText
      </div>
    </div>
  ),
  titleIcon: "https://cdn.zeplin.io/5e6c836215857e16f95b9f5a/assets/9D25FE77-8E3B-4B83-B960-41A3129C4545.svg",
  expansionText: "Close",
  expansionTextAfter: "Edit",
  isCardClickExpansion: false,
  isTextExpansion: true,
  expansionBtnTextClick: (e) => {
    e.stopPropagation();
    console.log("nabi click");
  }
};

AccordionWithExpansionText.storyName = "Accordion + titleComponent + titleIcon + expansionText";

export function MobileDefault(args) {
  return (
    <Accordion {...args}>
      <p>
        Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse
        malesuada lacus ex, sit amet blandit leo lobortis eget.
        <br />
        Lorem ipsum dolor sit amet, consectetur adipiscing elit. <br />
        Suspendisse malesuada lacus ex, sit amet blandit leo lobortis eget.
      </p>
    </Accordion>
  );
}

MobileDefault.args = {
  id: "accordion-mobile-default",
  defaultExpanded: false,
  title: "Item 1",
  isMobileView: true,
  isCardClickExpansion: true,
};

MobileDefault.storyName = "(MoWeb) Default accordion";
MobileDefault.parameters = {
  viewport: {
    defaultViewport: "iphone5",
  },
};


