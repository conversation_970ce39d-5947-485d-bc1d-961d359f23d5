import React, { useEffect, useState, useCallback, useMemo } from "react";
import PropTypes from "prop-types";
import {
  AccordionWrapper,
  AccordionHeaderDiv,
  AccordionTitle,
  AccordionIcon,
  AccordionBody,
  AccordionTitleIcon,
  AccordionTitleDiv,
  AccordionSvg,
  ExpansionTextButton,
} from "./Accordion.styled";
import { useClassName } from "../../hooks/useClassName";
import { ThemeWrapper } from "../Theme/ThemeContext";

const ERROR =
  "VMS_REACT_ACCORDION. It is mandatory to pass props onExpansionToggled when used with expanded. You cannot switch between controlled and uncontrolled state";

const isNonNull = (prop) => prop !== null && prop !== undefined;

export const Accordion = (props) => {
  const {
    id,
    children,
    title,
    expansionIcon,
    additionalClassName,
    additionalStyle,
    titleIcon,
    defaultExpanded = false,
    expanded,
    onExpansionToggled,
    titleComponent,
    expansionIconAfter,
    isMobileView = false,
    isMobile,
    expansionText,
    expansionTextAfter,
    isCardClickExpansion = true,
    isTextExpansion,
    isCardAndTextExpansion,
    expansionBtnTextClick
  } = props;

  const [expandedState, setExpandedState] = useState(defaultExpanded);
  const className = useClassName(props, additionalClassName);

  // Memoize computed values
  const isControlled = useMemo(() => isNonNull(expanded), [expanded]);
  const isAccordionExpanded = useMemo(() => expanded || expandedState, [expanded, expandedState]);
  const shouldEnableCardClick = useMemo(() =>
    isNonNull(isCardClickExpansion) || isNonNull(isCardAndTextExpansion),
    [isCardClickExpansion, isCardAndTextExpansion]
  );

  useEffect(() => {
    if (isControlled && !isNonNull(onExpansionToggled)) {
      console.error(ERROR);
    }
  }, [isControlled, onExpansionToggled]);

  const expansionHandler = useCallback((event) => {
    event?.stopPropagation();

    if (isControlled) {
      if (isNonNull(onExpansionToggled)) {
        onExpansionToggled(event, !expanded);
      } else {
        console.error(ERROR);
      }
    } else {
      setExpandedState(prev => !prev);
    }
  }, [isControlled, onExpansionToggled, expanded]);

  const handleExpansionTextClick = useCallback((e) => {
    if (isNonNull(isTextExpansion) || isNonNull(isCardAndTextExpansion)) {
      expansionHandler(e);
    }
    if (isNonNull(expansionBtnTextClick)) {
      expansionBtnTextClick(e);
    }
  }, [isTextExpansion, isCardAndTextExpansion, expansionHandler, expansionBtnTextClick]);

  // Memoize expansion icon/text rendering
  const expansionElement = useMemo(() => {
    if (isNonNull(expansionIcon) && isNonNull(expansionIconAfter)) {
      return (
        <AccordionIcon className="vms_accordion_iconimg">
          <img
            height={24}
            width={24}
            src={isAccordionExpanded ? expansionIcon : expansionIconAfter}
            alt="Expansion icon"
          />
        </AccordionIcon>
      );
    }

    if (isNonNull(expansionText) && isNonNull(expansionTextAfter)) {
      return (
        <ExpansionTextButton
          className="vms_accordion_expansionBtntxt"
          onClick={handleExpansionTextClick}
        >
          <p>{isAccordionExpanded ? expansionText : expansionTextAfter}</p>
        </ExpansionTextButton>
      );
    }

    return (
      <AccordionIcon className="vms_accordion_expansionicon">
        <ExpansionIcon isAccordionExpanded={isAccordionExpanded} />
      </AccordionIcon>
    );
  }, [
    expansionIcon,
    expansionIconAfter,
    expansionText,
    expansionTextAfter,
    isAccordionExpanded,
    handleExpansionTextClick
  ]);

  return (
    <ThemeWrapper isMobile={isMobile || isMobileView}>
      <AccordionWrapper
        id={id}
        className={`${isAccordionExpanded ? "" : "vms_accordion_closed"} ${className} vms_accordion_container`}
        style={additionalStyle}
        isMobileView={isMobileView}
      >
        <AccordionHeaderDiv
          className="vms_accordion_header"
          onClick={shouldEnableCardClick ? expansionHandler : undefined}
          isAccordionExpanded={isAccordionExpanded}
        >
          <AccordionTitleDiv className="vms_accordion_title">
            {isNonNull(titleIcon) && (
              <AccordionTitleIcon className="vms_accordion_titleicon">
                <img src={titleIcon} alt="Title icon" />
              </AccordionTitleIcon>
            )}
            {isNonNull(titleComponent) ? (
              titleComponent
            ) : (
              <AccordionTitle className="vms_accordion_titletext">{title}</AccordionTitle>
            )}
          </AccordionTitleDiv>
          {expansionElement}
        </AccordionHeaderDiv>
        <AccordionBody className="vms_accordion_body" isAccordionExpanded={isAccordionExpanded}>
          {children}
        </AccordionBody>
      </AccordionWrapper>
    </ThemeWrapper>
  );
};

// Memoized SVG component
const ExpansionIcon = React.memo(({ isAccordionExpanded }) => (
  <AccordionSvg
    isAccordionExpanded={isAccordionExpanded}
    xmlns="http://www.w3.org/2000/svg"
    width="15"
    height="8"
    viewBox="0 0 15 8"
  >
    <g fill="none" fillRule="evenodd">
      <path d="M0 0H24V24H0z" transform="translate(-5 -8)" />
      <g stroke="currentColor" strokeLinecap="round">
        <path
          d="M0 7L7 0M0 7L2.994 9.994 7 14"
          transform="translate(-5 -8) matrix(0 -1 -1 0 19.5 15.5)"
        />
      </g>
    </g>
  </AccordionSvg>
));

ExpansionIcon.displayName = 'ExpansionIcon';

Accordion.defaultProps = {
  defaultExpanded: false,
  expansionIcon: null,
  expanded: null,
  onExpansionToggled: null,
  isMobileView: false,
  isCardClickExpansion: true,
};

Accordion.propTypes = {
  id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  title: PropTypes.string,
  titleComponent: PropTypes.node,
  children: PropTypes.any,
  defaultExpanded: PropTypes.bool,
  disabled: PropTypes.bool,
  expanded: PropTypes.bool,
  titleIcon: PropTypes.string,
  expansionIcon: PropTypes.string,
  expansionIconAfter: PropTypes.string,
  onExpansionToggled: PropTypes.func,
  titleIconClassname: PropTypes.string,
  expansionIconClassname: PropTypes.string,
  titleIconeAlt: PropTypes.string,
  expansionIconeAlt: PropTypes.string,
  additionalClassName: PropTypes.arrayOf(PropTypes.string),
  additionalStyle: PropTypes.object,
  isMobileView: PropTypes.bool,
  expansionText: PropTypes.string,
  expansionTextAfter: PropTypes.string,
  isCardClickExpansion: PropTypes.bool,
  isTextExpansion: PropTypes.bool,
  isCardAndTextExpansion: PropTypes.bool,
  expansionBtnTextClick: PropTypes.func,
};
