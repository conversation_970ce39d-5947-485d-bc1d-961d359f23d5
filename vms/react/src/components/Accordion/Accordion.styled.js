import styled from "styled-components";
export const AccordionWrapper = styled.div`
  display: flex;
  flex-direction: column;
  /* ${({ isMobileView, theme }) =>
    isMobileView === true
      ? `
   border-top: 1px solid ${theme.palette.primary[theme.mode]};
   border-bottom: 1px solid ${theme.palette.primary[theme.mode]};
  `
      : `border: 1px solid ${theme.palette.primary[theme.mode]}`}; */
  border-radius: ${({ theme, isMobileView }) =>
    isMobileView === false && theme.shape.borderRadius};
  transition: margin 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  width: 100%;
  box-shadow: ${({isMobileView})=>isMobileView===true?"none":"0px 0px 1px 1px #ebebeb"};
  border-top: ${({isMobileView}) => isMobileView && "1px solid #ebebeb"};
  border-bottom: ${({isMobileView}) => isMobileView && "1px solid #ebebeb"};
`;
export const AccordionHeaderDiv = styled.div`
  display: flex;
  justify-content: space-between;
  cursor: pointer;
  padding: 1%;
  transition: min-height 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  background-color: 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  background-color: ${({ isAccordionExpanded }) =>
    isAccordionExpanded === true ? "#f4f4f4" : "#fff"};
  align-items: center;
  transition-timing-function: cubic-bezier(0.53, 0.56, 0.75, 0.7);
`;
export const AccordionTitleDiv = styled.div`
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
`;
export const AccordionTitleIcon = styled.div``;
export const AccordionTitle = styled.span`
  font-size: ${({ theme }) => theme.typography.title.fontSize};
  font-weight: ${({ theme }) => theme.typography.title.fontWeight};
  font-family: ${({ theme }) => theme.typography.fontFamily};
`;
export const AccordionIcon = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
`;
export const AccordionBody = styled.div`
  display: ${({isAccordionExpanded})=>(isAccordionExpanded ?"flex":"none")};
  /* visibility: ${({isAccordionExpanded})=>(isAccordionExpanded ?"visible":"hidden")};
  height: ${({isAccordionExpanded})=>(isAccordionExpanded ?"auto":"0")};
  padding: ${({isAccordionExpanded})=>(isAccordionExpanded ?"1% !important":"0%")}; */
  padding: 0 1.5%;
  transition-duration: 270ms;
  transition: height 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
`;
export const AccordionSvg = styled.svg`
  color: ${({ theme }) => theme.palette.primary[theme.mode]};
  transform: ${({ isAccordionExpanded }) =>
    isAccordionExpanded === true ? `rotate(180deg)` : ""};
`;

export const ExpansionTextButton = styled.button`
  border:none;
  background : transparent;
`
