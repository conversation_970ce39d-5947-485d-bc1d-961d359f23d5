import React from "react";
import { ThemeProvider } from "styled-components";
import {theme_desktop} from "../../../../../vms-theme/theme_desktop.js";
import {theme_mobile} from "../../../../../vms-theme/theme_mobile.js";

const Theme = ({ children, isMobile }) => {
  let VMS_THEME = isMobile ? theme_mobile : theme_desktop;
  return <ThemeProvider theme={VMS_THEME}>{children}</ThemeProvider>;
};

const ThemeWrapper = ({ children, isMobile }) => {
  let VMS_THEME = isMobile ? theme_mobile : theme_desktop;
  return <ThemeProvider theme={VMS_THEME}>{children}</ThemeProvider>;
};

const getConfig = (isMobile) => {
  return isMobile ? theme_mobile : theme_desktop;
};

export { Theme, ThemeWrapper, getConfig };