import React, { useState, useEffect, useRef, useMemo, useCallback } from "react";
import PropTypes from "prop-types";
import { DateInput } from "./Components/DateInput";
import {
  CalendarContainer,
  OverlayContainer,
  OverlayContainer1,
  OverlayDiv,
  PickerBodyContainer,
  PickerContainer,
} from "./Components/CalendarContainer";
import { MonthHeader } from "./Components/MonthHeader";
import {
  addDay,
  addMonth,
  formatWithLocale,
  getNextYear,
  subMonth,
  whetherBefore,
  whetherSameDay,
  checkRangeIsValid,
  checkRangeErrorCode,
  whetherAfter,
  getMonthDiff,
  differenceCalendarDays,
  getAllMonths,
} from "../../hooks/calendarHooks";
import { RangeCell } from "./Components/RangeCell";
import { useClassName } from "../../hooks/useClassName";
import { useClickAway, useOnClickOutside } from "../../hooks/outsideClickHandler";
import { AutoFocusInput } from './Components/AutoFocusInput';
import { ThemeWrapper } from "../Theme/ThemeContext";

const isNonNull = (prop) => {
  return prop !== null && prop !== undefined;
};

const DateRangeCalendar = React.memo((props) => {
  const className = useClassName(props, props.additionalClassName);
  const itemContainerClassName = useClassName(props, props.itemContainer);
  const [editting, setEditting] = useState(props.openCalendar);
  const [fromDate, setFromDate] = useState(props.startDate);
  const [toDate, setToDate] = useState(props.endDate);
  const [fromMonth, setFromMonth] = useState(new Date());
  const [toMonth, setToMonth] = useState(
    addMonth(new Date(), props.noofMonthView - 1)
  );
  const [selectedDate, setSelectedDate] = useState();
  const [selectDateChange, setSelectedDateChange] = useState(false);
  const [whetherFirstInput, setWhetherFirstInput] = useState(true);
  const [autoCurrentSelectedInput, setAutoCurrentSelectedInput] = useState();
  let calendarDropdownref = useRef()
  
  const {
    calendarType,
    noofMonthView,
    maxBookingDays,
    isOverlay,
    minDate,
    maxDate,
    dateDisplayFormat,
    indicatorColor,
    arrowImages,
    hideHeader,
    autoInputFocusData,
    autoFocus,
    showEndDate,
    showTodayDate,
    todaysDateCss,
    onInputClick,
    accessibleId,
    isMobile,
    isMobileView
  } = props;

  const memoizedRangeProps = useMemo(() => ({
    minDate,
    maxDate,
    fromMonth,
    toMonth,
    noofMonthView,
    whetherFirstInput,
    maxBookingDays,
    currentSelection: props.currentSelection,
    arrowImages,
    mode: props.mode,
    disabledColor: props.disabledColor,
    showDisabled: props.showDisabled,
    autoFocus,
    showTodayDate,
    todaysDateCss,
    autoCurrentSelectedInput,
    disableDateList: props.disableDateList
  }), [
    minDate,
    maxDate,
    fromMonth,
    toMonth,
    noofMonthView,
    whetherFirstInput,
    maxBookingDays,
    props.currentSelection,
    arrowImages,
    props.mode,
    props.disabledColor,
    props.showDisabled,
    autoFocus,
    showTodayDate,
    todaysDateCss,
    autoCurrentSelectedInput,
    props.disableDateList
  ]);

  const memoizedDateInputProps = useMemo(() => ({
    editting,
    fromDate,
    toDate,
    selectedDate,
    mode: props.mode,
    currentSelection: props.currentSelection,
    inputBoxLabel: props.inputBoxLabel,
    formatDateInput: dateDisplayFormat,
    selectDateChange,
    customInputView: props.customInputView,
    calendarType,
    id: props.id,
    autoInputFocusData,
    autoFocus,
    showEndDate,
    autoCurrentSelectedInput,
    altText: props.altText
  }), [
    editting,
    fromDate,
    toDate,
    selectedDate,
    props.mode,
    props.currentSelection,
    props.inputBoxLabel,
    dateDisplayFormat,
    selectDateChange,
    props.customInputView,
    calendarType,
    props.id,
    autoInputFocusData,
    autoFocus,
    showEndDate,
    autoCurrentSelectedInput,
    props.altText
  ]);

  const calculateNewRage = useCallback((fromDate, toDate) => {
    const { onRangeSelected, mode } = props;
    if (!whetherSameDay(fromDate, toDate) && !whetherBefore(fromDate, toDate)) {
      setFromDate(toDate);
      setToDate(fromDate);
    }
    const getDiiferenceInMonth = getMonthDiff(new Date(), props.startDate);
    if (getDiiferenceInMonth > 0) {
      let newFromMonth = addMonth(new Date(), getDiiferenceInMonth);
      setFromMonth(newFromMonth);
      setToMonth(addMonth(newFromMonth, props.noofMonthView - 1))
    }
  }, [props.onRangeSelected, props.mode, props.startDate, props.noofMonthView]);

  const nextMonth = useCallback(() => {
    setFromMonth(addMonth(fromMonth, 1));
    setToMonth(addMonth(toMonth, 1));
  }, [fromMonth, toMonth]);

  const prevMonth = useCallback(() => {
    setFromMonth(subMonth(fromMonth, 1));
    setToMonth(subMonth(toMonth, 1));
  }, [fromMonth, toMonth]);

  const onMonthClick = useCallback((index, lastIndex) => {
    const { noofMonthView, maxDate } = props;
    let newFromMonth = addMonth(
      new Date(),
      lastIndex
        ? index - (noofMonthView > 2 ? noofMonthView - 2 : noofMonthView - 1)
        : index
    );
    let newToMonth = addMonth(newFromMonth, noofMonthView - 1);
    setFromMonth(newFromMonth);
    setToMonth(newToMonth);
    if (
      !whetherSameDay(newToMonth, maxDate) &&
      whetherAfter(newToMonth, maxDate)
    ) {
      let newFromMonth = subMonth(newToMonth, noofMonthView);
      setFromMonth(newFromMonth);
      setToMonth(addMonth(newFromMonth, noofMonthView - 1));
    }
  }, [props.noofMonthView, props.maxDate]);

  const handleInputClick = useCallback((e, focusInputValue) => {
    console.log("focusInputValue", focusInputValue)
    setEditting(!editting);
    setAutoCurrentSelectedInput(focusInputValue)
  }, [editting]);

  const handleDateInputClick = useCallback(() => {
    setEditting(!editting)
    console.log("onInputClick", onInputClick)
  }, [editting, onInputClick]);

  useClickAway(calendarDropdownref, () => {
    if (editting) {
      if (!props.showCalendarOnOutsideClick && editting && isNonNull(props.onCalendarVisibilityChange)) {
        props.onCalendarVisibilityChange(editting);
      }
      if (!props.showCalendarOnOutsideClick) {
        setEditting(props.showCalendarOnOutsideClick ? props.showCalendarOnOutsideClick : false)
      }
    }
  })

  useEffect(() => {
    calculateNewRage(fromDate, toDate);
  }, [calculateNewRage, fromDate, toDate]);

  useEffect(() => {
    const { onCalendarVisibilityChange } = props;
    if (editting) {
      if (isNonNull(onCalendarVisibilityChange)) {
        onCalendarVisibilityChange(editting);
      }
    }
  }, [editting, props.onCalendarVisibilityChange]);

  useEffect(() => {
    setEditting(props.openCalendar)
    
    if (props.openCalendar) {
      const getAllMonthsName = getAllMonths(new Date(), props.maxDate);
      const ActiveMonthName = getAllMonths(props.startDate, props.endDate);
      if (ActiveMonthName && ActiveMonthName.length > 0) {
        const firstMonthIndex = ActiveMonthName[0];
        const matchingIndex = getAllMonthsName.findIndex((item1) => (
          item1.monthName === firstMonthIndex.monthName && item1.year === firstMonthIndex.year
        ));
        if (matchingIndex !== -1) {
          onMonthClick(matchingIndex, (matchingIndex === getAllMonthsName.length - 1))
        }
      }
    }

    // Fix: Ensure dates are always valid
    if (props.startDate && props.startDate instanceof Date && !isNaN(props.startDate.getTime())) {
      setFromDate(props.startDate);
    }
    
    if (props.endDate && props.endDate instanceof Date && !isNaN(props.endDate.getTime())) {
      setToDate(props.endDate);
    } else {
      // Set a default end date if props.endDate is invalid
      setToDate(props.startDate || new Date());
    }
    
    const selectedDateValue = props.currentSelection === 'startDate' ? props.startDate : 
                             props.currentSelection === 'endDate' ? props.endDate : 
                             new Date();
    
    if (selectedDateValue && selectedDateValue instanceof Date && !isNaN(selectedDateValue.getTime())) {
      setSelectedDate(selectedDateValue);
    } else {
      setSelectedDate(new Date());
    }
    
    if (autoFocus) {
      setAutoCurrentSelectedInput('startDate')
    }
  }, [props.openCalendar, props.currentSelection, props.startDate, props.endDate, props.maxDate, autoFocus, onMonthClick])

  useEffect(() => {
    setFromDate(props.startDate);
    setToDate(props.endDate)
  }, [props.startDate, props.endDate])

  const onItemClick = useCallback((date) => {
    const {
      onSingleDateRangeSelectionChange,
      maxBookingDays,
      minBookingDays,
      currentSelection,
      onDateSelectionChange,
    } = props;
    
    if (props.mode == "singleDateRange") {
      let isValid = checkRangeIsValid(
        fromDate,
        date,
        minBookingDays,
        maxBookingDays
      );
      
      let result = {
        calendarState: {
          valid: isValid,
        },
        date: date,
        dateType: autoFocus ? autoCurrentSelectedInput : currentSelection,
        format1: formatWithLocale(date, "yyyy-MM-dd'T'HH:mm:ss'Z'"),
        format2: formatWithLocale(date, "dd-MM-yyyy"),
      };

      if (autoFocus) {
        if (autoCurrentSelectedInput === 'startDate') {
          result.calendarState.startDate = date;
          result.calendarState.endDate = props.endDate || toDate || date;
        }
        if (autoCurrentSelectedInput === 'endDate') {
          result.calendarState.startDate = fromDate;
          result.calendarState.endDate = date;
        }
      } else {
        if (currentSelection === 'startDate') {
          result.calendarState.startDate = date;
          result.calendarState.endDate = props.endDate || toDate || date;
        }
        if (currentSelection === 'endDate') {
          result.calendarState.startDate = fromDate;
          result.calendarState.endDate = date;
        }
      }

      if (!isValid) {
        let errorCode = checkRangeErrorCode(
          fromDate,
          date,
          minBookingDays,
          maxBookingDays
        );
        if (errorCode !== 0) {
          result.calendarState.errorCode = errorCode;
        }
      }

      if (autoFocus) {
        if (autoCurrentSelectedInput === "startDate") {
          setFromDate(date);
          setToDate(toDate || date);
          setAutoCurrentSelectedInput("endDate");
        } else {
          setFromDate(fromDate);
          setToDate(date);
          setAutoCurrentSelectedInput("startDate");
          setEditting(!editting);
        }
        setSelectedDateChange(true);
        onSingleDateRangeSelectionChange(result);
      } else {
        onSingleDateRangeSelectionChange(result);
        setSelectedDate(date);
        setSelectedDateChange(true);
        setEditting(!editting);
      }
    } else {
      if (whetherFirstInput) {
        setWhetherFirstInput(false);
        setFromDate(date);
        setToDate(toDate);
      } else {
        if (whetherBefore(date, fromDate)) {
          let isValid = checkRangeIsValid(
            fromDate,
            date,
            minBookingDays,
            maxBookingDays
          );
          let result = {
            startDate: date,
            endDate: date,
            valid: isValid,
            datesInFormat: {
              format1: {
                startDate: formatWithLocale(date, "yyyy-MM-dd'T'HH:mm:ss'Z'"),
                endDate: formatWithLocale(date, "yyyy-MM-dd'T'HH:mm:ss'Z'"),
              },
              format2: {
                startDate: formatWithLocale(date, "dd-MM-yyyy"),
                endDate: formatWithLocale(date, "dd-MM-yyyy"),
              },
            },
          };
          if (!isValid) {
            let errorCode = checkRangeErrorCode(
              fromDate,
              date,
              minBookingDays,
              maxBookingDays
            );
            if (errorCode !== 0) {
              result.errorCode = errorCode;
            }
          }
          setWhetherFirstInput(true);
          setFromDate(date);
          setToDate(date);
          onDateSelectionChange(result);
          setEditting(!editting);
        } else {
          let isValid = checkRangeIsValid(
            fromDate,
            date,
            minBookingDays,
            maxBookingDays
          );
          let result = {
            startDate: fromDate,
            endDate: date,
            valid: isValid,
            datesInFormat: {
              format1: {
                startDate: formatWithLocale(fromDate, "yyyy-MM-dd'T'HH:mm:ss'Z'"),
                endDate: formatWithLocale(date, "yyyy-MM-dd'T'HH:mm:ss'Z'"),
              },
              format2: {
                startDate: formatWithLocale(fromDate, "dd-MM-yyyy"),
                endDate: formatWithLocale(date, "dd-MM-yyyy"),
              },
            },
          };
          if (!isValid) {
            let errorCode = checkRangeErrorCode(
              fromDate,
              date,
              minBookingDays,
              maxBookingDays
            );
            if (errorCode !== 0) {
              result.errorCode = errorCode;
            }
          }
          setWhetherFirstInput(true);
          setFromDate(fromDate);
          setToDate(date);
          onDateSelectionChange(result);
          setEditting(!editting);
        }
      }
    }
  }, [
    props.mode,
    props.onSingleDateRangeSelectionChange,
    props.maxBookingDays,
    props.minBookingDays,
    props.currentSelection,
    props.onDateSelectionChange,
    props.endDate,
    fromDate,
    toDate,
    autoFocus,
    autoCurrentSelectedInput,
    whetherFirstInput,
    editting
  ]);

  const maxDateForRange = useMemo(() => {
    return maxBookingDays !== 30
      ? addDay(new Date(), maxBookingDays)
      : maxDate;
  }, [maxBookingDays, maxDate]);

  const fromDateForRange = useMemo(() => {
    return autoFocus ? fromDate :
      props.mode === "multiDateRange"
        ? fromDate
        : props.startDate;
  }, [autoFocus, fromDate, props.mode, props.startDate]);

  const toDateForRange = useMemo(() => {
    return autoFocus ? toDate :
      props.mode === "multiDateRange" ? toDate : props.endDate;
  }, [autoFocus, toDate, props.mode, props.endDate]);

  return (
    <ThemeWrapper isMobile={isMobile || isMobileView || false}>
      <OverlayContainer className={`vms_DateRangeCalendar_OverlayContainer`} isOverlay={isOverlay} ref={calendarDropdownref} editting={editting}>
        <PickerContainer id={"vms_DateRangeCalendar_PickerContainer"} className={`vms_DateRangeCalendar_PickerContainer`}>
          {autoFocus && props.mode == 'singleDateRange' ? (
            <AutoFocusInput
              {...memoizedDateInputProps}
              inputClick={handleInputClick}
            />
          ) : (
            <DateInput
              {...memoizedDateInputProps}
              inputClick={handleDateInputClick}
            />
          )}

          {editting && (
            <>
              <CalendarContainer
                type={calendarType}
                isMobile={false}
                className={`${className} vms_DateRangeCalendar_CalendarContainer`}
                style={props.additionalStyle}
              >
                <PickerBodyContainer className={`vms_DateRangeCalendar_PickerBodyContainer`} editting={editting}>
                  {!hideHeader && (
                    <MonthHeader
                      from={new Date()}
                      to={getNextYear(1)}
                      fromMonth={fromMonth}
                      toMonth={toMonth}
                      onClick={onMonthClick}
                      indicatorColor={indicatorColor}
                    />
                  )}

                  <RangeCell
                    view="range"
                    onItemClick={onItemClick}
                    fromDate={fromDateForRange}
                    toDate={toDateForRange}
                    from={new Date()}
                    to={maxDateForRange}
                    onNextMonth={nextMonth}
                    onPrevMonth={prevMonth}
                    {...memoizedRangeProps}
                  />
                </PickerBodyContainer>
              </CalendarContainer>
            </>
          )}
        </PickerContainer>
      </OverlayContainer>
      {isOverlay && editting && <OverlayDiv className="vms_DateRangeCalendar_OverlayDiv" editting={editting} />}
    </ThemeWrapper>
  );
});

DateRangeCalendar.displayName = 'DateRangeCalendar';

DateRangeCalendar.defaultProps = {
  minDate: new Date(),
  maxDate: getNextYear(1),
  noofMonthView: 2,
  onRangeSelected: () => { },
  onSingleDateRangeSelectionChange: () => { },
  onDateSelectionChange: () => { },
  view: "range",
  onCalendarVisibilityChange: () => { },
  dateDisplayFormat: "dd MMM yyyy",
  additionalClassName: null,
  additionalStyle: null,
  inputBoxLabel: "",
  inputPlaceholder: "Select Date",
  isOverlay: false,
  calendarType: "primary",
  minBookingDays: 1,
  maxBookingDays: differenceCalendarDays(new Date(new Date(new Date().setYear(new Date().getFullYear() + 1)).setDate(0)), new Date()),
  currentSelection: "startDate",
  mode: "multiDateRange",
  openCalendar: false,
  indicatorColor: "#f26b6a",
  hideHeader: false,
  autoFocus: false,
  showDisabled: false,
  showEndDate: false,
  showTodayDate: false,
  showCalendarOnOutsideClick: false,
};

DateRangeCalendar.propTypes = {
  /**
    
  * ID for the input
   */
  id: PropTypes.string,

  /**
   * Variations of Calendar Type
   */
  calendarType: PropTypes.oneOf(["primary", "secondary"]),

  /**
   * Range start Date.
Date
   */

  startDate: PropTypes.instanceOf(Date).isRequired,

  /**
   * Range End Date.
Date
   */
  endDate: PropTypes.instanceOf(Date).isRequired,

  /**
   * Defines how many months need to display side by side. it will take 2 as default value.
   */
  noofMonthView: PropTypes.number,

  /**
   * If I want to open calendar pass true.
   */
  openCalendar: PropTypes.bool,
  /**
   * To display label above input box. Note: It will be ignored if @property customInputView is provided
string
string
   */

  inputBoxLabel: PropTypes.string,

  /**
   * 	
Defines minimum date. Disabled earlier dates. it will take current Date as default value.

Date
   */
  minDate: PropTypes.instanceOf(Date),

  /**
   * Defines maximum date. Disabled later dates it will take current Date + 1 year as default value.
    Date
   */
  maxDate: PropTypes.oneOfType([PropTypes.instanceOf(Date)]),

  /**
   * It will replace back and next arrow with given values. you can pass direct web URL or local image path
{ prevArrowImage: string; nextArrowImage: string; prevArrowImageAlt?: string; nextArrowImageAlt?: string; }
   */
  arrowImages: PropTypes.shape({
    prevArrowImage: PropTypes.string,
    nextArrowImage: PropTypes.string,
    prevArrowImageAlt: PropTypes.string,
    nextArrowImageAlt: PropTypes.string,
  }),

  /**
   * If @property isMobile is true then this function will be called when user click on done button
If @property isMobile is false then this function will be called when user click on any date
it will return {
   date: Date | undefined,
   format1 (ISO format): string,
   format2 (DD-MM-YYYY): string,
   };
}

(result: CalendarOutput) => void
   */
  onRangeSelected: PropTypes.func,

  /**
   * 
    callback function for range changes when @property mode is multiDateRange.
it will return {
   startDate: Date,
   endDate: Date,
   valid: boolean,
   errorCode?: number (optional),
   datesInFormat: {
     format1 (ISO format): {
       startDate: string;
       endDate: string;
     };
     format2 (DD-MM-YYYY): {
       startDate: string;
       endDate: string;
     };
   };
}
if valid = false then it will return errorCode.
errorCode values
1 = When start and end Date same
2 = When selected range is less than minBookingDays
3 = When selected range is more than maxBookingDays

(result: DateRangeCalendarOutput) => void
   */
  onDateSelectionChange: PropTypes.func,

  /**
   * Displayed Date format . Default value dd MMM yyyy
Please refer https://date-fns.org/v2.12.0/docs/format for various allowed format

string
   */
  dateDisplayFormat: PropTypes.string,

  /**
   * 	
It will give you callback when calendar open/close. It will be useful when you want to do some conditional coding based on calendarVisibility

(isOpen: boolean) => void
   */
  onCalendarVisibilityChange: PropTypes.func,
  /**
   *Inline styles to add additional styling in the parent container
CSSProperties
   */
  additionalStyle: PropTypes.object,
  /**
   * Classes to add additional styling in the parent container
string[]
   */

  additionalClassName: PropTypes.arrayOf(PropTypes.string),
  /**
   * Place holder text for input box. Default value Select Date
string
   */
  inputPlaceholder: PropTypes.string,
  /**
   *
To wrap the component with overlay. Default value false
boolean
   */
  isOverlay: PropTypes.bool,

  /**
   * Custom input, It will replace calendar's default input box
However component will bind onClick event to this custom input so calendar will open on click of it

ReactNode
   */
  customInputView: PropTypes.oneOfType([PropTypes.node, PropTypes.string]),

  /**
   * Calendar Mode
singleDateRange - It will allow to select only one date (based on @property currentSelection value) in single calendar
multiDateRange - It will allow to select two dates in single calendar

"multiDateRange" | "singleDateRange"
   * 
   */
  mode: PropTypes.oneOf(["singleDateRange", "multiDateRange"]),

  /**
   *  Minimum days differece between start and end date. it will take 1 as default value.
number
   */
  minBookingDays: PropTypes.number,
  /**
   *  Maximum days differece between start and end date. it will take 30 as default value.
number
 */
  maxBookingDays: PropTypes.number,

  /**
   *   Only used in singleDateRange mode, based on this value calendar will select the range type
  "startDate" | "endDate"
 */
  currentSelection: PropTypes.oneOf(["startDate", "endDate"]),
  /**
     *  Active MonthName color
  string
     */
  indicatorColor: PropTypes.string,
  onSingleDateRangeSelectionChange: PropTypes.func,
  /**
     *  Hide MonthHeader color
  string
     */
  hideHeader: PropTypes.bool,
  /**
    *  disabled date color code date
 string
    */
  disabledColor: PropTypes.string,

  /**
    *  autoFocus without close the calendar select date range  bool,it is only work if mode is singleDateRange
    */
  autoFocus: PropTypes.bool,
  /**
   * If autofocus true then data of autoInputFocusData of label 
{ leftInputViewLabel: string; leftInputViewValue: string; rightInputViewLable?: string; rightInputViewValue?: string; }
   */
  autoInputFocusData: PropTypes.shape({
    leftInputViewLabel: PropTypes.string,
    leftInputViewValue: PropTypes.string,
    rightInputViewLable: PropTypes.string,
    rightInputViewValue: PropTypes.string,
    inputWidth: PropTypes.string,
  }),
  /**
     *  showDisabled month date  
  string
     */
  showDisabled: PropTypes.bool,

  /**
     *  show End   date  
  string
     */
  showEndDate: PropTypes.bool,


  /**
   * highlight Today's Date in calendare date 
   */
  showTodayDate: PropTypes.bool,
  /**
    * custom css for today's date  it is applied when showTodayDate is true
    */
  todaysDateCss: PropTypes.shape({
    border: PropTypes.string,
    backgroundColor: PropTypes.string,
    height: PropTypes.string,
    borderRadius: PropTypes.string,
    width: PropTypes.string,
  }),


  /**
   *  show calendar on outside click 
bool
   */
  showCalendarOnOutsideClick: PropTypes.bool,
  /**
   * Disable a list of dates in the format 'dd/MM/yyyy' from an array.
   */
  disableDateList : PropTypes.arrayOf(PropTypes.string),

};

export { DateRangeCalendar };
