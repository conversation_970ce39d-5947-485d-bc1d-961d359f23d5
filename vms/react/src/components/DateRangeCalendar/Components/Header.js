import React from "react";
import styled from "styled-components";
import PropTypes from "prop-types";
import { Row, Col } from "./CalendarContainer";
import { addMonth, formatWithLocale, whetherBefore, whetherSameMonth } from "../../../hooks/calendarHooks";
import BackArrowSvg from "../../../assets/images/DateRangeCalendar/backArrow.svg";
import ForwardArrowSvg from "../../../assets/images/DateRangeCalendar/forwardArrow.svg";

const HeaderContainer = styled(Row)`
  position: relative;
  margin: 18px 10px 0px 24px;
  display: flex;
  align-items: center;
  width: 300px;
`;

const IconContainer = styled.div`
  background-repeat: no-repeat;
  border: 0;
  width: 20px;
  height: 25px;
  background-size: unset;
  outline: none;
  display: ${(props) => (props.isDisplay === false ? "none" : "")};
  margin-left: ${({align}) => align==="left" && `10px`};
  margin-right: ${({align}) => align==="right" && `25px`};
`;

const TitleButton = styled.div`
  text-align: center;
  font-weight: 700;
  font-family: ${({ theme }) => theme?.typography?.fontFamily || ""};
  font-size: ${({ theme }) => theme?.typography?.text?.fontSize || ""};
  color: #4d4d4f;
  padding: 0.6em 0;
  line-height: 1.43;
  letter-spacing: 0.3px;
  /* margin-bottom: 10px */
`;

const Header = (props) => {
  const {
    selectedDate,
    next,
    prev,
    arrowImages,
    isFromMonth,
    isToMonth,
    startDate,
    endDate,
    minDate,
    maxDate
  } = props;
  const formattedStartDate = formatWithLocale(startDate, "MMMM yyyy");
  // const formattedEndDate = formatWithLocale(endDate, "MMMM yyyy");
  const formattedSelectedDate = formatWithLocale(selectedDate, "MMMM yyyy");
  const checkEndDate = whetherSameMonth(maxDate,selectedDate);
  return (
    <HeaderContainer className={"vms_DateRangeCalendar_HeaderContainer"}>
      <Col className={"vms_DateRangeCalendar_Col"} justifyContent="flex-start" textAlign="left" onClick={prev}>
        {isFromMonth && formattedSelectedDate !== formattedStartDate && (
          <IconContainer align="left" className={"vms_DateRangeCalendar_PrevIconContainer"} isDisplay={true}>
            <img
              src={arrowImages?.prevArrowImage || BackArrowSvg}
              alt={
                arrowImages?.prevArrowImageAlt
                  ? arrowImages?.prevArrowImageAlt
                  : "prev"
              }
            />
          </IconContainer>
        )}
      </Col>
      <Col className={"vms_DateRangeCalendar_SelectDateContainer"} justifyContent="center" textAlign="center">
        <TitleButton className={"vms_DateRangeCalendar_SelectDateButton"}>{formattedSelectedDate}</TitleButton>
      </Col>
      <Col className={"vms_DateRangeCalendar_Col"}  justifyContent="flex-end" textAlign="right" onClick={next}>
        {isToMonth && !checkEndDate && (
          <IconContainer align="right" className={"vms_DateRangeCalendar_NextIconContainer"} isDisplay={true}>
            <img
              src={arrowImages?.nextArrowImage || ForwardArrowSvg}
              alt={
                arrowImages?.nextArrowImageAlt
                  ? arrowImages?.nextArrowImageAlt
                  : "next"
              }
            />
          </IconContainer>
        )}
      </Col>
    </HeaderContainer>
  );
};

Header.defaultProps = {
  selectedDate: new Date(),
  prev: () => {},
  next: () => {},
  prevYear: () => {},
  nextYear: () => {},
  changeView: () => {},
  showSubView: () => {},
  fromMonth: new Date(),
  formatMonthYear: "MMMM yyyy",
  toMonth: addMonth(new Date(), 1),
};

Header.propTypes = {
  selectedDate: PropTypes.instanceOf(Date),
  view: PropTypes.string.isRequired,
  prev: PropTypes.func,
  next: PropTypes.func,
  formatMonthYear: PropTypes.string,
  changeView: PropTypes.func,
  fromMonth: PropTypes.instanceOf(Date),
  toMonth: PropTypes.instanceOf(Date),
};

export { Header };
