import React from "react";
import PropTypes from "prop-types";
import { DateCell } from "./DateCell";
import { addMonth } from "../../../hooks/calendarHooks";
import styled from "styled-components";
import { Header } from "./Header";
import { useClassName } from "../../../hooks/useClassName";

const Row = styled.div`
  margin: 0;
  padding: 0;
  flex-grow: 1;
  flex-basis: auto;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  align-items: flex-start;
  justify-content: "center";
`;
const Col = styled.div`
  flex-grow: 1;
  flex-basis: auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
`;

const RangeCell = (props) => {
  const {
    view,
    onItemClick,
    showConfirmButton,
    minDate,
    maxDate,
    fromDate,
    toDate,
    fromMonth,
    toMonth,
    noofMonthView,
    onNextMonth,
    onPrevMonth,
    from,
    to,
    maxBookingDays,
    arrowImages,
    mode,
    currentSelection,
    itemContainerClassName,
    disabledColor,
    showDisabled,
    autoFocus,
    autoCurrentSelectedInput,
    showTodayDate,
    todaysDateCss,
    whetherFirstInput,
    disableDateList
  } = props;
  const constContainerClassName = useClassName(props, itemContainerClassName);
  const renderMonths = () => {
    var monthList = [];
    for (var i = 0; i < noofMonthView; ++i) {
      var monthsToAdd = i - 0;
      var monthDate = addMonth(fromMonth, monthsToAdd);
      var monthKey = `month-${i}`;
      var monthShowsDuplicateDaysEnd = i == noofMonthView - 1;
      var monthShowsDuplicateDaysStart = i == 0;
        monthList.push(
          <Row key={monthKey} className={`vms_DateRangeCalendar_RowContainer`}>
            <Col>
              <Header
                selectedDate={monthDate}
                view="range"
                isFromMonth={monthShowsDuplicateDaysStart}
                isToMonth={monthShowsDuplicateDaysEnd}
                formatMonthYear="MMMM yyyy"
                next={onNextMonth}
                prev={onPrevMonth}
                startDate={from}
                endDate={to}
                arrowImages={arrowImages}
                maxDate={maxDate}
                minDate={minDate}
              />
              <DateCell
                selectedDate={monthDate}
                renderMonth={monthDate}
                view={view}
                onItemClick={onItemClick}
                showConfirmButton={showConfirmButton}
                minDate={minDate}
                maxDate={maxDate}
                from={fromDate}
                to={toDate}
                mode={mode}
                currentSelection={currentSelection}
                disabledColor={disabledColor}
                showDisabled={showDisabled}
                autoFocus={autoFocus}
                autoCurrentSelectedInput={autoCurrentSelectedInput}
                showTodayDate={showTodayDate}
                todaysDateCss={showTodayDate ? todaysDateCss:{}}
                maxBookingDays={maxBookingDays}
                whetherFirstInput={whetherFirstInput}
                disableDateList={disableDateList}
              />
            </Col>
          </Row>
        );
    }
    return monthList;
  };

  return (
    <>
      <Row className={`vms_DateRangeCalendar_RowContainer`}>
        {noofMonthView !== 2 ? (
          <>{renderMonths()}</>
        ) : (
          <>
            <Col className={`vms_DateRangeCalendar_ColContainer`}>
              <Header
                selectedDate={fromMonth}
                view="range"
                isFromMonth
                formatMonthYear="MMMM yyyy"
                next={onNextMonth}
                prev={onPrevMonth}
                startDate={from}
                endDate={to}
                arrowImages={arrowImages}
                maxDate={maxDate}
                minDate={minDate}
              />
              <DateCell
                selectedDate={fromDate}
                renderMonth={fromMonth}
                view={view}
                onItemClick={onItemClick}
                showConfirmButton={showConfirmButton}
                minDate={minDate}
                maxDate={maxDate}
                from={fromDate}
                to={toDate}
                mode={mode}
                currentSelection={currentSelection}
                disabledColor={disabledColor}
                showDisabled={showDisabled}
                autoFocus={autoFocus}
                autoCurrentSelectedInput={autoCurrentSelectedInput}
                showTodayDate={showTodayDate}
                todaysDateCss={showTodayDate ? todaysDateCss:{}}
                maxBookingDays={maxBookingDays}
                whetherFirstInput={whetherFirstInput}
                disableDateList={disableDateList}
              />
            </Col>
            <Col>
              <Header
                selectedDate={toMonth}
                view={"range"}
                formatMonthYear="MMMM yyyy"
                isToMonth
                next={onNextMonth}
                prev={onPrevMonth}
                startDate={from}
                endDate={to}
                arrowImages={arrowImages}
                maxDate={maxDate}
                minDate={minDate}
              />
              <DateCell
                selectedDate={toDate}
                renderMonth={toMonth}
                view={view}
                onItemClick={onItemClick}
                showConfirmButton={showConfirmButton}
                minDate={minDate}
                maxDate={maxDate}
                from={fromDate}
                to={toDate}
                mode={mode}
                currentSelection={currentSelection}
                disabledColor={disabledColor}
                showDisabled={showDisabled}
                autoFocus={autoFocus}
                autoCurrentSelectedInput={autoCurrentSelectedInput}
                showTodayDate={showTodayDate}
                todaysDateCss={showTodayDate ? todaysDateCss:{}}
                maxBookingDays={maxBookingDays}
                whetherFirstInput={whetherFirstInput}
                disableDateList={disableDateList}
              />
            </Col>
          </>
        )}
      </Row>
    </>
  );
};

RangeCell.defaultProps = {
  view: "day",
  noofMonthView: 2,
  showConfirmButton: false,
  minDate: undefined,
  maxDate: undefined,
  fromDate: new Date(),
  toDate: new Date(),
  fromMonth: new Date(),
  toMonth: addMonth(new Date(), 1),
};

RangeCell.propTypes = {
  view: PropTypes.string,
  noofMonthView: PropTypes.number,
  onItemClick: PropTypes.func.isRequired,
  showConfirmButton: PropTypes.bool,
  minDate: PropTypes.instanceOf(Date),
  maxDate: PropTypes.instanceOf(Date),
  fromDate: PropTypes.instanceOf(Date),
  toDate: PropTypes.instanceOf(Date),
  fromMonth: PropTypes.instanceOf(Date),
  toMonth: PropTypes.instanceOf(Date),
};

export { RangeCell };
