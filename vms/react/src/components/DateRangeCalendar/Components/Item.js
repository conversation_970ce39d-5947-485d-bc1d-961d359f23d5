import React from "react";
import styled from "styled-components";
import classNames from "classnames";
import StartDateSvg from "../../../assets/images/DateRangeCalendar/startDate.svg";
import EndDateSvg from "../../../assets/images/DateRangeCalendar/endDate.svg";
import "./style.css";

const ItemContainer = styled.td`
  position: relative;
  overflow: hidden;
  cursor: pointer;
  background: ${({ theme }) => "#fff"};
  /* transition: 0.25s ease-out; */
  border: 0px solid #333;
  padding: 0px;
  height: 30px;
  line-height: 30px;
  border: 2px solid transparent;
  font-size: 12px;
  width: 32px;
  font-weight: 400;
  font-family: "Montserrat", sans-serif;
`;

const Number = styled.div`
  border: ${({disabledCell,isTodayCell,showTodayDate,todaysDateCss,theme }) => (disabledCell ? "" :  showTodayDate &&  isTodayCell ? `${todaysDateCss && todaysDateCss.border ?  todaysDateCss.border  : `2px solid ${theme.palette.primary[theme.mode]}`}` : "")};
  background-color: ${({ disabledCell,isTodayCell,showTodayDate,todaysDateCss }) => (disabledCell ?  "" : showTodayDate && isTodayCell ? `${todaysDateCss && todaysDateCss.backgroundColor ?  todaysDateCss.backgroundColor  : '#fffff'}` : "")};
  height: ${({ disabledCell,isTodayCell,showTodayDate,todaysDateCss }) => (disabledCell ? "" : showTodayDate && isTodayCell ? `${todaysDateCss && todaysDateCss.height ? todaysDateCss.height : '30px'}` : "")};
  border-radius: ${({ disabledCell,isTodayCell,showTodayDate,todaysDateCss }) => ( disabledCell ? "" : showTodayDate && isTodayCell ? `${todaysDateCss && todaysDateCss.borderRadius ? todaysDateCss.borderRadius :  '50%'}` : "")};
  width: ${({ disabledCell,isTodayCell,showTodayDate,todaysDateCss }) => (disabledCell ? "" : showTodayDate && isTodayCell ? `${todaysDateCss && todaysDateCss.width ? todaysDateCss.width : '30px'}` : "")};

  ${ItemContainer}.selected.notrangeview & {
    opacity: 0;
    transition: 0.5s ease-out;
  }

  ${ItemContainer}.selected.between & {
    font-weight: 400;
  }

  ${ItemContainer}.selected & {
    text-align: center;
    font-weight: 600;
    font-size: 12px;
    font-family: ${({ theme }) => theme?.typography?.fontFamily || ""};
  }
  ${ItemContainer}:hover & {
    background: #f1f6de;
    text-align: center;
  }
`;
const SelectedNumber = styled.div`
  ${ItemContainer}.selected.notrangeview & {
    opacity: 0;
    transition: 0.5s ease-out;
  }

  ${ItemContainer}.selected & {
    text-align: center;
    font-weight: 600;
    font-size: 12px;
    font-family: ${({ theme }) => theme?.typography?.fontFamily || ""};
  }
  ${ItemContainer}:hover & {
    background: "";
    text-align: center;
  }
`;

const Bg = styled.div`
  position: absolute;
  top: 0;
  font-weight: 700;
  line-height: 1;
  text-align: center;
  color: ${({ theme }) => "red"};
  width: 100%;
  opacity: 0;
  height: 1em;
  font-size: 2em;
  transition: 0.25s ease-out;

  ${ItemContainer}.disabled & {
    cursor: not-allowed;
  }
`;

const ImageContainer = styled.div`
  text-align: center;
  color: #4d4d4f;
`;

const CenteredImage = styled.div`
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
`;

// With wrapper, target the generated className
const IconWrapperTargetClassname = styled.img``;

const Item = (props) => {
  const {
    value,
    onDateClick,
    showConfirmButton,
    formattedDate,
    selectedCell,
    disabledCell,
    startCell,
    betweenCell,
    endCell,
    selectedSat,
    selectedSun,
    rangeView,
    view,
    sameMonthCell,
    showDisabled,
    todayCell,
    showTodayDate,
    todaysDateCss
  } = props;
  const onDateClickHandler = () => {
    onDateClick(value, showConfirmButton);
  };
  return (
    <ItemContainer
      key={value}
      className={`${classNames({
        disabled: disabledCell,
        selected: selectedCell,
        start: startCell,
        end: endCell,
        between: betweenCell,
        saturday: selectedSat,
        sunday: selectedSun,
        notRangeView: !rangeView,
      })}   vms_DateRangeCalendar_itemMainContainer ${todayCell ? 'vms_DateRangeCalendar_todayDate_Active' : ''}` }
      startCell={startCell}
      endCell={endCell}
      onClick={!disabledCell ? onDateClickHandler : () => {}}
    >
      {!disabledCell && sameMonthCell && selectedCell && (startCell || endCell) ? (
        <ImageContainer className="vms_DateRangeCalendar_SelectedDateImage_Container">
          <img
            src={startCell ? StartDateSvg : EndDateSvg}
            alt="plus"
            className="imgselected"
          />
          <CenteredImage>
            <SelectedNumber
              view={view}
              sameMonthCell={sameMonthCell}
              startCell={startCell}
              endCell={endCell}
              className="vms_DateRangeCalendar_SelectedDate"
            >
              {formattedDate}
            </SelectedNumber>
          </CenteredImage>
        </ImageContainer>
      ) : (
        <>
          <div  className="vms_DateRangeCalendar_Date_Container" style={{ display: `${sameMonthCell ? "" : showDisabled ? '' : "none"}` }}>
            <Number
              view={view}
              sameMonthCell={sameMonthCell}
              startCell={startCell}
              endCell={endCell}
              value={value}
              className="vms_DateRangeCalendar_dateItem"
              isTodayCell={todayCell}
              showTodayDate={showTodayDate}
              todaysDateCss={todaysDateCss}
              disabledCell={disabledCell}
            >
              {formattedDate}
            </Number>
          </div>
        </>
      )}
      {/* 
      <Number view={view} >
        {formattedDate}
      </Number> */}

      {rangeView ? (
        ""
      ) : (
        <Bg className="vms_DateRangeCalendar_Bg">{formattedDate}</Bg>
      )}
    </ItemContainer>
  );
};

export default Item;
