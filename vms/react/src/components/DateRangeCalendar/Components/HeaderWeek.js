import React from "react";
import styled from "styled-components";
import {
  formatWithLocale,
  getStartOfWeek,
  addDay,
} from "../../../hooks/calendarHooks";

const DaysContainer = styled.thead`
  width: 100%;
  margin-top: 18px;
  margin-bottom: 16px;
  padding-left: 10px !important;
  padding-right: 10px !important;
  box-sizing: border-box;
`;

const WeekDayCol = styled.th`
  font-size: 12px;
  line-height: 1.4;
  letter-spacing: normal;
  text-align: center;
  color: #4d4d4f;
  font-weight: 400;
  padding-bottom: 10px;
  font-family: ${({ theme }) => theme?.typography?.fontFamily || ""};
`;

const HeaderWeek = (props) => {
  const days = [];
  const { currentMonth, formatWeek, view } = props;
  switch (view) {
    case "day": {
      const startDate = getStartOfWeek(currentMonth);
      for (let i = 0; i < 7; i += 1) {
        days.push(
          <WeekDayCol className="vms_DateRangeCalendar_WeekdayCol" key={i} colSpan="1">
            {formatWithLocale(addDay(startDate, i), formatWeek)}
          </WeekDayCol>
        );
      }

      return (
        <DaysContainer className="vms_DateRangeCalendar_HeaderweekConatiner">
          <tr className="vms_DateRangeCalendar_WeekdateTr">{days}</tr>
        </DaysContainer>
      );
    }
    default: {
      return "";
    }
  }
};

export default HeaderWeek;
