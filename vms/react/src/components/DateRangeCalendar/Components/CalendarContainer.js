import styled from "styled-components";

const Row = styled.div`
  margin: 0;
  padding: 0;
  flex-grow: 1;
  flex-basis: auto;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  align-items: flex-start;
  justify-content: ${(props) =>
    props.justifyContent ? props.justifyContent : ""};
`;

const Col = styled.div`
  flex-grow: 1;
  flex-basis: auto;
  display: flex;
  /* flex-direction: column; */
  align-items: center;
  justify-content: ${(props) =>
    props.justifyContent ? props.justifyContent : "center"};
  text-align: ${(props) => props.textAlign};
`;

const CalendarContainer = styled.div`
  display: flex;
  width: ${(props) => (props.isMobile ? "100%" : "-webkit-max-content")};
  width: ${(props) => (props.isMobile ? "100%" : "-moz-max-content")};
  width: ${(props) => (props.isMobile ? "100%" : "max-content")};
  border-radius: 3px;
  box-shadow: 0 2px 4px 0rgba (0, 0, 0, 0.12);
  border: ${({ theme, type, isMobile }) =>
    (isMobile ? "none" : `solid 1px ${theme?.palette?.[type]?.main}`) || ""};
  background-color: ${({ theme, type }) =>
    theme?.palette?.[type]?.contrastText || ""};
  height:360px  
`;

const PickerBodyContainer = styled.div`
  display: ${(props) => (props.editting ? "block" : "none")};
  background-color: #fff;
  min-width: 360px;
`;

const PickerContainer = styled.div`
 /* z-index: 101;
  position: relative; */
  background: transparent;
  width: ${(props) => (props.isMobile ? "100%" : "14em")};
`;
const OverlayDiv = styled.div`
  top: 0%;
  left: 0%;
  width: 100%;
  height: 100%;
  position: fixed;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 99;
  display: ${(props) => (props.editting ? "block" : "none")};
`;

const OverlayContainer = styled.div`
  position: relative;
  z-index: ${({isOverlay}) => (isOverlay ? 100 : "")};
`;
const OverlayContainer1 = styled.div`
  ${({ isOverlay }) =>
    isOverlay
      ? `
      display: block;
      position: fixed;
      top: 0;
      left: 0;
      z-index: 100;
      width: 100%;
      height: 100vh;
      background-color: rgba(0,0,0,0.8);
`
      : `display: none;`};
`;
const DateContainer = styled.div`
  display: flex;
`;

export {
  Col,
  Row,
  PickerContainer,
  PickerBodyContainer,
  CalendarContainer,
  OverlayDiv,
  OverlayContainer,
  DateContainer,
  OverlayContainer1
};
