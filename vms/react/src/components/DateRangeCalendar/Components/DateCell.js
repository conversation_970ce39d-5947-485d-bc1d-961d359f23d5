import React from "react";
import styled from "styled-components";
import Item from "./Item";
import HeaderWeek from "./HeaderWeek";
import PropTypes from "prop-types";
import {
  formatWithLocale,
  whetherDisabled,
  whetherSameMonth,
  getStartOfMonth,
  getEndOfMonth,
  getStartOfWeek,
  getEndOfWeek,
  addDay,
  whetherisToday,
  whetherSelected,
  whetherSameDay,
  whetherBetween,
  whetherSat,
  whetherSun,
  whetherBefore,
  differenceCalendarDays
} from "../../../hooks/calendarHooks";

const DateTr = styled.tr`
  .selected {
    border-left: 5px solid transparent;
  }
  .disabled {
    color: #d1d3d4 !important;
    cursor: not-allowed;
    pointer-events: none;
  }
`;

const DateTable = styled.table`
  width: 300px;
  margin: 10px 17px 13px;
  padding: 0;
  border-spacing: 0px;
`;

const DateTb = styled.tbody``;

const RangeTr = styled.tr`
  .disabled {
    cursor: not-allowed;
    color: ${(props) => props.disabledColor || "#d1d3d4"} !important;
    pointer-events: none;
  }
  .selected {
    color: #4d4d4f;
    font-weight: bold !important;
    background-color: #ddeede !important;
  }
  .saturday {
    color: #4d4d4f;
    background-color: #ddeede !important;
  }
  .sunday {
    color: #4d4d4f;
    background-color: #ddeede !important;
  }
  .start {
    color: #4d4d4f;
    background-color: #fff !important;
  }
  .between {
    color: #4d4d4f;
    background-color: #ddeede !important;
  }
  .end {
    color: #4d4d4f;
    background-color: #fff !important;
  }
`;

const DateCell = ({
  selectedDate,
  view,
  onItemClick,
  showConfirmButton,
  minDate,
  maxDate,
  isMobile,
  isDateChange,
  newSelectedDate,
  renderMonth,
  from,
  to,
  mode,
  currentSelection,
  disabledColor,
  showDisabled,
  autoFocus,
  autoCurrentSelectedInput,
  showTodayDate,
  todaysDateCss,
  maxBookingDays,
  whetherFirstInput,
  disableDateList,
}) => {
  let cloneDate;
  let formattedDate;
  let itemPerRow;
  switch (view) {
    case "range": {
      const monthStart = getStartOfMonth(renderMonth);
      const monthEnd = getEndOfMonth(monthStart);
      const startDate = getStartOfWeek(monthStart);
      const endDate = getEndOfWeek(monthEnd);
      
      // Add validation for fromDate
      const checkSingleSelectionEndDate = mode == "singleDateRange" && currentSelection == "endDate" && from && from instanceof Date && !isNaN(from.getTime()) ? from : null;
      const userMinDate = minDate ? minDate : checkSingleSelectionEndDate == null ? new Date() : checkSingleSelectionEndDate;
      const userMaxDate = maxDate ? maxDate : new Date().setFullYear(new Date().getFullYear() + 1);
      const rows = [];
      let row = [];
      let i;

      cloneDate = startDate;
      itemPerRow = 7;
      while (cloneDate <= endDate) {
        for (i = 0; i < itemPerRow; i += 1) {
          formattedDate = formatWithLocale(cloneDate, "d");
          const disabled = whetherDisabled(
            cloneDate,
            monthStart,
            userMinDate,
            userMaxDate,
            mode,
            currentSelection,
            checkSingleSelectionEndDate,
            maxBookingDays,
            disableDateList,
            showTodayDate,
          );
          
          // Add validation for date calculations
          const validFromDate = from && from instanceof Date && !isNaN(from.getTime());
          const isMaxBookingDays = validFromDate ? differenceCalendarDays(cloneDate, addDay(from, 1)) : 0;
    
          const selected =
            !disabled && whetherSelected(cloneDate, selectedDate, from, to);
          const startCell = selected && whetherSameDay(cloneDate, from);
          const between = selected && whetherBetween(cloneDate, from, to);
          const endCell = selected && whetherSameDay(cloneDate, to);
          const selectedSat = selected && whetherSat(cloneDate);
          const selectedSun = selected && whetherSun(cloneDate);
          const sameMonth =
            whetherBefore(cloneDate, monthEnd) &&
            whetherSameMonth(cloneDate, monthStart);
          const isTodayCell = whetherisToday(cloneDate)
          row.push(
            <Item
              key={cloneDate}
              value={cloneDate}
              formattedDate={formattedDate}
              onDateClick={onItemClick}
              showConfirmButton={showConfirmButton}
              disabledCell={autoFocus ? ((autoCurrentSelectedInput === 'endDate' && validFromDate && isMaxBookingDays >= maxBookingDays) ? true : (autoCurrentSelectedInput === 'endDate' && validFromDate && whetherBefore(cloneDate,from))  ?  true :  disabled):((!whetherFirstInput && validFromDate && isMaxBookingDays >= maxBookingDays) ? true : disabled)}
              selectedCell={selected}
              startCell={startCell}
              betweenCell={between}
              endCell={endCell}
              selectedSat={selectedSat}
              selectedSun={selectedSun}
              sameMonthCell={sameMonth}
              showDisabled={showDisabled}
              rangeView
              todayCell={showTodayDate && isTodayCell}
              showTodayDate={showTodayDate}
              todaysDateCss={showTodayDate ? todaysDateCss:{}}
            />
          );
          cloneDate = addDay(cloneDate, 1);
        }
        rows.push(<RangeTr className="vms_DateRangeCalendar_dateTableContainer_row" key={cloneDate} disabledColor={disabledColor}>{row}</RangeTr>);
        row = [];
      }
      return (
        <DateTable className="vms_DateRangeCalendar_dateTableContainer">
          <HeaderWeek
            currentMonth={selectedDate}
            formatWeek="eeeee"
            view="day"
          />
          <DateTb className="vms_DateRangeCalendar_dateTableContainer_body">{rows}</DateTb>
        </DateTable>
      );
    }

    case "day": {
      const monthStart = getStartOfMonth(selectedDate);
      const monthEnd = getEndOfMonth(monthStart);
      const startDate = getStartOfWeek(monthStart);
      const endDate = getEndOfWeek(monthEnd);
      const rows = [];
      let row = [];
      let i;

      cloneDate = startDate;
      itemPerRow = 7;
      while (cloneDate <= endDate) {
        for (i = 0; i < itemPerRow; i += 1) {
          formattedDate = formatWithLocale(cloneDate, "d");
          const disabled = whetherDisabled(
            cloneDate,
            monthStart,
            minDate,
            maxDate,
            null,
            null,
            null,
            null,
            disableDateList,
            showTodayDate
          );
          const checkTodayDate = isDateChange
            ? formatWithLocale(cloneDate, "dd.MM.yyyy") ==
              formatWithLocale(newSelectedDate, "dd.MM.yyyy")
              ? true
              : false
            : whetherisToday(cloneDate);

          const selected = !disabled && checkTodayDate;
          const isTodayCell = whetherisToday(cloneDate)

          row.push(
            <Item
              key={cloneDate}
              value={cloneDate}
              formattedDate={formattedDate}
              onDateClick={(value) => onItemClick(value, true)}
              showConfirmButton={showConfirmButton}
              disabledCell={disabled}
              selectedCell={selected}
              view={view}
              showDisabled={showDisabled}
              todayCell={showTodayDate && isTodayCell}
              showTodayDate={showTodayDate}
              todaysDateCss={showTodayDate ? todaysDateCss:{}}
            />
          );

          cloneDate = addDay(cloneDate, 1);
        }
        rows.push(<DateTr className="vms_DateRangeCalendar_dateTableContainer_row" key={cloneDate} disabledColor={disabledColor}>{row}</DateTr>);
        row = [];
      }

      return (
        <>
          <DateTable className="vms_DateRangeCalendar_dateTableContainer" isMobile={isMobile}>
            <HeaderWeek
              currentMonth={selectedDate}
              formatWeek="eeeee"
              view="day"
            />
            <DateTb className="vms_DateRangeCalendar_dateTableContainer_body">{rows}</DateTb>
          </DateTable>
        </>
      );
    }
    default: {
      return undefined;
    }
  }
};

DateCell.defaultProps = {
  onScroll: () => {},
};

DateCell.propTypes = {
  onScroll: PropTypes.func,
  isDateChange: PropTypes.bool,
};

export { DateCell };
