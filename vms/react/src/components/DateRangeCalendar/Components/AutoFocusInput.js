import React from "react";
import styled from "styled-components";
import { formatWithLocale } from "../../../hooks/calendarHooks";
import ArrowUpSvg from "../../../assets/images/DateRangeCalendar/rangeArrowup.svg";
import ArrowDownSvg from "../../../assets/images/DateRangeCalendar/rangeArrowdown.svg";
import PropTypes from "prop-types";

function border(isMobile, editting, theme, type) {
  if (isMobile) {
    return "none";
  } else {
    return editting
      ? `solid 1px ${theme?.palette?.[type]?.main}`
      : "solid 1px #4d4d4f";
  }
}

function borderBottom(isMobile, theme, type) {
  if (isMobile) {
    return `1px solid ${theme?.palette?.[type]?.main}`;
  } else {
    return "1px solid #4d4d4f";
  }
}

const DateInputContainer = styled.div`
  position: relative;
  min-width: ${(props) => (props.isMobile ? "100%" :  props.autoInputFocusData && props.autoInputFocusData.inputWidth ? props.autoInputFocusData.inputWidth : "290px")};
  width: ${(props) => (props.isMobile ? "100%" : props.autoInputFocusData && props.autoInputFocusData.inputWidth ? props.autoInputFocusData.inputWidth : "290px")};
  --widthA: calc(100% - 28px);
  --widthB: calc(var(--widthA) / 7);
`;

const Label = styled.label`
  font-weight: 400;
  font-family: ${({ theme }) => theme?.typography?.fontFamily || ""};
  font-size: ${({ theme }) => theme?.typography?.text?.fontSize || ""};
  font-stretch: normal;
  font-style: normal;
  line-height: 20px;
  letter-spacing: 0.3px;
  color: #4d4d4f;
  display: block;
  position: relative;
  text-transform: uppercase;
`;

const HightLight = styled.span`
  position: absolute;
  height: 60%;
  width: 100%;
  top: 25%;
  left: 0;
  pointer-events: none;
  opacity: 0.5;
`;

const Bar = styled.span`
  position: relative;
  display: block;
  width: 100%;
  &:before,
  &:after {
    content: "";
    height: 48px;
    width: ${(props) => (props.editting ? "50%" : "0")};
    bottom: 1px;
    position: absolute;
    background-color: rgb(255, 255, 255);
  }
  &:before {
    left: 50%;
  }
  &:after {
    right: 50%;
  }
`;

const InputContainer = styled.input`
  display: block;
  box-sizing: border-box;
  font-weight: 700;
  font-family: ${({ theme }) => theme?.typography?.fontFamily || ""};
  font-size: ${({ theme }) => `${theme?.typography?.text?.fontSize}` || ""};
  font-stretch: normal;
  font-style: normal;
  line-height: 1.43;
  width: 100%;
  border-radius: 3px;
  letter-spacing: 0.3px;
  color: #4d4d4f;
  padding: 14px 16px;
  margin-top: 12px;
  text-overflow: ellipsis;
  height: 48px;
  position: relative;
  border: ${({ theme, isMobile, editting, calendarType }) =>
    border(isMobile, editting ? true : false, theme, calendarType)};
  background-color: #fff;

  &::placeholder {
    font-weight: 700;
    font-family: ${({ theme }) => theme?.typography?.fontFamily || ""};
    font-size: ${({ theme }) => `${theme?.typography?.text?.fontSize}` || ""};
    font-stretch: normal;
    font-style: normal;
    line-height: 1.43;
    border-radius: 3px;
    letter-spacing: 0.3px;
    color: #4d4d4f;
  }

  &:focus {
    outline: none;
    height: 48px;
    border-radius: 3px;
    border: ${({ theme, isMobile, editting, calendarType }) =>
      border(isMobile, editting ? true : false, theme, calendarType)};
    background-color: #fff;
  }
`;

const IconContainer = styled.div`
  display: inline-block;
  background-size: 20px 18px !important;
  background-repeat: no-repeat !important;
  background-position: center !important;
  width: 24px;
  height: 24px;
  text-decoration: none;
  outline: none;
  position: absolute;
  bottom: 12px;
  right: 15px;
`;

const MainContainer = styled.div`
 display: flex;
 align-items: center;
 gap: 10px;
`;

const AutoFocusInput = (props) => {
  const {
    inputClick,
    editting,
    fromDate,
    toDate,
    formatDateInput,
    inputBoxLabel,
    mode,
    selectedDate,
    currentSelection,
    selectDateChange,
    customInputView,
    calendarType,
    autoInputFocusData,
    autoCurrentSelectedInput,
    showEndDate,
    altText,
  } = props;
  const FormattedFromDate = fromDate
    ? formatWithLocale(fromDate, formatDateInput)
    : "";
  const FormattedToDate = toDate
    ? formatWithLocale(toDate, formatDateInput)
    : "";
  const FormattedSelectedDate = selectedDate
    ? formatWithLocale(selectedDate, formatDateInput)
    : "";
  return (
    <>
      {customInputView ? (
        <>{customInputView}</>
      ) : (
        <MainContainer>
          <div style={{padding:"20px"}}>
          <DateInputContainer className={`vms_DateRangeCalendar_InputContainer`} {...props}>
          { autoInputFocusData &&  autoInputFocusData.leftInputViewLabel ? <Label className={`vms_DateRangeCalendar_Input_lable`}>{autoInputFocusData.leftInputViewLabel}</Label> : ""}
          <InputContainer
            type="date-text"
            editting={editting}
            value={
              FormattedFromDate
            }
            onClick={(e) => inputClick(e,"startDate")}
            readOnly
            isMobile={props.isMobile}
            placeholder={
              selectDateChange
                ? FormattedFromDate
                : autoCurrentSelectedInput == "endDate" &&
                  mode === "singleDateRange" &&
                  "Select Placeholder"
            }
            className={`vms_DateRangeCalendar_Input`}
            calendarType={calendarType}
          />

          <IconContainer className={`vms_DateRangeCalendar_IconCotainer`}>
            <img src={editting ? ArrowUpSvg : ArrowDownSvg} alt={altText || "plus"} />
          </IconContainer>
        </DateInputContainer>
          </div>
      

            <div style={{padding:"20px"}}>
            <DateInputContainer className={`vms_DateRangeCalendar_InputContainer`} {...props}>
          { autoInputFocusData &&  autoInputFocusData.rightInputViewLable ? <Label className={`vms_DateRangeCalendar_Input_lable`}>{autoInputFocusData.rightInputViewLable}</Label> : ""}
          <InputContainer
            type="date-text"
            editting={editting}
            value={
              FormattedToDate &&  mode === "singleDateRange" && autoCurrentSelectedInput == "endDate"
              ? FormattedToDate
              : ""
            }
            onClick={(e) => inputClick(e,'endDate')}
            readOnly
            isMobile={props.isMobile}
            placeholder={
              // selectDateChange
              //   ? FormattedSelectedDate
              //   : autoCurrentSelectedInput == "endDate" &&
              //     mode === "singleDateRange" &&
              //     "Select Placeholder"
                  selectDateChange
                  ? FormattedToDate
                  : showEndDate ?  FormattedToDate : currentSelection == "endDate" &&
                    mode === "singleDateRange" &&
                    "Select Placeholder"
            }
            className={`vms_DateRangeCalendar_Input`}
            calendarType={calendarType}
          />

          <IconContainer className={`vms_DateRangeCalendar_IconCotainer`}>
            <img src={editting ? ArrowUpSvg : ArrowDownSvg} alt={altText || "plus"} />
          </IconContainer>
        </DateInputContainer>
            </div>
      
        </MainContainer>
      )}
    </>
  );
};

AutoFocusInput.defaultProps = {
  inputClick: () => {},
};

AutoFocusInput.propTypes = {
  inputClick: PropTypes.func,
  placeholder: PropTypes.string,
};

export { AutoFocusInput };
