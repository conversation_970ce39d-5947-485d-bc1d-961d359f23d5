import React, { useState } from "react";
import { addDay, getNextYear } from "../../hooks/calendarHooks";

import { DateRangeCalendar } from "./DateRangeCalendar";
import { addDays } from "date-fns";

export default {
  title: "VMS_REACT/DateRangeCalendar",
  component: DateRangeCalendar,
  argTypes: {
    id: { control: { type: "" } },
    additionalStyle: { control: { type: "" } },
    additionalClassName: { control: { type: "" } },
    dateDisplayFormat: { control: { type: "" } },
    startDate: { control: { type: "" } },
    endDate: { control: { type: "" } },
    customInputView: { control: { type: "" } },
    arrowImages: { control: { type: "" } },
    minDate: { control: { type: "" } },
    maxDate: { control: { type: "" } },
    showTodayDate: { control: { type: "" } },
    altText : { control: { type: "" } },
  },
};


const today = new Date();
let tomorrow = new Date();
tomorrow.setDate(new Date().getDate() + 1);

const DisplayCurrentState = (fromDate, toDate, result, mode) => {
  const isValid = result?.calendarState
    ? result?.calendarState?.valid.toString()
    : result?.valid.toString();
  return (
    <div style={{ marginTop: "10px" }}>
      <h5>Callback Value</h5>
      <ul style={{ paddingLeft: "15px", paddingTop: "1px" }}>
        <li>Start Date : {fromDate.toString()}</li>
        <li>End Date :{toDate.toString()}</li>
        <li>Valid :{isValid}</li>
        {isValid === "false" && (
          <>
            <li>
              ErrorCode :
              {result?.calendarState
                ? result?.calendarState?.errorCode
                : result?.errorCode}
            </li>
          </>
        )}
      </ul>
    </div>
  );
};

const Template = (args) => {
  const [startDate, setStartDate] = useState(new Date());
  const [endDate, setEndDate] = useState(addDay(new Date(), 0));
  const [result, setResult] = useState();
  return (
    <>
      <div>
        <DateRangeCalendar
         {...args}
        startDate={startDate}
        endDate={endDate}
        minDate={startDate}
        showTodayDate={true}
        onDateSelectionChange={(resul) => {
            console.log("resonDateSelectionChange",resul)
            setStartDate(resul?.startDate);
            setEndDate(resul?.endDate);
            setResult(resul);
          }}
          altText={"altText"}
        
        />
      </div>
      <>{result ? DisplayCurrentState(startDate, endDate, result) : ""} </>
    </>
  );
};

const customInputView = (args) => {
  const [startDate, setStartDate] = useState(today);
  const [endDate, setEndDate] = useState(tomorrow);
  const [result, setResult] = useState();
  return (
    <>
      <div style={{ display: "flex", flex: 1 }}>
        <DateRangeCalendar
          inputBoxLabel="Start Date"
          mode="singleDateRange"
          currentSelection="startDate"
          startDate={startDate}
          endDate={endDate}
          onSingleDateRangeSelectionChange={(resul) => {
            setStartDate(resul.date);
            setResult(resul);
          }}
          altText={"altText"}
          {...args}
        />

        <div style={{ marginLeft: "100px" }}>
          <DateRangeCalendar
            inputBoxLabel="End Date"
            mode="singleDateRange"
            currentSelection="endDate"
            startDate={startDate}
            endDate={endDate}
            onSingleDateRangeSelectionChange={(resul) => {
              setEndDate(resul.date);
              setResult(resul);
            }}
            altText={"altText"}
            {...args}
          />
        </div>
      </div>
      <>{result ? DisplayCurrentState(startDate, endDate, result) : ""} </>
    </>
  );
};

const CustomInput = () => {
  return (
    <div>
      <div>Start Date</div>
      <input placeholder={"Select Date"} id={id} />
    </div>
  );
};

export const Default = Template.bind({});
Default.args = {
  isOverlay: false,
  noofMonthView: 2,
  openCalendar: false,
  inputBoxLabel: "",
  inputPlaceholder: "",
  onInputClick: (e) => {
    console.log("Input clicked", e);
  }
};

export const SpecificDisabledDates = Template.bind({});
SpecificDisabledDates.args = {
  isOverlay: false,
  noofMonthView: 2,
  openCalendar: false,
  inputBoxLabel: "",
  inputPlaceholder: "",
  disableDateList:["20/09/2025", "22/09/2025", "25/09/2025", "27/10/2024", "20/10/2024", "22/10/2024", "25/10/2024", "27/11/2024"]
};

export const oneMonthView = Template.bind({});
oneMonthView.storyName = "1 Month view";

oneMonthView.args = {
  isOverlay: false,
  startDate: new Date(),
  endDate: addDay(new Date(), 1),
  noofMonthView: 1,
  openCalendar: false,
  inputBoxLabel: "",
  inputPlaceholder: "",
};

export const threeMonthView = Template.bind({});
threeMonthView.storyName = "3 Month view";

threeMonthView.args = {
  isOverlay: false,
  startDate: new Date(),
  endDate: addDay(new Date(), 1),
  noofMonthView: 3,
  openCalendar: false,
  inputBoxLabel: "",
  inputPlaceholder: "",
};

export const WithOverlay = Template.bind({});
WithOverlay.storyName = "Wrap with Overlay";
WithOverlay.args = {
  isOverlay: true,
  startDate: new Date(),
  endDate: addDay(new Date(), 1),
  openCalendar: false,
  inputBoxLabel: "",
  inputPlaceholder: "",
};

export const MutipleCalendar = customInputView.bind({});
MutipleCalendar.storyName = "Multiple Calendar";
MutipleCalendar.args = {
  isOverlay: false,
  startDate: new Date(),
  endDate: addDay(new Date(), 1),
  openCalendar: false,
};

export const CustomDateFormat = Template.bind({});
CustomDateFormat.storyName = "Custom Date format";
CustomDateFormat.args = {
  startDate: new Date(),
  endDate: addDay(new Date(), 1),
  dateDisplayFormat: "MMM dd yyyy",
};

export const customArrowImages = Template.bind({});
customArrowImages.args = {
  startDate: new Date(),
  endDate: addDay(new Date(), 1),
  arrowImages: {
    prevArrowImage: "https://img.icons8.com/pastel-glyph/24/000000/minus.png",
    nextArrowImage: "https://img.icons8.com/pastel-glyph/24/000000/plus.png",
    prevArrowImageAlt: "prev",
    nextArrowImageAlt: "next",
  },
};

export const openCalendarByApplication = Template.bind({});
openCalendarByApplication.args = {
  startDate: new Date(),
  endDate: addDay(new Date(), 1),
  openCalendar: true,
};

export const selectedRangePassByApplication = Template.bind({});
selectedRangePassByApplication.args = {
  startDate: addDay(new Date(), 50),
  endDate: addDay(new Date(), 60),
  openCalendar: false,
};

export const minAndMaxBookingDays = Template.bind({});
minAndMaxBookingDays.args = {
  startDate: new Date(),
  endDate: addDay(new Date(), 1),
  minBookingDays: 3,
  maxBookingDays: 30,
  openCalendar: false,
};

export const hideMonth = Template.bind({});
hideMonth.args = {
  hideHeader:false,
  disabledColor:"blue",
  isOverlay: false,
  startDate: new Date(),
  endDate: addDay(new Date(), 1),
  noofMonthView: 2,
  openCalendar: false,
  inputBoxLabel: "",
  inputPlaceholder: "",
};


export const autoFocusCalendar = Template.bind({});
autoFocusCalendar.args = {
  autoFocus:true,
  hideHeader:true,
  isOverlay: false,
  startDate: new Date(),
  endDate: addDay(new Date(), 1),
  noofMonthView: 2,
  openCalendar: false,
  inputBoxLabel: "sdsd",
  inputPlaceholder: "",
  mode:"singleDateRange",
  autoInputFocusData:{
    leftInputViewLabel:"Check-In",
    leftInputViewValue:new Date(),
    rightInputViewLable:"Check-Out",
    rightInputViewValue:addDays(new Date(), 1),
    // inputWidth:'100px'
  },
  showEndDate:true,
  showdisabled:true,
  // showTodayDate:true
};