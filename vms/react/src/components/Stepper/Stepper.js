import React, { Fragment, useEffect, useState, useCallback, useMemo } from "react";
import PropTypes from "prop-types";
import {
  MinusButton,
  StyledWrapper,
  PlusButton,
  StepperText,
  Svg,
} from "./Stepper.styled";
import { ThemeWrapper } from "../Theme/ThemeContext";

const isNonNull = (prop) => {
  return prop !== null && prop !== undefined;
};

const nullCheck = (obj) => {
  return obj !== null && obj !== undefined;
};

const MinusImage = React.memo(({ disabled, images, className }) => {
  const hasCustomImages = useMemo(() => 
    nullCheck(images) &&
    nullCheck(images?.minusDisabledImage) &&
    nullCheck(images?.minusImage),
    [images]
  );

  const imageSrc = useMemo(() => {
    if (!hasCustomImages) return null;
    return disabled ? images.minusDisabledImage : images.minusImage;
  }, [disabled, images, hasCustomImages]);

  if (hasCustomImages) {
    return <img src={imageSrc} alt="minus" className={className} />;
  }

  return (
    <Svg className={className}>
      <g fill="none" fillRule="evenodd">
        <path d="M0 0H24V24H0z" transform="translate(-3 -3)" />
        <g transform="translate(-3 -3) translate(4 4)">
          <path
            stroke="currentColor"
            strokeLinecap="round"
            d="M.058 7.028C.02 7.347 0 7.671 0 8c0 4.418 3.582 8 8 8s8-3.582 8-8-3.582-8-8-8C5.799 0 3.805.889 2.359 2.328"
          />
          <path
            stroke="currentColor"
            strokeLinecap="round"
            d="M0.5 0.5L7.5 0.5"
            transform="translate(4.007 7.509)"
          />
          <circle cx=".806" cy="4.505" r="1" fill="currentColor" />
        </g>
      </g>
    </Svg>
  );
});

MinusImage.displayName = 'MinusImage';

const PlusImage = React.memo(({ disabled, images, className }) => {
  const hasCustomImages = useMemo(() => 
    nullCheck(images) &&
    nullCheck(images?.plusDisabledImage) &&
    nullCheck(images?.plusImage),
    [images]
  );

  const imageSrc = useMemo(() => {
    if (!hasCustomImages) return null;
    return disabled ? images.plusDisabledImage : images.plusImage;
  }, [disabled, images, hasCustomImages]);

  if (hasCustomImages) {
    return <img src={imageSrc} alt="plus" className={className} />;
  }

  return (
    <Svg className={className}>
      <g fill="none" fillRule="evenodd" transform="translate(1 1)">
        <path
          stroke="currentColor"
          strokeLinecap="round"
          d="M8 4L8 13M3.5 8.5L12.5 8.5"
        />
        <path
          stroke="currentColor"
          strokeLinecap="round"
          d="M.058 7.028C.02 7.347 0 7.671 0 8c0 4.418 3.582 8 8 8s8-3.582 8-8-3.582-8-8-8C5.799 0 3.805.889 2.359 2.328"
        />
        <circle cx=".806" cy="4.505" r="1" fill="currentColor" />
      </g>
    </Svg>
  );
});

PlusImage.displayName = 'PlusImage';

const Stepper = React.memo((props) => {
  const {
    initialValue,
    minValue,
    step,
    maxValue,
    onValueChange,
    images,
    additionalStyle,
    customInputView,
    value,
    id,
    additionalClassName,
    className,
    isMobile,
    isMobileView
  } = props;
  
  // Memoize step value
  const stepValue = useMemo(() => step || 1, [step]);
  
  // Initialize with value if provided, otherwise use initialValue
  const [stepperValue, setStepperValue] = useState(() => 
    value !== null && value !== undefined ? value : initialValue
  );
  
  // Sync with external value changes
  useEffect(() => {
    if (isNonNull(value)) {
      setStepperValue(value);
    }
  }, [value]);
  
  // Memoize button disabled states
  const buttonStates = useMemo(() => {
    const isMinusDisabled = (stepperValue - stepValue) < minValue;
    const isPlusDisabled = maxValue !== null && (stepperValue + stepValue) > maxValue;
    
    return { isMinusDisabled, isPlusDisabled };
  }, [stepperValue, stepValue, minValue, maxValue]);
  
  // Memoize click handler
  const onStepperClickHandler = useCallback((stepperType) => {
    let finalValue = null;
    
    if (stepperType === "minus") {
      const updatedValue = stepperValue - stepValue;
      if (updatedValue >= minValue) {
        finalValue = updatedValue;
      }
    } else if (stepperType === "plus") {
      const updatedValue = stepperValue + stepValue;
      if (maxValue !== null && updatedValue <= maxValue) {
        finalValue = updatedValue;
      } else if (maxValue === null) {
        finalValue = updatedValue;
      }
    }
    
    if (finalValue !== null) {
      setStepperValue(finalValue);
      if (onValueChange && typeof onValueChange === 'function') {
        onValueChange(finalValue);
      }
    }
  }, [stepperValue, stepValue, minValue, maxValue, onValueChange]);
  
  // Memoize minus button click handler
  const handleMinusClick = useCallback((e) => {
    e.preventDefault();
    e.stopPropagation();
    onStepperClickHandler("minus");
  }, [onStepperClickHandler]);
  
  // Memoize plus button click handler
  const handlePlusClick = useCallback((e) => {
    e.preventDefault();
    e.stopPropagation();
    onStepperClickHandler("plus");
  }, [onStepperClickHandler]);
  
  // Memoize className calculation
  const computedClassName = useMemo(() => {
    let cssClasses = [];
    if (
      additionalClassName !== null &&
      additionalClassName !== undefined &&
      additionalClassName?.length > 0
    ) {
      cssClasses = cssClasses.concat(additionalClassName);
    }

    if (cssClasses.length > 0) {
      return `${cssClasses.join(" ")}`;
    }
    return className === undefined || null ? "" : className;
  }, [additionalClassName, className]);
  
  // Memoize mobile detection
  const isMobileDevice = useMemo(() => 
    isMobile || isMobileView || false, 
    [isMobile, isMobileView]
  );
  
  // Memoize button IDs
  const buttonIds = useMemo(() => ({
    minus: id ? `${id}-minus` : null,
    plus: id ? `${id}-plus` : null
  }), [id]);
  
  return (
    <ThemeWrapper isMobile={isMobileDevice}>
      <StyledWrapper
        className={`${computedClassName} vms_stepper_container`}
        style={additionalStyle}
      >
        <MinusButton
          id={buttonIds.minus}
          className="vms_stepper_minusbutton"
          disabled={buttonStates.isMinusDisabled}
          onClick={handleMinusClick}
          aria-label="Decrease value"
        >
          <MinusImage
            className="vms_stepper_minusimg"
            images={images}
            disabled={buttonStates.isMinusDisabled}
          />
        </MinusButton>
        
        {isNonNull(customInputView) ? (
          customInputView
        ) : (
          <StepperText className="vms_stepper_text" aria-live="polite">
            {stepperValue}
          </StepperText>
        )}

        <PlusButton
          id={buttonIds.plus}
          className="vms_stepper_plusbutton"
          disabled={buttonStates.isPlusDisabled}
          onClick={handlePlusClick}
          aria-label="Increase value"
        >
          <PlusImage
            className="vms_stepper_plusimg"
            images={images}
            disabled={buttonStates.isPlusDisabled}
          />
        </PlusButton>
      </StyledWrapper>
    </ThemeWrapper>
  );
});

Stepper.displayName = 'Stepper';

Stepper.defaultProps = {
  minValue: 0,
  initialValue: 0,
  step: 1,
  maxValue: null,
  onValueChange: null,
  images: null,
  customInputView: null,
  value: null
};

Stepper.propTypes = {
  /**
   * Images for the icon button
   */
  images: PropTypes.shape({
    plusImage: PropTypes.string,
    minusImage: PropTypes.string,
    plusDisabledImage: PropTypes.string,
    minusDisabledImage: PropTypes.string,
  }),

  id:PropTypes.string,
  /**
   * Minimum value of stepper. Default value `0`.
   */
  minValue: PropTypes.number,
  /**
   * Maximum value of stepper. Optional property
   */
  maxValue: PropTypes.number,
  /**
   * Initial value of stepper. Default value `0`.
   */
  initialValue: PropTypes.number,
  /**
   * Step value. On click of image this value will be added/subtract from current value. Default value `1`.
   */
  step: PropTypes.number,
  /**
   * It will called when user click on plus/minus icon and will return current state value.
   */
  onValueChange: PropTypes.func,
  /**
   * Additional style for counter text value
   */
  counterTextStyle: PropTypes.object,
  /**
   * Prefix to apply with counter
   */
  counterPrefix: PropTypes.string,
  /**
   * Suffix to apply with counter
   */
  counterSuffix: PropTypes.string,

  /**
   * Property to append additional css class
   */
  additionalClassName: PropTypes.arrayOf(PropTypes.string),

  /**
   * Property to set the `inline style` object
   */
  additionalStyle: PropTypes.object,
  /**
   * To display customview between the two stepper buttons
   */
  customInputView: PropTypes.node,
  /**
   * Active value of stepper which user can set from application
   */
  value: PropTypes.number
};

export { Stepper };
