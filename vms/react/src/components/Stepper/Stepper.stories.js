import React, { useState } from "react";

import { Stepper } from "./Stepper";

export default {
  title: "VMS_REACT/Stepper",
  component: Stepper,
  argTypes: {
    additionalStyle: { control: { type: "" } },
    additionalClassName: { control: { type: "" } },
  },
};

const Template = (args) => <Stepper  {...args} />;

export const Default = Template.bind({});
Default.args = {
  initialValue: 0,
  minValue: 0,
  maxValue: 9,
  id:"stepper-id"
};
export const CustomInputView = () => {
  const minValue = 0;
  const maxValue = 100;
  const [stepperValue, setStepperValue] = useState(0);
  return (
    <>
      <Stepper
      id="custom-stp-id"
        initialValue={stepperValue}
        minValue={minValue}
        maxValue={maxValue}
        onValueChange={(value) => {
          console.log();
          setStepperValue(value);
        }}
        value={stepperValue}
        customInputView={
          <div style={{ marginRight: "10px", marginLeft: "10px" }}>
            <input
              type={"text"}
              value={stepperValue}
              onChange={(e) => {
                const value = e.target.value;
                if (value.trim().length > 0 && value <= maxValue && value >=minValue) {
                  setStepperValue(Number(value.trim()));
                } else if (value.trim().length == 0) {
                  setStepperValue(value);
                }
              }}
            />
          </div>
        }
      />
      <p>{`Stepper value ${stepperValue}`}</p>
    </>
  );
};
