import styled from "styled-components";

export const StyledWrapper = styled.div`
  display: flex;
`;

export const MinusButton = styled.button`
  padding: 0px 3px;
  margin: 0px;
  border-width: 0px;
  height: 24px;
  width: 24px;
  opacity: ${({ disabled }) => (disabled ? `0.3` : `transparent`)};
  background-color: #ffffff;
`;
export const Svg = styled.svg`
  color: ${({ theme }) => theme.palette.primary.main};
  width: 24px;
  height: 24px;
  cursor: pointer;
`;

export const StepperText = styled.span`
  font-weight: 700;
  font-size: 14px;
  line-height: 20px;
  letter-spacing: 0.3px;
  //padding: 0px 20px;
  width:40px;
  text-align:center;
`;

export const PlusButton = styled.button`
  border-radius: 50%;
  padding: 0px;
  margin: 0px;
  height: 24px;
  width: 24px;
  border-width: 0px;
  background-color: transparent;
  opacity: ${({ disabled }) => (disabled ? `0.3` : `transparent`)};
`;
