import styled, {keyframes, css} from "styled-components";

const getModeColor = (mode) => {
  switch (mode) {
    case "error":
      return "#FDECEC";
    case "alert":
      return "#FCF8E3";
    case "information":
      return "#E8F5FC";
    case "success":
      return "#DDEEDD";
    default:
      return null;
  }
};




export const toastAnimationRight = keyframes`
0%{
  transform:translate(100%);
  opacity:0;
}
100%{
  transform:translate(0%);
  opacity:1;
}
`;
export const toastAnimationLeft = keyframes`
0%{
  transform:translateX(-100%);
  opacity:0;
}
100%{
  transform:translateX(0%);
  opacity:1;
}
`;
export const toastAnimationRightClose = keyframes`
0%{
  transform:translate(0%);
  opacity:0;
}
100%{
  transform:translate(100%);
  opacity:1;
}
`;
export const toastAnimationLeftClose = keyframes`
0%{
  transform:translateX(0%);
  opacity:0;
}
100%{
  transform:translateX(-100%);
  opacity:1;
}
`;

const getDirAnimation = (dir, showToast) => {
  console.log("showToast**", showToast, dir);
  // debugger;
  if(!showToast){
    if(dir === "left"){
      return toastAnimationLeftClose 
     }
     if(dir === "right"){
      return toastAnimationRightClose
     }
  }else{
    if(dir === "left"){
      return toastAnimationLeft 
     }
     if(dir === "right"){
      return toastAnimationRight
     }
  }
  
}

export const ToastMessageWrapper = styled.div`
position: ${({ dir }) => dir ? "absolute" : "unset"};
right: ${({ dir }) => dir=="right" ? "20px" : ""};
left: ${({ dir }) => dir=="left" ? "20px" : ""};
animation: ${({dir, showToast, autoClose}) => (dir == "left" || dir == "right" && autoClose ? css`${getDirAnimation(dir, showToast)} .5s ease-in-out;` : '')} ;
border-radius:10px;
padding : 10px 30px;
background-color: ${({ mode }) => getModeColor(mode)};
display: flex;
align-items: center;
`;
export const LeftView = styled.div`
  display: flex;
  align-items: flex-start;
  justify-content: left;
`;
export const CenterView = styled.div`
  display: flex;
  justify-content: ${({ textAlign }) => textAlign};
  flex: 1 1 0%;
`;
export const RightView = styled.div`
  display: flex;
  align-content: center;
  justify-content: right;
`;
export const Svg = styled.svg`
  color: #000;
  width: 15px;
  height: 15px;
  margin-left:20px
`;
