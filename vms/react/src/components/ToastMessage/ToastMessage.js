import React, {
  Fragment,
  useEffect,
  useState,
  memo,
  useCallback,
  useMemo,
} from "react";
import { Text } from "../Text/Text";
import PropTypes from "prop-types";
import { useClassName } from "../../hooks/useClassName";
import {
  CenterView,
  ToastMessageWrapper,
  RightView,
  LeftView,
  Svg,
} from "./ToastMessage.styled";
import { ThemeWrapper } from "../Theme/ThemeContext";

// Utility function (placed before main component)
const isNonNull = (prop) => prop !== null && prop !== undefined;

// Memoized CrossIcon component
const CrossIcon = memo(({ onCrossIconClick }) => {
  const handleClick = useCallback(
    (e) => {
      e.stopPropagation();
      if (isNonNull(onCrossIconClick)) {
        onCrossIconClick();
      }
    },
    [onCrossIconClick]
  );

  return (
    <Svg
      x="0px"
      y="0px"
      width="24px"
      height="24px"
      viewBox="364 364 24 24"
      enableBackground="new 364 364 24 24"
      version="1.1"
      id="Layer_1"
      xmlSpace="preserve"
      xmlns="http://www.w3.org/2000/svg"
      xmlnsXlink="http://www.w3.org/1999/xlink"
      onClick={handleClick}
      style={{ cursor: "pointer" }}
      role="button"
      tabIndex={0}
      aria-label="Close toast message"
    >
      <path
        fill="currentColor"
        d="M386.631,366.739l-1.361-1.362l-9.265,9.265l-9.265-9.265l-1.361,1.362l9.265,9.264l-9.265,9.265l1.361,1.361l9.265-9.264
	l9.265,9.264l1.361-1.361l-9.265-9.265L386.631,366.739z"
      />
    </Svg>
  );
});

CrossIcon.displayName = "CrossIcon";

export const ToastMessage = memo((props) => {
  // 1. PROP DESTRUCTURING & VALIDATION
  const {
    mode,
    children,
    customTextView = false,
    showCrossIcon = false,
    onCrossIconClick,
    rightView,
    leftView,
    additionalClassName,
    additionalStyle,
    textProps = {
      textAlign: "center",
      iconPath: null,
      iconStyle: null,
      iconPosition: "left",
      fontVariant: "bold",
    },
    theme,
    dir,
    autoClose = false,
    delayTime = 3000,
    onDissmiss,
    showToaster = true,
    isMobile,
    isMobileView,
    ...otherProps
  } = props;

  // 2. STATE MANAGEMENT
  const [showToast, setShowToast] = useState(showToaster);

  // 3. PERFORMANCE OPTIMIZATIONS
  const computedClassName = useMemo(
    () => useClassName(props, additionalClassName),
    [props, additionalClassName]
  );

  const textColor = useMemo(() => {
    switch (mode) {
      case "error":
        return "#E11B22";
      case "alert":
        return "#F68B1F";
      case "information":
        return "#0095DA";
      case "success":
        return "#48A842";
      default:
        return null;
    }
  }, [mode]);

  const hasLeftView = useMemo(() => isNonNull(leftView), [leftView]);
  const hasRightView = useMemo(() => isNonNull(rightView), [rightView]);
  const shouldShowRightView = useMemo(
    () => showCrossIcon || hasRightView,
    [showCrossIcon, hasRightView]
  );

  // 4. EVENT HANDLING with useCallback
  const handleCrossIconClick = useCallback(() => {
    setShowToast(false);
    if (isNonNull(onCrossIconClick)) {
      onCrossIconClick();
    }
  }, [onCrossIconClick]);

  // 5. EFFECTS
  useEffect(() => {
    setShowToast(showToaster);
  }, [showToaster]);

  useEffect(() => {
    if (showToast && autoClose) {
      const timeoutId = setTimeout(() => {
        setShowToast(false);
        if (isNonNull(onDissmiss)) {
          onDissmiss(false);
        }
      }, delayTime);

      return () => clearTimeout(timeoutId);
    }
  }, [showToast, autoClose, delayTime, onDissmiss]);

  // 6. CONDITIONAL RENDERING (memoized)
  const leftViewElement = useMemo(() => {
    if (!hasLeftView) return null;

    return (
      <LeftView className="vms_toastmessage_leftview">{leftView}</LeftView>
    );
  }, [hasLeftView, leftView]);

  const centerContent = useMemo(() => {
    if (customTextView) {
      return children;
    }

    return (
      <Text
        className="vms_toastmessage_text"
        iconPath={textProps?.iconPath}
        iconStyle={textProps?.iconStyle}
        iconPosition={textProps?.iconPosition}
        textAlign={textProps?.textAlign}
        fontVariant={textProps?.fontVariant}
        additionalStyle={{ color: textColor }}
      >
        {children}
      </Text>
    );
  }, [customTextView, children, textProps, textColor]);

  const rightViewElement = useMemo(() => {
    if (!shouldShowRightView) return null;

    return (
      <RightView className="vms_toastmessage_rightview">
        {hasRightView ? (
          rightView
        ) : (
          <CrossIcon
            className="vms_toastmessage_crossicon"
            onCrossIconClick={handleCrossIconClick}
          />
        )}
      </RightView>
    );
  }, [shouldShowRightView, hasRightView, rightView, handleCrossIconClick]);

  const toastContent = useMemo(() => {
    if (!showToast) return null;

    return (
      <ToastMessageWrapper
        mode={mode}
        dir={dir}
        autoClose={autoClose}
        delayTime={delayTime}
        style={additionalStyle}
        showToast={showToast}
        className={`${computedClassName} vms_toastmessage_container`}
        role="alert"
        aria-live="polite"
        aria-atomic="true"
        {...otherProps}
      >
        {leftViewElement}

        <CenterView
          textAlign={textProps?.textAlign}
          className="vms_toastmessage_centerview"
        >
          {centerContent}
        </CenterView>

        {rightViewElement}
      </ToastMessageWrapper>
    );
  }, [
    showToast,
    mode,
    dir,
    autoClose,
    delayTime,
    additionalStyle,
    computedClassName,
    leftViewElement,
    centerContent,
    rightViewElement,
    textProps?.textAlign,
    otherProps,
  ]);

  // 7. ERROR HANDLING
  React.useEffect(() => {
    if (!mode) {
      console.warn("ToastMessage: mode prop is required");
    }
    if (!children) {
      console.warn(
        "ToastMessage: children prop is required for toast content"
      );
    }
    if (autoClose && delayTime <= 0) {
      console.warn(
        "ToastMessage: delayTime should be greater than 0 when autoClose is true"
      );
    }
  }, [mode, children, autoClose, delayTime]);

  return (
    <ThemeWrapper isMobile={isMobile || isMobileView || false}>
      <Fragment>{toastContent}</Fragment>
    </ThemeWrapper>
  );
});

ToastMessage.displayName = "ToastMessage";

ToastMessage.defaultProps = {
  customTextView: false,
  showCrossIcon: false,
  textProps: {
    textAlign: "center",
    iconPath: null,
    iconStyle: null,
    iconPosition: "left",
    fontVariant: "bold",
  },
  leftView: null,
  rightView: null,
  autoClose: false,
  delayTime: 3000,
  showToaster: true,
};
ToastMessage.propTypes = {
  /**
   * Based on this value background color and text color will change
   */
  mode: PropTypes.oneOf(["error", "information", "success", "alert"])
    .isRequired,
  /**
   * If property customTextView is true then it will considerd as custom text component
   * and all the text styling need to handle by the application else
   * it will wrap children into Text component
   */
  children: PropTypes.node.isRequired,
  /**
   * Pass true if you want to render property children as custom component
   */
  customTextView: PropTypes.bool,
  /**
   * Pass true if you want to render cross icon in right side
   */
  showCrossIcon: PropTypes.bool,
  /**
   * callback to handle cross icon click
   * () => void
   */
  onCrossIconClick: PropTypes.func,
  /**
   * Custom component for right side. If this property is passed then cross icon will hide
   */
  rightView: PropTypes.node,
  /**
   * Custom component for left side.
   */
  leftView: PropTypes.node,
  /**
   *Props for Text.
   */
  textProps: PropTypes.shape({
    textAlign: PropTypes.oneOf(["left", "right", "center"]),
    fontVariant: PropTypes.oneOf([
      "regular",
      "bold",
      "light",
      "thin",
      "semi-bold",
    ]),
    iconPath: PropTypes.string,
    iconStyle: PropTypes.object,
    iconPosition: PropTypes.oneOf(["left", "right", "top", "bottom"]),
  }),
  /**
   * Property to append additional css class
   */
  additionalClassName: PropTypes.arrayOf(PropTypes.string),
  /**
   * Property to set the `inline style` object
   */
  additionalStyle: PropTypes.object,
  /**
   * specifies the text direction of the element's content
   */
  dir: PropTypes.string,
  /**
   * Property to close the toast-message automatically
   */
  autoClose: PropTypes.bool,
  /**
   * Property to time for which notification will appear
   */
  delayTime: PropTypes.number,
};
