import React, { useState } from "react";
import { ToastMessage } from "./ToastMessage";
import { Button } from "../Button/Button";
import { Text } from "../Text/Text";

export default {
  title: "VMS_REACT/ToastMessage",
  component: ToastMessage,
  argTypes: {
    additionalStyle: { control: { type: "" } },
    additionalClassName: { control: { type: "" } },
  },
};

const defaultDisplayObj = [
  {
    header: "Error Mode",
    mode: "error",
    children: "Error Message",
    dir: "left",
  },
  {
    header: "Success Mode",
    mode: "success",
    children: "Success Message",
    dir: "right",
  },
  {
    header: "Information Mode",
    mode: "information",
    children: "Information Message",
    dir: "left",
  },
  {
    header: "Alert Mode",
    mode: "alert",
    children: "Alert Message",
    dir: "right",
  },
];
export const Default = (props) => {
  return (
    <div style={{ display: "flex", flexDirection: "column", gap: "20px" }}>
      {defaultDisplayObj.map((ele, index) => (
        <div key={index}>
          <h4>{defaultDisplayObj.header}</h4>
          <ToastMessage mode={ele.mode}>{ele.children}</ToastMessage>
        </div>
      ))}
    </div>
  );
};

const withCrossIconDisplayObj = [
  {
    header: "Error with text align left",
    mode: "error",
    children: "Error Message",
    showCrossIcon: true,
    textProps: {
      textAlign: "center",
      iconPath: null,
      iconStyle: null,
      iconPosition: "left",
      fontVariant: "bold",
    },
  },
  {
    header: "Success with icon text align center",
    mode: "success",
    showCrossIcon: true,
    children: "Success Message",
    textProps: {
      textAlign: "center",
      iconPath: "https://img.icons8.com/pastel-glyph/24/000000/plus.png",
      iconStyle: null,
      iconPosition: "left",
      fontVariant: "bold",
    },
  },
  {
    header: "Information with text align right",
    mode: "information",
    children: "Information Message",
    showCrossIcon: true,
    textProps: {
      textAlign: "right",
      iconPath: null,
      iconStyle: null,
      iconPosition: "left",
      fontVariant: "bold",
    },
  },
  {
    header: "Alert with text align center and font variant normal",
    mode: "alert",
    children: "Alert Message",
    showCrossIcon: true,
    textProps: {
      textAlign: "center",
      iconPath: null,
      iconStyle: null,
      iconPosition: "left",
      fontVariant: "light",
    },
  },
];
export const WithCrossIcon = (props) => {
  return (
    <div style={{ display: "flex", flexDirection: "column", gap: "20px" }}>
      {withCrossIconDisplayObj.map((ele, index) => (
        <div key={index}>
          <h4>{ele.header}</h4>
          <ToastMessage
            showCrossIcon={ele.showCrossIcon}
            textProps={ele.textProps}
            mode={ele.mode}
          >
            {ele.children}
          </ToastMessage>
        </div>
      ))}
    </div>
  );
};

const withCustomViewDisplayProps = [
  {
    header: "Left View",
    mode: "error",
    leftView: (
      <Button additionalStyle={{ width: "100px", height: "35px" }}>
        Submit
      </Button>
    ),
    children: "Error Message",
  },
  {
    header: "Custom Text View",
    mode: "error",
    children: (
      <div style={{ display: "flex" }}>
        <Text fontVariant={"bold"}>Error</Text>
        <Text additionalStyle={{ marginLeft: "5px" }} fontVariant="light">
          Message
        </Text>
      </div>
    ),
  },
  {
    header: "Custom Right View",
    mode: "error",
    rightView: (
      <Button
        buttonType={"tertiary"}
        additionalStyle={{ width: "100px", height: "35px" }}
      >
        Submit
      </Button>
    ),
    children: "Error Message",
  },
  {
    header: "Custom Left view with cross icon",
    mode: "error",
    leftView: (
      <Button
        buttonType={"link"}
        additionalStyle={{ width: "100px", height: "35px" }}
      >
        Submit
      </Button>
    ),
    showCrossIcon: true,
    children: `Error Message Error Message Error Message Error Message Error Message Error Message Error
     Message Error Message Error Message Error Message Error Message Error Message Error
     Message Error Message Error Message Error Message Error Message Error Message Error
     Message Error Message`,
  },
  {
    header: "Custom Left and Right View",
    mode: "success",
    leftView: (
      <Button
        buttonType={"link"}
        additionalStyle={{ width: "100px", height: "35px", fontWeight: "bold" }}
      >
        Submit
      </Button>
    ),
    rightView: (
      <Button
        buttonType={"link"}
        additionalStyle={{ width: "100px", height: "35px", fontWeight: "bold" }}
      >
        Close
      </Button>
    ),
    children: "Success Message",
  },
];
export const WithCustomView = (props) => {
  return (
    <div style={{ display: "flex", flexDirection: "column", gap: "20px" }}>
      {withCustomViewDisplayProps.map((ele, index) => (
        <div key={index}>
          <h4>{ele.header}</h4>
          <ToastMessage
            showCrossIcon={ele?.showCrossIcon || false}
            mode={ele.mode}
            leftView={ele?.leftView || null}
            rightView={ele.rightView || null}
          >
            {ele.children}
          </ToastMessage>
        </div>
      ))}
    </div>
  );
};

export const ToastMsgWithDelay = (props) => {
  //  const [showToaster, setShowToaster] = useState(true
  return (
    <div style={{ display: "flex", flexWrap: "wrap" }}>
      {defaultDisplayObj.map((ele, index) => {
        console.log("dir##", ele.dir);

        return (
          <div key={index} style={{ width: "50%", height: "80px" }}>
            <h4>{defaultDisplayObj.header}</h4>
            <ToastMessage
              dir={ele.dir}
              autoClose
              delayTime={1000000}
              showCrossIcon={true}
              showToaster={true}
              onCrossIconClick={() => {}}
              onDissmiss={(val) => {
                console.log("val", val);
              }}
              mode={ele.mode}
            >
              {ele.children}
            </ToastMessage>
          </div>
        );
      })}
    </div>
  );
};

export const ToastMsgCloseIcon = (props) => {
  const [showToaster, setShowToaster] = useState(true);
  return (
    <div style={{ display: "flex", width: "100%" }}>
      <ToastMessage
        autoClose={false}
        showCrossIcon={true}
        showToaster={showToaster}
        onCrossIconClick={() => {
          setShowToaster(false);
        }}
        mode={"success"}
      >
        {<span>Sent Email Successfully</span>}
      </ToastMessage>
    </div>
  );
};

export const ToastMsgAutoClose = (props) => {
  //  const [showToaster, setShowToaster] = useState(true
  return (
    <div style={{ display: "flex", width: "100%" }}>
      <ToastMessage autoClose={true} delayTime={3000} mode={"success"}>
        {<span>Sent Email Successfully</span>}
      </ToastMessage>
    </div>
  );
};
