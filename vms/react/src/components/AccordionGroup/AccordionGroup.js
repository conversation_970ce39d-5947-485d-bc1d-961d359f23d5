import React, { useEffect, useState, memo, useCallback, useMemo } from "react";
import PropTypes from "prop-types";
import { AccordionGroupWrapper } from "./AccordionGroup.styled";
import { useClassName } from "../../hooks/useClassName";
import { Accordion } from "../Accordion/Accordion";
import { ThemeWrapper } from "../Theme/ThemeContext";

// Utility function (placed before main component)
const isNonNull = (prop) => prop !== null && prop !== undefined;

export const AccordionGroup = memo((props) => {
  // 1. PROP DESTRUCTURING & VALIDATION
  const {
    id,
    onAccordionSelectionChange,
    children,
    initialExpandedId,
    isMobileView,
    isMobile,
    additionalClassName,
    additionalStyle,
    idToExpand,
    ...otherProps
  } = props;

  // 2. STATE MANAGEMENT
  const [expandedId, setExpandedId] = useState(null);

  // 3. PERFORMANCE OPTIMIZATIONS
  const computedClassName = useMemo(
    () => useClassName(props, additionalClassName),
    [props, additionalClassName]
  );

  const isControlled = useMemo(() => isNonNull(idToExpand), [idToExpand]);

  // 4. EVENT HANDLING with useCallback
  const handleAccordionToggle = useCallback(
    (accordionId, expanded) => {
      setExpandedId((prevState) => {
        return prevState === accordionId ? null : accordionId;
      });

      if (isNonNull(onAccordionSelectionChange)) {
        onAccordionSelectionChange(accordionId);
      }
    },
    [onAccordionSelectionChange]
  );

  // 5. EFFECTS
  useEffect(() => {
    if (isNonNull(initialExpandedId)) {
      setExpandedId(initialExpandedId);
    }
  }, [initialExpandedId]);

  useEffect(() => {
    if (isControlled) {
      setExpandedId(idToExpand);
    }
  }, [idToExpand, isControlled]);

  // 6. CONDITIONAL RENDERING (memoized)
  const accordionItems = useMemo(() => {
    if (!children?.length) return null;

    return children.map((accordion) => (
      <Accordion
        {...accordion.props}
        key={`Accordion-${accordion.props.id}`}
        isMobileView={isMobileView}
        expanded={expandedId === accordion.props.id}
        onExpansionToggled={(e, expanded) =>
          handleAccordionToggle(accordion.props.id, expanded)
        }
      >
        {accordion.props.children}
      </Accordion>
    ));
  }, [children, expandedId, isMobileView, handleAccordionToggle]);

  // 7. ERROR HANDLING
  React.useEffect(() => {
    if (!children || !Array.isArray(children)) {
      console.warn(
        "AccordionGroup: children prop should be an array of Accordion components"
      );
    }
  }, [children]);

  // 8. RENDER
  return (
    <ThemeWrapper isMobile={isMobile || isMobileView || false}>
      <AccordionGroupWrapper
        id={id}
        className={`${computedClassName} vms_accordiongroup_wrapper`}
        style={additionalStyle}
        role="group"
        aria-label="Accordion group"
        {...otherProps}
      >
        {accordionItems}
      </AccordionGroupWrapper>
    </ThemeWrapper>
  );
});

// Add display name for better debugging
AccordionGroup.displayName = "AccordionGroup";

AccordionGroup.defaultProps = {
  id: null,
  onAccordionSelectionChange: null,
  initialExpandedId: null,
  additionalClassName: null,
  additionalStyle: null,
  isMobileView: false,
  idToExpand: null,
};
AccordionGroup.propTypes = {
  /**
   * id: unique identifier for every accordion any
   */
  id: PropTypes.any,
  /**
   * callback function to be called after any accordion is expanded or collapsed
   * (value: string | number) => void
   */
  onAccordionSelectionChange: PropTypes.func,
  /**
   * children of the component/dom passed as child any
   * **children must be of type Accordion component only**
   */
  children: PropTypes.arrayOf(PropTypes.any).isRequired,
  /**
   * id of the accordion which should be expanded on load of accordion-group
   * string | number
   */
  initialExpandedId: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  /**
   * Pass True if it is mobileView. Default `false`
   */
  isMobileView: PropTypes.bool,
  /**
   * Property to append additional css class
   */
  additionalClassName: PropTypes.arrayOf(PropTypes.string),
  /**
   * Property to set the `inline style` object
   */
  additionalStyle: PropTypes.object,
  /**
   * Property to set id to expand. Can be used when controlling the component from application
   */
  idToExpand: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
};
