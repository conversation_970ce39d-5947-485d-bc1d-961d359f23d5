import React, { Fragment, useState } from "react";
import { Accordion } from "../Accordion/Accordion";
import { AccordionGroup } from "./AccordionGroup";

export default {
  title: "VMS_REACT/AccordionGroup",
  component: AccordionGroup,
  argTypes: {
    additionalStyle: { control: { type: "" } },
    additionalClassName: { control: { type: "" } },
  },
};

export const Default = (args) => {
  return (
    <AccordionGroup {...args} id={"Ad_group"}>
      {["First Item", "Second Item", "Third Item", "Fourth Item"].map(
        (counter) => (
          <Accordion key={counter} id={counter} title={counter}>
            <p>
              Lorem ipsum dolor sit amet, consectetur adipiscing
              elit.Suspendisse malesuada lacus ex, sit amet blandit leo lobortis
              eget.
              <br />
              Lorem ipsum dolor sit amet, consectetur adipiscing elit. <br />
              Suspendisse malesuada lacus ex, sit amet blandit leo lobortis
              eget.
            </p>
          </Accordion>
        )
      )}
    </AccordionGroup>
  );
};

export const AccordianWithOneAccordianOpen = (args) => {
  return (
    <AccordionGroup id={"Ad_group"} initialExpandedId={"First Item"}>
      {["First Item", "Second Item", "Third Item", "Fourth Item"].map(
        (counter) => (
          <Accordion key={counter} id={counter} title={counter}>
            <p>
              Lorem ipsum dolor sit amet, consectetur adipiscing
              elit.Suspendisse malesuada lacus ex, sit amet blandit leo lobortis
              eget.
              <br />
              Lorem ipsum dolor sit amet, consectetur adipiscing elit. <br />
              Suspendisse malesuada lacus ex, sit amet blandit leo lobortis
              eget.
            </p>
          </Accordion>
        )
      )}
    </AccordionGroup>
  );
};
AccordianWithOneAccordianOpen.storyName =
  "Accordion-group with one accordion open by default";

export const AccordianWithTitle = (args) => {
  return (
    <Fragment>
      <AccordionGroup id={"Ad_group"}>
        {["First Item", "Second Item", "Third Item", "Fourth Item"].map(
          (counter, index) => (
            <Accordion
              key={counter}
              id={counter}
              titleComponent={
                index < 2 ? (
                  <div style={{ display: "flex", flexDirection: "column" }}>
                    <div style={{ fontSize: 14, fontWeight: "bold" }}>
                      {counter}
                    </div>
                    <div style={{ fontSize: 12, fontStyle: "italic" }}>
                      Accordion + titleComponent + titleIcon + expansionIcon
                    </div>
                  </div>
                ) : (
                  <div>{counter}</div>
                )
              }
              titleIcon={
                index < 2
                  ? "https://cdn.zeplin.io/5e6c836215857e16f95b9f5a/assets/9D25FE77-8E3B-4B83-B960-41A3129C4545.svg"
                  : null
              }
              expansionIconAfter={
                index < 2
                  ? "https://cdn2.iconfinder.com/data/icons/ios-7-icons/50/plus-512.png"
                  : null
              }
              expansionIcon={
                index < 2
                  ? "https://cdn4.iconfinder.com/data/icons/ionicons/512/icon-minus-512.png"
                  : null
              }
            >
              <p>
                Lorem ipsum dolor sit amet, consectetur adipiscing
                elit.Suspendisse malesuada lacus ex, sit amet blandit leo
                lobortis eget.
                <br />
                Lorem ipsum dolor sit amet, consectetur adipiscing elit. <br />
                Suspendisse malesuada lacus ex, sit amet blandit leo lobortis
                eget.
              </p>
            </Accordion>
          )
        )}
      </AccordionGroup>
    </Fragment>
  );
};
AccordianWithTitle.storyName = "Accordion with title and titleComponent";

export const AccordianGroupControlled = (args) => {
  const accordians = ["First Item", "Second Item", "Third Item", "Fourth Item"];
  const [expanded, setExpanded] = useState(null);
  return (
    <div style={{ display: "flex", flexDirection: "column", gap: "10px" }}>
      <div style={{ display: "flex", gap: "5px" }}>
        {Array.from({ length: 4 }).map((item, index) => (
          <button
            key={index}
            onClick={() => {
              setExpanded(accordians[index]);
            }}
          >
            {`Open Accordian number ${index + 1}`}
          </button>
        ))}
      </div>
      <AccordionGroup
        id={"Ad_group"}
        idToExpand={expanded == null ? "First Item" : expanded}
      >
        {accordians.map((counter, index) => (
          <Accordion
            key={counter}
            id={counter}
            titleComponent={
              index < 2 ? (
                <div style={{ display: "flex", flexDirection: "column" }}>
                  <div style={{ fontSize: 14, fontWeight: "bold" }}>
                    {counter}
                  </div>
                  <div style={{ fontSize: 12, fontStyle: "italic" }}>
                    Accordion + titleComponent + titleIcon + expansionIcon
                  </div>
                </div>
              ) : (
                <div>{counter}</div>
              )
            }
            titleIcon={
              index < 2
                ? "https://cdn.zeplin.io/5e6c836215857e16f95b9f5a/assets/9D25FE77-8E3B-4B83-B960-41A3129C4545.svg"
                : null
            }
            expansionIconAfter={
              index < 2
                ? "https://cdn2.iconfinder.com/data/icons/ios-7-icons/50/plus-512.png"
                : null
            }
            expansionIcon={
              index < 2
                ? "https://cdn4.iconfinder.com/data/icons/ionicons/512/icon-minus-512.png"
                : null
            }
          >
            <p>
              Lorem ipsum dolor sit amet, consectetur adipiscing
              elit.Suspendisse malesuada lacus ex, sit amet blandit leo lobortis
              eget.
              <br />
              Lorem ipsum dolor sit amet, consectetur adipiscing elit. <br />
              Suspendisse malesuada lacus ex, sit amet blandit leo lobortis
              eget.
            </p>
          </Accordion>
        ))}
      </AccordionGroup>
    </div>
  );
};
AccordianGroupControlled.storyName =
  "Accordion Group Controlled via Application";

export const MobileDefault = (args) => {
  return (
    <AccordionGroup {...args} id={"Ad_group"} isMobileView={true}>
      {["First Item", "Second Item", "Third Item", "Fourth Item"].map(
        (counter) => (
          <Accordion key={counter} id={counter} title={counter}>
            <p>
              Lorem ipsum dolor sit amet, consectetur adipiscing
              elit.Suspendisse malesuada lacus ex, sit amet blandit leo lobortis
              eget.
              <br />
              Lorem ipsum dolor sit amet, consectetur adipiscing elit. <br />
              Suspendisse malesuada lacus ex, sit amet blandit leo lobortis
              eget.
            </p>
          </Accordion>
        )
      )}
    </AccordionGroup>
  );
};
MobileDefault.storyName = "(MoWeb) Accordian group";
MobileDefault.parameters = {
  viewport: {
    defaultViewport: "iphone5",
  },
};
export const MobileAccordianWithOneAccordianOpen = (args) => {
  return (
    <AccordionGroup
      {...args}
      id={"Ad_group"}
      isMobileView={true}
      initialExpandedId={"First Item"}
    >
      {["First Item", "Second Item", "Third Item", "Fourth Item"].map(
        (counter) => (
          <Accordion key={counter} id={counter} title={counter}>
            <p>
              Lorem ipsum dolor sit amet, consectetur adipiscing
              elit.Suspendisse malesuada lacus ex, sit amet blandit leo lobortis
              eget.
              <br />
              Lorem ipsum dolor sit amet, consectetur adipiscing elit. <br />
              Suspendisse malesuada lacus ex, sit amet blandit leo lobortis
              eget.
            </p>
          </Accordion>
        )
      )}
    </AccordionGroup>
  );
};
MobileAccordianWithOneAccordianOpen.storyName =
  "(MoWeb) Accordion-group with one accordion open by default";
MobileAccordianWithOneAccordianOpen.parameters = {
  viewport: {
    defaultViewport: "iphone5",
  },
};

export const MobileAccordianGroupWithTitle = (args) => {
  return (
    <AccordionGroup id={"Ad_group"} isMobileView={true}>
      {["First Item", "Second Item", "Third Item", "Fourth Item"].map(
        (counter, index) => (
          <Accordion
            key={counter}
            id={counter}
            titleComponent={
              index < 2 ? (
                <div style={{ display: "flex", flexDirection: "column" }}>
                  <div style={{ fontSize: 14, fontWeight: "bold" }}>
                    {counter}
                  </div>
                  <div style={{ fontSize: 12, fontStyle: "italic" }}>
                    Accordion + titleComponent + titleIcon + expansionIcon
                  </div>
                </div>
              ) : (
                <div>{counter}</div>
              )
            }
            titleIcon={
              index < 2
                ? "https://cdn.zeplin.io/5e6c836215857e16f95b9f5a/assets/9D25FE77-8E3B-4B83-B960-41A3129C4545.svg"
                : null
            }
            expansionIconAfter={
              index < 2
                ? "https://cdn2.iconfinder.com/data/icons/ios-7-icons/50/plus-512.png"
                : null
            }
            expansionIcon={
              index < 2
                ? "https://cdn4.iconfinder.com/data/icons/ionicons/512/icon-minus-512.png"
                : null
            }
          >
            <p>
              Lorem ipsum dolor sit amet, consectetur adipiscing
              elit.Suspendisse malesuada lacus ex, sit amet blandit leo lobortis
              eget.
              <br />
              Lorem ipsum dolor sit amet, consectetur adipiscing elit. <br />
              Suspendisse malesuada lacus ex, sit amet blandit leo lobortis
              eget.
            </p>
          </Accordion>
        )
      )}
    </AccordionGroup>
  );
};
MobileAccordianGroupWithTitle.storyName =
  "(MoWeb) Accordion with title and titleComponent";

MobileAccordianGroupWithTitle.parameters = {
  viewport: {
    defaultViewport: "iphone5",
  },
};
