import {  useEffect,useRef } from "react";
function useOnClickOutside(ref, handler) {
    useEffect(
      () => {
        const listener = (event) => {
            // Skip scroll events
          if (event.type === "scroll") {
            return;
          }
          // Do nothing if clicking ref's element or descendent elements
          if (!ref.current || ref.current.contains(event.target)) {
            return;
          }
          handler(event);
        };
        document.addEventListener("mousedown", listener);
        document.addEventListener("touchstart", listener);
        return () => {
          document.removeEventListener("mousedown", listener);
          document.removeEventListener("touchstart", listener);
        };
      },
      // Add ref and handler to effect dependencies
      // It's worth noting that because passed in handler is a new ...
      // ... function on every render that will cause this effect ...
      // ... callback/cleanup to run every render. It's not a big deal ...
      // ... but to optimize you can wrap handler in useCallback before ...
      // ... passing it into this hook.
      [ref, handler]
    );
  }


  function useClickAway(ref, onClickAway) {
    // Keep a mutable reference to click away callback
    // and change it every time the component using it changes
    // using 'useRef' here will make sure that we have a mutable
    // and single callback lying around.
    const callbackRef = useRef(onClickAway);
    useEffect(() => {
      callbackRef.current = onClickAway;
    }, [onClickAway]);
  
    // listen for click events on ref element
    // attaching a handler and calling the callback if necessary
    useEffect(() => {
      const onPointerDown = (event) => {
        if (ref.current && !ref.current.contains(event.target)) {
          callbackRef.current(event);
        }
      };
      document.addEventListener("pointerdown", onPointerDown);
      return () => {
        document.removeEventListener("pointerdown", onPointerDown);
      };
    }, [ref]);
  }


  export { useOnClickOutside,useClickAway };
