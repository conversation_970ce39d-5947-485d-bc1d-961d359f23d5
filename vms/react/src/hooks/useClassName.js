/**
 * @function useClassName
 * @description React Hook to join and handle additional ClassName
 * @returns className and concate to the existing ClassName
 */
export const useClassName = (props, additionalClassName) => {
  try {
    // Add safety checks for all parameters
    if (!props) {
      return '';
    }
    
    let className = '';
    
    if (additionalClassName) {
      if (Array.isArray(additionalClassName)) {
        className = additionalClassName.filter(cls => cls && typeof cls === 'string').join(' ');
      } else if (typeof additionalClassName === 'string') {
        className = additionalClassName;
      }
    }
    
    return className;
  } catch (error) {
    console.error('Error in useClassName:', error);
    return '';
  }
};
