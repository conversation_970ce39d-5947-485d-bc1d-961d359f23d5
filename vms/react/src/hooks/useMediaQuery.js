import { useEffect, useState } from "react";

export function useMediaQuery(query) {
  const [matches, setMatches] = useState(false);
  useEffect(() => {
    const matchQueryList = window.matchMedia(query);
    function handleChange(e) {
      setMatches(e.matches);
    }
    try {
      matchQueryList.addEventListener("change", handleChange);
    } catch (e) {
      matchQueryList.addListener(handleChange);
    }
    return () => {
      try {
        matchQueryList.removeEventListener("change", handleChange);
      } catch (e) {
        matchQueryList.removetListener(handleChange);
      }
    };
  }, [query]);
  return matches;
}
