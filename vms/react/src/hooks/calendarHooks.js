import addDays from "date-fns/addDays";
import addMonths from "date-fns/addMonths";
import addWeeks from "date-fns/addWeeks";
import endOfDay from "date-fns/endOfDay";
import endOfMonth from "date-fns/endOfMonth";
import endOfWeek from "date-fns/endOfWeek";
import endOfYear from "date-fns/endOfYear";
import startOfDay from "date-fns/startOfDay";
import startOfMonth from "date-fns/startOfMonth";
import startOfWeek from "date-fns/startOfWeek";
import startOfYear from "date-fns/startOfYear";
import isSunday from "date-fns/isSunday";
import isSameDay from "date-fns/isSameDay";
import isSaturday from "date-fns/isSaturday";
import isSameHour from "date-fns/isSameHour";
import isBefore from "date-fns/isBefore";
import isAfter from "date-fns/isAfter";
import isSameMonth from "date-fns/isSameMonth";
import subMonths from "date-fns/subMonths";
import getISOWeek from "date-fns/getISOWeek";
import format from "date-fns/format";
import getYear from "date-fns/getYear";
import addYears from "date-fns/addYears";
import getISOWeekYear from "date-fns/getISOWeekYear";
import subYears from "date-fns/subYears";
import isSameYear from "date-fns/isSameYear";
import setYear from "date-fns/setYear";
import isToday from "date-fns/isToday";
import differenceInCalendarDays from "date-fns/differenceInCalendarDays";
import { differenceInDays } from "date-fns";
import differenceInCalendarMonths from 'date-fns/differenceInCalendarMonths'
import eachMonthOfInterval from 'date-fns/eachMonthOfInterval';
import isWeekend from "date-fns/isWeekend"
const DEFAULT_YEAR_ITEM_NUMBER = 16;
const namedMonths = [
  "Jan",
  "Feb",
  "Mar",
  "Apr",
  "May",
  "Jun",
  "Jul",
  "Aug",
  "Sep",
  "Oct",
  "Nov",
  "Dec",
];

function addDay(day, number) {
  return addDays(day, number);
}

function addMonth(day, number) {
  return addMonths(day, number);
}

function addWeek(day, number) {
  return addWeeks(day, number);
}

function addYear(day, number) {
  return addYears(day, number);
}
function getEndOfDay(day) {
  return endOfDay(day);
}

function getEndOfMonth(day) {
  return endOfMonth(day);
}

function getEndOfWeek(day) {
  return endOfWeek(day);
}

function getYears(day) {
  return getYear(day);
}
function getEndOfYear(day) {
  return endOfYear(day);
}

function getStartOfDay(day) {
  return startOfDay(day);
}

function getStartOfMonth(day) {
  return startOfMonth(day);
}

function getStartOfWeek(day) {
  return startOfWeek(day);
}

function getStartOfYear(day) {
  return startOfYear(day);
}

function formatWithLocale(date, formatStr="dd MMM yyyy") {
  if (typeof formatStr !== 'string') {
    console.warn('Invalid format string passed:', formatStr);
    return '';
  }
  return format(date, formatStr);
}

function whetherSun(day) {
  return isSunday(day);
}

function whetherSat(day) {
  return isSaturday(day);
}

function whetherSameHour(hour, selectedHour) {
  return isSameHour(hour, selectedHour);
}

function whetherSameDay(day, selectedDate) {
  return isSameDay(day, selectedDate);
}

function whetherSameMonth(day, selectedDate) {
  return isSameMonth(day, selectedDate);
}

function whetherSameYear(day, selectedDate) {
  return isSameYear(day, selectedDate);
}

const convertToDate = (dateStr) => {
  const [day, month, year] = dateStr.split("/");
  return new Date(`${year}-${month}-${day}T00:00:00`);  
};

function whetherDisabled(day, month, minDate, maxDate,mode,currentSelection,fromDate,maxBookingDays, dateList, showTodayDate) {
  // Add null/undefined checks for fromDate
  const validFromDate = fromDate && fromDate instanceof Date && !isNaN(fromDate.getTime());
  let checkdiff = validFromDate ? differenceInDays(day, fromDate) : 0;
  
  const isSameDate = dateList && dateList.some(dateStr => {
    const date = convertToDate(dateStr);
    return date.getTime() === day.getTime();
  });
  
  if(isSameDate){
    return true;
  }
  
  if(isToday(day) && !isSameDay(day, minDate) && isBefore(day, minDate) && !showTodayDate){
    return true
  }

  if(currentSelection === 'endDate' && validFromDate && checkdiff > maxBookingDays){
    return true;
  }

  if (month && !isSameMonth(day, month)) {
    return true;
  }
  
  if (mode==='singleDateRange' && currentSelection== 'endDate' && validFromDate && isBefore(day, fromDate) )  {
    if(isSameDay(day,fromDate)){
      return false;
    }else{
      return true;
    }
  }

  if (day && isToday(day) && isSameMonth(day, month)) {
    return false;
  }
  
  if (minDate && isBefore(day, minDate) ) {
    return true;
  }
  
  if (maxDate && isAfter(day, maxDate)) {
    return true;
  }
  
  return false;
}


function whetherBetween(date, from, to) {
  return isBefore(date, to) && isAfter(date, from);
}

function whetherSelected(day, selectedDate, from, to) {
  if (
    isSameDay(day, from) ||
    whetherBetween(day, from, to) ||
    isSameDay(day, to)
  )
    return true;
  return false;
}

function whetherBefore(day, selectedDate) {
  return isBefore(day, selectedDate);
}


function whetherAfter(day, selectedDate) {
  return isAfter(day, selectedDate);
}
function subMonth(month, number) {
  return subMonths(month, number);
}

function getWeekNumber(day) {
  return getISOWeek(day);
}

function getYearNumber(day) {
  return getISOWeekYear(day);
}

function setYears(day, year) {
  return setYear(day, year);
}

function getYearsPeriod(date, yearItemNumber = DEFAULT_YEAR_ITEM_NUMBER) {
  const endPeriod = Math.ceil(getYear(date) / yearItemNumber) * yearItemNumber;
  const startPeriod = endPeriod - (yearItemNumber - 1);
  return { startPeriod, endPeriod };
}

function subYear(month, number) {
  return subYears(month, number);
}

function whetherisToday(day) {
  return isToday(day);
}
function getWeek(yearStart, date, weekStartsOn) {
  const yearStartDate =
    typeof yearStart === "number"
      ? new Date(yearStart, 0, 1) // 1st Jan of the Year
      : yearStart;

  return Math.ceil(
    (Math.round((date - yearStartDate) / (60 * 60 * 24 * 1000)) +
      yearStartDate.getDay() +
      1 -
      weekStartsOn) /
      7
  );
}
function getNextYear(n) {
  var now = new Date();
  return new Date(now.setFullYear(now.getFullYear() + n));
}

function monthsBetween(from, to, cb) {
  if (cb === void 0) {
    cb = function (month) {};
  }
  //Convert to date objects
  var d1 = from;
  var d2 = to;
  //month counter
  var months = 0;
  //Call callback function with month
  cb(d1.getMonth());
  //While year or month mismatch, reduce by one day
  while (
    d2.getFullYear() != d1.getFullYear() ||
    d2.getMonth() != d1.getMonth()
  ) {
    var oldmonth = d1.getMonth();
    d1 = new Date(d1.getTime() + 86400000);
    //if we enter into new month, add to month counter
    if (oldmonth != d1.getMonth()) {
      //Call callback function with month
      cb(d1.getMonth());
      months++;
    }
  }
  //return month counter as result
  return months;
}

const getAllMonths = (fromDate, toDate) => {
  const fromYear = fromDate.getFullYear();
  const fromMonth = fromDate.getMonth();
  const toYear = toDate.getFullYear();
  const toMonth = toDate.getMonth();
  const months = [];

  for (let year = fromYear; year <= toYear; year++) {
    let monthNum = year === fromYear ? fromMonth : 0;
    const monthLimit = year === toYear ? toMonth : 11;

    for (; monthNum <= monthLimit; monthNum++) {
      let month = monthNum + 1;
      let monthName = namedMonths[month - 1];
      months.push({ year, monthName });
    }
  }
  return months;
};

const checkRangeIsValid = (startDate, endDate,minBookingDays,maxBookingDays) => {
  let checkSameDay = whetherSameDay(startDate, endDate);
  let checkIsBefore = isAfter(startDate, endDate);
  let checkdiff = differenceInDays(endDate,startDate)

  if (checkSameDay) {
    return false;
  }
  if (checkIsBefore) {
    return false;
  }
  if(checkdiff < minBookingDays){
    return false;
  }
  if(checkdiff > maxBookingDays){
    return false;
  }
  return true;
};

const checkRangeErrorCode = (
  startDate,
  endDate,
  minBookingDays,
  maxBookingDays
) => {
  let checkSameDay = whetherSameDay(startDate, endDate);
  if (checkSameDay) {
    return 1;
  }

  let checkdiff = differenceInDays(endDate, startDate);

  if (checkdiff < minBookingDays) {
    return 2;
  }
  if (checkdiff > maxBookingDays) {
    return 3;
  }
  return 0;
};

function getMonthOfInterval(startDate,endDate){
  const result = eachMonthOfInterval({
    start: startDate,
    end: endDate
  })
  return result;
}

function getMonthDiff(startDate,endDate){
  const result = differenceInCalendarMonths(endDate,startDate);
  return result;
}

 function getMonthDisplayRange(date, dateOptions,fixedHeight) {
  const startDateOfMonth = startOfMonth(date, dateOptions);
  const endDateOfMonth = endOfMonth(date, dateOptions);
  const startDateOfCalendar = startOfWeek(startDateOfMonth, dateOptions);
  let endDateOfCalendar = endOfWeek(endDateOfMonth, dateOptions);
  if (fixedHeight && differenceInCalendarDays(endDateOfCalendar, startDateOfCalendar) <= 34) {
    endDateOfCalendar = addDays(endDateOfCalendar, 7);
  }
  return {
    start: startDateOfCalendar,
    end: endDateOfCalendar,
    startDateOfMonth,
    endDateOfMonth,
  };
}

export function calcFocusDate(currentFocusedDate, props,ranges) {
  const { selectedDate, date, months,focusedRange,displayMode } = props;
  // find primary date according the props
  let targetInterval;
  if (displayMode === 'dateRange') {
    const range = ranges[focusedRange[0]] || {};
    targetInterval = {
      start: range.startDate,
      end: range.endDate,
    };
  } else {
    targetInterval = {
      start: date,
      end: date,
    };
  }
  targetInterval.start = startOfMonth(targetInterval.start || new Date());
  targetInterval.end = endOfMonth(targetInterval.end || targetInterval.start);
  const targetDate = targetInterval.start || targetInterval.end || selectedDate || new Date();

  // initial focus
  if (!currentFocusedDate) return selectedDate || targetDate;

  // // just return targetDate for native scrolled calendars
  // if (props.scroll.enabled) return targetDate;
  if (differenceInCalendarMonths(targetInterval.start, targetInterval.end) > months) {
    // don't change focused if new selection in view area
    return currentFocusedDate;
  }
  return targetDate;
}

export function findNextRangeIndex(ranges, currentRangeIndex = -1) {
  const nextIndex = ranges.findIndex(
    (range, i) => i > currentRangeIndex && range.autoFocus !== false && !range.disabled
  );
  if (nextIndex !== -1) return nextIndex;
  return ranges.findIndex(range => range.autoFocus !== false && !range.disabled);
}

export function whetherIsWeekend(day, local) {
  return isWeekend(day, local);
}

export function differenceCalendarDays(startDate, endDate) {
  return differenceInCalendarDays(startDate, endDate);
}



export {
  addDay,
  addMonth,
  getEndOfYear,
  getEndOfWeek,
  getEndOfMonth,
  getEndOfDay,
  addWeek,
  getStartOfDay,
  getStartOfMonth,
  getStartOfWeek,
  getStartOfYear,
  getWeekNumber,
  formatWithLocale,
  whetherSun,
  whetherSelected,
  isSameDay,
  whetherSat,
  whetherSameHour,
  whetherSameDay,
  whetherDisabled,
  whetherBetween,
  whetherBefore,
  whetherAfter,
  subMonth,
  getYears,
  addYear,
  getYearNumber,
  getYearsPeriod,
  subYear,
  whetherSameMonth,
  whetherSameYear,
  setYears,
  whetherisToday,
  getWeek,
  getNextYear,
  monthsBetween,
  getAllMonths,
  checkRangeIsValid,
  checkRangeErrorCode,
  getMonthOfInterval,
  getMonthDiff,
  getMonthDisplayRange
};
