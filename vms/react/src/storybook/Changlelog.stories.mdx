import { Meta, Canvas } from "@storybook/addon-docs";
import LinkTo from "@storybook/addon-links/react";
import packageJson from "../../../../package.json";
import { changeHistory } from "./changelog.js";


<Meta title="Changelog" />

## Changelog

<div>
  {changeHistory.map((change, index) => {
    return (
      <Canvas key={index} withSource="none">
        <div style={{ display: "flex", flexDirection: "column" }}>
          <h2>{`Version ${change.version} [${change.release_date}] 🎊 🎉 🎁 💃`}</h2>
          {change?.added != null && change?.added?.length > 0 && (
            <div style={{ display: "flex", flexDirection: "column" }}>
              <div>
                <h3>Added 🎊 🎉 🎁</h3>
              </div>
              <div>
                <ul style={{ listStyle: "square" }}>
                  {change.added.map((added, index) => {
                    return (
                      <li key={index} style={{ padding: "10px 0px" }}>
                        <LinkTo kind={added.story_link} target="_blank">
                          {added.story_name}
                        </LinkTo>
                        <ul style={{ listStyle: "none" }}>
                          <li>{added.story_description}</li>
                        </ul>
                      </li>
                    );
                  })}
                </ul>
              </div>
            </div>
          )}
          {change?.improvements != null && change?.improvements?.length > 0 && (
            <div style={{ display: "flex", flexDirection: "column" }}>
              <div>
                <h3>Improvements 💅</h3>
              </div>
              <div>
                <ul style={{ listStyle: "square", lineHeight:"2rem" }}>
                  {change?.improvements.map((improve, index) => {
                    return (<><li key={index}>{improve.description}</li>
                    {improve?.subDescription !== null && improve?.subDescription?.length > 0 && (
                      <div>
                        <ul>
                          {improve?.subDescription.map((item,index) => {
                            return <li style={{fontSize:"14px"}}>{item.subPoint}</li>
                          })}
                        </ul>
                      </div>
                    )
                    }
                    </>);
                  })}
                </ul>
              </div>
            </div>
          )}
          {change?.bugFixes != null && change?.bugFixes?.length > 0 && (
            <div style={{ display: "flex", flexDirection: "column" }}>
              <div>
                <h3>Bug-Fixes 🔨</h3>
              </div>
              <div>
                <ul style={{ listStyle: "square" }}>
                  {change?.bugFixes.map((bug, index) => {
                    return <li key={index}>{bug.description}</li>;
                  })}
                </ul>
              </div>
            </div>
          )}
        </div>
      </Canvas>
    );
  })}
</div>
