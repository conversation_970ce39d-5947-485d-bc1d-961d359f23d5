import { Meta } from "@storybook/addon-docs";
import packageJson from "../../../../package.json";

<Meta title="Overview" />

## Latest released version

<span>{`${packageJson.name} - ${packageJson.version}`}</span>

## Supported Technology stack

- React JS
- React JS SSR

## Getting started

This guide will help you use components in your application.

** Install **<br></br>

<span>{`npm install ${packageJson.name}`}</span><br></br>

## Recommended starter kits

** Create React App **

<a href="https://github.com/facebook/create-react-app" target="_blank">Create React App</a> is a
good way to setup a simple project.

<p>
  npx create-react-app my-app<br></br>
  cd my-app<br></br>
  npm install {packageJson.name}
  <br></br>
  npm start<br></br>
</p>

## Standalone configurations

** Configuring your app **<br></br>

To use these components you need to wrap your view with `Theme`.<br></br>

<pre>
  <code>
    {`
import { Theme } from '${packageJson.name}'

function App() {
  return (
    <Theme>
      <div>
        WELCOME to VMS REACT COMPONENT
      </div>
    </Theme>
  );
}

`}
  </code>
</pre>

## Dependencies
<span>{`${JSON.stringify(packageJson.peerDependencies,null,"\t")}`}</span>
