import { version } from "react";


export const changeHistory = [
  {
    version: "1.11.0",
    release_date: "20-06-2025",
    added: [
      {
        description: "Added new component (Table).",
      }
    ],
    improvements: [
      {
        description: "Replaced button with input field in DropDownCalendar.",
        description: "Added altText prop in VarientOfAutocomplete, TravelerClass & DateRangeCalender.",
      }
    ],
    bugFixes: [
      {
        description: "Fixed issue with autofocus in AutocompleteDropdown.",
      }
    ]
  },
  {
    version: "1.10.9",
    release_date: "29-05-2025",
    improvements: [
      {
        description: "tabIndex added in mobileInputText",
      },
     
    ]
  },
  {
    version: "1.10.8",
    release_date: "29-04-2025",
    improvements: [
      {
        description: "date change value is handled for empty value",
      },
     
    ]
  },
  {
    version: "1.10.7",
    release_date: "24-04-2025",
    improvements: [
      {
        description: "date jsx changed and value added for invalid date.",
      },
     
    ]
  },
  {
    version: "1.10.6",
    release_date: "23-04-2025",
    improvements: [
      {
        description: "date selection issue is fixed.",
      },
     
    ]
  },
  {
    version: "1.10.5",
    release_date: "17-04-2025",
    improvements: [
      {
        description: "className added in dropdown calender and sidbar component.",
      },
     
    ]
  },
  {
    version: "1.10.4",
    release_date: "09-04-2025",
    improvements: [
      {
        description: "value prop added in dropdown calender for pre-fetch",
      },
     
    ]
  },
  {
    version: "1.10.3",
    release_date: "08-04-2025",
    bugFixes: [
      {
        description: "dropdown icons will now chnages on open and close.",
      },
      {
        description: "pax selection component bug fixes for mobile.",
      },
    ]
  },
  {
    version: "1.10.2",
    release_date: "01-04-2025",
    bugFixes: [
      {
        description: "Traveller Class component bug fixes",
      },
    ]
  },
  {
    version: "1.10.1",
    release_date: "01-04-2025",
    improvement: [
      {
        description: "Added new prop onDoneButtonClick in Traveller Class",
      },
    ]
  },
  {
    version: "1.10.0",
    release_date: "29-03-2025",
    added: [
      {
        description: "side bar component added",
      },
      {
        description: "dropdown calander component added",
      },
    ],
    improvement: [
      {
        description: "added new section of traveller class in pax selection",
      },
      {
        description: "new props isdMaxLength added in MobileInputText",
      },
    ]
  },
  {
    version: "1.9.2",
    release_date: "24-02-2025",
    added: [],
    bugFixes: [
      {
        description: "DateRangeCalendar showTodaydate props bug fixes",
      },
    ]
  },
  {
    version: "1.9.1",
    release_date: "17-02-2025",
    added: [],

    improvements: [
      {
        description: "Add new props for subTitle text in anchoredHorizontalTab",
      },
      {
        description: "add new sequence element for progress tracker component",
      },
    ],
    bugFixes: [
      {
        description: "ReadioButton Component bug fixes",
      },
    ]
  },
  {
    version: "1.9.0",
    release_date: "02-01-2025",
    added: [],

    improvements: [
      {
        description: "Added ids for all form element components",
      },
    ],
  },
  {
    version: "1.8.11",
    release_date: "20-12-2024",
    added: [],

    bugFixes: [
      {
        description: "Mobile input text bug fix",
      },
    ]
  },
  {
    version: "1.8.10",
    release_date: "04-12-2024",
    added: [],

    bugFixes: [
      {
        description: "Progress Tracker Component bug fixes",
      },
      {
        description: "DateRange Calendar Mobile Component bug fixes",
      },
      {
        description: "Done button added in Traveller class component",
      },
    ]
  },
  {
    version: "1.8.9",
    release_date: "25-10-2024",
    added: [],

    bugFixes: [
      {
        description: "Button Component bug fixes",
      },
      {
        description: "Progress Tracker Component css bug fixes",
      },
      {
        description: "VariantOfAutocomplete Component css bug fixes",
      },
    ]
  },
  {
    version: "1.8.8",
    release_date: "17-10-2024",
    added: [],

    bugFixes: [
      {
        description: "Removed vulnerabilities",
      },
    ]
  },
  {
    version: "1.8.7",
    release_date: "17-10-2024",
    added: [],

    bugFixes: [
      {
        description: "MobileInputText Country Code placeholder bug fixes",
      },
    ]
  },
  {
    version: "1.8.6",
    release_date: "10-10-2024",
    added: [],

    bugFixes: [
      {
        description: "MobileInputText Component bug fixes",
      },
    ]
  },
  {
    version: "1.8.5",
    release_date: "07-10-2024",
    added: [],

    bugFixes: [
      {
        description: "Minor bug fixes im MobileDateRangeCalendar Component",
      },
      {
        description: "Progress Tracker Component bug fixes",
      },
      {
        description: "MobileInputText Component bug fixes",
      },
    ]
  },
  {
    version: "1.8.4",
    release_date: "24-09-2024",
    added: [],
    improvements: [
      {
        description: "Added custom icons for progress tracker component",
      },
      {
        description: "Added custom dropdown icon on Auto complete Dropdown Component",
      },

    ],
    bugFixes: [
      {
        description: "Minor bug fixes im multipe components",
      }
    ]
  },
  {
    version: "1.8.2",
    release_date: "19-07-2024",
    added: [],
    improvements: [
      {
        description: "Dynamic Theme to imported from external file",
      },
      {
        description: "Theme has to pass from _app.js",
      },
      {
        description: "Configure vms-theme in root directory of each project",
      },
      {
        description: "vms-theme/theme_desktop.js and vms-theme/theme_mobile.js Files to be created in root directory for using Theme Component",
      }
    ],
    bugFixes: []
  },
  {
    version: "1.8.1",
    release_date: "19-07-2024",
    added: [],
    improvements: [
      {
        description: "Dynamic Theme Created",
      },
      {
        description: "Theme has to pass from _app.js",
      }
    ],
    bugFixes: []
  },
  {
    version: "1.7.3",
    release_date: "10-07-2024",
    added: [],
    improvements: [
      {
        description: "Accordion expansion text props added",
      },
      {
        description: "Dropdown Icon Added in Button Component",
      },
      {
        description: "Input click event added in MobileDateRangeCalendar Component",
      },
      {
        description: "customCountryList props added in MobileInputText Component",
      },
    ],
    bugFixes: []
  },
  {
    version: "1.7.2",
    release_date: "25-06-2024",
    added: [],
    improvements: [
      {
        description: "Added new feature for near by & All Airport listing view in a VariantOfAutoComplete Component",
      },
      {
        description: "Added new mode named (textWithIcon) in OptionSelector Component",
      },
      {
        description: "Added new props named (secondPlaceholder) in AutoCompleteDropdown Component",
      },
    ],
    bugFixes: []
  },
  {
    version: "1.7.1",
    release_date: "21-05-2024",
    added: [],
    bugFixes: [
      {
        description: "Calendar_V2 Bug fixes and new changes",
      },
    ]
  },
  {
    version: "1.7.0",
    release_date: "10-05-2024",
    added: [
      {
        story_link: "vms-react-calendar-v2--default",
        story_name: "calendar-v2",
        story_description: "New calendar-v2 Component",
      }
    ],
    improvements: [
      {
        description: "New Prop named customDot added in carousel_v1",
      }
    ],
    bugFixes: [
      {
        description: "VariantOfAutoComplete and Traveller Class bug fixes",
      },
    ]
  },
  {
    version: "1.6.12",
    release_date: "25-04-2024",
    added: [],
    bugFixes: [
      {
        description: "MobileDateRange Calendar inputSelection feature added and bug fixes",
      },
    ]
  },
  {
    version: "1.6.11",
    release_date: "17-04-2024",
    added: [],
    bugFixes: [
      {
        description: "Outsideclick handler added in TravelerClass component",
      },
    ]
  },
  {
    version: "1.6.10",
    release_date: "22-03-2024",
    added: [],
    bugFixes: [
      {
        description: "MobileDateRangeCalendar and Calendar Bug fixed and new props & events added",
      },
      {
        description: "Minor bug fixes, related to multiple components",
      }
    ]
  },
  {
    version: "1.6.9",
    release_date: "12-03-2024",
    added: [],
    bugFixes: [
      {
        description: "indexSlider bug fixes and new event added",
      },
      {
        description: "Minor bug fixes, related to multiple components",
      }
    ]
  },
  {
    version: "1.6.8",
    release_date: "11-03-2024",
    added: [],
    bugFixes: [
      {
        description: "ToastMessage component autoClose fix added",
      },
      {
        description: "MobileInputText component msgText props passed",
      },
      {
        description: "Minor bug fixes, related to multiple components",
      }
    ]
  },
  {
    version: "1.6.7",
    release_date: "05-03-2024",
    added: [],
    bugFixes: [
      {
        description: "New props changes in InputDatefield",
      },
      {
        description: "Minor bug fixes, related to multiple components",
      }
    ]
  },
  {
    version: "1.6.6",
    release_date: "04-03-2024",
    added: [],
    bugFixes: [
      {
        description: "New Prop named  onChangeComplete added in IndexSlider",
      },
      {
        description: "Minor bug fixes, related to multiple components",
      }
    ]
  },
  {
    version: "1.6.5",
    release_date: "09-02-2024",
    added: [],
    improvements: [
      {
        description: "New Prop named idToExpand added in AccordianGroup. To control the expansion of AccordiaGroup via application",
      },
    ],
    bugFixes: [
      {
        description: "Css bug fixes, related to InputRadioButton selection",
      },
    ]
  },
  {
    version: "1.6.4",
    release_date: "08-02-2024",
    added: [],
    improvements: [
      {
        description: "DateRangeCalendar changes",
      },
      {
        description: "Index Slider improvement",
      },
      {
        description: "New Prop named customViewLabel added in Traveller class",
      },
    ],
  },
  {
    version: "1.6.3",
    release_date: "15-01-2024",
    added: [],
    improvements: [
      {
        description: "Added ModalFooter props in Modalpopup component",
      },
      {
        description: "Added disabledPreviousDate props in Mobile DateRange Calendar",
      },
    ],
  },
  {
    version: "1.6.2",
    release_date: "09-01-2024",
    added: [
      {
        story_link: "vms-react-rangeselector-v1--default",
        story_name: "rangeselector-v1",
        story_description: "New rangeselector Component",
      },
    ],
    improvements: [
      {
        description: "Added rangeselector-v1",
      },
    ],
    bugFixes: [
      {
        description: "Minor bug fixes, related to multiple components",
      },
    ],
  },
  {
    version: "1.6.1",
    release_date: "28-12-2023",
    added: [
      {
        story_link: "vms-react-carousel-v1--default",
        story_name: "carousel-v1",
        story_description: "New Carousel Component",
      },
    ],
    improvements: [
      {
        description:
          "Added showCancelButton props in Mobile Date Range calendar",
      },
      {
        description:
          "Added cancelButtonText and cancelButtonType in Mobile Date Range calendar",
      },
      {
        description:
          "Added selectedValue  props to Mobile Input Text",
      },
      {
        description:
          "Added  selectedValueFilterBy props to Mobile Input Text",
      },
      {
        description:
          "Added  custom label prop added for children and adult in PaxSelection",
      },
      {
        description:
          "Added  children age description prop added in PaxSelection",
      },
      {
        description:
          "Added modalHeaderCustomView props in ModalPopup Component",
      },
      {
        description:
          "Added primaryTextIcon props in TravelerClass Component",
      }
    ],
    bugFixes: [
      {
        description: "Minor bug fixes, related to multiple components",
      },
      {
        description: "Animation bug-fix in ModalPopup Component",
      },
    ],
  },


  {
    version: "1.6.0",
    release_date: "20-10-2023",
    added: [
      {
        story_link: "vms-react-videoplayer--default",
        story_name: "Video player",
        story_description: "Video player Component",
      },
    ],
    improvements: [
      {
        description:
          "Added custom control functionality to carousel",
      },
      {
        description:
          "Added slideDirection and activeSelectedIndex props to carousel",
      },
      {
        description:
          "Added onPaxVisibilityChange  props to paxSelection",
      },
    ],
    bugFixes: [
      {
        description: "Minor bug fixes, related to multiple components",
      },
    ],
  },
  {
    version: "1.5.3",
    release_date: "13-09-2023",
    added: [],
    improvements: [],
    bugFixes: [
      {
        description: "Minor bug fixes, related to multiple components",
      },
    ],
  },
  {
    version: "1.5.2",
    release_date: "12-09-2023",
    added: [],
    improvements: [],
    bugFixes: [
      {
        description: "Minor bug fixes, related to multiple components",
      },
    ],
  },
  {
    version: "1.5.1",
    release_date: "30-08-2023",
    added: [],
    improvements: [],
    bugFixes: [
      {
        description: "Minor bug fixes, related to multiple components",
      },
    ],
  },
  {
    version: "1.5.0",
    release_date: "30-08-2023",
    added: [
      {
        story_link: "vms-react-google-map--map-with-advance-marker",
        story_name: "Google Map",
        story_description: "Google Map Component",
      },
    ],
    improvements: [
      {
        description:
          "Added showTodayDate  Attributes Date Range Calendar for Desktop View",
      },
      {
        description:
          "Added todaysDateCss  Attributes Date Range Calendar for Desktop View",
      },
      {
        description: "Code && Bundle Size optimization",
      },
    ],
    bugFixes: [
      {
        description: "Minor bug fixes, related to multiple components",
      },
    ],
  },

  {
    version: "1.4.4",
    release_date: "02-08-2023",
    added: [],
    improvements: [
      {
        description: "Added AutoFocus Attributes for Date Range Calendar",
      },
      {
        description: "PaxSelection: EditBtnIcon new Extra props is added",
      },
      {
        description: "PaxSelection: DeleteBtnIcon new Extra props is added",
      },
      {
        description: "PaxSelection: doneButton new Extra props is added",
      },
    ],
    bugFixes: [
      {
        description: "Minor bug fixes, related to multiple components",
      },
    ],
  },

  {
    version: "1.4.3",
    release_date: "08-06-2023",
    added: [],
    improvements: [],
    bugFixes: [
      {
        description: "Minor bug fixes, related to multiple components",
      },
    ],
  },
  {
    version: "1.4.2",
    release_date: "31-05-2023",
    added: [],
    improvements: [],
    bugFixes: [
      {
        description: "Minor bug fixes, related to multiple components",
      },
    ],
  },
  {
    version: "1.4.1",
    release_date: "30-05-2023",
    added: [],
    improvements: [],
    bugFixes: [
      {
        description: "Minor bug fixes, related to multiple components",
      },
    ],
  },
  {
    version: "1.4.0",
    release_date: "17-05-2023",
    added: [
      {
        story_link: "vms-react-timepicker--default",
        story_name: "Time Picker",
        story_description: "Time Picker Component",
      },
      {
        story_link: "vms-react-calendar-v1--default",
        story_name: "Calendar V1",
        story_description: "Calendar V1 Component",
      },
      {
        story_link: "vms-react-daterangecalendar-v1--default",
        story_name: "DateRangeCalendar V1",
        story_description: "DateRangeCalendar V1 Component",
      },
      {
        story_link: "vms-react-daterangecalendarmobile-v1--default",
        story_name: "DateRangeCalendarMobile V1",
        story_description: "DateRangeCalendarMobile V1 Component",
      },
      {
        story_link: "vms-react-accordiongroup--default",
        story_name: "AccordionGroup",
        story_description: "AccordionGroup Component",
      },
      {
        story_link: "vms-react-tooltip--default",
        story_name: "ToolTip",
        story_description: "ToolTip Component",
      },
      {
        story_link: "vms-react-tabs--tabs-default",
        story_name: "Tabs",
        story_description: "Tabs Component",
      },
    ],
    improvements: [
      {
        description:
          "Added new feature of Mobile Input Text filter data by country code and country name",
      },
      {
        description:
          "ProgressTracker: ActiveStepColor new Extra props is added",
      },
      {
        description: "FilterBar: selectedFilterColor new Extra props is added",
      },
      {
        description:
          "Chip: selectedChipStyle new Extra props is added for selected chip",
      },
      {
        description:
          "ToastMessage : leftView,rightView and delayTime new Props is added with animation.",
      },
      {
        description: "modalpop:Added new prop as animationType",
      },
      {
        description: "Added customize icon props to below components",
        subDescription: [
          {
            subPoint:
              "Anchored Horizontal Tab : Add new props as customiseIcons(rightIcon,leftIcon)",
          },
          {
            subPoint: "BottomBar : Added new props as (image,selectedimage)",
          },
          {
            subPoint: "FilterBar : Added new prop as (fixFilterSelectedIcon)",
          },
          {
            subPoint: "InputRadioButton : Added new prop as (icons)",
          },
          {
            subPoint: "ModalPopup : Added new prop as (customiseCloseIcon)",
          },
          {
            subPoint:
              "Progress Stepper : Added new prop as (customiseBackIcon)",
          },
          {
            subPoint:
              "Traveller Class:Added new props as (inside passengerData)",
          },
        ],
      },
    ],
    bugFixes: [
      {
        description: "Minor bug fixes, related to multiple components",
      },
    ],
  },
  {
    version: "1.3.3",
    release_date: "01-02-2023",
    added: [],
    improvements: [
      {
        description: "Added manual edit input box in stepper component",
      },
    ],
    bugFixes: [
      {
        description: "Minor bug fixes, related to multiple components",
      },
    ],
  },
  {
    version: "1.3.2",
    release_date: "27-01-2023",
    added: [],
    improvements: [],
    bugFixes: [
      {
        description: "Minor bug fixes, related to multiple components",
      },
    ],
  },
  {
    version: "1.3.1",
    release_date: "10-01-2023",
    added: [],
    improvements: [],
    bugFixes: [
      {
        description: "Minor bug fixes, related to multiple components",
      },
    ],
  },
  {
    version: "1.3.0",
    release_date: "09-01-2023",
    added: [
      {
        story_link: "vms-react-autocomplete--default",
        story_name: "Autocomplete",
        story_description: "Autocomplete Component",
      },
      {
        story_link: "vms-react-bottombar--default",
        story_name: "BottomBar",
        story_description: "BottomBar Component",
      },
      {
        story_link: "vms-react-chip--single-selection",
        story_name: "Chip",
        story_description: "Chip Component",
      },
      {
        story_link: "vms-react-filterbar--default",
        story_name: "FilterBar",
        story_description: "FilterBar Component",
      },
      {
        story_link: "vms-react-filtertag--default",
        story_name: "FilterTag",
        story_description: "FilterTag Component",
      },
      {
        story_link: "vms-react-fairbreakup--default",
        story_name: "FairBreakup Tab",
        story_description: "FairBreakup Component",
      },
      {
        story_link: "vms-react-lazyimage--default",
        story_name: "LazyImage",
        story_description: "LazyImage Component",
      },
      {
        story_link: "vms-react-mobileinputtext--default",
        story_name: "MobileInputText",
        story_description: "MobileInputText Component",
      },
      {
        story_link: "vms-react-progresstracker--two-steps",
        story_name: "ProgressTracker",
        story_description: "ProgressTracker Component",
      },
      {
        story_link: "vms-react-ratingbar--using-external-images",
        story_name: "RatingBar",
        story_description: "RatingBar Component",
      },

      {
        story_link: "vms-react-iconbutton--default",
        story_name: "IconButton",
        story_description: "IconButton Component",
      },
      {
        story_link: "vms-react-toastmessage--default",
        story_name: "ToastMessage",
        story_description: "ToastMessage Component",
      },
    ],
    improvements: [],
    bugFixes: [],
  },
  {
    version: "1.2.0",
    release_date: "04-11-2022",
    added: [
      {
        story_link: "vms-react-pagination--default",
        story_name: "Pagination",
        story_description: "Pagination Component",
      },
      {
        story_link: "vms-react-index-slider--default",
        story_name: "Index Slider",
        story_description: "Index Slider Component",
      },
      {
        story_link: "vms-react-input-date-field--default",
        story_name: "Input DateField",
        story_description: "Input DateField Component",
      },
      {
        story_link: "vms-react-anchored-horizontal-tabs--default-tabs",
        story_name: "Anchored Horizontal Tab",
        story_description: " Anchored Horizontal Tab Component",
      },
    ],
    improvements: [],
    bugFixes: [],
  },
  {
    version: "1.1.0",
    release_date: "02-11-2022",
    added: [
      {
        story_link: "vms-react-progress-stepper--default",
        story_name: "Progress stepper",
        story_description: "Progress stepper Component",
      },
      {
        story_link: "vms-react-paxselection--default",
        story_name: "Pax selection",
        story_description: "Pax selection Component",
      },
    ],
    improvements: [
      // {
      //   description: "Improved inline documentation",
      // },
    ],
    bugFixes: [
      // {
      //   description: "Fixed RangeSelector Slider issue",
      // },
    ],
  },
];
