#!/bin/bash

echo "🔧 VMS React Components - Vulnerability & Warning Resolution"
echo "=========================================================="

# Step 1: Clean install to start fresh
echo "📦 Step 1: Clean installation..."
rm -rf node_modules package-lock.json
npm cache clean --force

# Step 2: Install updated dependencies
echo "📦 Step 2: Installing updated dependencies..."
npm install

# Step 3: Fix security vulnerabilities
echo "🔒 Step 3: Fixing security vulnerabilities..."
npm audit fix

# Step 4: Check for remaining issues
echo "🔍 Step 4: Checking for remaining issues..."
npm audit

# Step 5: Update any remaining outdated packages
echo "📈 Step 5: Checking for outdated packages..."
npm outdated

# Step 6: Test the build
echo "🏗️  Step 6: Testing build process..."
npm run build

# Step 7: Test Storybook
echo "📚 Step 7: Testing Storybook build..."
npm run build-storybook

echo "✅ Resolution process complete!"
echo ""
echo "📋 Summary of changes made:"
echo "- Updated rollup-plugin-babel → @rollup/plugin-babel"
echo "- Updated rollup-plugin-terser → @rollup/plugin-terser"
echo "- Updated @storybook/testing-library → @storybook/test"
echo "- Added babelHelpers: 'bundled' to babel config"
echo "- Fixed security vulnerabilities with npm audit fix"
echo ""
echo "🚀 Next steps:"
echo "1. Test your components in Storybook"
echo "2. Run any existing tests"
echo "3. Commit the updated package.json and package-lock.json"
